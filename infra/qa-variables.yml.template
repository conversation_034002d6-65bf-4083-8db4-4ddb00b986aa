# QA Environment Variables Template
# Copy this to qa-variables.yml and update values as needed
# This file should be added as GitHub secret: QA_ANSIBLE_VARIABLES

domain: "ems.atharyouth.org"

# QA-specific configurations
qa_domain_prefix: "qa"  # Results in qa.ems.atharyouth.org

# Traefik configuration (shared with production)
traefik:
  network: "athar-network"
  # QA uses existing Traefik instance - no separate auth needed

# Docker registry (shared with production)
registry:
  username: "athar"
  password: "Reg@athar25May"

# Rails configuration
rails_master_key: "cc141d0660907d7e9249e168c198fc74"

# Fury.io token (shared with production)
fury_io_token: "1vTAEX-cwWMTXij5BALmoQMENxcg6ZJy0Q"

# Shared tools (MinIO - shared with production)
shared_tools:
  minio_username: "minioadmin"
  minio_password: "minioadmin123"

# QA Application configurations with resource limits
apps:
  core:
    image_path: "athar/core"
    db_password: "qaDbPass@123"
    # Resource limits for QA
    cpu_limit: "0.5"
    memory_limit: "256M"
    cpu_reservation: "0.1"
    memory_reservation: "128M"
    # Database resource limits
    db_cpu_limit: "0.3"
    db_memory_limit: "512M"
    db_cpu_reservation: "0.1"
    db_memory_reservation: "256M"

  people:
    image_path: "athar/people"
    db_password: "qaDbPass@123"
    # Resource limits for QA
    cpu_limit: "0.5"
    memory_limit: "256M"
    cpu_reservation: "0.1"
    memory_reservation: "128M"
    # Database resource limits
    db_cpu_limit: "0.3"
    db_memory_limit: "512M"
    db_cpu_reservation: "0.1"
    db_memory_reservation: "256M"

  procure:
    image_path: "athar/procure"
    db_password: "qaDbPass@123"
    # Resource limits for QA
    cpu_limit: "0.5"
    memory_limit: "256M"
    cpu_reservation: "0.1"
    memory_reservation: "128M"
    # Database resource limits
    db_cpu_limit: "0.3"
    db_memory_limit: "512M"
    db_cpu_reservation: "0.1"
    db_memory_reservation: "256M"

  case_manager:
    image_path: "athar/case-manager"
    db_password: "qaDbPass@123"
    # Resource limits for QA
    cpu_limit: "0.5"
    memory_limit: "256M"
    cpu_reservation: "0.1"
    memory_reservation: "128M"
    # Database resource limits
    db_cpu_limit: "0.3"
    db_memory_limit: "512M"
    db_cpu_reservation: "0.1"
    db_memory_reservation: "256M"

  frontend:
    image_path: "athar/frontend"
    # Resource limits for QA
    cpu_limit: "0.5"
    memory_limit: "256M"
    cpu_reservation: "0.1"
    memory_reservation: "128M"

# QA Database configurations
qa_database:
  # PostgreSQL optimizations for QA environment
  shared_buffers: "64MB"
  effective_cache_size: "128MB"
  work_mem: "2MB"
  maintenance_work_mem: "16MB"
  max_connections: "20"
  
  # Database naming convention
  suffix: "_qa"  # Results in core_athar_qa_db, people_athar_qa_db, etc.

# QA Environment settings
qa_environment:
  # Container naming prefix
  container_prefix: "qa-"
  
  # Network settings (uses existing athar-network)
  network_name: "athar-network"
  
  # Deployment path on server
  deploy_path: "/home/<USER>/qa-deployment"
  
  # Health check settings
  health_check_timeout: "30s"
  health_check_interval: "10s"
  health_check_retries: 3
  
  # Restart policy
  restart_policy: "on-failure"
  restart_max_attempts: 3
