name: athar-case-manager

services:
  app:
    image: {{ registry_host }}/{{ apps.case_manager.image_path }}:{{ submodules_info.submodules["services/case-manager"] }}
    hostname: case-manager-app
    environment:
      DB_HOST: case-manager-db
      DB_PASSWORD: "{{ apps.case_manager.db_password }}"
      PORT: 3000
      RAILS_MASTER_KEY: {{ rails_master_key }}
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.cm.rule=Host(`cm.{{ domain }}`)"
      - "traefik.http.routers.cm.entrypoints=web,websecure"
      - "traefik.http.routers.cm.tls.certresolver=myresolver"
      - "traefik.http.routers.cm.tls=true"
      - "traefik.http.services.cm.loadbalancer.server.port=3000"
      - "traefik.http.services.cm.loadbalancer.healthcheck.path=/up"
      - "traefik.http.services.cm.loadbalancer.healthcheck.interval=10s"
      - "traefik.http.services.cm.loadbalancer.healthcheck.timeout=3s"
    networks:
      - athar-network
    deploy:
      restart_policy:
        condition: on-failure
      resources:
        limits:
          cpus: '1.0'
          memory: 512M

  case-manager-db:
    image: postgres:17
    container_name: case-manager-db
    restart: unless-stopped
    environment:
      POSTGRES_USER: "postgres"
      POSTGRES_PASSWORD: "{{ apps.case_manager.db_password }}"
      POSTGRES_DB: "case-manager_athar_db"
      GRPC_BACKTRACE_ON_ERROR: "1"
      GRPC_SERVER_HOST: "0.0.0.0"
      GRPC_SERVER_PORT: "10541"
{#      GRPC_SERVER_POOL_SIZE: "100"#}
{#      GRPC_SERVER_POOL_KEEP_ALIVE: "1"#}
{#      GRPC_SERVER_POLL_PERIOD: "1"#}
    networks:
      - athar-network
    volumes:
      - db-data:/var/lib/postgresql/data
    labels:
      - "traefik.enable=false"

networks:
  athar-network:
    external: true
    name: proxy_{{ traefik.network }}

volumes:
  db-data:
