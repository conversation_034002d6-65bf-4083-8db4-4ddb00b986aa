name: athar-frontend

services:
  app:
    image: {{ registry_host }}/{{ apps.frontend.image_path }}:{{ submodules_info.submodules["services/frontend"] }}
    hostname: frontend-app
    environment:
      HOSTNAME: "0.0.0.0"
      PORT: "3000"
      BASE_API_URL: "https://{subsystem}.{{ domain }}"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.app.rule=Host(`app.{{ domain }}`)"
      - "traefik.http.routers.app.entrypoints=web,websecure"
      - "traefik.http.routers.app.tls.certresolver=myresolver"
      - "traefik.http.routers.app.tls=true"
      - "traefik.http.services.app.loadbalancer.server.port=3000"
      - "traefik.http.services.app.loadbalancer.healthcheck.path=/api/health"
      - "traefik.http.services.app.loadbalancer.healthcheck.interval=10s"
      - "traefik.http.services.app.loadbalancer.healthcheck.timeout=3s"
    networks:
      - athar-network
    deploy:
      restart_policy:
        condition: on-failure
      resources:
        limits:
          cpus: '1.0'
          memory: 512M

networks:
  athar-network:
    external: true
    name: proxy_{{ traefik.network }}
