name: qa-athar-procure

services:
  qa-app:
    image: {{ registry_host }}/{{ apps.procure.image_path }}:{{ qa_image_tags.procure }}
    hostname: qa-procure-app
    container_name: qa-procure-app
    environment:
      DB_HOST: qa-procure-db
      DB_NAME: "procure_athar{{ qa_database.suffix }}_db"
      DB_PASSWORD: "{{ apps.procure.db_password }}"
      PORT: "3000"
      RAILS_MASTER_KEY: "{{ rails_master_key }}"
      MINIO_ACCESS_KEY: "{{ shared_tools.minio_username }}"
      MINIO_SECRET_KEY: "{{ shared_tools.minio_password }}"
      MINIO_ENDPOINT: "http://minio:9000"
      MINIO_BUCKET: "qa-procure"
      CDN_BASE_URL: "https://minio-api.{{ domain }}"
      RAILS_ENV: "{{ qa_environment.rails_env }}"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.qa-procure.rule=Host(`procure.{{ qa_domain_prefix }}.{{ domain }}`)"
      - "traefik.http.routers.qa-procure.entrypoints=web,websecure"
      - "traefik.http.routers.qa-procure.tls.certresolver=myresolver"
      - "traefik.http.routers.qa-procure.tls=true"
      - "traefik.http.services.qa-procure.loadbalancer.server.port=3000"
      - "traefik.http.services.qa-procure.loadbalancer.healthcheck.path=/up"
      - "traefik.http.services.qa-procure.loadbalancer.healthcheck.interval={{ qa_environment.health_check_interval }}"
      - "traefik.http.services.qa-procure.loadbalancer.healthcheck.timeout={{ qa_environment.health_check_timeout }}"
    networks:
      - {{ qa_environment.network_name }}
    deploy:
      restart_policy:
        condition: {{ qa_environment.restart_policy }}
        max_attempts: {{ qa_environment.restart_max_attempts }}
      resources:
        limits:
          cpus: '{{ apps.procure.cpu_limit }}'
          memory: {{ apps.procure.memory_limit }}
        reservations:
          cpus: '{{ apps.procure.cpu_reservation }}'
          memory: {{ apps.procure.memory_reservation }}
    restart: unless-stopped

  qa-procure-db:
    image: postgres:17
    container_name: qa-procure-db
    hostname: qa-procure-db
    restart: unless-stopped
    environment:
      POSTGRES_USER: "postgres"
      POSTGRES_PASSWORD: "{{ apps.procure.db_password }}"
      POSTGRES_DB: "procure_athar{{ qa_database.suffix }}_db"
      # QA-optimized PostgreSQL settings
      POSTGRES_SHARED_BUFFERS: "{{ qa_database.shared_buffers }}"
      POSTGRES_EFFECTIVE_CACHE_SIZE: "{{ qa_database.effective_cache_size }}"
      POSTGRES_WORK_MEM: "{{ qa_database.work_mem }}"
      POSTGRES_MAINTENANCE_WORK_MEM: "{{ qa_database.maintenance_work_mem }}"
      POSTGRES_MAX_CONNECTIONS: "{{ qa_database.max_connections }}"
    networks:
      - {{ qa_environment.network_name }}
    volumes:
      - qa-procure-db-data:/var/lib/postgresql/data
    labels:
      - "traefik.enable=false"
    deploy:
      resources:
        limits:
          cpus: '{{ apps.procure.db_cpu_limit }}'
          memory: {{ apps.procure.db_memory_limit }}
        reservations:
          cpus: '{{ apps.procure.db_cpu_reservation }}'
          memory: {{ apps.procure.db_memory_reservation }}

networks:
  {{ qa_environment.network_name }}:
    external: true
    name: proxy_{{ traefik.network }}

volumes:
  qa-procure-db-data:
