name: qa-athar-frontend

services:
  qa-app:
    image: {{ registry_host }}/{{ apps.frontend.image_path }}:{{ qa_image_tags.frontend }}
    hostname: qa-frontend-app
    container_name: qa-frontend-app
    environment:
      HOSTNAME: "0.0.0.0"
      PORT: "3000"
      BASE_API_URL: "https://{subsystem}.{{ qa_domain_prefix }}.{{ domain }}"
      NODE_ENV: "production"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.qa-app.rule=Host(`app.{{ qa_domain_prefix }}.{{ domain }}`)"
      - "traefik.http.routers.qa-app.entrypoints=web,websecure"
      - "traefik.http.routers.qa-app.tls.certresolver=myresolver"
      - "traefik.http.routers.qa-app.tls=true"
      - "traefik.http.services.qa-app.loadbalancer.server.port=3000"
      - "traefik.http.services.qa-app.loadbalancer.healthcheck.path=/api/health"
      - "traefik.http.services.qa-app.loadbalancer.healthcheck.interval={{ qa_environment.health_check_interval }}"
      - "traefik.http.services.qa-app.loadbalancer.healthcheck.timeout={{ qa_environment.health_check_timeout }}"
    networks:
      - {{ qa_environment.network_name }}
    deploy:
      restart_policy:
        condition: {{ qa_environment.restart_policy }}
        max_attempts: {{ qa_environment.restart_max_attempts }}
      resources:
        limits:
          cpus: '{{ apps.frontend.cpu_limit }}'
          memory: {{ apps.frontend.memory_limit }}
        reservations:
          cpus: '{{ apps.frontend.cpu_reservation }}'
          memory: {{ apps.frontend.memory_reservation }}
    restart: unless-stopped

networks:
  {{ qa_environment.network_name }}:
    external: true
    name: proxy_{{ traefik.network }}
