name: qa-athar-people

services:
  qa-app:
    image: {{ registry_host }}/{{ apps.people.image_path }}:{{ qa_image_tags.people }}
    hostname: people-app
    container_name: qa-people-app
    environment:
      DB_HOST: people-db
      DB_NAME: "people_athar{{ qa_database.suffix }}_db"
      DB_PASSWORD: "{{ apps.people.db_password }}"
      PORT: "3000"
      RAILS_MASTER_KEY: "{{ rails_master_key }}"
      MINIO_ACCESS_KEY: "{{ shared_tools.minio_username }}"
      MINIO_SECRET_KEY: "{{ shared_tools.minio_password }}"
      MINIO_ENDPOINT: "http://minio:9000"
      MINIO_BUCKET: "qa-people"
      CDN_BASE_URL: "https://minio-api.{{ domain }}"
      RAILS_ENV: "{{ qa_environment.rails_env }}"
      # Chrome headless for PDF generation (shared service)
      CHROME_URL: "http://people-chrome:3000"
      # Redis for background jobs and caching
      REDIS_URL: "redis://people-redis:6379/0"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.qa-people.rule=Host(`people.{{ qa_domain_prefix }}.{{ domain }}`)"
      - "traefik.http.routers.qa-people.entrypoints=web,websecure"
      - "traefik.http.routers.qa-people.tls.certresolver=myresolver"
      - "traefik.http.routers.qa-people.tls=true"
      - "traefik.http.services.qa-people.loadbalancer.server.port=3000"
      - "traefik.http.services.qa-people.loadbalancer.healthcheck.path=/up"
      - "traefik.http.services.qa-people.loadbalancer.healthcheck.interval={{ qa_environment.health_check_interval }}"
      - "traefik.http.services.qa-people.loadbalancer.healthcheck.timeout={{ qa_environment.health_check_timeout }}"
    networks:
      - {{ qa_environment.network_name }}
    deploy:
      restart_policy:
        condition: {{ qa_environment.restart_policy }}
        max_attempts: {{ qa_environment.restart_max_attempts }}
      resources:
        limits:
          cpus: '{{ apps.people.cpu_limit }}'
          memory: {{ apps.people.memory_limit }}
        reservations:
          cpus: '{{ apps.people.cpu_reservation }}'
          memory: {{ apps.people.memory_reservation }}
    restart: unless-stopped

  qa-people-db:
    image: postgres:17
    container_name: qa-people-db
    hostname: people-db
    restart: unless-stopped
    environment:
      POSTGRES_USER: "postgres"
      POSTGRES_PASSWORD: "{{ apps.people.db_password }}"
      POSTGRES_DB: "people_athar{{ qa_database.suffix }}_db"
      # QA-optimized PostgreSQL settings
      POSTGRES_SHARED_BUFFERS: "{{ qa_database.shared_buffers }}"
      POSTGRES_EFFECTIVE_CACHE_SIZE: "{{ qa_database.effective_cache_size }}"
      POSTGRES_WORK_MEM: "{{ qa_database.work_mem }}"
      POSTGRES_MAINTENANCE_WORK_MEM: "{{ qa_database.maintenance_work_mem }}"
      POSTGRES_MAX_CONNECTIONS: "{{ qa_database.max_connections }}"
    networks:
      - {{ qa_environment.network_name }}
    volumes:
      - qa-people-db-data:/var/lib/postgresql/data
    labels:
      - "traefik.enable=false"
    deploy:
      resources:
        limits:
          cpus: '{{ apps.people.db_cpu_limit }}'
          memory: {{ apps.people.db_memory_limit }}
        reservations:
          cpus: '{{ apps.people.db_cpu_reservation }}'
          memory: {{ apps.people.db_memory_reservation }}

  qa-people-redis:
    image: redis:7-alpine
    container_name: qa-people-redis
    hostname: people-redis
    restart: unless-stopped
    networks:
      - {{ qa_environment.network_name }}
    volumes:
      - qa-people-redis-data:/data
    labels:
      - "traefik.enable=false"
    deploy:
      resources:
        limits:
          cpus: '0.2'
          memory: 128M
        reservations:
          cpus: '0.1'
          memory: 64M

  qa-people-sidekiq:
    image: {{ registry_host }}/{{ apps.people.image_path }}:{{ qa_image_tags.people }}
    hostname: people-sidekiq
    container_name: qa-people-sidekiq
    command: bundle exec sidekiq -C config/sidekiq.yml
    environment:
      DB_HOST: people-db
      DB_NAME: "people_athar{{ qa_database.suffix }}_db"
      DB_PASSWORD: "{{ apps.people.db_password }}"
      RAILS_MASTER_KEY: "{{ rails_master_key }}"
      REDIS_URL: "redis://people-redis:6379/0"
      MINIO_ACCESS_KEY: "{{ shared_tools.minio_username }}"
      MINIO_SECRET_KEY: "{{ shared_tools.minio_password }}"
      MINIO_ENDPOINT: "http://minio:9000"
      MINIO_BUCKET: "qa-people"
      CDN_BASE_URL: "https://minio-api.{{ domain }}"
      RAILS_ENV: "{{ qa_environment.rails_env }}"
      # Chrome headless for PDF generation (shared service)
      CHROME_URL: "http://people-chrome:3000"
    networks:
      - {{ qa_environment.network_name }}
    depends_on:
      - qa-people-db
      - qa-people-redis
    deploy:
      restart_policy:
        condition: {{ qa_environment.restart_policy }}
        max_attempts: {{ qa_environment.restart_max_attempts }}
      resources:
        limits:
          cpus: '{{ apps.people.cpu_limit }}'
          memory: {{ apps.people.memory_limit }}
        reservations:
          cpus: '{{ apps.people.cpu_reservation }}'
          memory: {{ apps.people.memory_reservation }}
    labels:
      - "traefik.enable=false"
    restart: unless-stopped

networks:
  {{ qa_environment.network_name }}:
    external: true
    name: proxy_{{ traefik.network }}

volumes:
  qa-people-db-data:
  qa-people-redis-data:
