name: qa-athar-core

services:
  qa-app:
    image: {{ registry_host }}/{{ apps.core.image_path }}:{{ qa_image_tags.core }}
    hostname: qa-core-app
    container_name: qa-core-app
    environment:
      DB_HOST: core-db
      DB_NAME: "core_athar{{ qa_database.suffix }}_db"
      DB_PASSWORD: "{{ apps.core.db_password }}"
      PORT: "3000"
      RAILS_MASTER_KEY: "{{ rails_master_key }}"
      MINIO_ACCESS_KEY: "{{ shared_tools.minio_username }}"
      MINIO_SECRET_KEY: "{{ shared_tools.minio_password }}"
      MINIO_ENDPOINT: "http://minio:9000"
      MINIO_BUCKET: "qa-core"
      CDN_BASE_URL: "https://minio-api.{{ domain }}"
      RAILS_ENV: "{{ qa_environment.rails_env }}"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.qa-core.rule=Host(`core.{{ qa_domain_prefix }}.{{ domain }}`)"
      - "traefik.http.routers.qa-core.entrypoints=web,websecure"
      - "traefik.http.routers.qa-core.tls.certresolver=myresolver"
      - "traefik.http.routers.qa-core.tls=true"
      - "traefik.http.services.qa-core.loadbalancer.server.port=3000"
      - "traefik.http.services.qa-core.loadbalancer.healthcheck.path=/up"
      - "traefik.http.services.qa-core.loadbalancer.healthcheck.interval={{ qa_environment.health_check_interval }}"
      - "traefik.http.services.qa-core.loadbalancer.healthcheck.timeout={{ qa_environment.health_check_timeout }}"
    networks:
      - {{ qa_environment.network_name }}
    deploy:
      restart_policy:
        condition: {{ qa_environment.restart_policy }}
        max_attempts: {{ qa_environment.restart_max_attempts }}
      resources:
        limits:
          cpus: '{{ apps.core.cpu_limit }}'
          memory: {{ apps.core.memory_limit }}
        reservations:
          cpus: '{{ apps.core.cpu_reservation }}'
          memory: {{ apps.core.memory_reservation }}
    restart: unless-stopped

  qa-grpc:
    image: {{ registry_host }}/{{ apps.core.image_path }}:{{ qa_image_tags.core }}
    hostname: core-app-grpc
    container_name: qa-core-grpc
    command: grpc
    environment:
      DB_HOST: core-db
      DB_NAME: "core_athar{{ qa_database.suffix }}_db"
      DB_PASSWORD: "{{ apps.core.db_password }}"
      GRPC_SERVER_PORT: 10541
      BUNDLE_GEM__FURY__IO: "{{ fury_io_token }}"
      RAILS_MASTER_KEY: "{{ rails_master_key }}"
      MINIO_ACCESS_KEY: "{{ shared_tools.minio_username }}"
      MINIO_SECRET_KEY: "{{ shared_tools.minio_password }}"
      MINIO_ENDPOINT: "http://minio:9000"
      MINIO_BUCKET: "qa-core"
      CDN_BASE_URL: "https://minio-api.{{ domain }}"
      RAILS_ENV: "{{ qa_environment.rails_env }}"
    networks:
      - {{ qa_environment.network_name }}
    deploy:
      restart_policy:
        condition: {{ qa_environment.restart_policy }}
        max_attempts: {{ qa_environment.restart_max_attempts }}
      resources:
        limits:
          cpus: '0.3'  # Reduced for gRPC service
          memory: 512M  # Increased for Rails gRPC service
        reservations:
          cpus: '0.1'
          memory: 256M  # Increased reservation
    restart: unless-stopped

  qa-core-db:
    image: postgres:17
    container_name: qa-core-db
    hostname: core-db
    restart: unless-stopped
    environment:
      POSTGRES_USER: "postgres"
      POSTGRES_PASSWORD: "{{ apps.core.db_password }}"
      POSTGRES_DB: "core_athar{{ qa_database.suffix }}_db"
      # QA-optimized PostgreSQL settings
      POSTGRES_SHARED_BUFFERS: "{{ qa_database.shared_buffers }}"
      POSTGRES_EFFECTIVE_CACHE_SIZE: "{{ qa_database.effective_cache_size }}"
      POSTGRES_WORK_MEM: "{{ qa_database.work_mem }}"
      POSTGRES_MAINTENANCE_WORK_MEM: "{{ qa_database.maintenance_work_mem }}"
      POSTGRES_MAX_CONNECTIONS: "{{ qa_database.max_connections }}"
    networks:
      - {{ qa_environment.network_name }}
    volumes:
      - qa-core-db-data:/var/lib/postgresql/data
    labels:
      - "traefik.enable=false"
    deploy:
      resources:
        limits:
          cpus: '{{ apps.core.db_cpu_limit }}'
          memory: {{ apps.core.db_memory_limit }}
        reservations:
          cpus: '{{ apps.core.db_cpu_reservation }}'
          memory: {{ apps.core.db_memory_reservation }}

networks:
  {{ qa_environment.network_name }}:
    external: true
    name: proxy_{{ traefik.network }}

volumes:
  qa-core-db-data:
