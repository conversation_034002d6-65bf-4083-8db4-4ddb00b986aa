name: athar-core

services:
  app:
    image: {{ registry_host }}/{{ apps.core.image_path }}:{{ submodules_info.submodules["services/core"] }}
    hostname: core-app
    environment:
      DB_HOST: core-db
      DB_PASSWORD: "{{ apps.core.db_password }}"
      PORT: "3000"
      RAILS_MASTER_KEY: "{{ rails_master_key }}"
      MINIO_ACCESS_KEY: "{{ shared_tools.minio_username }}"
      MINIO_SECRET_KEY: "{{ shared_tools.minio_password }}"
      MINIO_ENDPOINT: "http://minio:9000"
      MINIO_BUCKET: "core"
      CDN_BASE_URL: "https://minio-api.{{ domain }}"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.core.rule=Host(`core.{{ domain }}`)"
      - "traefik.http.routers.core.entrypoints=web,websecure"
      - "traefik.http.routers.core.tls.certresolver=myresolver"
      - "traefik.http.routers.core.tls=true"
      - "traefik.http.services.core.loadbalancer.server.port=3000"
      - "traefik.http.services.core.loadbalancer.healthcheck.path=/up"
      - "traefik.http.services.core.loadbalancer.healthcheck.interval=10s"
      - "traefik.http.services.core.loadbalancer.healthcheck.timeout=3s"
    networks:
      - athar-network
    deploy:
      restart_policy:
        condition: on-failure
      resources:
        limits:
          cpus: '1.0'
          memory: 512M

  grpc:
    image: {{ registry_host }}/{{ apps.core.image_path }}:{{ submodules_info.submodules["services/core"] }}
    hostname: core-app-grpc
    command: grpc
    environment:
      DB_HOST: core-db
      DB_PASSWORD: "{{ apps.core.db_password }}"
      GRPC_SERVER_PORT: 10541
      BUNDLE_GEM__FURY__IO: "{{ fury_io_token }}"
      RAILS_MASTER_KEY: "{{ rails_master_key }}"
      MINIO_ACCESS_KEY: "{{ shared_tools.minio_username }}"
      MINIO_SECRET_KEY: "{{ shared_tools.minio_password }}"
      MINIO_ENDPOINT: "http://minio:9000"
      MINIO_BUCKET: "core"
      CDN_BASE_URL: "https://minio-api.{{ domain }}"
    networks:
      - athar-network
    deploy:
      restart_policy:
        condition: on-failure
      resources:
        limits:
          cpus: '0.5'
          memory: 256M

  core-db:
    image: postgres:17
    container_name: core-db
    restart: unless-stopped
    environment:
      POSTGRES_USER: "postgres"
      POSTGRES_PASSWORD: "{{ apps.core.db_password }}"
      POSTGRES_DB: "core_athar_db"
      GRPC_BACKTRACE_ON_ERROR: "1"
      GRPC_SERVER_HOST: "0.0.0.0"
      GRPC_SERVER_PORT: "10541"
{#      GRPC_SERVER_POOL_SIZE: "100"#}
{#      GRPC_SERVER_POOL_KEEP_ALIVE: "1"#}
{#      GRPC_SERVER_POLL_PERIOD: "1"#}
    networks:
      - athar-network
    volumes:
      - db-data:/var/lib/postgresql/data
    labels:
      - "traefik.enable=false"

networks:
  athar-network:
    external: true
    name: proxy_{{ traefik.network }}

volumes:
  db-data:
