name: athar-people

services:
  app:
    image: {{ registry_host }}/{{ apps.people.image_path }}:{{ submodules_info.submodules["services/people"] }}
    hostname: people-app
    environment:
      DB_HOST: people-db
      DB_PASSWORD: "{{ apps.people.db_password }}"
      PORT: 3000
      RAILS_MASTER_KEY: {{ rails_master_key }}
      MINIO_ACCESS_KEY: "{{ shared_tools.minio_username }}"
      MINIO_SECRET_KEY: "{{ shared_tools.minio_password }}"
      MINIO_ENDPOINT: "http://minio:9000"
      MINIO_BUCKET: "people"
      CDN_BASE_URL: "https://minio-api.{{ domain }}"
      REDIS_URL: "redis://people-redis:6379/0"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.people.rule=Host(`people.{{ domain }}`)"
      - "traefik.http.routers.people.entrypoints=web,websecure"
      - "traefik.http.routers.people.tls.certresolver=myresolver"
      - "traefik.http.routers.people.tls=true"
      - "traefik.http.services.people.loadbalancer.server.port=3000"
      - "traefik.http.services.people.loadbalancer.healthcheck.path=/up"
      - "traefik.http.services.people.loadbalancer.healthcheck.interval=10s"
      - "traefik.http.services.people.loadbalancer.healthcheck.timeout=3s"
    networks:
      - athar-network
    deploy:
      restart_policy:
        condition: on-failure
      resources:
        limits:
          cpus: '1.0'
          memory: 512M

  people-db:
    image: postgres:17
    container_name: people-db
    restart: unless-stopped
    environment:
      POSTGRES_USER: "postgres"
      POSTGRES_PASSWORD: "{{ apps.people.db_password }}"
      POSTGRES_DB: "people_athar_db"
      GRPC_BACKTRACE_ON_ERROR: "1"
      GRPC_SERVER_HOST: "0.0.0.0"
      GRPC_SERVER_PORT: "10541"
{#      GRPC_SERVER_POOL_SIZE: "100"#}
{#      GRPC_SERVER_POOL_KEEP_ALIVE: "1"#}
{#      GRPC_SERVER_POLL_PERIOD: "1"#}
    networks:
      - athar-network
    volumes:
      - db-data:/var/lib/postgresql/data
    labels:
      - "traefik.enable=false"

  sidekiq:
    image: {{ registry_host }}/{{ apps.people.image_path }}:{{ submodules_info.submodules["services/people"] }}
    hostname: people-sidekiq
    command: bundle exec sidekiq -C config/sidekiq.yml
    environment:
      DB_HOST: people-db
      DB_PASSWORD: "{{ apps.people.db_password }}"
      RAILS_MASTER_KEY: {{ rails_master_key }}
      REDIS_URL: "redis://people-redis:6379/0"
      MINIO_ACCESS_KEY: "{{ shared_tools.minio_username }}"
      MINIO_SECRET_KEY: "{{ shared_tools.minio_password }}"
      MINIO_ENDPOINT: "http://minio:9000"
      MINIO_BUCKET: "people"
      CDN_BASE_URL: "https://minio-api.{{ domain }}"
    networks:
      - athar-network
    depends_on:
      - people-db
      - people-redis
    deploy:
      restart_policy:
        condition: on-failure
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
    labels:
      - "traefik.enable=false"

  people-redis:
    image: redis:7-alpine
    container_name: people-redis
    restart: unless-stopped
    networks:
      - athar-network
    volumes:
      - redis-data:/data
    labels:
      - "traefik.enable=false"

networks:
  athar-network:
    external: true
    name: proxy_{{ traefik.network }}

volumes:
  db-data:
  redis-data:
