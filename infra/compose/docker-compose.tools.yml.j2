name: tools

services:
  ################################################################
  # 1. <PERSON><PERSON><PERSON> (UI) & Portainer Agent
  ################################################################
  portainer_agent:
    image: portainer/agent:latest
    restart: always
    volumes:
      - "/var/run/docker.sock:/var/run/docker.sock"
      - "/var/lib/docker/volumes:/var/lib/docker/volumes"
      - "/:/host"
    networks:
      - athar-network
      - qa-network

  portainer:
    image: portainer/portainer-ce:latest
    restart: unless-stopped
    # check this later https://gist.github.com/deviantony/62c009b41bde5e078b1a7de9f11f5e55
    command: --admin-password '{{ portainer_password_encrypted }}'
{#    command: --admin-password-file /tmp/htpasswd#}
{#    command: --admin-password '{{ portainer_password }}''#}
{#    labels:#}
{#      - "traefik.enable=true"#}
{#      - "traefik.http.routers.portainer.rule=Host(`portainer.{{ domain }}`)"#}
{#      - "traefik.http.routers.portainer.entrypoints=web,websecure"#}
{#      - "traefik.http.routers.portainer.service=portainer"#}
{#      - "traefik.http.routers.portainer.tls.certresolver=myresolver"#}
{#      - "traefik.http.routers.portainer.tls=true"#}
{#      - "traefik.http.services.portainer.loadbalancer.server.port=9000"#}
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.portainer.rule=Host(`portainer.{{ domain }}`)"
      - "traefik.http.routers.portainer.entrypoints=web,websecure"
      - "traefik.http.routers.portainer.tls.certresolver=myresolver"
      - "traefik.http.routers.portainer.service=portainer"
      - "traefik.http.routers.portainer.tls=true"
      - "traefik.http.services.portainer.loadbalancer.server.port=9000"
    ports:
      - "9000:9000"
    volumes:
      - portainer_data:/data
      - ./portainer_htpasswd:/tmp/htpasswd:ro
    networks:
      - athar-network
      - qa-network

  ################################################################
  # 2. Private Docker Registry + UI
  ################################################################
  registry-server:
    image: registry:3.0.0-rc.3
    restart: always
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.registry-server.rule=Host(`registry.{{ domain }}`) && PathPrefix(`/v2`)"
      - "traefik.http.routers.registry-server.entrypoints=web,websecure"
      - "traefik.http.routers.registry-server.tls.certresolver=myresolver"
      - "traefik.http.routers.registry-server.service=registry-server"
      - "traefik.http.routers.registry-server.priority=20"
      - "traefik.http.routers.registry-server.tls=true"
      - "traefik.http.services.registry-server.loadbalancer.server.port=5000"
    environment:
      REGISTRY_AUTH: htpasswd
      REGISTRY_AUTH_HTPASSWD_REALM: "Registry Realm"
      REGISTRY_AUTH_HTPASSWD_PATH: "/auth/htpasswd"
      REGISTRY_STORAGE_FILESYSTEM_ROOTDIRECTORY: "/var/lib/registry"
{#      REGISTRY_HTTP_HEADERS_Access-Control-Allow-Origin: "[\"*\"]"#}
{#      REGISTRY_HTTP_HEADERS_Access-Control-Allow-Methods: "[\"HEAD\", \"GET\", \"OPTIONS\", \"DELETE\"]"#}
{#      REGISTRY_HTTP_HEADERS_Access-Control-Allow-Credentials: "[\"true\"]"#}
    volumes:
      - registry_data:/var/lib/registry
      - ./registry_htpasswd:/auth/htpasswd
    networks:
      - athar-network
      - qa-network

  registry-ui:
    image: joxit/docker-registry-ui:main
    restart: always
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.registry-ui.rule=Host(`registry.{{ domain }}`) && PathPrefix(`/`)"
      - "traefik.http.routers.registry-ui.entrypoints=web,websecure"
      - "traefik.http.routers.registry-ui.tls.certresolver=myresolver"
      - "traefik.http.routers.registry-ui.service=registry-ui"
      - "traefik.http.routers.registry-ui.priority=10"
      - "traefik.http.routers.registry-ui.tls=true"
      - "traefik.http.services.registry-ui.loadbalancer.server.port=80"
    environment:
      SINGLE_REGISTRY: "true"
      REGISTRY_TITLE: "Athar Registry"
      DELETE_IMAGES: "true"
      SHOW_CONTENT_DIGEST: "true"
      NGINX_PROXY_PASS_URL: "http://registry-server:5000"
      SHOW_CATALOG_NB_TAGS: "true"
      CATALOG_MIN_BRANCHES: "1"
      CATALOG_MAX_BRANCHES: "1"
      TAGLIST_PAGE_SIZE: "100"
      REGISTRY_SECURED: "false"
      CATALOG_ELEMENTS_LIMIT: "1000"
      NGINX_LISTEN_PORT: "80"
    depends_on:
      - registry-server
    networks:
      - athar-network
      - qa-network

  minio:
    image: minio/minio:latest
    command: server /data --console-address ":9001"
    container_name: minio
    environment:
      MINIO_ROOT_USER: "{{ shared_tools.minio_username }}"
      MINIO_ROOT_PASSWORD: {{ shared_tools.minio_password }}
    volumes:
      - minio_data:/data
    networks:
      - athar-network
      - qa-network
    labels:
      - "traefik.enable=true"

      # S3 API
      - "traefik.http.routers.minio-api.rule=Host(`minio-api.{{ domain }}`)"
      - "traefik.http.routers.minio-api.entrypoints=web,websecure"
      - "traefik.http.routers.minio-api.service=minio-api"
      - "traefik.http.routers.minio-api.tls=true"
      - "traefik.http.routers.minio-api.tls.certresolver=myresolver"
      - "traefik.http.services.minio-api.loadbalancer.server.port=9000"

      # Web UI
      - "traefik.http.routers.minio-console.rule=Host(`minio.{{ domain }}`)"
      - "traefik.http.routers.minio-console.entrypoints=web,websecure"
      - "traefik.http.routers.minio-console.service=minio-console"
      - "traefik.http.routers.minio-console.tls=true"
      - "traefik.http.routers.minio-console.tls.certresolver=myresolver"
      - "traefik.http.services.minio-console.loadbalancer.server.port=9001"
    restart: unless-stopped

################################################################
# 3. Browser Automation (Chrome Headless)
################################################################
  chrome-headless:
    image: browserless/chrome:latest
    hostname: people-chrome
    container_name: people-chrome
    networks:
      - athar-network
      - qa-network
    ports:
      - "9222:3000"
    environment:
      - CONCURRENT=10
      - TOKEN=
      - MAX_CONCURRENT_SESSIONS=10
      - PREBOOT_CHROME=true
      - KEEP_ALIVE=true
      - CHROME_REFRESH_TIME=2000
    labels:
      - "traefik.enable=false"


################################################################
# 4. Networks & Volumes
################################################################
networks:
  athar-network:
    external: true
    name: proxy_{{ traefik.network }}
  qa-network:
    external: true
    name: proxy_qa-network

volumes:
  registry_data:
  portainer_data:
  minio_data:
