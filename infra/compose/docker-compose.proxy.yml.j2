name: proxy

services:
  ################################################################
  # 1. Trae<PERSON><PERSON> (Reverse Proxy)
  ################################################################
  traefik:
    image: traefik:v3.3.3
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.traefik.rule=Host(`traefik.{{ domain }}`)"
      - "traefik.http.routers.traefik.entrypoints=web,websecure"
      - "traefik.http.services.traefik.loadbalancer.server.port=8080"
      - "traefik.http.routers.traefik.tls=true"
      - "traefik.http.routers.traefik.tls.certresolver=myresolver"
      - "traefik.http.routers.traefik.middlewares=auth@docker"
      - "traefik.http.middlewares.auth.basicauth.users={{ traefik_password_encrypted }}"
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - "/var/run/docker.sock:/var/run/docker.sock:ro"
      - "./traefik.yml:/etc/traefik/traefik.yml:ro"
      - "traefik_logs:/var/log/traefik"
      - "./acme.json:/etc/traefik/acme.json"
    networks:
      - {{ traefik.network }}
    restart: unless-stopped

################################################################
# 4. Networks & Volumes
################################################################
networks:
  {{ traefik.network }}:
    driver: bridge

volumes:
  traefik_logs:
