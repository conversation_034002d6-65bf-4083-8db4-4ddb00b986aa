name: qa-athar-case-manager

services:
  qa-app:
    image: {{ registry_host }}/{{ apps.case_manager.image_path }}:{{ qa_image_tags.case_manager }}
    hostname: qa-case-manager-app
    container_name: qa-case-manager-app
    environment:
      DB_HOST: qa-case-manager-db
      DB_NAME: "case_manager_athar{{ qa_database.suffix }}_db"
      DB_PASSWORD: "{{ apps.case_manager.db_password }}"
      PORT: "3000"
      RAILS_MASTER_KEY: "{{ rails_master_key }}"
      MINIO_ACCESS_KEY: "{{ shared_tools.minio_username }}"
      MINIO_SECRET_KEY: "{{ shared_tools.minio_password }}"
      MINIO_ENDPOINT: "http://minio:9000"
      MINIO_BUCKET: "qa-case-manager"
      CDN_BASE_URL: "https://minio-api.{{ domain }}"
      RAILS_ENV: "{{ qa_environment.rails_env }}"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.qa-cm.rule=Host(`cm.{{ qa_domain_prefix }}.{{ domain }}`)"
      - "traefik.http.routers.qa-cm.entrypoints=web,websecure"
      - "traefik.http.routers.qa-cm.tls.certresolver=myresolver"
      - "traefik.http.routers.qa-cm.tls=true"
      - "traefik.http.services.qa-cm.loadbalancer.server.port=3000"
      - "traefik.http.services.qa-cm.loadbalancer.healthcheck.path=/up"
      - "traefik.http.services.qa-cm.loadbalancer.healthcheck.interval={{ qa_environment.health_check_interval }}"
      - "traefik.http.services.qa-cm.loadbalancer.healthcheck.timeout={{ qa_environment.health_check_timeout }}"
    networks:
      - {{ qa_environment.network_name }}
    deploy:
      restart_policy:
        condition: {{ qa_environment.restart_policy }}
        max_attempts: {{ qa_environment.restart_max_attempts }}
      resources:
        limits:
          cpus: '{{ apps.case_manager.cpu_limit }}'
          memory: {{ apps.case_manager.memory_limit }}
        reservations:
          cpus: '{{ apps.case_manager.cpu_reservation }}'
          memory: {{ apps.case_manager.memory_reservation }}
    restart: unless-stopped

  qa-case-manager-db:
    image: postgres:17
    container_name: qa-case-manager-db
    hostname: qa-case-manager-db
    restart: unless-stopped
    environment:
      POSTGRES_USER: "postgres"
      POSTGRES_PASSWORD: "{{ apps.case_manager.db_password }}"
      POSTGRES_DB: "case_manager_athar{{ qa_database.suffix }}_db"
      # QA-optimized PostgreSQL settings
      POSTGRES_SHARED_BUFFERS: "{{ qa_database.shared_buffers }}"
      POSTGRES_EFFECTIVE_CACHE_SIZE: "{{ qa_database.effective_cache_size }}"
      POSTGRES_WORK_MEM: "{{ qa_database.work_mem }}"
      POSTGRES_MAINTENANCE_WORK_MEM: "{{ qa_database.maintenance_work_mem }}"
      POSTGRES_MAX_CONNECTIONS: "{{ qa_database.max_connections }}"
    networks:
      - {{ qa_environment.network_name }}
    volumes:
      - qa-case-manager-db-data:/var/lib/postgresql/data
    labels:
      - "traefik.enable=false"
    deploy:
      resources:
        limits:
          cpus: '{{ apps.case_manager.db_cpu_limit }}'
          memory: {{ apps.case_manager.db_memory_limit }}
        reservations:
          cpus: '{{ apps.case_manager.db_cpu_reservation }}'
          memory: {{ apps.case_manager.db_memory_reservation }}

networks:
  {{ qa_environment.network_name }}:
    external: true
    name: proxy_{{ traefik.network }}

volumes:
  qa-case-manager-db-data:
