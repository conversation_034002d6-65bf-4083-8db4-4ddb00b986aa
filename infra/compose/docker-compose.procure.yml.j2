name: athar-procure

services:
  app:
    image: {{ registry_host }}/{{ apps.procure.image_path }}:{{ submodules_info.submodules["services/procure"] }}
    hostname: procure-app
    environment:
      DB_HOST: procure-db
      DB_PASSWORD: "{{ apps.procure.db_password }}"
      PORT: 3000
      RAILS_MASTER_KEY: {{ rails_master_key }}
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.procure.rule=Host(`procure.{{ domain }}`)"
      - "traefik.http.routers.procure.entrypoints=web,websecure"
      - "traefik.http.routers.procure.tls.certresolver=myresolver"
      - "traefik.http.routers.procure.tls=true"
      - "traefik.http.services.procure.loadbalancer.server.port=3000"
      - "traefik.http.services.procure.loadbalancer.healthcheck.path=/up"
      - "traefik.http.services.procure.loadbalancer.healthcheck.interval=10s"
      - "traefik.http.services.procure.loadbalancer.healthcheck.timeout=3s"
    networks:
      - athar-network
    deploy:
      restart_policy:
        condition: on-failure
      resources:
        limits:
          cpus: '1.0'
          memory: 512M

  procure-db:
    image: postgres:17
    container_name: procure-db
    restart: unless-stopped
    environment:
      POSTGRES_USER: "postgres"
      POSTGRES_PASSWORD: "{{ apps.procure.db_password }}"
      POSTGRES_DB: "procure_athar_db"
      GRPC_BACKTRACE_ON_ERROR: "1"
      GRPC_SERVER_HOST: "0.0.0.0"
      GRPC_SERVER_PORT: "10541"
{#      GRPC_SERVER_POOL_SIZE: "100"#}
{#      GRPC_SERVER_POOL_KEEP_ALIVE: "1"#}
{#      GRPC_SERVER_POLL_PERIOD: "1"#}
    networks:
      - athar-network
    volumes:
      - db-data:/var/lib/postgresql/data
    labels:
      - "traefik.enable=false"

networks:
  athar-network:
    external: true
    name: proxy_{{ traefik.network }}

volumes:
  db-data:
