---
- name: Deploy QA Docker Compose Stack
  hosts: qa_host
  become: yes
  gather_facts: no

  vars_files:
    - qa-variables.yml

  vars:
    deploy_path: "/home/<USER>/qa-deployment"

  tasks:
    ################################################################
    # 1. Environment Preparation
    ################################################################

    - name: Ensure QA deployment directory exists
      ansible.builtin.file:
        path: "{{ deploy_path }}"
        state: directory

    - name: Install python3-pip
      ansible.builtin.apt:
        name: python3-pip
        state: present
        update_cache: yes

    - name: Install required Python dependencies
      ansible.builtin.pip:
        executable: pip3
        extra_args: "--break-system-packages"
        name:
          - jsondiff
          - docker

    ################################################################
    # 2. QA Environment Setup (Separate Network for Security)
    ################################################################

    - name: Create QA Docker network for security isolation
      community.docker.docker_network:
        name: "proxy_{{ traefik.network }}"
        driver: bridge
        state: present

    - name: Connect Traefik to QA network for routing
      community.docker.docker_container:
        name: "proxy-traefik-1"
        networks:
          - name: "proxy_athar-network" # Keep production network
          - name: "proxy_{{ traefik.network }}" # Add QA network
        networks_cli_compatible: false
        state: started
        restart: false

    - name: Check QA network isolation setup
      ansible.builtin.debug:
        msg: "QA uses separate network 'proxy_{{ traefik.network }}' for security isolation from production"

    ################################################################
    # 3. Application Deployment
    ################################################################

    - name: Set facts for the QA application stacks
      ansible.builtin.set_fact:
        registry_host: "registry.{{ domain }}"

    ################################################################
    # 4. QA Image Tags (Simple - Always Use develop)
    ################################################################

    - name: Set QA image tags (always develop)
      ansible.builtin.set_fact:
        qa_image_tags:
          core: "develop"
          people: "develop"
          case_manager: "develop"
          procure: "develop"
          frontend: "develop"
      changed_when: false

    - name: Debug QA image tags
      ansible.builtin.debug:
        var: qa_image_tags
      changed_when: false

    - name: Wait for Docker registry to be reachable
      ansible.builtin.uri:
        url: "https://registry.{{ domain }}/v2/"
        status_code: 401
        validate_certs: yes
      register: registry_check
      retries: 20
      delay: 3
      until: registry_check.status == 401

    - name: Log in to private Docker registry
      community.docker.docker_login:
        registry_url: "registry.{{ domain }}"
        username: "{{ registry.username }}"
        password: "{{ registry.password }}"

    ################################################################
    # 5. Deploy QA Applications
    ################################################################

    - name: Render QA Core Compose file
      ansible.builtin.template:
        src: "compose/docker-compose.qa-core.yml.j2"
        dest: "{{ deploy_path }}/docker-compose.qa-core.yml"
        owner: athar
        group: athar
        mode: "0644"

    - name: Deploy QA Core Application stack
      community.docker.docker_compose_v2:
        project_src: "{{ deploy_path }}"
        state: present
        remove_orphans: true
        pull: always
        files:
          - "docker-compose.qa-core.yml"

    - name: Render QA People Compose file
      ansible.builtin.template:
        src: "compose/docker-compose.qa-people.yml.j2"
        dest: "{{ deploy_path }}/docker-compose.qa-people.yml"
        owner: athar
        group: athar
        mode: "0644"

    - name: Deploy QA People Application stack
      community.docker.docker_compose_v2:
        project_src: "{{ deploy_path }}"
        state: present
        remove_orphans: true
        pull: always
        files:
          - "docker-compose.qa-people.yml"

    # - name: Render QA Case Manager Compose file
    #   ansible.builtin.template:
    #     src: "compose/docker-compose.qa-case-manager.yml.j2"
    #     dest: "{{ deploy_path }}/docker-compose.qa-case-manager.yml"
    #     owner: athar
    #     group: athar
    #     mode: "0644"

    # - name: Deploy QA Case Manager Application stack
    #   community.docker.docker_compose_v2:
    #     project_src: "{{ deploy_path }}"
    #     state: present
    #     remove_orphans: true
    #     pull: always
    #     files:
    #       - "docker-compose.qa-case-manager.yml"

    # - name: Render QA Procure Compose file
    #   ansible.builtin.template:
    #     src: "compose/docker-compose.qa-procure.yml.j2"
    #     dest: "{{ deploy_path }}/docker-compose.qa-procure.yml"
    #     owner: athar
    #     group: athar
    #     mode: "0644"

    # - name: Deploy QA Procure Application stack
    #   community.docker.docker_compose_v2:
    #     project_src: "{{ deploy_path }}"
    #     state: present
    #     remove_orphans: true
    #     pull: always
    #     files:
    #       - "docker-compose.qa-procure.yml"

    - name: Render QA Frontend Compose file
      ansible.builtin.template:
        src: "compose/docker-compose.qa-frontend.yml.j2"
        dest: "{{ deploy_path }}/docker-compose.qa-frontend.yml"
        owner: athar
        group: athar
        mode: "0644"

    - name: Deploy QA Frontend Application stack
      community.docker.docker_compose_v2:
        project_src: "{{ deploy_path }}"
        state: present
        remove_orphans: true
        pull: always
        files:
          - "docker-compose.qa-frontend.yml"

    ################################################################
    # 6. QA Environment Health Check
    ################################################################

    - name: Wait for QA services to be healthy
      ansible.builtin.uri:
        url: "https://core.qa.{{ domain }}/up"
        status_code: 200
        validate_certs: yes
      register: qa_health_check
      retries: 10
      delay: 10
      until: qa_health_check.status == 200
      ignore_errors: yes

    - name: Display QA deployment status
      ansible.builtin.debug:
        msg: |
          QA Environment Deployed Successfully!

          QA Services:
          - Frontend: https://app.qa.{{ domain }}
          - Core: https://core.qa.{{ domain }}
          - People: https://people.qa.{{ domain }}
          - Case Manager: https://cm.qa.{{ domain }}
          - Procure: https://procure.qa.{{ domain }}

          Shared Tools (Production):
          - Traefik: https://traefik.{{ domain }}
          - Portainer: https://portainer.{{ domain }}
          - Registry: https://registry.{{ domain }}
          - MinIO: https://minio.{{ domain }}
