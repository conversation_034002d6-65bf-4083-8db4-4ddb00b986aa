api:
  dashboard: true
  insecure: true

entryPoints:
  web:
    address: ":80"
    proxyProtocol:
      insecure: true
    transport:
      respondingTimeouts:
        idleTimeout: "0"
        readTimeout: "0"
        writeTimeout: "0"
  websecure:
    address: ":443"
{#    proxyProtocol:#}
{#      insecure: true#}
    transport:
      respondingTimeouts:
        idleTimeout: "0"
        readTimeout: "0"
        writeTimeout: "0"


{#    http:#}
{#      tls: { }#}
{#  traefik:#}
{#    address: ":8080"#}

serversTransport:
  insecureSkipVerify: true

# http:
#   routers:
#     traefik-dashboard:
#       rule: "Host(`traefik.{{ domain }}`)"
#       entryPoints:
#         - web
#       service: api@internal
#       tls:
#         certResolver: "myresolver"

accessLog:
  filePath: "/var/log/traefik/access.log"
  format: json

providers:
  docker:
    endpoint: "unix:///var/run/docker.sock"
    exposedByDefault: false
    network: "{{ traefik.network }}"

#http:
#  serversTransports:
#    hihi:
#      insecureSkipVerify: true

#tls:
#  stores:
#    default:
#      #      defaultCertificate:
#      #        certFile: "/etc/traefik/certs/default.crt"
#      #        keyFile: "/etc/traefik/certs/default.key"
##      defaultGeneratedCert:
##        resolver: "myresolver"
##        domain:
##          main: "${DOMAIN:athar.test}"
##          sans:
##            - "${TLS_WILDCARD:*.athar.test}"
certificatesResolvers:
  myresolver:
    acme:
      email: "<EMAIL>"
      storage: "/etc/traefik/acme.json"
      httpChallenge:
        entryPoint: web
