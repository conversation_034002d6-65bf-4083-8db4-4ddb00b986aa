---
- name: Deploy Production Docker Compose Stack
  hosts: prod_host
  become: yes
  gather_facts: no

  vars_files:
    - variables.yml

  vars:
    deploy_path: "/home/<USER>/deployment"

  tasks:
    ################################################################
    # 1. Environment Preparation
    ################################################################

    - name: Ensure deployment directory exists
      ansible.builtin.file:
        path: "{{ deploy_path }}"
        state: directory

    - name: Install python3-pip
      ansible.builtin.apt:
        name: python3-pip
        state: present
        update_cache: yes

    - name: Install required Python dependencies
      ansible.builtin.pip:
        executable: pip3
        extra_args: "--break-system-packages"
        name:
          - jsondiff
          - docker

    ################################################################
    # 2. Generate & Write Registry Credentials
    ################################################################

    - name: Check if registry htpasswd file exists
      ansible.builtin.stat:
        path: "{{ deploy_path }}/registry_htpasswd"
      register: registry_file_stat

    - name: Generate bcrypt htpasswd entry for registry
      shell: >
        docker run --rm httpd:2.4-alpine
        htpasswd -nbB {{ registry.username }} {{ registry.password }}
      register: registry_htpasswd
      become: false
      changed_when: false
      when: not registry_file_stat.stat.exists

    - name: Write registry htpasswd file to remote
      ansible.builtin.copy:
        content: "{{ registry_htpasswd.stdout }}"
        dest: "{{ deploy_path }}/registry_htpasswd"
        owner: athar
        group: athar
        mode: "0600"
      when: not registry_file_stat.stat.exists

    ################################################################
    # 3. Generate & Write Portainer Password
    ################################################################

    - name: Check if portainer password file exists
      ansible.builtin.stat:
        path: "{{ deploy_path }}/portainer_htpasswd"
      register: portainer_file_stat

    - name: Generate bcrypt password for Portainer
      shell: >
        docker run --rm httpd:2.4-alpine
        htpasswd -nbB admin {{ portainer.admin_password }}
        | cut -d ":" -f 2
        | sed 's/\$/\$\$/g'
      register: portainer_htpasswd
      become: false
      changed_when: false
      when: not portainer_file_stat.stat.exists

    - name: Write portainer password file to remote
      ansible.builtin.copy:
        content: "{{ portainer_htpasswd.stdout }}"
        dest: "{{ deploy_path }}/portainer_htpasswd"
        owner: athar
        group: athar
        mode: "0600"
      when: not portainer_file_stat.stat.exists

    - name: Read Portainer password from remote file
      slurp:
        src: "{{ deploy_path }}/portainer_htpasswd"
      register: portainer_file_slurp

    - name: Set fact for Portainer password
      ansible.builtin.set_fact:
        portainer_password_encrypted: "{{ portainer_file_slurp.content | b64decode }}"

    ################################################################
    # 4. Generate & Write Traefik Dashboard Auth
    ################################################################

    - name: Check if traefik dashboard htpasswd file exists
      ansible.builtin.stat:
        path: "{{ deploy_path }}/traefik_htpasswd"
      register: traefik_file_stat

    - name: Generate bcrypt htpasswd entry for Traefik dashboard
      shell: >
        docker run --rm httpd:2.4-alpine
        htpasswd -nbB {{ traefik.username }} {{ traefik.password }}
        | sed 's/\$/\$\$/g'
      register: traefik_htpasswd
      become: false
      changed_when: false
      when: not traefik_file_stat.stat.exists

    - name: Write Traefik htpasswd file to remote
      ansible.builtin.copy:
        content: "{{ traefik_htpasswd.stdout }}"
        dest: "{{ deploy_path }}/traefik_htpasswd"
        owner: athar
        group: athar
        mode: "0600"
      when: not traefik_file_stat.stat.exists

    - name: Read Traefik password from remote file
      slurp:
        src: "{{ deploy_path }}/traefik_htpasswd"
      register: traefik_file_slurp

    - name: Set fact for Traefik password
      ansible.builtin.set_fact:
        traefik_password_encrypted: "{{ traefik_file_slurp.content | b64decode }}"

    ################################################################
    # 5. Configuration & Template Rendering
    ################################################################

    - name: Check if acme.json file exists
      ansible.builtin.stat:
        path: "{{ deploy_path }}/acme.json"
      register: acme_file_stat

    - name: Generate acme.json for Traefik
      ansible.builtin.copy:
        content: ""
        dest: "{{ deploy_path }}/acme.json"
        owner: athar
        group: athar
        mode: "0600"
      when: not acme_file_stat.stat.exists

    - name: Render Traefik configuration
      ansible.builtin.template:
        src: "traefik.yml.j2"
        dest: "{{ deploy_path }}/traefik.yml"
        owner: athar
        group: athar
        mode: "0644"

    - name: Render proxy compose file
      ansible.builtin.template:
        src: "compose/docker-compose.proxy.yml.j2"
        dest: "{{ deploy_path }}/docker-compose.proxy.yml"
        owner: athar
        group: athar
        mode: "0644"

    - name: Render tools compose file
      ansible.builtin.template:
        src: "compose/docker-compose.tools.yml.j2"
        dest: "{{ deploy_path }}/docker-compose.tools.yml"
        owner: athar
        group: athar
        mode: "0644"

    ################################################################
    # 6. Deployment
    ################################################################

    - name: Deploy proxy stack
      community.docker.docker_compose_v2:
        project_src: "{{ deploy_path }}"
        state: present
        remove_orphans: true
        files:
          - "docker-compose.proxy.yml"

    - name: Deploy tools stack
      community.docker.docker_compose_v2:
        project_src: "{{ deploy_path }}"
        state: present
        remove_orphans: true
        files:
          - "docker-compose.tools.yml"

    ################################################################
    # 7. Application Deployment
    ################################################################

    - name: Set facts for the application stacks
      ansible.builtin.set_fact:
        registry_host: "registry.{{ domain }}"

    ################################################################
    # 8. Deploy  Applications
    ################################################################

    - name: Remove any existing submodules_info.yml
      delegate_to: localhost
      become: false
      file:
        path: "{{ playbook_dir }}/submodules_info.yml"
        state: absent
      changed_when: false

    - name: Generate submodules_info.yml
      delegate_to: localhost
      become: false
      shell: |
        echo "submodules:" > "{{ playbook_dir }}/submodules_info.yml"
        export OUTFILE="{{ playbook_dir }}/submodules_info.yml"
        git submodule foreach --quiet '
          branch=$(git symbolic-ref --short HEAD 2>/dev/null \
                   || git describe --tags 2>/dev/null \
                   || echo "detached")
          echo "  $sm_path: \"$branch\"" >> "$OUTFILE"
        '
      args:
        chdir: "{{ playbook_dir }}"
      changed_when: false

    - name: Slurp submodules_info.yml
      delegate_to: localhost
      become: false
      slurp:
        src: "{{ playbook_dir }}/submodules_info.yml"
      register: submodules_info_file

    - name: Set fact for submodules_info
      ansible.builtin.set_fact:
        submodules_info: "{{ submodules_info_file.content | b64decode | from_yaml }}"
      changed_when: false

    - name: Debug submodules_info
      ansible.builtin.debug:
        var: submodules_info
      changed_when: false

    - name: Wait for Docker registry to be reachable
      ansible.builtin.uri:
        url: "https://registry.{{ domain }}/v2/"
        status_code: 401
        validate_certs: yes # set to `no` if self-signed certs
      register: registry_check
      retries: 20
      delay: 3
      until: registry_check.status == 401

    - name: Log in to private Docker registry
      community.docker.docker_login:
        registry_url: "registry.{{ domain }}"
        username: "{{ registry.username }}"
        password: "{{ registry.password }}"

    - name: Render Core Compose file
      ansible.builtin.template:
        src: "compose/docker-compose.core.yml.j2"
        dest: "{{ deploy_path }}/docker-compose.core.yml"
        owner: athar
        group: athar
        mode: "0644"

    - name: Deploy Core Application stack
      community.docker.docker_compose_v2:
        project_src: "{{ deploy_path }}"
        state: present
        remove_orphans: true
        files:
          - "docker-compose.core.yml"

    - name: Render People Compose file
      ansible.builtin.template:
        src: "compose/docker-compose.people.yml.j2"
        dest: "{{ deploy_path }}/docker-compose.people.yml"
        owner: athar
        group: athar
        mode: "0644"

    - name: Deploy People Application stack
      community.docker.docker_compose_v2:
        project_src: "{{ deploy_path }}"
        state: present
        remove_orphans: true
        files:
          - "docker-compose.people.yml"

    - name: Render Case Manager Compose file
      ansible.builtin.template:
        src: "compose/docker-compose.case-manager.yml.j2"
        dest: "{{ deploy_path }}/docker-compose.case-manager.yml"
        owner: athar
        group: athar
        mode: "0644"

    - name: Deploy Case Manager Application stack
      community.docker.docker_compose_v2:
        project_src: "{{ deploy_path }}"
        state: present
        remove_orphans: true
        files:
          - "docker-compose.case-manager.yml"

    # - name: Render Procure Compose file
    #   ansible.builtin.template:
    #     src: "compose/docker-compose.procure.yml.j2"
    #     dest: "{{ deploy_path }}/docker-compose.procure.yml"
    #     owner: athar
    #     group: athar
    #     mode: "0644"

    # - name: Deploy Procure Application stack
    #   community.docker.docker_compose_v2:
    #     project_src: "{{ deploy_path }}"
    #     state: present
    #     remove_orphans: true
    #     files:
    #       - "docker-compose.procure.yml"

    - name: Render Frontend Compose file
      ansible.builtin.template:
        src: "compose/docker-compose.frontend.yml.j2"
        dest: "{{ deploy_path }}/docker-compose.frontend.yml"
        owner: athar
        group: athar
        mode: "0644"

    - name: Deploy Frontend Application stack
      community.docker.docker_compose_v2:
        project_src: "{{ deploy_path }}"
        state: present
        remove_orphans: true
        files:
          - "docker-compose.frontend.yml"
