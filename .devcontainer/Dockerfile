# Use a Ruby on Rails base image
# Reference: https://github.com/devcontainers/images/tree/main/src/ruby
FROM mcr.microsoft.com/devcontainers/ruby:dev-3.4

# Install system dependencies
RUN apt-get update && apt-get install -y \
    postgresql-client \
    curl \
    build-essential \
    libpq-dev \
    zsh \
    git \
    openssh-server \
    && rm -rf /var/lib/apt/lists/*

# Install Rails
RUN gem install rails -v 6.1.7

# Set working directory
WORKDIR /workspace

# Start SSHD (needed for JetBrains Gateway)
RUN mkdir -p /var/run/sshd

# Set default shell
CMD ["bash"]