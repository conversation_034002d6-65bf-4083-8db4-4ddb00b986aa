name: "Read File Content"
description: "Reads the content of a file and sets it as an output."
inputs:
  file:
    description: "The file path to read from."
    required: true
outputs:
  content:
    description: "The content of the file."
runs:
  using: composite
  steps:
    - name: Read file and set output
      shell: bash
      run: |
        echo "content<<EOF" >> "$GITHUB_OUTPUT"
        cat "${{ inputs.file }}"
        echo "EOF" >> "$GITHUB_OUTPUT"