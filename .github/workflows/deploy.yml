name: Deploy Stack

on:
  release:
    types: [ published ]
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: '0'
          submodules: 'recursive'
          token: '${{ secrets.ATHAR_PAT }}'

      - name: Read Ansible configuration from file
        id: ansible_config
        uses: ./.github/actions/read-file
        with:
          file: infra/ansible.cfg

      - name: Write Ansible Variables from Secrets
        run: |
          printf "%s" "${{ secrets.ANSIBLE_VARIABLES }}" > infra/variables.yml

      - name: Run Ansible Playbook to Deploy Stack
        uses: dawidd6/action-ansible-playbook@v3
        with:
          playbook: deploy.yml
          directory: ./infra
          configuration: ${{ steps.ansible_config.outputs.content }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          inventory: |
            [prod_host]
            ${{ vars.SSH_HOST }} ansible_user=${{ vars.SSH_USER }}
