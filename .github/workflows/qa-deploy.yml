name: Deploy QA Stack

on:
  workflow_dispatch:
    inputs:
      force_deploy:
        description: 'Force deployment even if no changes'
        required: false
        default: false
        type: boolean
  repository_dispatch:
    types: [ qa-deploy-trigger ]

# Ensure only one QA deployment runs at a time
concurrency:
  group: qa-deployment
  cancel-in-progress: true

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: '0'
          submodules: 'recursive'
          token: '${{ secrets.ATHAR_PAT }}'

      - name: Read Ansible configuration from file
        id: ansible_config
        uses: ./.github/actions/read-file
        with:
          file: infra/ansible.cfg

      - name: Write QA Ansible Variables from Secrets
        run: |
          printf "%s" "${{ secrets.QA_ANSIBLE_VARIABLES }}" > infra/qa-variables.yml

      - name: Run QA Ansible Playbook to Deploy Stack
        uses: dawidd6/action-ansible-playbook@v3
        with:
          playbook: qa-deploy.yml
          directory: ./infra
          configuration: ${{ steps.ansible_config.outputs.content }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          inventory: |
            [qa_host]
            ${{ vars.SSH_HOST }} ansible_user=${{ vars.SSH_USER }}

      - name: QA Deployment Success Notification
        if: success()
        run: |
          echo "✅ QA Environment Deployed Successfully!"
          echo ""
          echo "🌐 QA Services Available:"
          echo "  - Frontend: https://app.qa.ems.atharyouth.org"
          echo "  - Core: https://core.qa.ems.atharyouth.org"
          echo "  - People: https://people.qa.ems.atharyouth.org"
          echo "  - Case Manager: https://cm.qa.ems.atharyouth.org"
          echo "  - Procure: https://procure.qa.ems.atharyouth.org"
