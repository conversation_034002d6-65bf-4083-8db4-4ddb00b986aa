#!/usr/bin/env ruby

# Simple test script to verify the custom authorization feature works

require_relative 'lib/athar_auth'
require_relative 'lib/athar_auth/resource_authorization'
require_relative 'lib/athar_auth/authorization'

# Mock Rails environment
module Rails
  def self.logger
    @logger ||= Logger.new(STDOUT)
  end
end

class Logger
  def debug(msg)
    puts "[DEBUG] #{msg}"
  end
end

# Mock controller class
class TestController
  include AtharAuth::ResourceAuthorization
  
  attr_accessor :params, :action_name, :controller_name
  
  def initialize
    @params = { id: '123' }
    @action_name = 'render_data'
    @controller_name = 'form_templates'
  end
  
  # Mock methods
  def can?(permission, subject)
    puts "Checking permission: #{permission} on #{subject}"
    true # Always return true for test
  end
  
  def user_signed_in?
    true
  end
  
  def render_forbidden(message)
    puts "FORBIDDEN: #{message}"
    false
  end
  
  def find_resource(klass, options)
    puts "Loading resource: #{klass}"
    MockFormTemplate.new
  end
  
  def instance_variable_set(name, value)
    puts "Setting #{name} = #{value}"
    super
  end
  
  # Set up authorization
  authorize_resources only: [:index, :show, :validate_data]
  map_action_permission :render_data, :read
end

# Mock model
class MockFormTemplate
  def self.name
    'FormTemplate'
  end
  
  def to_s
    'MockFormTemplate instance'
  end
end

# Mock constantize
class String
  def classify
    MockFormTemplate
  end
  
  def constantize
    MockFormTemplate
  end
  
  def underscore
    'form_template'
  end
  
  def singularize
    'form_template'
  end
end

# Test the implementation
puts "=== Testing Custom Authorization Feature ==="
puts

controller = TestController.new

puts "1. Testing action permission mappings:"
puts "   Mappings: #{controller.class.action_permission_mappings}"
puts

puts "2. Testing authorize_and_load_resource! method:"
result = controller.send(:authorize_and_load_resource!)
puts "   Result: #{result}"
puts

puts "3. Checking instance variable:"
form_template = controller.instance_variable_get(:@form_template)
puts "   @form_template: #{form_template}"
puts

puts "=== Test Complete ==="
