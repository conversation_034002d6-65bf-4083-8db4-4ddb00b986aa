# AtharAuth

**AtharAuth** is a Ruby gem that provides secure, flexible **JWT-based authentication and authorization** for Rails APIs and microservices. It supports both **API tokens** and **user sessions**, with helper methods for verifying roles, permissions, and rendering standardized error responses.

---

## Installation

### 1. Add the Gem to Your Application

Install via Bundler from `fury.io`:

```bash
gem install athar_auth --source 'https://gem.fury.io/athar/'
```

Or add it to your `Gemfile`:

```ruby
source 'https://gem.fury.io/athar/' do
   gem 'athar_auth', '~> 0.1.2'
end
```

### 2. Run the Installer

```bash
bundle exec rails generate athar_auth:install
# to customize the user model:
bundle exec rails generate athar_auth:install MyUser
```

This will generate:

- `config/initializers/athar_auth.rb` (for config)
- `app/models/auth_user.rb` (or your custom model)

---

## Configuration

Edit **`config/initializers/athar_auth.rb`**:

```ruby
AtharAuth.configure do |config|
  config.secret_key = Rails.application.credentials.jwt_secret!
  config.algorithm = "HS256"
  config.user_class = "User" # or your custom model name
end
```

---

## Usage

### 🔐 Authentication Helpers

#### `authenticate_api_user!`

- Ensures a valid token is provided
- Decodes and assigns `@current_user`
- Renders 401 if missing or invalid

#### `authenticate_session!`

- Same as above, but enforces `token_type: session`

#### `current_user`

- Returns the user object built from JWT payload

#### `user_signed_in?`

- True if a valid user is present

Example:

```ruby
class Api::BaseController < ActionController::API
  include AtharAuth::ControllerHelpers

  before_action :authenticate_api_user!
end
```

---

### 🛡 Authorization

Include `AtharAuth::Authorization` to access permission helpers:

```ruby
class ApplicationController < ActionController::API
  include AtharAuth::ControllerHelpers
  include AtharAuth::Authorization
end
```

#### `authorize!(action, subject)`

- Checks if the current user has permission like `read:employee`
- Renders 403 Forbidden if not allowed

```ruby
authorize!(:read, :employee)
```

#### `can?(action, subject)`

- Returns true/false if user is authorized

```ruby
can?(:manage, :users) # => true or false
```

#### `current_permissions`

- Returns an array of permissions from token

#### `current_roles`

- Returns user roles from token payload

---

### 🔧 Token Decoding

Tokens are automatically decoded from the `Authorization: Bearer <token>` header.

You can access the raw payload via:

```ruby
decoded_token # => { "sub" => ..., "permissions" => ..., ... }
```

---

## Session Endpoints

The auth gem provides a built-in session controller with endpoints for retrieving session permissions and capabilities. This is useful for frontend applications that need to determine what the current user can access.

### 🚀 Mounting the Engine

Add this to your `config/routes.rb`:

```ruby
Rails.application.routes.draw do
  scope '/api' do
    mount AtharAuth::Engine, at: 'session'
    # Your other API routes...
  end
end
```

This will make the session endpoints available at `/api/session/*`.

### 🔑 How Permissions Work

**Permissions are extracted from the JWT session token**, not fetched from a database. Here's how it works:

1. **Session Token Creation**: When a user requests a session token for a specific service (e.g., "people", "cm"), the Core service:

   - Looks up the user's roles and permissions for that specific system
   - Compresses the permissions into the JWT token payload
   - Returns a scoped session token

2. **Permission Extraction**: The session controller extracts permissions using:

   ```ruby
   current_permissions # => ["read:employee", "create:employee", "manage:leave"]
   ```

3. **Service-Specific Permissions**: Each session token contains only the permissions relevant to that service:

   - **People service token**: Contains employee, leave, attendance permissions
   - **Case Manager token**: Contains case, beneficiary, service permissions
   - **Core service token**: Contains user, role, project permissions

4. **Token Scope**: The `scope` field indicates which service the permissions apply to:
   ```json
   {
     "scope": "people",
     "permissions": ["read:employee", "manage:leave"]
   }
   ```

### 📋 Available Endpoints

#### `GET /api/session/permissions`

Returns comprehensive session information in JSON:API format:

```json
{
  "data": {
    "id": "session-123-people",
    "type": "session_permissions",
    "attributes": {
      "user": {
        "id": 123,
        "name": "John Doe",
        "email": "<EMAIL>",
        "global": false,
        "user_type": "project",
        "status": "active"
      },
      "role": {
        "id": 5,
        "name": "hr_manager",
        "level": 30,
        "scope": "global_role"
      },
      "project": {
        "id": 1,
        "name": "Main Project"
      },
      "permissions": ["read:employee", "create:employee", "manage:leave"],
      "scope": "people",
      "access_type": "project"
    },
    "relationships": {
      "accessible_projects": {
        "data": [{ "id": "1", "type": "project" }]
      }
    }
  },
  "included": [
    {
      "id": "1",
      "type": "project",
      "attributes": {
        "name": "Main Project",
        "status": "active"
      }
    }
  ],
  "meta": {
    "capabilities": {
      "can_manage_employees": true,
      "can_manage_attendance": true,
      "can_manage_payroll": false
    },
    "session_info": {
      "authenticated": true,
      "token_type": "session",
      "expires_at": 1640995200,
      "permissions_source": "jwt_token",
      "permissions_scope": "people"
    }
  }
}
```

### 📚 API Documentation Integration

If you're using **Apipie** for API documentation, you need to configure it to include the auth gem's controllers:

#### Update `config/initializers/apipie.rb`:

```ruby
Apipie.configure do |config|
  # ... other configuration ...

  # Include auth gem controllers in documentation
  config.api_controllers_matcher = [
    "#{Rails.root}/app/controllers/api/**/*.rb",
    "#{Gem.loaded_specs['athar_auth'].full_gem_path}/app/controllers/**/*.rb"
  ]
end
```

After updating the configuration, restart your Rails server. The session endpoints will now appear in your Apipie documentation at `/apipie`.

> **⚠️ Important**: This configuration update is required for **every service** that uses Apipie and mounts the AtharAuth engine. Without it, the session endpoints will work but won't appear in the API documentation.

### 🔧 Usage in Frontend Applications

The session permissions endpoint is designed to be called once when the user logs in or when the application starts. The response provides all the information needed for:

- **Authorization checks**: Use `permissions` array to check if user can perform actions
- **UI rendering**: Use `capabilities` to show/hide features
- **Navigation**: Use `accessible_projects` for project switching
- **User context**: Display current user, role, and project information

Example frontend usage:

```javascript
// Fetch session permissions
const response = await fetch("/api/session/permissions", {
  headers: {
    Authorization: `Bearer ${sessionToken}`,
    "Content-Type": "application/json",
  },
});

const { data, meta } = await response.json();

// Check permissions
const canManageEmployees =
  data.attributes.permissions.includes("manage:employee");

// Use capabilities
const showPayrollSection = meta.capabilities.can_manage_payroll;

// Display user info
const userName = data.attributes.user.name;
const currentProject = data.attributes.project.name;
```

### 🔍 Troubleshooting

#### Session endpoints work but don't appear in Apipie documentation

**Problem**: You can call `/api/session/permissions` successfully, but the endpoint doesn't show up in your `/apipie` documentation.

**Solution**: Update your `config/initializers/apipie.rb` to include the auth gem controllers (see [API Documentation Integration](#-api-documentation-integration) above).

#### "NoMethodError: undefined method 'global?' for User"

**Problem**: The session controller tries to call `global?` method on your user model.

**Solution**: The auth gem uses `global_user?` helper method instead of calling methods directly on the user model. This error usually indicates a version mismatch or incorrect controller inclusion.

Make sure your controller includes both:

```ruby
include AtharAuth::ControllerHelpers
include AtharAuth::Authorization
```

---

## Development

1. **Setup Dependencies:**

```bash
bin/setup
```

2. **Run Specs:**

```bash
rake spec
```

3. **Build Locally:**

```bash
bundle exec rake install
```

4. **Release a New Version:**

```bash
bundle exec rake release
```

---

## License

MIT © Athar Team
