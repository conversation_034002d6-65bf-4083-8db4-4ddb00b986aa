module AtharAuth
  class Error < StandardError
  end

  # Custom error for expired tokens
  class ExpiredTokenError < Error
    def initialize: (?String message) -> void
  end

  # Custom error for invalid tokens
  class InvalidToken < Error
    def initialize: (?String message) -> void
  end

  attr_accessor self.secret_key: String?
  attr_accessor self.algorithm: String?
  attr_accessor self.user_class: String?
  attr_accessor self.main_token_expire_time: ::ActiveSupport::Duration?
  attr_accessor self.session_token_expire_time: ::ActiveSupport::Duration?

  def self.configure: () { (self) -> void } -> void

  def self.decode_token: (String) -> Hash[String, untyped]

  def self.encode_token: (Hash[String | Symbol, untyped]) -> String
end
