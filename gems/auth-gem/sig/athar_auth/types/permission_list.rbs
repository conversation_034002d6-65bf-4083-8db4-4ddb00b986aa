module AtharAuth
  module Types
    class PermissionList < ActiveModel::Type::Value
      # Casts a value from user input or the database to a Ruby Array
      def cast: (untyped value) -> Array[String]

      # Serializes an Array to a comma-separated string for storage
      def serialize: (untyped value) -> String?

      # Deserializes a comma-separated string back to an Array
      def deserialize: (untyped value) -> Array[String]

      # Determines the type for schema dumping
      def type: () -> Symbol

      private

      def cast_value: (untyped value) -> Array[String]
    end
  end
end
