module AtharAuth
  module Authorization
    @current_permissions: Array[String]?

    @current_roles: Array[String]?

    # Returns the permissions array from the decoded token
    def current_permissions: () -> Array[String]

    # Checks if the permission string exists
    # e.g. can?(:read, :employee) checks for "read:employee"
    def can?: (Symbol | String action, Symbol | String subject) -> bool

    # Renders forbidden if not authorized
    def authorize!: (Symbol | String action, Symbol | String subject) -> (bool | void)

    def current_roles: () -> Array[String]
  end
end
