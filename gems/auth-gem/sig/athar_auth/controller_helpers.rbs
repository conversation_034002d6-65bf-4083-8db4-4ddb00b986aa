module AtharAuth
  module Controller<PERSON><PERSON>pers
    @current_user: User?

    @decoded_token: Hash[String, untyped]?

    # Extract token from the Authorization header
    def bearer_token: () -> String?

    # Authenticate for API endpoints using the main token
    def authenticate_api_user!: () -> User?

    # Authenticate for endpoints that require a session token
    def authenticate_session!: () -> User?

    # Provide a Devise-like current_user helper
    def current_user: () -> User?

    def user_signed_in?: () -> bool

    # Decode token using caching
    def decoded_token: () -> Hash[String, untyped]?

    private

    def render_unauthorized: (String) -> void

    def render_forbidden: (String) -> void

    # Extract user from JWT payload
    def extract_user_from_payload: (Hash[String, untyped]?) -> User?
  end
end