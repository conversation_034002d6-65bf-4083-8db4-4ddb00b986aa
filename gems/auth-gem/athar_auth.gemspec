# frozen_string_literal: true

require_relative "lib/athar_auth/version"

Gem::Specification.new do |spec|
  spec.name = "athar_auth"
  spec.version = AtharAuth::VERSION
  spec.authors = ["<PERSON><PERSON><PERSON>"]
  spec.email = ["<EMAIL>"]

  spec.summary = "Auth gem for Athar Association"
  spec.description = "Auth gem for Athar Association"
  spec.homepage = "https://github.com/athar-association/auth-gem"
  spec.license = "MIT"
  spec.required_ruby_version = ">= 3.1.0"

  spec.metadata["allowed_push_host"] = "TODO: Set to your gem server 'https://example.com'"

  spec.metadata["homepage_uri"] = spec.homepage
  spec.metadata["source_code_uri"] = "https://github.com/athar-association/auth-gem"
  spec.metadata["changelog_uri"] = "https://github.com/athar-association/auth-gem"

  # Specify which files should be added to the gem when it is released.
  # The `git ls-files -z` loads the files in the RubyGem that have been added into git.
  gemspec = File.basename(__FILE__)
  spec.files = IO.popen(%w[git ls-files -z], chdir: __dir__, err: IO::NULL) do |ls|
    ls.readlines("\x0", chomp: true).reject do |f|
      (f == gemspec) ||
        f.start_with?(*%w[bin/ test/ spec/ features/ .git .github appveyor Gemfile])
    end
  end
  spec.bindir = "exe"
  spec.executables = spec.files.grep(%r{\Aexe/}) { |f| File.basename(f) }
  spec.require_paths = ["lib"]

  # Uncomment to register a new dependency of your gem
  # spec.add_dependency "example-gem", "~> 1.0"

  # For more information and examples about making a new gem, check out our
  # guide at: https://bundler.io/guides/creating_gem.html

  # Dependencies
  spec.add_dependency "actionpack"
  spec.add_dependency "activesupport"
  spec.add_dependency "athar_commons"
  spec.add_dependency "devise"
  spec.add_dependency "jsonpath", "~> 1.1"
  spec.add_dependency "jwt", "~> 2.0"
  spec.add_dependency "ostruct"
  spec.add_dependency "rails", ">= 5.0"
  spec.add_dependency "railties"

  # Development dependencies
  spec.add_development_dependency "sqlite3", ">= 2.1"
end
