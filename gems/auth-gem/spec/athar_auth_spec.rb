# frozen_string_literal: true

RSpec.describe AtharAuth do
  let(:payload) { { "user_id" => 123, "role" => "admin" } }

  before do
    AtharAuth.configure do |config|
      config.secret_key = "my$ecretK3y"
      config.algorithm = "HS256"
    end
  end

  it "encodes and decodes a token" do
    token = AtharAuth.encode_token(payload)
    decoded_payload = AtharAuth.decode_token(token)
    expect(decoded_payload["user_id"]).to eq(123)
    expect(decoded_payload["role"]).to eq("admin")
  end

  it "raises error with invalid token" do
    expect {
      AtharAuth.decode_token("invalid.token")
    }.to raise_error(AtharAuth::Error)
  end
end
