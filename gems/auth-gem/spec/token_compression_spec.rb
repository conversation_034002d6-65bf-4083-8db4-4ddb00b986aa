# frozen_string_literal: true

require "spec_helper"

RSpec.describe AtharAuth::TokenCompression do
  include TestTokens

  let(:compressed_token) { case_manager_token }

  describe ".dig_token" do
    context "with valid paths" do
      it "returns user information" do
        expect(described_class.dig_token(compressed_token, "user.id")).to eq(456)
        expect(described_class.dig_token(compressed_token, "user.name")).to eq("<PERSON> Ali")
        expect(described_class.dig_token(compressed_token, "user.email")).to eq("<EMAIL>")
        expect(described_class.dig_token(compressed_token, "user.global")).to eq(false)
      end

      it "returns session role information" do
        expect(described_class.dig_token(compressed_token, "session.role.name")).to eq("case_manager")
        expect(described_class.dig_token(compressed_token, "session.role.level")).to eq(10)
        expect(described_class.dig_token(compressed_token, "session.role.scope")).to eq("project_based")
      end

      it "returns session project information" do
        expect(described_class.dig_token(compressed_token, "session.project.id")).to eq(2)
        expect(described_class.dig_token(compressed_token, "session.project.name")).to eq("Beta Project")
        expect(described_class.dig_token(compressed_token, "session.project.description")).to eq("A test project")
      end

      it "returns full nested objects" do
        role_object = described_class.dig_token(compressed_token, "session.role")
        expect(role_object).to be_a(Hash)
        expect(role_object["n"]).to eq("case_manager")
        expect(role_object["l"]).to eq(10)
        expect(role_object["s"]).to eq("project_based")

        project_object = described_class.dig_token(compressed_token, "session.project")
        expect(project_object).to be_a(Hash)
        expect(project_object["id"]).to eq(2)
        expect(project_object["n"]).to eq("Beta Project")
      end

      it "returns permissions array" do
        permissions = described_class.dig_token(compressed_token, "session.permissions")
        expect(permissions).to be_an(Array)
        expect(permissions).to include("index:test_case", "show:test_case", "create:test_case", "update:test_case")
      end
    end

    context "with invalid paths" do
      it "returns nil for non-existent paths" do
        expect(described_class.dig_token(compressed_token, "user.nonexistent")).to be_nil
        expect(described_class.dig_token(compressed_token, "session.role.nonexistent")).to be_nil
        expect(described_class.dig_token(compressed_token, "completely.invalid.path")).to be_nil
      end

      it "returns nil for unmapped paths" do
        expect(described_class.dig_token(compressed_token, "unmapped.path")).to be_nil
      end
    end

    context "with nil token" do
      it "returns nil gracefully" do
        expect(described_class.dig_token(nil, "user.name")).to be_nil
      end
    end
  end

  describe ".dig_token_batch" do
    let(:paths) { ["user.name", "user.email", "session.role.name", "session.project.id"] }

    it "returns hash with all requested paths" do
      result = described_class.dig_token_batch(compressed_token, paths)

      expect(result).to be_a(Hash)
      expect(result["user.name"]).to eq("Ahmed Ali")
      expect(result["user.email"]).to eq("<EMAIL>")
      expect(result["session.role.name"]).to eq("case_manager")
      expect(result["session.project.id"]).to eq(2)
    end

    it "includes nil values for missing paths" do
      paths_with_missing = ["user.name", "user.nonexistent", "session.role.name"]
      result = described_class.dig_token_batch(compressed_token, paths_with_missing)

      expect(result["user.name"]).to eq("Ahmed Ali")
      expect(result["user.nonexistent"]).to be_nil
      expect(result["session.role.name"]).to eq("case_manager")
    end
  end

  describe ".path_exists?" do
    it "returns true for existing paths" do
      expect(described_class.path_exists?(compressed_token, "user.name")).to be true
      expect(described_class.path_exists?(compressed_token, "session.role.name")).to be true
      expect(described_class.path_exists?(compressed_token, "session.permissions")).to be true
    end

    it "returns false for non-existent paths" do
      expect(described_class.path_exists?(compressed_token, "user.nonexistent")).to be false
      expect(described_class.path_exists?(compressed_token, "session.role.nonexistent")).to be false
    end

    it "returns false for nil values" do
      token_with_nil = compressed_token.dup
      token_with_nil["u"]["n"] = nil

      expect(described_class.path_exists?(token_with_nil, "user.name")).to be false
    end
  end

  describe ".validate_token" do
    context "with valid token" do
      it "validates successfully with no required paths" do
        result = described_class.validate_token(compressed_token)

        expect(result[:valid]).to be true
        expect(result[:missing]).to be_empty
        expect(result[:errors]).to be_empty
      end

      it "validates successfully with met required paths" do
        required_paths = ["user.id", "user.name", "session.role.name"]
        result = described_class.validate_token(compressed_token, required_paths)

        expect(result[:valid]).to be true
        expect(result[:missing]).to be_empty
        expect(result[:errors]).to be_empty
      end
    end

    context "with missing required paths" do
      it "reports missing paths" do
        required_paths = ["user.id", "user.nonexistent", "session.role.name"]
        result = described_class.validate_token(compressed_token, required_paths)

        expect(result[:valid]).to be false
        expect(result[:missing]).to include("user.nonexistent")
        expect(result[:missing]).not_to include("user.id", "session.role.name")
      end
    end

    context "with invalid token structure" do
      it "reports error for non-hash token" do
        result = described_class.validate_token("not a hash")

        expect(result[:valid]).to be false
        expect(result[:errors]).to include("Token must be a Hash, got String")
      end

      it "reports missing common required fields" do
        incomplete_token = { "s" => 123 } # Missing issued.at and expires.at
        result = described_class.validate_token(incomplete_token)

        expect(result[:valid]).to be false
        expect(result[:errors]).to include("Missing common required field: issued.at")
        expect(result[:errors]).to include("Missing common required field: expires.at")
      end
    end
  end

  describe ".token_stats" do
    it "returns comprehensive statistics" do
      stats = described_class.token_stats(compressed_token)

      expect(stats[:available_paths]).to be > 0
      expect(stats[:total_possible_paths]).to eq(described_class::SHORTHAND.keys.size)
      expect(stats[:coverage_percentage]).to be_a(Float)
      expect(stats[:token_size_bytes]).to be > 0
      expect(stats[:nested_objects]).to be >= 0
      expect(stats[:available_categories]).to be_a(Hash)
    end

    it "categorizes paths correctly" do
      stats = described_class.token_stats(compressed_token)
      categories = stats[:available_categories]

      expect(categories["user"]).to be > 0
      expect(categories["session"]).to be > 0
      expect(categories["access"]).to be > 0
    end

    it "handles invalid token gracefully" do
      stats = described_class.token_stats("not a hash")

      expect(stats[:error]).to eq("Token must be a Hash")
    end
  end

  describe ".available_paths" do
    it "returns all available paths in token" do
      paths = described_class.available_paths(compressed_token)

      expect(paths).to be_an(Array)
      expect(paths).to include("user.name", "user.email", "session.role.name", "session.project.id")
    end

    it "returns all possible paths when no token provided" do
      all_paths = described_class.available_paths

      expect(all_paths).to eq(described_class::SHORTHAND.keys)
    end
  end

  describe ".show_mappings" do
    it "displays all mappings without category filter" do
      expect { described_class.show_mappings }.to output(/TOKEN COMPRESSION MAPPINGS/).to_stdout
    end

    it "displays filtered mappings with category" do
      expect { described_class.show_mappings("user") }.to output(/user\.name.*=> u\.n/).to_stdout
    end
  end

  describe "SHORTHAND constant" do
    it "contains comprehensive path mappings" do
      expect(described_class::SHORTHAND).to be_a(Hash)
      expect(described_class::SHORTHAND.size).to be > 30

      # Check key categories are present
      user_paths = described_class::SHORTHAND.keys.select { |k| k.start_with?("user.") }
      session_paths = described_class::SHORTHAND.keys.select { |k| k.start_with?("session.") }
      access_paths = described_class::SHORTHAND.keys.select { |k| k.start_with?("access.") }

      expect(user_paths).not_to be_empty
      expect(session_paths).not_to be_empty
      expect(access_paths).not_to be_empty
    end

    it "has consistent nested role structure" do
      expect(described_class::SHORTHAND["session.role"]).to eq("ss.r")
      expect(described_class::SHORTHAND["session.role.name"]).to eq("ss.r.n")
      expect(described_class::SHORTHAND["session.role.level"]).to eq("ss.r.l")
      expect(described_class::SHORTHAND["session.role.scope"]).to eq("ss.r.s")
    end

    it "has consistent nested project structure" do
      expect(described_class::SHORTHAND["session.project"]).to eq("ss.pr")
      expect(described_class::SHORTHAND["session.project.id"]).to eq("ss.pr.id")
      expect(described_class::SHORTHAND["session.project.name"]).to eq("ss.pr.n")
    end
  end

  describe "REVERSE_SHORTHAND constant" do
    it "is the inverse of SHORTHAND" do
      expect(described_class::REVERSE_SHORTHAND).to eq(described_class::SHORTHAND.invert)
    end

    it "allows reverse lookup" do
      expect(described_class::REVERSE_SHORTHAND["u.n"]).to eq("user.name")
      expect(described_class::REVERSE_SHORTHAND["ss.r.n"]).to eq("session.role.name")
      expect(described_class::REVERSE_SHORTHAND["ss.pr.id"]).to eq("session.project.id")
    end
  end
end
