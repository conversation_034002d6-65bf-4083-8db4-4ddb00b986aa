# frozen_string_literal: true

require "spec_helper"

RSpec.describe AtharAuth::Authorization do
  include TestTokens

  let(:controller_class) do
    Class.new do
      include AtharAuth::Authorization

      attr_accessor :request, :compressed_token_value

      def initialize(request_mock = nil)
        @request = request_mock
      end

      # Single entry point - expand compressed tokens like the real implementation
      def decoded_token
        return nil unless @compressed_token_value

        @decoded_token ||= AtharAuth::TokenCompression.expand_token(@compressed_token_value)
      end

      def render(options)
        @rendered = options
      end

      def render_unauthorized(message)
        render json: { error: message }, status: :unauthorized
      end

      def render_forbidden(message)
        render json: { error: message }, status: :forbidden
      end

      def user_signed_in?
        !decoded_token.nil?
      end

      attr_reader :rendered
    end
  end

  let(:request_mock) { double("request", headers: { "Authorization" => "Bearer test_token" }) }
  let(:controller) { controller_class.new(request_mock) }

  describe "#current_permissions" do
    it "returns permissions from compressed token using TokenCompression" do
      controller.compressed_token_value = case_manager_token
      expected_permissions = [
        "index:test_case", "show:test_case", "create:test_case", "update:test_case",
        "index:test_employee", "show:test_employee", "create:test_employee", "update:test_employee"
      ]
      expect(controller.current_permissions).to eq(expected_permissions)
    end

    it "returns empty array when no permissions" do
      controller.compressed_token_value = { "ss" => {} }
      expect(controller.current_permissions).to eq([])
    end

    it "returns empty array when token is nil" do
      controller.compressed_token_value = nil
      expect(controller.current_permissions).to eq([])
    end
  end

  describe "#current_role" do
    it "returns role object from compressed token" do
      controller.compressed_token_value = case_manager_token
      role = controller.current_role

      expect(role).to be_a(AtharAuth::Models::Role)
      expect(role.name).to eq("case_manager")
      expect(role.level).to eq(10)
      expect(role.scope).to eq("project_based")
    end

    it "returns nil when no role data" do
      controller.compressed_token_value = { "ss" => {} }
      expect(controller.current_role).to be_nil
    end

    it "returns nil when token is nil" do
      controller.compressed_token_value = nil
      expect(controller.current_role).to be_nil
    end
  end

  describe "#current_project" do
    it "returns project object from compressed token" do
      controller.compressed_token_value = case_manager_token
      project = controller.current_project

      expect(project).to be_a(AtharAuth::Models::Project)
      expect(project.id).to eq(2)
      expect(project.name).to eq("Beta Project")
      expect(project.status).to eq("active")
    end

    it "returns nil when no project data" do
      controller.compressed_token_value = { "ss" => {} }
      expect(controller.current_project).to be_nil
    end

    it "returns nil for global users" do
      controller.compressed_token_value = hr_manager_token
      expect(controller.current_project).to be_nil
    end
  end

  describe "#global_user?" do
    it "returns true for global users" do
      controller.compressed_token_value = hr_manager_token
      expect(controller.global_user?).to be true
    end

    it "returns false for project-based users" do
      controller.compressed_token_value = case_manager_token
      expect(controller.global_user?).to be false
    end

    it "returns false when token is nil" do
      controller.compressed_token_value = nil
      expect(controller.global_user?).to be false
    end
  end

  describe "#accessible_project_ids" do
    it "returns project IDs for project-based users" do
      controller.compressed_token_value = case_manager_token
      expect(controller.accessible_project_ids).to include(2, 3) # Current project + other projects
    end

    it "returns empty array for global users (placeholder)" do
      controller.compressed_token_value = hr_manager_token
      expect(controller.accessible_project_ids).to eq([])
    end

    it "returns empty array when token is nil" do
      controller.compressed_token_value = nil
      expect(controller.accessible_project_ids).to eq([])
    end
  end

  describe "#user_can_access_project?" do
    it "returns true for accessible projects" do
      controller.compressed_token_value = case_manager_token
      expect(controller.user_can_access_project?(2)).to be true # Current project
      expect(controller.user_can_access_project?(3)).to be true # Other project
    end

    it "returns false for non-accessible projects" do
      controller.compressed_token_value = case_manager_token
      expect(controller.user_can_access_project?(999)).to be false
    end

    it "returns true for global users" do
      controller.compressed_token_value = hr_manager_token
      expect(controller.user_can_access_project?(999)).to be true
    end

    it "handles string project IDs" do
      controller.compressed_token_value = case_manager_token
      expect(controller.user_can_access_project?("2")).to be true
      expect(controller.user_can_access_project?("999")).to be false
    end
  end

  describe "#can?" do
    context "with valid permissions" do
      before { controller.compressed_token_value = case_manager_token }

      it "returns true for existing permissions" do
        expect(controller.can?(:index, :test_case)).to be true
        expect(controller.can?(:show, :test_case)).to be true
        expect(controller.can?(:create, :test_case)).to be true
        expect(controller.can?(:update, :test_case)).to be true
      end

      it "returns false for missing permissions" do
        expect(controller.can?(:delete, :test_case)).to be false
        expect(controller.can?(:manage, :user)).to be false
      end
    end

    context "with project validation" do
      before { controller.compressed_token_value = case_manager_token }

      let(:accessible_resource) do
        double("Resource", project_id: 2).tap do |resource|
          allow(resource).to receive_message_chain(:class, :name, :underscore).and_return("test_case")
        end
      end
      let(:inaccessible_resource) do
        double("Resource", project_id: 999).tap do |resource|
          allow(resource).to receive_message_chain(:class, :name, :underscore).and_return("test_case")
        end
      end

      it "returns true for resources in accessible projects" do
        expect(controller.can?(:show, accessible_resource)).to be true
      end

      it "returns false for resources in non-accessible projects" do
        expect(controller.can?(:read, inaccessible_resource)).to be false
      end
    end

    context "when user is not signed in" do
      before { controller.compressed_token_value = nil }

      it "returns false" do
        expect(controller.can?(:read, :case)).to be false
      end
    end
  end

  describe "#authorize!" do
    context "when user is not signed in" do
      before { controller.compressed_token_value = nil }

      it "renders unauthorized" do
        result = controller.authorize!(:read, :case)
        expect(result).to be false
        expect(controller.rendered).to eq(json: { error: "You must be signed in" }, status: :unauthorized)
      end
    end

    context "when user has permission" do
      before { controller.compressed_token_value = case_manager_token }

      it "returns true" do
        expect(controller.authorize!(:show, :test_case)).to be true
        expect(controller.rendered).to be_nil
      end
    end

    context "when user lacks permission" do
      before { controller.compressed_token_value = case_manager_token }

      it "renders forbidden" do
        result = controller.authorize!(:delete, :case)
        expect(result).to be false
        expect(controller.rendered).to eq(json: { error: "You are not allowed to delete this case" },
                                          status: :forbidden)
      end
    end

    context "with project validation" do
      before { controller.compressed_token_value = case_manager_token }

      let(:inaccessible_resource) do
        double("Resource", project_id: 999).tap do |resource|
          allow(resource).to receive(:to_s).and_return("resource")
          allow(resource).to receive_message_chain(:class, :name, :underscore).and_return("case")
        end
      end

      it "renders forbidden for inaccessible projects" do
        result = controller.authorize!(:read, inaccessible_resource)
        expect(result).to be false
        expect(controller.rendered).to eq(json: { error: "You are not allowed to read this case" }, status: :forbidden)
      end
    end
  end

  describe "#current_roles (legacy)" do
    it "returns array with current role name" do
      controller.compressed_token_value = case_manager_token
      expect(controller.current_roles).to eq(["case_manager"])
    end

    it "returns empty array when no role" do
      controller.compressed_token_value = { "ss" => {} }
      expect(controller.current_roles).to eq([])
    end
  end
end
