require 'logger'
if defined?(Rails)
  unless Rails.respond_to?(:logger)
    Rails.singleton_class.class_eval do
      def logger
        @logger ||= Logger.new(STDOUT)
      end
    end
  end
else
  module Rails
    def self.logger
      @logger ||= Logger.new(STDOUT)
    end
  end
end

# Ensure blank? and present? are available.
require 'active_support/core_ext/object/blank'

require 'spec_helper'
require 'active_support'
require 'active_support/concern'
require 'athar_auth/controller_helpers'

# Dummy implementations for testing (if not already defined in your project)
module AtharAuth
  class Error < StandardError; end

  class User
    attr_accessor :id, :email, :name, :role, :permissions

    def initialize(id:, email:, name:, role:, permissions:)
      @id = id
      @email = email
      @name = name
      @role = role
      @permissions = permissions
    end
  end
end

RSpec.describe AtharAuth::ControllerHelpers do
  # Create a dummy controller to simulate a Rails controller
  let(:controller) do
    Class.new do
      include AtharAuth::ControllerHelpers
      attr_accessor :request, :rendered

      # Override render to capture the output for testing
      def render(options)
        @rendered = options
        options
      end
    end.new
  end

  describe '#decoded_token' do
    context 'when Authorization header is present with a valid token' do
      let(:token) { "validtoken" }
      let(:compressed_payload) do
        {
          's' => 1,
          'u' => {
            'n' => 'Test User',
            'm' => '<EMAIL>'
          },
          'ss' => {
            'p' => ['read:employee']
          },
          't' => 'session'
        }
      end
      let(:expected_expanded_payload) do
        {
          'user' => {
            'id' => 1,
            'name' => 'Test User',
            'email' => '<EMAIL>'
          },
          'session' => {
            'permissions' => ['read:employee']
          },
          'token_type' => 'session'
        }
      end

      before do
        controller.request = double("request", headers: { "Authorization" => "Bearer #{token}" })
        allow(AtharAuth).to receive(:decode_token).with(token).and_return(compressed_payload)
      end

      it 'returns the decoded token payload' do
        expect(controller.decoded_token).to eq(expected_expanded_payload)
      end
    end

    context 'when Authorization header is missing' do
      before do
        controller.request = double("request", headers: {})
      end

      it 'returns nil' do
        expect(controller.decoded_token).to be_nil
      end
    end

    context 'when token is blank' do
      before do
        controller.request = double("request", headers: { "Authorization" => "Bearer " })
      end

      it 'returns nil' do
        expect(controller.decoded_token).to be_nil
      end
    end

    context 'when AtharAuth.decode_token raises an error' do
      let(:token) { "badtoken" }
      before do
        controller.request = double("request", headers: { "Authorization" => "Bearer #{token}" })
        allow(AtharAuth).to receive(:decode_token).with(token).and_raise(AtharAuth::Error.new("Invalid token"))
      end

      it 'returns nil' do
        expect(controller.decoded_token).to be_nil
      end
    end
  end

  describe '#authenticate_api_user!' do
    context 'when token is missing' do
      before do
        controller.request = double("request", headers: {})
      end

      it 'renders unauthorized with "Missing authorization header"' do
        result = controller.authenticate_api_user!
        expect(result).to eq({ json: { error: "Authorization header missing" }, status: :unauthorized })
      end
    end

    context 'when token is present and valid' do
      let(:token) { "validtoken" }
      let(:compressed_payload) do
        {
          's' => 1,
          'u' => {
            'n' => 'Test User',
            'm' => '<EMAIL>'
          },
          'ss' => {
            'p' => ['read:employee']
          }
        }
      end

      before do
        controller.request = double("request", headers: { "Authorization" => "Bearer #{token}" })
        allow(AtharAuth).to receive(:decode_token).with(token).and_return(compressed_payload)
      end

      it 'sets and returns current_user' do
        user = controller.authenticate_api_user!
        expect(user).to be_a(AtharAuth::Models::User)
        expect(user.id).to eq(1)
        expect(user.email).to eq('<EMAIL>')
      end
    end

    context 'when AtharAuth.decode_token raises an error' do
      let(:token) { "badtoken" }
      before do
        controller.request = double("request", headers: { "Authorization" => "Bearer #{token}" })
        allow(AtharAuth).to receive(:decode_token).with(token).and_raise(AtharAuth::Error.new("Invalid token"))
      end

      it 'renders unauthorized with "Missing authorization header"' do
        result = controller.authenticate_api_user!
        expect(result).to eq({ json: { error: "Invalid token" }, status: :unauthorized })
      end
    end
  end

  describe '#authenticate_session!' do
    context 'when token is missing' do
      before do
        controller.request = double("request", headers: {})
      end

      it 'renders unauthorized with "Missing authorization header"' do
        result = controller.authenticate_session!
        expect(result).to eq({ json: { error: "Authorization header missing" }, status: :unauthorized })
      end
    end

    context 'when token is present but token_type is not "session"' do
      let(:token) { "validtoken" }
      let(:payload) do
        {
          'sub' => '1',
          'email' => '<EMAIL>',
          'name' => 'Test User',
          'role' => ['user'],
          'permissions' => ['read:employee'],
          'token_type' => 'api'
        }
      end

      before do
        controller.request = double("request", headers: { "Authorization" => "Bearer #{token}" })
        allow(AtharAuth).to receive(:decode_token).with(token).and_return(payload)
      end

      it 'renders unauthorized with "Invalid token type"' do
        result = controller.authenticate_session!
        expect(result).to eq({ json: { error: "Invalid token type" }, status: :unauthorized })
      end
    end

    context 'when token is present and token_type is "session"' do
      let(:token) { "validtoken" }
      let(:compressed_payload) do
        {
          's' => 1,
          'u' => {
            'n' => 'Test User',
            'm' => '<EMAIL>'
          },
          'ss' => {
            'p' => ['read:employee']
          },
          't' => 'session'
        }
      end

      before do
        controller.request = double("request", headers: { "Authorization" => "Bearer #{token}" })
        allow(AtharAuth).to receive(:decode_token).with(token).and_return(compressed_payload)
      end

      it 'sets and returns current_user' do
        user = controller.authenticate_session!
        expect(user).to be_a(AtharAuth::Models::User)
        expect(user.id).to eq(1)
      end
    end

    context 'when AtharAuth.decode_token raises an error' do
      let(:token) { "badtoken" }
      before do
        controller.request = double("request", headers: { "Authorization" => "Bearer #{token}" })
        allow(AtharAuth).to receive(:decode_token).with(token).and_raise(AtharAuth::Error.new("Invalid token"))
      end

      it 'renders unauthorized with "Missing authorization header"' do
        result = controller.authenticate_session!
        expect(result).to eq({ json: { error: "Invalid token" }, status: :forbidden })
      end
    end
  end

  describe '#current_user' do
    context 'when token is valid' do
      let(:expanded_payload) do
        {
          'user' => {
            'id' => 2,
            'name' => 'User Name',
            'email' => '<EMAIL>'
          },
          'session' => {
            'permissions' => ['manage:all']
          }
        }
      end

      before do
        controller.instance_variable_set(:@decoded_token, expanded_payload)
      end

      it 'returns a user built from the token payload' do
        user = controller.current_user
        expect(user).to be_a(AtharAuth::Models::User)
        expect(user.id).to eq(2)
        expect(user.email).to eq('<EMAIL>')
      end
    end

    context 'when token is invalid or missing' do
      before do
        controller.request = double("request", headers: {})
      end

      it 'returns nil' do
        expect(controller.current_user).to be_nil
      end
    end
  end

  describe '#user_signed_in?' do
    context 'when current_user is present' do
      let(:expanded_payload) do
        {
          'user' => {
            'id' => 3,
            'name' => 'Signed User',
            'email' => '<EMAIL>'
          },
          'session' => {
            'permissions' => []
          }
        }
      end

      before do
        # Stub decoded_token directly to bypass header parsing.
        allow(controller).to receive(:decoded_token).and_return(expanded_payload)
      end

      it 'returns true' do
        expect(controller.user_signed_in?).to be true
      end
    end

    context 'when current_user is nil' do
      before do
        allow(controller).to receive(:decoded_token).and_return(nil)
      end

      it 'returns false' do
        expect(controller.user_signed_in?).to be false
      end
    end
  end
end
