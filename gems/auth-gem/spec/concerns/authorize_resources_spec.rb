# frozen_string_literal: true

require "spec_helper"

RSpec.describe AtharAuth::Authorization do
  include TestTokens

  # Use ActiveRecord models defined in spec_helper.rb
  # TestUser, TestProject, TestCase, TestEmployee

  let(:controller_class) do
    Class.new do
      include AtharAuth::Authorization

      attr_accessor :request, :decoded_token_value, :params

      def initialize(request_mock = nil)
        @request = request_mock
        @params = {}
      end

      def decoded_token
        @decoded_token_value
      end

      def render(options)
        @rendered = options
      end

      def render_unauthorized(message)
        render json: { error: message }, status: :unauthorized
      end

      def render_forbidden(message)
        render json: { error: message }, status: :forbidden
      end

      def user_signed_in?
        !decoded_token.nil?
      end

      def current_user
        # Override to return a TestUser instance
        @current_user ||= TestUser.new(
          id: 456,
          global: decoded_token&.dig('u', 'g') || false,
          name: decoded_token&.dig('u', 'n'),
          email: decoded_token&.dig('u', 'm')
        )
      end

      # Mock the methods that Authorization module expects but aren't in ControllerHelpers
      def current_project
        return nil unless decoded_token&.dig('ss', 'pr')

        project_data = decoded_token['ss']['pr']
        TestProject.new(
          id: project_data['id'],
          name: project_data['n']
        )
      end

      def current_role
        return nil unless decoded_token&.dig('ss', 'r')

        role_data = decoded_token['ss']['r']
        # For simplicity, just return a simple object with the required methods
        OpenStruct.new(
          name: role_data['n'],
          level: role_data['l'],
          scope: role_data['s']
        )
      end

      def accessible_project_ids
        if global_user?
          # Global users can access all projects
          []
        else
          # Project user: current project + other accessible projects
          current_id = current_project&.id
          other_projects = decoded_token&.dig('a', 'op') || []
          other_ids = other_projects.map { |p| p['id'] }

          ([current_id] + other_ids).compact.uniq
        end
      end

      def global_user?
        current_user&.global_user? || false
      end

      def user_can_access_project?(project)
        return true if global_user?
        return false unless project

        project_id = project.respond_to?(:id) ? project.id : project.to_i
        accessible_project_ids.include?(project_id)
      end

      # Override current_permissions to work without TokenCompression in tests
      def current_permissions
        @current_permissions ||= decoded_token&.dig('ss', 'p') || []
      end

      attr_reader :rendered
    end
  end

  let(:request_mock) { double("request", headers: { "Authorization" => "Bearer test_token" }) }
  let(:controller) { controller_class.new(request_mock) }

  describe "#authorize_resources" do
    context "with collection action (index)" do
      before do
        controller.decoded_token_value = case_manager_token
        # Remove can? mocking - test real permission logic
      end

      it "authorizes collection and sets instance variable" do
        result = controller.authorize_resources(:index, TestCase)

        expect(result).to be_a(ActiveRecord::Relation)
        expect(controller.instance_variable_get(:@test_cases)).to eq(result)
      end

      it "applies project filtering for project-based resources" do
        # Create test data
        project1 = TestProject.create!(name: "Project 1")
        project2 = TestProject.create!(name: "Project 2")
        TestCase.create!(title: "Case 1", project: project1)
        TestCase.create!(title: "Case 2", project: project2)

        result = controller.authorize_resources(:index, TestCase)

        expect(result).to be_a(ActiveRecord::Relation)
        # Should include cases from accessible projects (ids: 2, 3)
        expect(result.where_values_hash).to include("project_id" => [2, 3])
      end

      it "renders forbidden when user lacks permission" do
        # Use a token without the required permissions
        controller.decoded_token_value = {
          's' => 456,
          'ss' => {
            'p' => ['read:other_resource'] # No test_case permissions
          },
          'u' => { 'g' => false }
        }

        result = controller.authorize_resources(:index, TestCase)

        expect(result).to be false
        expect(controller.rendered).to eq(json: { error: "You are not allowed to index test_cases" },
                                          status: :forbidden)
      end

      it "accepts custom collection name" do
        result = controller.authorize_resources(:index, TestCase, collection_name: "@my_cases")

        expect(controller.instance_variable_get(:@my_cases)).to eq(result)
        expect(controller.instance_variable_get(:@test_cases)).to be_nil
      end
    end

    context "with single resource action (show)" do
      before do
        controller.decoded_token_value = case_manager_token
        controller.params = { id: "123" }
        # Create test data
        TestCase.create!(id: 123, title: "Test Case", project_id: 2)
      end

      it "authorizes single resource and sets instance variable" do
        result = controller.authorize_resources(:show, TestCase)

        expect(result).to be_a(TestCase)
        expect(result.id).to eq(123)
        expect(controller.instance_variable_get(:@test_case)).to eq(result)
      end

      it "accepts custom resource name" do
        result = controller.authorize_resources(:show, TestCase, resource_name: "@my_case")

        expect(controller.instance_variable_get(:@my_case)).to eq(result)
        expect(controller.instance_variable_get(:@test_case)).to be_nil
      end

      it "uses provided resource instead of finding by ID" do
        existing_resource = TestCase.new(id: "existing", name: "Existing Case")

        result = controller.authorize_resources(:show, TestCase, resource: existing_resource)

        expect(result).to eq(existing_resource)
        expect(controller.instance_variable_get(:@test_case)).to eq(existing_resource)
      end

      it "renders forbidden when user lacks permission" do
        # Use a token without the required permissions
        controller.decoded_token_value = {
          's' => 456,
          'ss' => {
            'p' => ['read:other_resource'] # No test_case permissions
          },
          'u' => { 'g' => false }
        }

        result = controller.authorize_resources(:show, TestCase)

        expect(result).to be false
        expect(controller.rendered).to eq(json: { error: "You are not allowed to show this test_case" },
                                          status: :forbidden)
      end
    end

    context "with create action" do
      before do
        controller.decoded_token_value = case_manager_token
        controller.params = {}
        # Remove can? mocking - test real permission logic
      end

      it "creates new resource for create actions" do
        result = controller.authorize_resources(:create, TestCase)

        expect(result).to be_a(TestCase)
        expect(result.id).to be_nil
        expect(controller.instance_variable_get(:@test_case)).to eq(result)
      end

      it "sets project_id for project-based resources" do
        # Mock current_project
        project = double("Project", id: 2)
        allow(controller).to receive(:current_project).and_return(project)

        result = controller.authorize_resources(:create, TestCase)

        expect(result.project_id).to eq(2)
      end
    end

    context "with different resource class formats" do
      before do
        controller.decoded_token_value = case_manager_token
        # Remove can? mocking - test real permission logic
      end

      it "accepts class directly" do
        result = controller.authorize_resources(:index, TestCase)
        expect(result).to be_a(ActiveRecord::Relation)
      end

      it "accepts string class name" do
        stub_const("TestCase", TestCase)
        result = controller.authorize_resources(:index, "TestCase")
        expect(result).to be_a(ActiveRecord::Relation)
      end

      it "accepts symbol and converts to class" do
        stub_const("TestCase", TestCase)
        result = controller.authorize_resources(:index, :test_case)
        expect(result).to be_a(ActiveRecord::Relation)
      end
    end

    context "when user is not signed in" do
      before do
        controller.decoded_token_value = nil
      end

      it "renders unauthorized" do
        result = controller.authorize_resources(:index, TestCase)

        expect(result).to be false
        expect(controller.rendered).to eq(json: { error: "You must be signed in" }, status: :unauthorized)
      end
    end

    context "with additional scoping" do
      before do
        controller.decoded_token_value = case_manager_token
        # Remove can? mocking - test real permission logic
      end

      it "applies hash-based additional scoping" do
        scope_conditions = { status: "active" }
        result = controller.authorize_resources(:index, TestCase, scope: scope_conditions)

        expect(result).to be_a(ActiveRecord::Relation)
        # Verify that additional scoping was actually applied
        expect(result.where_values_hash).to include("status" => "active")
      end

      it "applies proc-based additional scoping" do
        scope_proc = ->(scope) { scope.where(status: "active") }
        result = controller.authorize_resources(:index, TestCase, scope: scope_proc)

        expect(result).to be_a(ActiveRecord::Relation)
        # Verify that proc-based scoping was applied
        expect(result.where_values_hash).to include("status" => "active")
      end
    end

    context "project filtering behavior" do
      before do
        controller.decoded_token_value = case_manager_token
      end

      it "applies project filtering for project-based resources when user is not global" do
        # Mock accessible_project_ids to simulate project-based user
        allow(controller).to receive(:accessible_project_ids).and_return([2, 3])
        allow(controller).to receive(:global_user?).and_return(false)

        result = controller.authorize_resources(:index, TestCase)

        expect(result).to be_a(ActiveRecord::Relation)
        expect(result.where_values_hash).to include("project_id" => [2, 3])
      end

      it "does not apply project filtering for global users" do
        # Mock global user behavior
        allow(controller).to receive(:global_user?).and_return(true)
        allow(controller).to receive(:accessible_project_ids).and_return([1, 2, 3])

        result = controller.authorize_resources(:index, TestCase)

        expect(result).to be_a(ActiveRecord::Relation)
        # Global users should not have project filtering applied
        expect(result.where_values_hash).not_to have_key("project_id")
      end

      it "does not apply project filtering for resources without project_id column" do
        # TestEmployee doesn't have project_id column
        result = controller.authorize_resources(:index, TestEmployee)

        expect(result).to be_a(ActiveRecord::Relation)
        # No project filtering should be applied for non-project resources
        expect(result.where_values_hash).not_to have_key("project_id")
      end

      it "applies both project filtering and additional scoping" do
        allow(controller).to receive(:accessible_project_ids).and_return([2, 3])
        allow(controller).to receive(:global_user?).and_return(false)

        scope_conditions = { status: "active" }
        result = controller.authorize_resources(:index, TestCase, scope: scope_conditions)

        expect(result).to be_a(ActiveRecord::Relation)
        # Both project filtering and additional scoping should be applied
        expect(result.where_values_hash).to include("project_id" => [2, 3])
        expect(result.where_values_hash).to include("status" => "active")
      end
    end

    context "global user behavior" do
      let(:global_user_token) do
        {
          's' => 789, # user.id
          'i' => 1_640_995_200,
          'e' => 1_641_081_600,
          't' => 'session',
          'sc' => 'cm',
          'u' => {
            'n' => 'Sarah Manager',
            'm' => '<EMAIL>',
            'g' => true # Global user
          },
          'ss' => {
            'r' => {
              'n' => 'hr_manager',
              'l' => 30,
              's' => 'global_role'
            },
            'p' => [
              'index:test_case', 'show:test_case', 'create:test_case', 'update:test_case',
              'index:test_employee', 'show:test_employee', 'create:test_employee', 'update:test_employee',
              'manage:user'
            ]
          }
        }
      end

      before do
        controller.decoded_token_value = global_user_token
      end

      it "bypasses project filtering for global users" do
        result = controller.authorize_resources(:index, TestCase)

        expect(result).to be_a(ActiveRecord::Relation)
        # Global users should not have project filtering applied
        expect(result.where_values_hash).not_to have_key("project_id")
      end

      it "allows global users to access any resource regardless of project" do
        # Create a resource with a specific project_id
        resource_in_project = TestCase.new(id: "123", project_id: 999)

        result = controller.authorize_resources(:show, TestCase, resource: resource_in_project)

        expect(result).to eq(resource_in_project)
        expect(controller.instance_variable_get(:@test_case)).to eq(resource_in_project)
      end

      it "allows global users to create resources without project restrictions" do
        result = controller.authorize_resources(:create, TestCase)

        expect(result).to be_a(TestCase)
        expect(result.id).to be_nil
        # Global users can create resources without automatic project assignment
      end

      it "works with additional scoping for global users" do
        scope_conditions = { status: "active" }
        result = controller.authorize_resources(:index, TestCase, scope: scope_conditions)

        expect(result).to be_a(ActiveRecord::Relation)
        # Only additional scoping should be applied, not project filtering
        expect(result.where_values_hash).not_to have_key("project_id")
        expect(result.where_values_hash).to include("status" => "active")
      end
    end

    context "permission validation with real token data" do
      it "allows access when user has required permissions" do
        # Token with correct permissions
        token_with_permissions = {
          's' => 456,
          'ss' => {
            'p' => ['index:test_case', 'create:test_case'] # Has required permissions
          },
          'u' => { 'g' => false }
        }
        controller.decoded_token_value = token_with_permissions

        result = controller.authorize_resources(:index, TestCase)

        expect(result).to be_a(ActiveRecord::Relation)
      end

      it "denies access when user lacks required permissions" do
        # Token without required permissions
        token_without_permissions = {
          's' => 456,
          'ss' => {
            'p' => ['read:other_resource'] # Missing test_case permissions
          },
          'u' => { 'g' => false }
        }
        controller.decoded_token_value = token_without_permissions

        result = controller.authorize_resources(:index, TestCase)

        expect(result).to be false
        expect(controller.rendered).to eq(json: { error: "You are not allowed to index test_cases" },
                                          status: :forbidden)
      end

      it "allows access with manage permissions" do
        # Token with manage permissions (should allow any action)
        token_with_manage = {
          's' => 456,
          'ss' => {
            'p' => ['manage:test_case'] # Manage permission allows all actions
          },
          'u' => { 'g' => false }
        }
        controller.decoded_token_value = token_with_manage

        result = controller.authorize_resources(:show, TestCase)

        expect(result).to be_a(TestCase)
      end
    end

    context "TokenCompression integration" do
      before do
        # Use the real case_manager_token from TestTokens which uses compressed format
        controller.decoded_token_value = case_manager_token
      end

      it "correctly extracts permissions from compressed token" do
        # Verify that current_permissions works with compressed token
        permissions = controller.current_permissions
        expect(permissions).to include("index:test_case", "show:test_case", "create:test_case", "update:test_case")
      end

      it "correctly extracts user information from compressed token" do
        # Test that user data is properly extracted
        expect(controller.current_user.name).to eq("Ahmed Ali")
        expect(controller.current_user.email).to eq("<EMAIL>")
        expect(controller.current_user.global_user?).to be false
      end

      it "correctly extracts project information from compressed token" do
        # Test that project data is properly extracted
        expect(controller.current_project.id).to eq(2)
        expect(controller.current_project.name).to eq("Beta Project")
      end

      it "correctly extracts role information from compressed token" do
        # Test that role data is properly extracted
        expect(controller.current_role.name).to eq("case_manager")
        expect(controller.current_role.level).to eq(10)
        expect(controller.current_role.scope).to eq("project_based")
      end

      it "correctly extracts accessible project IDs from compressed token" do
        # Test that accessible_project_ids works with compressed format
        project_ids = controller.accessible_project_ids
        expect(project_ids).to include(2, 3) # Current project + other projects
      end

      it "works with authorization using compressed token data" do
        # Test that the full authorization flow works with compressed tokens
        result = controller.authorize_resources(:index, TestCase)

        expect(result).to be_a(ActiveRecord::Relation)
        expect(controller.instance_variable_get(:@test_cases)).to eq(result)
      end
    end

    context "edge cases and error handling" do
      it "handles missing token gracefully" do
        controller.decoded_token_value = nil

        result = controller.authorize_resources(:index, TestCase)

        expect(result).to be false
        expect(controller.rendered).to eq(json: { error: "You must be signed in" }, status: :unauthorized)
      end

      it "handles malformed token gracefully" do
        controller.decoded_token_value = { "invalid" => "token" }

        result = controller.authorize_resources(:index, TestCase)

        expect(result).to be false
        expect(controller.rendered).to eq(json: { error: "You are not allowed to index test_cases" },
                                          status: :forbidden)
      end

      it "handles invalid resource class gracefully" do
        result = controller.authorize_resources(:index, "NonExistentClass")
        expect(result).to be false
        expect(controller.rendered).to eq(json: { error: "You must be signed in" }, status: :unauthorized)
      end

      it "handles resources without required methods gracefully" do
        invalid_class = Class.new do
          def self.name
            "InvalidClass"
          end
          # Missing underscore, column_names, all methods
        end

        result = controller.authorize_resources(:index, invalid_class)
        expect(result).to be false
      end
    end

    context "complex resource finding logic" do
      before do
        controller.decoded_token_value = case_manager_token
        controller.params = { id: "123" }
      end

      it "finds resource by ID for non-project resources" do
        # Create a mock class without project_id
        mock_class_without_project = Class.new do
          def self.name
            "TestEmployee"
          end

          def self.underscore
            "test_employee"
          end

          def self.column_names
            %w[id name email] # No project_id
          end

          def self.find(id)
            TestCase.new(id: id, name: "Employee #{id}")
          end

          def self.all
            ActiveRecord::Relation.new([])
          end
        end

        result = controller.authorize_resources(:show, mock_class_without_project)

        expect(result).to be_a(TestCase)
        expect(result.id).to eq(123)
      end

      it "applies project filtering when finding project-based resources" do
        # Mock the supports_project_filtering? method to return true
        allow(controller).to receive(:supports_project_filtering?).and_return(true)
        allow(controller).to receive(:accessible_project_ids).and_return([2, 3])

        # Mock the where chain for project filtering
        filtered_scope = double("FilteredScope")
        allow(TestCase).to receive(:where).with(project_id: [2, 3]).and_return(filtered_scope)
        allow(filtered_scope).to receive(:find).with("123").and_return(TestCase.new(id: 123))

        result = controller.authorize_resources(:show, TestCase)

        expect(result).to be_a(TestCase)
        expect(result.id).to eq(123)
        expect(TestCase).to have_received(:where).with(project_id: [2, 3])
      end

      it "creates new resource with project_id for create actions" do
        controller.params = {} # No ID for create action

        # Mock current_project
        project = double("Project", id: 2)
        allow(controller).to receive(:current_project).and_return(project)
        allow(controller).to receive(:supports_project_filtering?).and_return(true)

        result = controller.authorize_resources(:create, TestCase)

        expect(result).to be_a(TestCase)
        expect(result.project_id).to eq(2)
        expect(result.id).to be_nil # New resource
      end

      it "creates new resource without project_id for non-project resources" do
        controller.params = {} # No ID for create action

        # Create a mock class without project_id
        mock_class_without_project = Class.new do
          def self.name
            "TestEmployee"
          end

          def self.underscore
            "test_employee"
          end

          def self.column_names
            %w[id name email] # No project_id
          end

          def self.new(attributes = {})
            TestCase.new(attributes)
          end

          def self.all
            ActiveRecord::Relation.new([])
          end
        end

        result = controller.authorize_resources(:create, mock_class_without_project)

        expect(result).to be_a(TestCase)
        expect(result.project_id).to be_nil # No project assignment
        expect(result.id).to be_nil # New resource
      end

      it "uses provided resource instead of finding by ID" do
        existing_resource = TestCase.new(id: "existing", name: "Existing Case", project_id: 2)

        result = controller.authorize_resources(:show, TestCase, resource: existing_resource)

        expect(result).to eq(existing_resource)
        expect(controller.instance_variable_get(:@test_case)).to eq(existing_resource)
      end

      it "validates project access for provided resource instances" do
        # Create a resource in an inaccessible project
        inaccessible_resource = TestCase.new(id: "123", project_id: 999)
        allow(controller).to receive(:accessible_project_ids).and_return([2, 3])

        result = controller.authorize_resources(:show, TestCase, resource: inaccessible_resource)

        expect(result).to be false
        expect(controller.rendered).to eq(json: { error: "You are not allowed to show this test_case" },
                                          status: :forbidden)
      end

      it "allows global users to access any resource regardless of project" do
        controller.decoded_token_value = {
          's' => 789,
          'u' => { 'g' => true }, # Global user
          'ss' => { 'p' => ['show:test_case'] }
        }

        # Resource in any project
        resource_in_any_project = TestCase.new(id: 123, project_id: 999)

        result = controller.authorize_resources(:show, TestCase, resource: resource_in_any_project)

        expect(result).to eq(resource_in_any_project)
      end
    end
  end
end
