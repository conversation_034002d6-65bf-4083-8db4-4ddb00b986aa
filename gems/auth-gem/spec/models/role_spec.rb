# frozen_string_literal: true

require "spec_helper"

RSpec.describe AtharAuth::Models::Role do
  include TestTokens

  let(:compressed_token) { case_manager_token }
  let(:expanded_token) { AtharAuth::TokenCompression.expand_token(compressed_token) }
  let(:role_data) { expanded_token.dig("session", "role") }

  describe ".from_token_data" do
    context "with valid role data" do
      it "creates role from compressed token data" do
        role = described_class.from_token_data(role_data)

        expect(role).to be_a(described_class)
        expect(role.name).to eq("case_manager")
        expect(role.level).to eq(10)
        expect(role.scope).to eq("project_based")
        expect(role.permissions).to eq([])
      end
    end

    context "with nil data" do
      it "returns nil" do
        expect(described_class.from_token_data(nil)).to be_nil
      end
    end

    context "with empty data" do
      it "creates role with nil attributes" do
        role = described_class.from_token_data({})

        expect(role).to be_a(described_class)
        expect(role.name).to be_nil
        expect(role.level).to be_nil
        expect(role.scope).to be_nil
      end
    end

    context "with incomplete data" do
      it "creates role with available attributes" do
        incomplete_data = { "name" => "incomplete_role" }
        role = described_class.from_token_data(incomplete_data)

        expect(role.name).to eq("incomplete_role")
        expect(role.level).to be_nil
        expect(role.scope).to be_nil
      end
    end
  end

  describe ".from_grpc_response" do
    let(:grpc_role) do
      double("GrpcRole",
             name: "supervisor",
             level: 30,
             global: false)
    end

    it "creates role from gRPC response" do
      role = described_class.from_grpc_response(grpc_role)

      expect(role.name).to eq("supervisor")
      expect(role.level).to eq(30)
      expect(role.scope).to eq("project_based")
    end

    it "handles global role correctly" do
      grpc_role = double("GrpcRole", name: "hr_manager", level: 30, global: true)
      role = described_class.from_grpc_response(grpc_role)

      expect(role.scope).to eq("global_role")
    end

    it "returns nil for nil input" do
      expect(described_class.from_grpc_response(nil)).to be_nil
    end
  end

  describe "domain methods" do
    let(:project_role) { described_class.new(name: "case_manager", level: 10, scope: "project_based") }
    let(:global_role) { described_class.new(name: "hr_manager", level: 30, scope: "global_role") }

    describe "#global_role?" do
      it "returns true for global roles" do
        expect(global_role.global_role?).to be true
      end

      it "returns false for project-based roles" do
        expect(project_role.global_role?).to be false
      end
    end

    describe "#project_based_role?" do
      it "returns true for project-based roles" do
        expect(project_role.project_based_role?).to be true
      end

      it "returns false for global roles" do
        expect(global_role.project_based_role?).to be false
      end
    end
  end

  describe "hierarchy methods" do
    let(:case_manager) { described_class.new(name: "case_manager", level: 10, scope: "project_based") }
    let(:supervisor) { described_class.new(name: "supervisor", level: 30, scope: "project_based") }
    let(:hr_manager) { described_class.new(name: "hr_manager", level: 30, scope: "global_role") }
    let(:super_admin) { described_class.new(name: "super_admin", level: 50, scope: "global_role") }

    describe "#higher_level_than?" do
      it "returns true when level is higher" do
        expect(supervisor.higher_level_than?(case_manager)).to be true
        expect(super_admin.higher_level_than?(supervisor)).to be true
      end

      it "returns false when level is lower or equal" do
        expect(case_manager.higher_level_than?(supervisor)).to be false
        expect(supervisor.higher_level_than?(hr_manager)).to be false # Same level
      end

      it "returns false for non-role objects" do
        expect(case_manager.higher_level_than?("not a role")).to be false
      end
    end

    describe "#same_level_as?" do
      it "returns true for same level roles" do
        expect(supervisor.same_level_as?(hr_manager)).to be true
      end

      it "returns false for different level roles" do
        expect(supervisor.same_level_as?(case_manager)).to be false
      end

      it "returns false for non-role objects" do
        expect(supervisor.same_level_as?("not a role")).to be false
      end
    end

    describe "#can_supervise?" do
      it "returns true for higher level roles" do
        expect(supervisor.can_supervise?(case_manager)).to be true
        expect(super_admin.can_supervise?(supervisor)).to be true
      end

      it "returns true for same level global roles supervising project roles" do
        expect(hr_manager.can_supervise?(supervisor)).to be true
      end

      it "returns false for same level project roles" do
        project_supervisor = described_class.new(name: "supervisor", level: 30, scope: "project_based")
        expect(supervisor.can_supervise?(project_supervisor)).to be false
      end

      it "returns false for lower level roles" do
        expect(case_manager.can_supervise?(supervisor)).to be false
      end
    end
  end

  describe "permission methods" do
    let(:role_with_permissions) do
      described_class.new(
        name: "case_manager",
        level: 10,
        scope: "project_based",
        permissions: ["read:case", "create:case"]
      )
    end

    describe "#has_permission?" do
      it "returns true for existing permissions" do
        expect(role_with_permissions.has_permission?("read:case")).to be true
        expect(role_with_permissions.has_permission?(:create_case)).to be false # Different format
      end

      it "returns false for non-existing permissions" do
        expect(role_with_permissions.has_permission?("delete:case")).to be false
      end
    end
  end

  describe "string representation" do
    let(:role) { described_class.new(name: "case_manager", level: 10, scope: "project_based") }

    describe "#to_s" do
      it "returns the role name" do
        expect(role.to_s).to eq("case_manager")
      end
    end

    describe "#inspect" do
      it "returns detailed string representation" do
        expect(role.inspect).to include("AtharAuth::Models::Role")
        expect(role.inspect).to include("name=\"case_manager\"")
        expect(role.inspect).to include("level=10")
        expect(role.inspect).to include("scope=\"project_based\"")
      end
    end
  end

  describe "comparison and sorting" do
    let(:case_manager) { described_class.new(name: "case_manager", level: 10, scope: "project_based") }
    let(:supervisor) { described_class.new(name: "supervisor", level: 30, scope: "project_based") }
    let(:hr_manager) { described_class.new(name: "hr_manager", level: 30, scope: "global_role") }

    describe "#<=>" do
      it "sorts by level descending, then by name ascending" do
        roles = [case_manager, supervisor, hr_manager]
        sorted = roles.sort

        # Level 30 roles should come first (hr_manager, supervisor), then level 10 (case_manager)
        # Within same level, sort by name: hr_manager < supervisor
        expect(sorted.map(&:name)).to eq(%w[hr_manager supervisor case_manager])
      end

      it "returns nil for non-role objects" do
        expect(case_manager <=> "not a role").to be_nil
      end
    end

    describe "#==" do
      it "returns true for roles with same name" do
        same_role = described_class.new(name: "case_manager", level: 20, scope: "global_role")
        expect(case_manager == same_role).to be true
      end

      it "returns false for roles with different names" do
        expect(case_manager == supervisor).to be false
      end

      it "returns false for non-role objects" do
        expect(case_manager == "case_manager").to be false
      end
    end

    describe "#eql? and #hash" do
      it "has consistent equality and hash behavior" do
        same_role = described_class.new(name: "case_manager", level: 20, scope: "global_role")

        expect(case_manager.eql?(same_role)).to be true
        expect(case_manager.hash).to eq(same_role.hash)
      end
    end
  end
end
