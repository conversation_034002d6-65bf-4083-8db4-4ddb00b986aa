# frozen_string_literal: true

require "spec_helper"

RSpec.describe AtharAuth::Models::User do
  include TestTokens

  let(:compressed_token) { case_manager_token }
  let(:global_token) { hr_manager_token }
  let(:expanded_token) { AtharAuth::TokenCompression.expand_token(compressed_token) }
  let(:expanded_global_token) { AtharAuth::TokenCompression.expand_token(global_token) }

  describe ".from_token_data" do
    context "with valid project-based user token" do
      it "creates user with rich associations" do
        user = described_class.from_token_data(expanded_token)

        expect(user).to be_a(described_class)
        expect(user.id).to eq(456)
        expect(user.name).to eq("<PERSON>")
        expect(user.email).to eq("<EMAIL>")
        expect(user.global).to be false
        expect(user.scope).to eq("cm")
        expect(user.permissions).to include("index:test_case", "show:test_case", "create:test_case", "update:test_case")
      end

      it "creates role association" do
        user = described_class.from_token_data(expanded_token)

        expect(user.role).to be_a(AtharAuth::Models::Role)
        expect(user.role.name).to eq("case_manager")
        expect(user.role.level).to eq(10)
        expect(user.role.scope).to eq("project_based")
      end

      it "creates project association" do
        user = described_class.from_token_data(expanded_token)

        expect(user.project).to be_a(AtharAuth::Models::Project)
        expect(user.project.id).to eq(2)
        expect(user.project.name).to eq("Beta Project")
        expect(user.project.id).to eq(2)
      end
    end

    context "with global user token" do
      it "creates global user without project" do
        user = described_class.from_token_data(expanded_global_token)

        expect(user.id).to eq(789)
        expect(user.name).to eq("Sarah Manager")
        expect(user.global).to be true
        expect(user.project).to be_nil
        expect(user.project).to be_nil
      end

      it "creates role association for global user" do
        user = described_class.from_token_data(expanded_global_token)

        expect(user.role).to be_a(AtharAuth::Models::Role)
        expect(user.role.name).to eq("hr_manager")
        expect(user.role.scope).to eq("global_role")
      end
    end

    context "with nil token" do
      it "returns nil" do
        expect(described_class.from_token_data(nil)).to be_nil
      end
    end

    context "with incomplete token" do
      it "creates user with available data" do
        incomplete_token = {
          "user" => {
            "id" => 123,
            "name" => "Incomplete User"
          }
        }

        user = described_class.from_token_data(incomplete_token)

        expect(user.id).to eq(123)
        expect(user.name).to eq("Incomplete User")
        expect(user.email).to be_nil
        expect(user.role).to be_nil
        expect(user.project).to be_nil
      end
    end
  end

  describe "domain methods" do
    let(:project_user) { described_class.from_token_data(expanded_token) }
    let(:global_user) { described_class.from_token_data(expanded_global_token) }

    describe "#global_user?" do
      it "returns true for global users" do
        expect(global_user.global_user?).to be true
      end

      it "returns true for users with global roles" do
        # Even if user.global is false, if role is global, user should be considered global
        expect(global_user.global_user?).to be true
      end

      it "returns false for project-based users" do
        expect(project_user.global_user?).to be false
      end
    end

    describe "#project_based_user?" do
      it "returns true for project-based users" do
        expect(project_user.project_based_user?).to be true
      end

      it "returns false for global users" do
        expect(global_user.project_based_user?).to be false
      end
    end
  end

  describe "role checking methods" do
    let(:case_manager) { described_class.from_token_data(expanded_token) }
    let(:hr_manager) { described_class.from_token_data(expanded_global_token) }

    describe "#case_manager?" do
      it "returns true for case managers" do
        expect(case_manager.case_manager?).to be true
      end

      it "returns false for other roles" do
        expect(hr_manager.case_manager?).to be false
      end
    end

    describe "#hr_manager?" do
      it "returns true for HR managers" do
        expect(hr_manager.hr_manager?).to be true
      end

      it "returns false for other roles" do
        expect(case_manager.hr_manager?).to be false
      end
    end

    describe "#supervisor?" do
      it "returns false when role is not supervisor" do
        expect(case_manager.supervisor?).to be false
        expect(hr_manager.supervisor?).to be false
      end
    end

    describe "#admin?" do
      it "returns false when role is not admin" do
        expect(case_manager.admin?).to be false
        expect(hr_manager.admin?).to be false
      end
    end

    describe "#super_admin?" do
      it "returns false when role is not super_admin" do
        expect(case_manager.super_admin?).to be false
        expect(hr_manager.super_admin?).to be false
      end
    end
  end

  describe "permission checking" do
    let(:user) { described_class.from_token_data(expanded_token) }

    describe "#can?" do
      it "returns true for existing permissions" do
        expect(user.can?("index:test_case")).to be true
        expect(user.can?("show:test_case")).to be true
        expect(user.can?("create:test_case")).to be true
        expect(user.can?("update:test_case")).to be true
      end

      it "returns false for non-existing permissions" do
        expect(user.can?("delete:case")).to be false
        expect(user.can?("manage:user")).to be false
      end

      it "handles symbol permissions" do
        expect(user.can?(:read_case)).to be false # Different format
      end
    end

    describe "#has_permission?" do
      it "is an alias for can?" do
        expect(user.has_permission?("index:test_case")).to eq(user.can?("index:test_case"))
        expect(user.has_permission?("delete:case")).to eq(user.can?("delete:case"))
      end
    end
  end

  describe "project access methods" do
    let(:project_user) { described_class.from_token_data(expanded_token) }
    let(:global_user) { described_class.from_token_data(expanded_global_token) }

    describe "#accessible_project_ids" do
      it "returns project ID for project-based users" do
        expect(project_user.accessible_project_ids).to eq([2])
      end

      it "returns empty array for global users (placeholder)" do
        # This is a placeholder implementation - in real system would fetch all project IDs
        expect(global_user.accessible_project_ids).to eq([])
      end
    end

    describe "#can_access_project?" do
      it "returns true for accessible projects" do
        expect(project_user.can_access_project?(2)).to be true
      end

      it "returns false for non-accessible projects" do
        expect(project_user.can_access_project?(999)).to be false
      end

      it "returns true for global users" do
        expect(global_user.can_access_project?(999)).to be true
      end

      it "handles string project IDs" do
        expect(project_user.can_access_project?("2")).to be true
        expect(project_user.can_access_project?("999")).to be false
      end
    end
  end

  describe "string representation" do
    let(:user) { described_class.from_token_data(expanded_token) }

    describe "#to_s" do
      it "returns name when available" do
        expect(user.to_s).to eq("Ahmed Ali")
      end

      it "returns email when name is nil" do
        user.name = nil
        expect(user.to_s).to eq("<EMAIL>")
      end

      it "returns user ID when name and email are nil" do
        user.name = nil
        user.email = nil
        expect(user.to_s).to eq("User #456")
      end
    end

    describe "#inspect" do
      it "returns detailed string representation" do
        expect(user.inspect).to include("AtharAuth::Models::User")
        expect(user.inspect).to include("id=456")
        expect(user.inspect).to include("name=\"Ahmed Ali\"")
        expect(user.inspect).to include("email=\"<EMAIL>\"")
        expect(user.inspect).to include("role=case_manager")
      end
    end
  end

  describe "comparison and sorting" do
    let(:user1) { described_class.from_token_data(expanded_token) }
    let(:user2) { described_class.from_token_data(expanded_global_token) }

    describe "#<=>" do
      it "sorts by name, then email, then id" do
        users = [user2, user1] # Sarah Manager, Ahmed Ali
        sorted = users.sort

        expect(sorted.map(&:name)).to eq(["Ahmed Ali", "Sarah Manager"])
      end

      it "returns nil for non-user objects" do
        expect(user1 <=> "not a user").to be_nil
      end
    end

    describe "#==" do
      it "returns true for users with same ID" do
        same_user = described_class.from_token_data(expanded_token)
        expect(user1 == same_user).to be true
      end

      it "returns false for users with different IDs" do
        expect(user1 == user2).to be false
      end

      it "returns false for non-user objects" do
        expect(user1 == "Ahmed Ali").to be false
      end
    end

    describe "#eql? and #hash" do
      it "has consistent equality and hash behavior" do
        same_user = described_class.from_token_data(expanded_token)

        expect(user1.eql?(same_user)).to be true
        expect(user1.hash).to eq(same_user.hash)
      end
    end
  end
end
