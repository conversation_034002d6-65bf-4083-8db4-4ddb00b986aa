# frozen_string_literal: true

require "spec_helper"

RSpec.describe AtharAuth::Models::Project do
  include TestTokens

  let(:compressed_token) { case_manager_token }
  let(:expanded_token) { AtharAuth::TokenCompression.expand_token(compressed_token) }
  let(:project_data) { expanded_token.dig("session", "project") }

  describe ".from_token_data" do
    context "with valid project data" do
      it "creates project from compressed token data" do
        project = described_class.from_token_data(project_data)

        expect(project).to be_a(described_class)
        expect(project.id).to eq(2)
        expect(project.name).to eq("Beta Project")
        expect(project.description).to eq("A test project")
        expect(project.status).to eq("active")
      end
    end

    context "with nil data" do
      it "returns nil" do
        expect(described_class.from_token_data(nil)).to be_nil
      end
    end

    context "with empty data" do
      it "creates project with nil attributes and default status" do
        project = described_class.from_token_data({})

        expect(project).to be_a(described_class)
        expect(project.id).to be_nil
        expect(project.name).to be_nil
        expect(project.description).to be_nil
        expect(project.status).to eq("active") # Default value
      end
    end

    context "with incomplete data" do
      it "creates project with available attributes" do
        incomplete_data = { "id" => 5, "name" => "Incomplete Project" }
        project = described_class.from_token_data(incomplete_data)

        expect(project.id).to eq(5)
        expect(project.name).to eq("Incomplete Project")
        expect(project.description).to be_nil
        expect(project.status).to eq("active") # Default
      end
    end

    context "with custom status" do
      it "uses provided status" do
        data_with_status = project_data.merge("status" => "inactive")
        project = described_class.from_token_data(data_with_status)

        expect(project.status).to eq("inactive")
      end
    end
  end

  describe ".from_grpc_response" do
    let(:grpc_project) do
      double("GrpcProject",
             id: "10",
             name: "gRPC Project",
             description: "From gRPC service",
             status: "active")
    end

    it "creates project from gRPC response" do
      project = described_class.from_grpc_response(grpc_project)

      expect(project.id).to eq(10)
      expect(project.name).to eq("gRPC Project")
      expect(project.description).to eq("From gRPC service")
      expect(project.status).to eq("active")
    end

    it "handles missing status with default" do
      grpc_project = double("GrpcProject", id: "10", name: "Test", description: "Test", status: nil)
      project = described_class.from_grpc_response(grpc_project)

      expect(project.status).to eq("active")
    end

    it "converts string ID to integer" do
      project = described_class.from_grpc_response(grpc_project)
      expect(project.id).to be_an(Integer)
      expect(project.id).to eq(10)
    end

    it "returns nil for nil input" do
      expect(described_class.from_grpc_response(nil)).to be_nil
    end
  end

  describe "domain methods" do
    let(:active_project) { described_class.new(id: 1, name: "Active", status: "active") }
    let(:inactive_project) { described_class.new(id: 2, name: "Inactive", status: "inactive") }

    describe "#active?" do
      it "returns true for active projects" do
        expect(active_project.active?).to be true
      end

      it "returns false for inactive projects" do
        expect(inactive_project.active?).to be false
      end
    end

    describe "#inactive?" do
      it "returns true for inactive projects" do
        expect(inactive_project.inactive?).to be true
      end

      it "returns false for active projects" do
        expect(active_project.inactive?).to be false
      end
    end
  end

  describe "string representation" do
    let(:project) { described_class.new(id: 1, name: "Test Project", status: "active") }

    describe "#to_s" do
      it "returns the project name" do
        expect(project.to_s).to eq("Test Project")
      end

      it "returns nil when name is nil" do
        project.name = nil
        expect(project.to_s).to be_nil
      end
    end

    describe "#inspect" do
      it "returns detailed string representation" do
        expect(project.inspect).to include("AtharAuth::Models::Project")
        expect(project.inspect).to include("id=1")
        expect(project.inspect).to include("name=\"Test Project\"")
        expect(project.inspect).to include("status=\"active\"")
      end
    end
  end

  describe "comparison and sorting" do
    let(:project_a) { described_class.new(id: 1, name: "Alpha Project", status: "active") }
    let(:project_b) { described_class.new(id: 2, name: "Beta Project", status: "active") }
    let(:project_c) { described_class.new(id: 3, name: "Alpha Project", status: "inactive") }

    describe "#<=>" do
      it "sorts by name" do
        projects = [project_b, project_a, project_c]
        sorted = projects.sort

        # Should sort by name: Alpha, Alpha, Beta
        expect(sorted.map(&:name)).to eq(["Alpha Project", "Alpha Project", "Beta Project"])
      end

      it "returns nil for non-project objects" do
        expect(project_a <=> "not a project").to be_nil
      end
    end

    describe "#==" do
      it "returns true for projects with same ID" do
        same_project = described_class.new(id: 1, name: "Different Name", status: "inactive")
        expect(project_a == same_project).to be true
      end

      it "returns false for projects with different IDs" do
        expect(project_a == project_b).to be false
      end

      it "returns false for non-project objects" do
        expect(project_a == "Alpha Project").to be false
      end
    end

    describe "#eql? and #hash" do
      it "has consistent equality and hash behavior" do
        same_project = described_class.new(id: 1, name: "Different Name", status: "inactive")

        expect(project_a.eql?(same_project)).to be true
        expect(project_a.hash).to eq(same_project.hash)
      end

      it "allows projects to be used as hash keys" do
        project_hash = { project_a => "value_a", project_b => "value_b" }
        same_project = described_class.new(id: 1, name: "Different Name")

        expect(project_hash[same_project]).to eq("value_a")
      end
    end
  end

  describe "edge cases" do
    it "handles nil name gracefully" do
      project = described_class.new(id: 1, name: nil, status: "active")

      expect(project.to_s).to be_nil
      expect(project.inspect).to include("name=nil")
    end

    it "handles nil status gracefully" do
      project = described_class.new(id: 1, name: "Test", status: nil)

      expect(project.active?).to be false
      expect(project.inactive?).to be false
    end

    it "handles zero ID" do
      project = described_class.new(id: 0, name: "Zero ID Project")

      expect(project.id).to eq(0)
      expect(project.inspect).to include("id=0")
    end
  end

  describe "ActiveStruct behavior" do
    it "behaves like an ActiveStruct model" do
      project = described_class.new(id: 1, name: "Test")

      # Should have ActiveStruct methods
      expect(project).to respond_to(:attributes)
      expect(project).to respond_to(:attribute_names)

      # Should be able to access attributes
      expect(project.id).to eq(1)
      expect(project.name).to eq("Test")
    end

    it "supports attribute assignment" do
      project = described_class.new

      project.id = 5
      project.name = "Assigned Project"
      project.status = "inactive"

      expect(project.id).to eq(5)
      expect(project.name).to eq("Assigned Project")
      expect(project.status).to eq("inactive")
    end
  end
end
