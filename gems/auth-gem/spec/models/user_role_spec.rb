# frozen_string_literal: true

require "spec_helper"

RSpec.describe AtharAuth::Models::UserRole do
  include TestTokens

  describe ".from_token_data" do
    context "with valid user role data" do
      let(:user_role_data) do
        {
          "is_default" => true,
          "project" => {
            "id" => 2,
            "name" => "Test Project"
          },
          "role" => {
            "name" => "case_manager",
            "level" => 10,
            "scope" => "project_based"
          }
        }
      end

      it "creates user role from token data" do
        user_role = described_class.from_token_data(user_role_data)

        expect(user_role).to be_a(described_class)
        expect(user_role.project.id).to eq(2)
        expect(user_role.project.id).to be_a(Integer)
        expect(user_role.role.name).to eq("case_manager")
        expect(user_role.is_default).to be true
      end

      it "ensures project_id is numeric, not string" do
        user_role = described_class.from_token_data(user_role_data)

        expect(user_role.project.id).to be_a(Numeric)
        expect(user_role.project.id).not_to be_a(String)
        expect(user_role.project.id).to eq(2)
      end
    end

    context "with string project_id in token data" do
      let(:user_role_data_with_string_id) do
        {
          "is_default" => false,
          "project" => {
            "id" => "3", # String ID from API/token
            "name" => "Test Project 3"
          },
          "role" => {
            "name" => "supervisor",
            "level" => 20,
            "scope" => "project_based"
          }
        }
      end

      it "converts string project_id to integer" do
        user_role = described_class.from_token_data(user_role_data_with_string_id)

        expect(user_role.project.id).to eq(3)
        expect(user_role.project.id).to be_a(Integer)
        expect(user_role.project.id).not_to be_a(String)
      end
    end

    context "with nil data" do
      it "returns nil" do
        expect(described_class.from_token_data(nil)).to be_nil
      end
    end

    context "with empty data" do
      it "creates user role with nil attributes" do
        user_role = described_class.from_token_data({})

        expect(user_role).to be_a(described_class)
        expect(user_role.project).to be_nil
        expect(user_role.role).to be_nil
        expect(user_role.is_default).to be false
      end
    end
  end

  describe ".from_grpc_response" do
    let(:grpc_user_role) do
      double("GrpcUserRole",
             project_id: 5,
             role_name: "hr_manager",
             is_default: true)
    end

    it "creates user role from gRPC response" do
      user_role = described_class.from_grpc_response(grpc_user_role)

      expect(user_role.project.id).to eq(5)
      expect(user_role.project.id).to be_a(Integer)
      expect(user_role.role.name).to eq("hr_manager")
      expect(user_role.is_default).to be true
    end

    context "with string project_id from gRPC" do
      let(:grpc_user_role_with_string_id) do
        double("GrpcUserRole",
               project_id: "7", # String from gRPC
               role_name: "supervisor",
               is_default: false)
      end

      it "converts string project_id to integer" do
        user_role = described_class.from_grpc_response(grpc_user_role_with_string_id)

        expect(user_role.project.id).to eq(7)
        expect(user_role.project.id).to be_a(Integer)
        expect(user_role.project.id).not_to be_a(String)
      end
    end

    it "returns nil for nil input" do
      expect(described_class.from_grpc_response(nil)).to be_nil
    end
  end

  describe "domain methods" do
    let(:project_role) do
      described_class.new(
        project: AtharAuth::Models::Project.new(id: 2),
        role: AtharAuth::Models::Role.new(name: "case_manager"),
        is_default: true
      )
    end

    let(:global_role) do
      described_class.new(
        project: nil,
        role: AtharAuth::Models::Role.new(name: "hr_manager"),
        is_default: false
      )
    end

    describe "#project_based?" do
      it "returns true for roles with project_id" do
        expect(project_role.project_based?).to be true
      end

      it "returns false for roles without project_id" do
        expect(global_role.project_based?).to be false
      end
    end

    describe "#default?" do
      it "returns true when is_default is true" do
        expect(project_role.default?).to be true
      end

      it "returns false when is_default is false" do
        expect(global_role.default?).to be false
      end
    end

    describe "#global" do
      it "is an alias for global?" do
        expect(project_role.global).to eq(project_role.global?)
        expect(global_role.global).to eq(global_role.global?)
      end
    end
  end

  # NOTE: project_id type validation tests removed since we now use associations
  # The Project model handles its own id type validation

  describe "string representation" do
    let(:project_role) do
      described_class.new(
        project: AtharAuth::Models::Project.new(id: 2),
        role: AtharAuth::Models::Role.new(name: "case_manager"),
        is_default: true
      )
    end

    let(:global_role) do
      described_class.new(
        project: nil,
        role: AtharAuth::Models::Role.new(name: "hr_manager"),
        is_default: false
      )
    end

    describe "#to_s" do
      it "includes project_id for project-based roles" do
        expect(project_role.to_s).to eq("case_manager in Project 2")
      end

      it "shows only role name for global roles" do
        expect(global_role.to_s).to eq("hr_manager")
      end
    end

    describe "#inspect" do
      it "returns detailed string representation" do
        expect(project_role.inspect).to include("AtharAuth::Models::UserRole")
        expect(project_role.inspect).to include("role=\"case_manager\"")
        expect(project_role.inspect).to include("project_id=2")
        expect(project_role.inspect).to include("default=true")
      end
    end
  end

  describe "comparison and equality" do
    let(:user_role1) do
      user_role = described_class.new
      user_role.project = AtharAuth::Models::Project.new(id: 2)
      user_role.role = AtharAuth::Models::Role.new(name: "case_manager")
      user_role
    end

    let(:user_role2) do
      user_role = described_class.new
      user_role.project = AtharAuth::Models::Project.new(id: 2)
      user_role.role = AtharAuth::Models::Role.new(name: "case_manager")
      user_role
    end

    let(:user_role3) do
      user_role = described_class.new
      user_role.project = AtharAuth::Models::Project.new(id: 3)
      user_role.role = AtharAuth::Models::Role.new(name: "case_manager")
      user_role
    end

    describe "#==" do
      it "returns true for same role_name and project_id" do
        expect(user_role1 == user_role2).to be true
      end

      it "returns false for different project_id" do
        expect(user_role1 == user_role3).to be false
      end
    end

    describe "#<=>" do
      it "sorts by role name, then by project_id" do
        roles = [user_role3, user_role1] # Different project_ids, same role
        sorted = roles.sort

        expect(sorted.map { |ur| ur.project.id }).to eq([2, 3])
      end

      it "returns nil for non-UserRole objects" do
        expect(user_role1 <=> "not a user role").to be_nil
      end
    end
  end
end
