# frozen_string_literal: true

# Ensure RSpec is loaded when running specs directly
require "rspec"

require "athar_auth"
require "athar_auth/controller_helpers"
require "json"
require "active_record"
require "sqlite3"

# Load enhanced authorization modules
require_relative '../lib/athar_auth/token_compression'
require_relative '../lib/athar_auth/types/permission_list'
require_relative '../lib/athar_auth/models/role'
require_relative '../lib/athar_auth/models/project'
require_relative '../lib/athar_auth/models/user'

# Set up in-memory SQLite database for testing
ActiveRecord::Base.establish_connection(
  adapter: 'sqlite3',
  database: ':memory:'
)

# Create test database schema
ActiveRecord::Schema.define do
  create_table :test_users do |t|
    t.string :name
    t.string :email
    t.boolean :global, default: false
    t.timestamps
  end

  create_table :test_projects do |t|
    t.string :name
    t.integer :status, default: 1
    t.timestamps
  end

  create_table :test_cases do |t|
    t.string :title
    t.string :name
    t.string :status
    t.integer :project_id
    t.timestamps
  end

  create_table :test_employees do |t|
    t.string :name
    t.string :email
    t.string :status
    t.timestamps
  end
end

# Define test ActiveRecord models
class TestUser < ActiveRecord::Base
  self.table_name = 'test_users'

  def global_user?
    global
  end
end

class TestProject < ActiveRecord::Base
  self.table_name = 'test_projects'
  has_many :test_cases, foreign_key: 'project_id'
end

class TestCase < ActiveRecord::Base
  self.table_name = 'test_cases'
  belongs_to :project, class_name: 'TestProject', optional: true

  def self.name
    "TestCase"
  end

  def self.underscore
    "test_case"
  end
end

class TestEmployee < ActiveRecord::Base
  self.table_name = 'test_employees'

  def self.name
    "TestEmployee"
  end

  def self.underscore
    "test_employee"
  end
end

RSpec.configure do |config|
  # Enable flags like --only-failures and --next-failure
  config.example_status_persistence_file_path = ".rspec_status"

  # Disable RSpec exposing methods globally on `Module` and `main`
  config.disable_monkey_patching!

  config.expect_with :rspec do |c|
    c.syntax = :expect
  end

  # Configure RSpec mocks
  config.mock_with :rspec do |mocks|
    mocks.syntax = :expect
    mocks.verify_partial_doubles = true
  end

  # Clean database between tests
  config.before(:each) do
    TestUser.delete_all
    TestProject.delete_all
    TestCase.delete_all
    TestEmployee.delete_all
  end

  # Include RSpec mocks
  config.include RSpec::Mocks::ExampleMethods

  # Mock Rails logger for tests
  config.before(:each) do
    unless defined?(Rails)
      rails_double = double("Rails")
      logger_double = double("Logger")
      allow(logger_double).to receive(:warn)
      allow(logger_double).to receive(:info)
      allow(logger_double).to receive(:error)
      allow(rails_double).to receive(:logger).and_return(logger_double)
      stub_const("Rails", rails_double)
    end
  end
end

# Shared test data for enhanced authorization tests
module TestTokens
  def case_manager_token
    {
      's' => 456, # user.id
      'i' => 1_640_995_200,            # issued.at
      'e' => 1_641_081_600,            # expires.at
      't' => 'session',             # token.type
      'sc' => 'cm',                 # scope
      'u' => {                      # user context
        'n' => 'Ahmed Ali',         # user.name
        'm' => '<EMAIL>', # user.email
        'g' => false,               # user.global
        'ty' => 'employee',         # user.type
        'st' => 'active'            # user.status
      },
      'ss' => {                     # session context
        'r' => {                    # session.role (nested object)
          'n' => 'case_manager',    # session.role.name
          'l' => 10,                # session.role.level
          's' => 'project_based'    # session.role.scope
        },
        'p' => [
          'index:test_case', 'show:test_case', 'create:test_case', 'update:test_case',
          'index:test_employee', 'show:test_employee', 'create:test_employee', 'update:test_employee'
        ], # session.permissions
        'pr' => {                   # session.project (nested object)
          'id' => 2,                # session.project.id
          'n' => 'Beta Project',    # session.project.name
          'd' => 'A test project',  # session.project.description
          'st' => 'active'          # session.project.status
        }
      },
      'a' => {                      # access context
        'ty' => 'project',          # access.type
        'op' => [                   # access.other_projects
          { 'id' => 3, 'n' => 'Gamma Project', 'r' => 'supervisor' }
        ]
      }
    }
  end

  def hr_manager_token
    {
      's' => 789,
      'i' => 1_640_995_200,
      'e' => 1_641_081_600,
      't' => 'session',
      'sc' => 'people',
      'u' => {
        'n' => 'Sarah Manager',
        'm' => '<EMAIL>',
        'g' => true # Global user
      },
      'ss' => {
        'r' => {
          'n' => 'hr_manager',
          'l' => 30,
          's' => 'global_role'
        },
        'p' => [
          'index:test_case', 'show:test_case', 'create:test_case', 'update:test_case',
          'index:test_employee', 'show:test_employee', 'create:test_employee', 'update:test_employee',
          'manage:user'
        ]
      }
    }
  end
end
