# frozen_string_literal: true

require "jwt"
require_relative "athar_auth/version"
require_relative "athar_auth/controller_helpers"
require_relative "athar_auth/authorization"
require_relative "athar_auth/resource_authorization"
require_relative "athar_auth/token_compression"
require_relative "athar_auth/railtie"
require_relative "athar_auth/types/permission_list"
require_relative "athar_auth/models/user"
require_relative "athar_auth/models/role"
require_relative "athar_auth/models/project"
require_relative "athar_auth/models/user_role"
require_relative "athar_auth/models/user_role_collection"
require_relative "athar_auth/engine"

# AtharAuth provides secure, flexible JWT-based authentication and authorization
# for Rails APIs and microservices. It supports both API tokens and user sessions,
# with helper methods for verifying roles, permissions, and rendering standardized
# error responses.
#
# Features:
# - JWT token encoding and decoding
# - API and session-based authentication
# - Role and permission-based authorization
# - Rails controller helpers for authentication
# - Configurable token expiration times
# - Custom error handling for expired and invalid tokens
#
# @example Basic configuration
#   AtharAuth.configure do |config|
#     config.secret_key = Rails.application.credentials.jwt_secret!
#     config.algorithm = "HS256"
#     config.user_class = "User"
#   end
#
# @example Using in controllers
#   class ApiController < ActionController::API
#     include AtharAuth::ControllerHelpers
#     before_action :authenticate_api_user!
#   end
module AtharAuth
  class Error < StandardError; end

  # Custom error for expired tokens
  class ExpiredTokenError < Error
    def initialize(message = "Expired access token")
      super(message)
    end
  end

  # Custom error for invalid tokens
  class InvalidToken < Error
    def initialize(message = "Invalid token")
      super(message)
    end
  end

  # Simple configuration struct
  class << self
    # We store these in class-level attributes
    attr_accessor :secret_key, :algorithm, :user_class,
                  :main_token_expire_time, :session_token_expire_time,
                  :organization_project_name

    # Allows the target app to configure the gem
    def configure
      yield self
    end

    # Decode and verify a JWT, return payload if valid
    def decode_token(token)
      raise Error, "Missing secret_key" unless secret_key

      begin
        decoded, = JWT.decode(token, secret_key, true, { algorithm: algorithm || "HS256" })
        decoded
      rescue JWT::ExpiredSignature => e
        raise ExpiredTokenError
      rescue JWT::DecodeError => e
        raise InvalidToken, "Invalid token: #{e.message}"
      end
    end

    # Encode a payload into a JWT
    #
    # @param payload [Hash<String|Symbol>] The payload to encode
    # @return [String] The encoded JWT token
    # @raise [Error] If secret_key is not configured
    def encode_token(payload)
      raise Error, "Missing secret_key" unless secret_key

      JWT.encode(payload, secret_key, algorithm || "HS256")
    end
  end

  # Set default values
  self.organization_project_name = "Athar"
end
