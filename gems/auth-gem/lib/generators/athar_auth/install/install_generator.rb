module AtharAuth
  module Generators
    class InstallGenerator < Rails::Generators::Base
      source_root File.expand_path("templates", __dir__)

      argument :class_name, type: :string, default: "AuthUser"

      def copy_initializer_file
        template "athar_auth_initializer.rb.erb", "config/initializers/athar_auth.rb"
      end

      def copy_user_model_file
        template "auth_user_model.rb.erb", "app/models/#{file_name}.rb"
      end

      def inject_into_application_controller
        inject_into_class(
          "app/controllers/application_controller.rb",
          "ApplicationController"
        ) do
          "  include AtharAuth::ControllerHelpers\n" \
            "  before_action :authenticate_user!\n"
        end
      end

      private

      def file_name
        class_name.underscore
      end
    end
  end
end
