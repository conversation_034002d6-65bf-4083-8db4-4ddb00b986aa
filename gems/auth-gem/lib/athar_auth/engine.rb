# frozen_string_literal: true

require "rails/engine"

module AtharAuth
  class Engine < ::Rails::Engine
    isolate_namespace AtharAuth

    config.generators do |g|
      g.test_framework :rspec
      g.fixture_replacement :factory_bot
      g.assets false
      g.helper false
    end

    # Ensure controllers and serializers are loaded
    config.autoload_paths << File.expand_path("../../app/controllers", __dir__)
    config.autoload_paths << File.expand_path("../../app/serializers", __dir__)
  end
end
