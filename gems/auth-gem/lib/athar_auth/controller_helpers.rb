# frozen_string_literal: true

require "active_support/concern"
require_relative "token_compression"

module AtharAuth
  module ControllerHelpers
    extend ActiveSupport::Concern

    # included do
    #   before_action :authenticate_user!
    # end

    # Extract token from the Authorization header
    def bearer_token
      header = request.headers["Authorization"]
      header&.split("Bearer ")&.last
    end

    # Authenticate for API endpoints using the main token
    def authenticate_api_user!
      token = bearer_token
      return render_unauthorized("Authorization header missing") if token.blank?

      begin
        compressed_payload = AtharAuth.decode_token(token)

        # Expand compressed token to human-readable format
        payload = AtharAuth::TokenCompression.expand_token(compressed_payload)

        @current_user ||= extract_user_from_payload(payload)
      rescue AtharAuth::ExpiredTokenError, AtharAuth::Error => e
        render_unauthorized(e.message) # 401 for expired token
      rescue StandardError
        render_unauthorized("Authentication failed")
      end
    end

    # Authenticate for endpoints that require a session token (with a token_type check)
    def authenticate_session!
      token = bearer_token
      return render_unauthorized("Authorization header missing") if token.blank?

      begin
        compressed_payload = AtharAuth.decode_token(token)

        # Expand compressed token to human-readable format
        payload = AtharAuth::TokenCompression.expand_token(compressed_payload)

        return render_unauthorized("Invalid token type") unless payload["token_type"] == "session"

        @current_user ||= extract_user_from_payload(payload)
      rescue AtharAuth::ExpiredTokenError => e
        render_unauthorized(e.message)
      rescue AtharAuth::Error => e
        render_forbidden(e.message)
      rescue StandardError
        render_unauthorized("Authentication failed")
      end
    end

    # Provide a Devise-like current_user helper
    def current_user
      @current_user ||= extract_user_from_payload(decoded_token)
    end

    def user_signed_in?
      user = current_user
      !!(user && user.respond_to?(:id) && user.id)
    end

    # TODO: TO BE REMOVED, NO ALIASES NEEDED
    # Convenience methods for accessing expanded token data
    def current_permissions
      @current_permissions ||= decoded_token.dig('session', 'permissions') || []
    end

    def current_project_id
      @current_project_id ||= decoded_token.dig('session', 'project', 'id')
    end

    def current_project_name
      @current_project_name ||= decoded_token.dig('session', 'project', 'name')
    end

    def current_user_email
      @current_user_email ||= decoded_token.dig('user', 'email')
    end

    def global_user?
      @global_user ||= decoded_token.dig('user', 'global') || false
    end

    def organization_project?
      current_project_name == AtharAuth.organization_project_name
    end

    def global_user_in_organization?
      global_user? && organization_project?
    end

    # Single Entry Point: Decode and expand token to logical format
    # All client code receives expanded token data with logical paths
    def decoded_token
      @decoded_token ||= begin
        token = bearer_token
        raise AtharAuth::Error, "Missing token" if token.blank?

        # Decode compressed token and immediately expand to logical format
        compressed_token = AtharAuth.decode_token(token)
        TokenCompression.expand_token(compressed_token)
      end
    rescue AtharAuth::Error
      nil
    end

    private

    def render_unauthorized(message)
      render json: { error: message }, status: :unauthorized
    end

    def render_forbidden(message)
      render json: { error: message }, status: :forbidden
    end

    # Example: look up user in DB by ID from JWT
    def extract_user_from_payload(payload)
      return nil unless payload

      # Use the enhanced User model with token compression support
      user_klass = AtharAuth.user_class&.safe_constantize || AtharAuth::Models::User

      # Try the enhanced from_token_data method first
      if user_klass.respond_to?(:from_token_data)
        user = user_klass.from_token_data(payload)
        return user if user
      end

      # Fallback to basic user creation using AtharAuth::Models::User
      fallback_klass = AtharAuth.user_class&.safe_constantize || AtharAuth::Models::User

      # Extract data from expanded token payload
      user_id = payload.dig('user', 'id')
      user_name = payload.dig('user', 'name')
      user_email = payload.dig('user', 'email')
      payload.dig('user', 'global')
      session_permissions = payload.dig('session', 'permissions')
      session_role_name = payload.dig('session', 'role', 'name')

      fallback_klass.new(
        id: user_id&.to_i,
        email: user_email,
        name: user_name,
        role: session_role_name,
        permissions: session_permissions || []
      )
    end
  end
end
