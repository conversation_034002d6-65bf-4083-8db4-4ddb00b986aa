# frozen_string_literal: true

require "json"

module AtharAuth
  # TokenCompression module provides JSONPath-based token compression and decompression
  # using dot notation for semantic path mapping to compressed keys
  module TokenCompression
    # Pure JSONPath approach with dot notation - single source of truth
    SHORTHAND = {
      # Standard JWT claims
      'user.id' => 's',                    # sub -> s
      'issued.at' => 'i',                  # iat -> i
      'expires.at' => 'e',                 # exp -> e
      'token_type' => 't',                 # token_type -> t
      'scope' => 'sc',                     # scope -> sc (no nesting needed)

      # User context
      'user.name' => 'u.n',               # user.name -> u.n
      'user.email' => 'u.m',              # user.email -> u.m (m for mail)
      'user.global' => 'u.g',             # user.global -> u.g
      'user.type' => 'u.ty',              # user.type -> u.ty
      'user.status' => 'u.st',            # user.status -> u.st
      'user.phone' => 'u.ph',             # user.phone -> u.ph

      # Session context
      'session.role' => 'ss.r',           # session.role -> ss.r (full object)
      'session.role.name' => 'ss.r.n',    # session.role.name -> ss.r.n
      'session.role.level' => 'ss.r.l',   # session.role.level -> ss.r.l
      'session.role.scope' => 'ss.r.s',   # session.role.scope -> ss.r.s
      'session.permissions' => 'ss.p',    # session.permissions -> ss.p
      'session.project' => 'ss.pr',       # session.project -> ss.pr (full object)
      'session.project.id' => 'ss.pr.id', # session.project.id -> ss.pr.id
      'session.project.name' => 'ss.pr.n', # session.project.name -> ss.pr.n
      'session.project.description' => 'ss.pr.d', # session.project.description -> ss.pr.d
      'session.project.status' => 'ss.pr.st', # session.project.status -> ss.pr.st

      # Access context
      'access.type' => 'a.ty',            # access.type -> a.ty
      'access.projects' => 'a.prs',       # access.projects -> a.prs
      'access.other_projects' => 'a.op',  # access.other_projects -> a.op
      'access.capabilities' => 'a.cap',   # access.capabilities -> a.cap
      'access.restrictions' => 'a.res',   # access.restrictions -> a.res

      # System context
      'system.version' => 'sy.v',         # system.version -> sy.v
      'system.features' => 'sy.f',        # system.features -> sy.f
      'system.environment' => 'sy.env',   # system.environment -> sy.env

      # Security context
      'security.ip' => 'sec.ip',          # security.ip -> sec.ip
      'security.device' => 'sec.dev',     # security.device -> sec.dev
      'security.session_id' => 'sec.sid', # security.session_id -> sec.sid
      'security.mfa_verified' => 'sec.mfa', # security.mfa_verified -> sec.mfa

      # Common simple key mappings for nested objects
      'name' => 'n',                      # name -> n
      'description' => 'd',               # description -> d
      'status' => 'st',                   # status -> st
      'level' => 'l',                     # level -> l
      'email' => 'm',                     # email -> m (mail)
      'global' => 'g',                    # global -> g
      'role' => 'r',                      # role -> r (in context like other_projects)
      'type' => 'ty'                      # type -> ty
    }.freeze

    # Reverse mapping for decoding/debugging
    REVERSE_SHORTHAND = SHORTHAND.invert.freeze

    # Main method for token access using JSONPath
    # @param token [Hash] The compressed token
    # @param path_key [String, Symbol] The JSONPath key (e.g., 'user.name', 'session.role.level')
    # @return [Object, nil] The value at the path or nil if not found
    def self.dig_token(token, path_key)
      compressed_path = SHORTHAND[path_key.to_s]
      return nil unless compressed_path

      if compressed_path.include?(".")
        # JSONPath - split and dig
        token&.dig(*compressed_path.split("."))
      else
        # Simple key - direct access
        token&.dig(compressed_path)
      end
    end

    # Batch access method for multiple paths (performance optimization)
    # @param token [Hash] The compressed token
    # @param path_keys [Array<String>] Array of JSONPath keys
    # @return [Hash] Hash of path_key => value pairs
    def self.dig_token_batch(token, path_keys)
      result = {}
      path_keys.each do |path_key|
        result[path_key] = dig_token(token, path_key)
      end
      result
    end

    # Check if a path exists in the token (without returning the value)
    # @param token [Hash] The compressed token
    # @param path_key [String, Symbol] The JSONPath key
    # @return [Boolean] True if the path exists and has a non-nil value
    def self.path_exists?(token, path_key)
      !dig_token(token, path_key).nil?
    end

    # Helper method to compress a token using SHORTHAND mappings
    def self.compress_token(expanded_token)
      compressed = {}

      SHORTHAND.each do |expanded_path, compressed_path|
        # For nested paths, check if the value exists in the flat structure
        value = expanded_token[expanded_path]
        next if value.nil?

        set_compressed_value(compressed, compressed_path, value)
      end

      compressed
    end

    # Dig into expanded token using dot notation
    def self.dig_expanded_token(token, path)
      return token[path] unless path.include?(".")

      keys = path.split(".")
      token&.dig(*keys)
    end

    # Dig into compressed token using dot notation
    def self.dig_compressed_token(token, path)
      return token[path] unless path.include?(".")

      keys = path.split(".")
      token&.dig(*keys)
    end

    # Set value in compressed token using dot notation
    def self.set_compressed_value(token, path, value)
      if path.include?(".")
        keys = path.split(".")
        last_key = keys.pop
        nested = keys.reduce(token) { |hash, key| hash[key] ||= {} }
        nested[last_key] = value
      else
        token[path] = value
      end
    end

    # Set value in expanded token using dot notation
    def self.set_expanded_value(token, path, value)
      if path.include?(".")
        keys = path.split(".")
        last_key = keys.pop
        nested = keys.reduce(token) { |hash, key| hash[key] ||= {} }
        nested[last_key] = value
      else
        token[path] = value
      end
    end

    # Expand compressed token to logical format for client consumption
    # @param compressed_token [Hash] The compressed token
    # @return [Hash] Expanded token with logical paths
    def self.expand_token(compressed_token)
      return {} unless compressed_token.is_a?(Hash)

      expanded = {}

      # Build logical structure from compressed token using REVERSE_SHORTHAND
      REVERSE_SHORTHAND.each do |compressed_path, logical_path|
        # Extract value from compressed token using dot notation
        value = if compressed_path.include?('.')
                  # Nested path - split and dig
                  compressed_token.dig(*compressed_path.split('.'))
                else
                  # Simple key - direct access
                  compressed_token[compressed_path]
                end

        next unless value

        # Recursively expand nested objects
        expanded_value = if value.is_a?(Hash)
                           expand_nested_object(value)
                         elsif value.is_a?(Array)
                           value.map { |item| item.is_a?(Hash) ? expand_nested_object(item) : item }
                         else
                           value
                         end

        # Set value in logical structure using dot notation
        set_expanded_value(expanded, logical_path, expanded_value)
      end

      expanded
    end

    # Recursively expand nested objects that might contain compressed keys
    def self.expand_nested_object(obj)
      return obj unless obj.is_a?(Hash)

      expanded = {}
      obj.each do |key, value|
        # Check if this key has a logical mapping
        logical_key = REVERSE_SHORTHAND[key.to_s] || key

        # Recursively expand the value
        expanded_value = if value.is_a?(Hash)
                           expand_nested_object(value)
                         elsif value.is_a?(Array)
                           value.map { |item| item.is_a?(Hash) ? expand_nested_object(item) : item }
                         else
                           value
                         end

        expanded[logical_key] = expanded_value
      end
      expanded
    end
  end
end
