# frozen_string_literal: true

require "athar_commons"

module AtharAuth
  module Models
    class Role < Athar::Commons::ActiveStruct::Base
      # Disable auto-generated ID since we'll use role names as identifiers
      with_id(auto_generate: false, type: :integer)

      # Unified attributes from existing AtharPeople::Role + token enhancements
      attribute :name, :string
      attribute :level, :integer
      attribute :scope, :string # 'global_role' or 'project_based'
      # attribute :permissions, :permission_list, default: []

      # Factory method to create from expanded token data
      def self.from_token_data(role_data)
        return nil unless role_data

        new(
          name: role_data["name"],
          level: role_data["level"],
          scope: role_data["scope"],
          # permissions: [] # Permissions are stored separately in session context
        )
      end

      # Factory method for backward compatibility with AtharPeople
      def self.from_grpc_response(grpc_role)
        return nil unless grpc_role

        new(
          name: grpc_role.name,
          level: grpc_role.level,
          scope: grpc_role.global ? "global_role" : "project_based",
          permissions: []
        )
      end

      # Domain methods for role checking
      def global_role?
        scope == "global_role"
      end

      def project_based_role?
        scope == "project_based"
      end

      # Backward compatibility methods for People service
      def global
        global_role?
      end

      def global=(value)
        self.scope = value ? "global_role" : "project_based"
      end

      # Role hierarchy methods
      def higher_level_than?(other_role)
        return false unless other_role.is_a?(Role)

        level > other_role.level
      end

      def same_level_as?(other_role)
        return false unless other_role.is_a?(Role)

        level == other_role.level
      end

      def can_supervise?(other_role)
        return false unless other_role.is_a?(Role)

        # Can supervise if higher level or same level with global scope
        higher_level_than?(other_role) || (same_level_as?(other_role) && global_role?)
      end

      # Permission checking (if permissions are loaded)
      def has_permission?(permission)
        permissions.include?(permission.to_s)
      end

      # String representation
      def to_s
        name
      end

      def inspect
        "#<#{self.class.name} name=#{name.inspect} level=#{level} scope=#{scope.inspect}>"
      end

      # Comparison methods for sorting
      def <=>(other)
        return nil unless other.is_a?(Role)

        # Sort by level (descending), then by name
        comparison = other.level <=> level
        comparison.zero? ? name <=> other.name : comparison
      end

      # Equality based on name (roles are unique by name)
      def ==(other)
        other.is_a?(Role) && name == other.name
      end

      def eql?(other)
        self == other
      end

      def hash
        name.hash
      end
    end
  end
end
