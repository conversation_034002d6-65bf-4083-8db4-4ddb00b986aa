# frozen_string_literal: true

require "athar_commons"
require_relative "user_role"

module AtharAuth
  module Models
    class UserRoleCollection
      include Athar::Commons::ActiveStruct::Collection

      # Use unified UserRole model from auth gem
      collection_item_class AtharAuth::Models::UserRole

      # Alias methods for backward compatibility with People service
      alias user_role_ids ids
      alias user_role_ids= ids=

      # Factory method to create from gRPC user_roles_list
      # def self.from_grpc_list(grpc_user_roles_list)
      #   return new([]) unless grpc_user_roles_list

      #   user_roles = grpc_user_roles_list.map do |grpc_user_role|
      #     AtharAuth::Models::UserRole.from_grpc_response(grpc_user_role)
      #   end.compact

      #   new(user_roles)
      # end

      # Domain methods for filtering (only when actually needed)
      def global_roles
        select(&:global?)
      end

      def project_based_roles
        reject(&:global?)
      end

      # Alias for backward compatibility
      alias project_roles project_based_roles

      def default_roles
        select(&:default?)
      end

      def default_role
        find(&:default?)
      end

      def roles_for_project(project_id)
        select { |ur| ur.project&.id == project_id.to_i }
      end

      # Find methods for convenience
      def find_by_role_name(role_name)
        find { |user_role| user_role.role&.name == role_name.to_s }
      end

      def find_by_project_id(project_id)
        select { |user_role| user_role.project&.id == project_id.to_i }
      end

      def find_by_role_and_project(role_name, project_id)
        find do |user_role|
          user_role.role&.name == role_name.to_s &&
            user_role.project&.id == project_id.to_i
        end
      end

      # Role name extraction methods
      def role_names
        map { |ur| ur.role&.name }.uniq.compact
      end

      def project_ids
        map { |ur| ur.project&.id }.uniq.compact
      end

      # Validation methods
      def valid?
        all?(&:valid?)
      end

      def errors
        flat_map(&:errors).uniq
      end

      # Statistics methods
      def stats
        {
          total_roles: size,
          global_roles: global_roles.size,
          project_roles: project_based_roles.size,
          default_roles: default_roles.size,
          unique_role_names: role_names.size,
          unique_projects: project_ids.size
        }
      end

      # String representation
      def to_s
        if empty?
          "No user roles"
        else
          "#{size} user role(s): #{role_names.join(", ")}"
        end
      end

      def inspect
        "#<#{self.class.name} size=#{size} roles=#{role_names.inspect}>"
      end

      # Legacy find methods for backward compatibility
      def find_by_role_id(_role_id)
        # This method exists in People service but role_id concept doesn't apply here
        # since we use role_name. Return nil for compatibility.
        nil
      end
    end
  end
end
