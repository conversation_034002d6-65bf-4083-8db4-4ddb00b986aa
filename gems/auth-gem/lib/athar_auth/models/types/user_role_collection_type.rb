# frozen_string_literal: true

require "athar_commons"
require_relative "../user_role_collection"

module AtharAuth
  module Models
    module Types
      class UserRoleCollectionType < Athar::Commons::ActiveStruct::Type
        def initialize(precision: nil, limit: nil, scale: nil)
          super(AtharAuth::Models::UserRoleCollection, precision: precision, limit: limit, scale: scale)
        end
      end
    end
  end
end
