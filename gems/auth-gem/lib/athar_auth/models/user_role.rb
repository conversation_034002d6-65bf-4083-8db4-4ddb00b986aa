# frozen_string_literal: true

require "athar_commons"

module AtharAuth
  module Models
    class UserRole < Athar::Commons::ActiveStruct::Base
      # Disable auto-generated ID since we'll use IDs from Core service
      # Use integer type to match protobuf int64 fields
      with_id(auto_generate: false, type: :integer)

      # Unified attributes from existing AtharPeople::UserRole
      attribute :is_default, :boolean, default: false

      # Associations to unified models (provide project_id and role.name automatically)
      belongs_to :project, class_name: "AtharAuth::Models::Project", optional: true
      belongs_to :role, class_name: "AtharAuth::Models::Role", optional: true

      # Factory method to create from expanded token data
      def self.from_token_data(user_role_data)
        return nil unless user_role_data

        user_role = new(
          is_default: user_role_data["is_default"] || false
        )

        # Set associations using the expanded data
        if user_role_data["project"]
          user_role.project = AtharAuth::Models::Project.from_token_data(user_role_data["project"])
        end

        user_role.role = AtharAuth::Models::Role.from_token_data(user_role_data["role"]) if user_role_data["role"]

        user_role
      end

      # Factory method for backward compatibility with AtharPeople
      def self.from_grpc_response(grpc_user_role)
        return nil unless grpc_user_role

        user_role = new(
          is_default: grpc_user_role.is_default || false
        )

        # Set associations from gRPC data
        if grpc_user_role.project_id.present?
          user_role.project = AtharAuth::Models::Project.new(id: grpc_user_role.project_id)
        end

        if grpc_user_role.role_name.present?
          user_role.role = AtharAuth::Models::Role.new(name: grpc_user_role.role_name)
        end

        user_role
      end

      # Domain methods for user role checking
      def global?
        role&.global_role?
      end

      def project_based?
        project.present?
      end

      def default?
        is_default == true
      end

      # Compatibility method for People service
      def global
        global?
      end

      # String representation
      def to_s
        if project.present?
          "#{role&.name} in Project #{project.id}"
        else
          role&.name
        end
      end

      def inspect
        "#<#{self.class.name} role=#{role&.name.inspect} project_id=#{project&.id} default=#{is_default}>"
      end

      # Comparison methods for sorting
      def <=>(other)
        return nil unless other.is_a?(UserRole)

        # Sort by role name, then by project_id
        comparison = (role&.name || "") <=> (other.role&.name || "")
        return comparison unless comparison.zero?

        (project&.id || 0) <=> (other.project&.id || 0)
      end

      # Equality based on role.name and project.id combination
      def ==(other)
        other.is_a?(UserRole) &&
          role&.name == other.role&.name &&
          project&.id == other.project&.id
      end

      def eql?(other)
        self == other
      end

      def hash
        [role&.name, project&.id].hash
      end

      # Validation methods
      def valid?
        role.present? && role.name.present?
      end

      def errors
        errors = []
        errors << "Role is required" if role.blank?
        errors << "Role name is required" if role.present? && role.name.blank?
        errors << "Project is required for project-based roles" if project_based? && project.blank?
        errors
      end
    end
  end
end
