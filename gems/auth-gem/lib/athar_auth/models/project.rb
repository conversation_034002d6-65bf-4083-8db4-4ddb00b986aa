# frozen_string_literal: true

require "athar_commons"

module AtharAuth
  module Models
    class Project < Athar::Commons::ActiveStruct::Base
      # Disable auto-generated ID since we'll use project IDs from Core service
      # Use integer type to match protobuf int64 fields
      with_id(auto_generate: false, type: :integer)

      # Unified attributes from existing AtharPeople::Project + token enhancements
      attribute :name, :string
      attribute :description, :string
      attribute :status, :string

      # Factory method to create from expanded token data
      def self.from_token_data(project_data)
        return nil unless project_data

        new(
          id: project_data["id"],
          name: project_data["name"],
          description: project_data["description"],
          status: project_data["status"] || "active"
        )
      end

      # Factory method for backward compatibility with AtharPeople
      def self.from_grpc_response(grpc_project)
        return nil unless grpc_project

        new(
          id: grpc_project.id.to_i,
          name: grpc_project.name,
          description: grpc_project.description,
          status: grpc_project.status || "active"
        )
      end

      # Domain methods for project status
      def active?
        status == "active"
      end

      def inactive?
        status == "inactive"
      end

      # String representation
      def to_s
        name
      end

      def inspect
        "#<#{self.class.name} id=#{id} name=#{name.inspect} status=#{status.inspect}>"
      end

      # Comparison methods for sorting
      def <=>(other)
        return nil unless other.is_a?(Project)

        # Sort by name
        name <=> other.name
      end

      # Equality based on id (projects are unique by id)
      def ==(other)
        other.is_a?(Project) && id == other.id
      end

      def eql?(other)
        self == other
      end

      def hash
        id.hash
      end
    end
  end
end
