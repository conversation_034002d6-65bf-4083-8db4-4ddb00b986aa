# frozen_string_literal: true

require "athar_commons"

module AtharAuth
  module Models
    class User < Athar::Commons::ActiveStruct::Base
      # Disable auto-generated ID since we'll use user IDs from Core service
      with_id(auto_generate: false, type: :integer)

      # Core user attributes (from token payload)
      attribute :name, :string
      attribute :email, :string
      attribute :global, :boolean, default: false

      # Token context attributes (for service-specific usage)
      attribute :user_type, :string
      attribute :scope, :string
      attribute :permissions, :permission_list, default: []

      # ActiveStruct associations - return rich objects with proper foreign keys
      belongs_to :role, class_name: "AtharAuth::Models::Role", optional: true
      belongs_to :project, class_name: "AtharAuth::Models::Project", optional: true

      # Factory method to create from expanded token data with rich associations
      def self.from_token_data(decoded_token)
        return nil unless decoded_token

        # Check if this is the old format (has 'sub' key) vs expanded format (has 'user' key)
        if decoded_token.key?("sub")
          # Old format - return nil to let fallback handle it
          return nil
        end

        # Extract user data from expanded format
        user_data = decoded_token["user"] || {}
        session_data = decoded_token["session"] || {}

        # Create role instance from token data
        role_instance = (AtharAuth::Models::Role.from_token_data(session_data["role"]) if session_data["role"])

        # Create project instance from token data
        project_instance = if session_data["project"]
                             AtharAuth::Models::Project.from_token_data(session_data["project"])
                           end

        # Create user with rich associations
        new(
          id: user_data["id"],
          name: user_data["name"],
          email: user_data["email"],
          global: user_data["global"] || false,
          scope: decoded_token["scope"],
          permissions: session_data["permissions"] || [],
          role: role_instance,
          project: project_instance
        )
      end

      # Domain methods for user type checking (using role association)
      def global_user?
        global || role&.global_role?
      end

      def project_based_user?
        !global_user?
      end

      # Role checking methods (using role association)
      def case_manager?
        role&.name == "case_manager"
      end

      def supervisor?
        role&.name == "supervisor"
      end

      def hr_manager?
        role&.name == "hr_manager"
      end

      def admin?
        role&.name == "admin"
      end

      def super_admin?
        role&.name == "super_admin"
      end

      # Permission checking
      def can?(permission)
        permissions.include?(permission.to_s)
      end

      def has_permission?(permission)
        can?(permission)
      end

      # Project access methods
      def accessible_project_ids
        if global_user?
          # Global users would need to fetch all project IDs
          # This is a placeholder - implement based on business logic
          []
        else
          [project&.id].compact.map(&:to_i)
        end
      end

      def can_access_project?(project_id)
        return true if global_user?

        target_id = project_id.to_i
        accessible_project_ids.include?(target_id)
      end

      # String representation
      def to_s
        name || email || "User ##{id}"
      end

      def inspect
        "#<#{self.class.name} id=#{id} name=#{name.inspect} email=#{email.inspect} role=#{role&.name}>"
      end

      # Comparison methods
      def <=>(other)
        return nil unless other.is_a?(User)

        # Sort by name, then email, then id
        comparison = (name || "") <=> (other.name || "")
        return comparison unless comparison.zero?

        comparison = (email || "") <=> (other.email || "")
        return comparison unless comparison.zero?

        id <=> other.id
      end

      # Equality based on id (users are unique by id)
      def ==(other)
        other.is_a?(User) && id == other.id
      end

      def eql?(other)
        self == other
      end

      delegate :hash, to: :id
    end
  end
end
