# frozen_string_literal: true

module AtharAuth
  module Types
    class PermissionList < ActiveModel::Type::Value
      # Casts a value from user input or the database to a Ruby Array.
      def cast(value)
        return [] if value.nil?

        case value
        when String
          value.split(',').map(&:strip)
        when Array
          value
        else
          []
        end
      end

      # Serializes a Ruby Array into a string for database storage.
      def serialize(value)
        return if value.nil?
        value.is_a?(Array) ? value.join(',') : value.to_s
      end

      # Optionally, if you need to transform data when reading from the database,
      # you can override deserialize as well.
      def deserialize(value)
        cast(value)
      end
    end
  end
end


# Register the custom type so it can be referenced as :permission_list
ActiveModel::Type.register(:permission_list, AtharAuth::Types::PermissionList)
