PATH
  remote: .
  specs:
    athar_auth (2.0.1)
      actionpack
      activesupport
      athar_commons
      devise
      jsonpath (~> 1.1)
      jwt (~> 2.0)
      ostruct
      rails (>= 5.0)
      railties

GEM
  remote: https://rubygems.org/
  specs:
    actioncable (8.0.1)
      actionpack (= 8.0.1)
      activesupport (= 8.0.1)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (8.0.1)
      actionpack (= 8.0.1)
      activejob (= 8.0.1)
      activerecord (= 8.0.1)
      activestorage (= 8.0.1)
      activesupport (= 8.0.1)
      mail (>= 2.8.0)
    actionmailer (8.0.1)
      actionpack (= 8.0.1)
      actionview (= 8.0.1)
      activejob (= 8.0.1)
      activesupport (= 8.0.1)
      mail (>= 2.8.0)
      rails-dom-testing (~> 2.2)
    actionpack (8.0.1)
      actionview (= 8.0.1)
      activesupport (= 8.0.1)
      nokogiri (>= 1.8.5)
      rack (>= 2.2.4)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
      useragent (~> 0.16)
    actiontext (8.0.1)
      actionpack (= 8.0.1)
      activerecord (= 8.0.1)
      activestorage (= 8.0.1)
      activesupport (= 8.0.1)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (8.0.1)
      activesupport (= 8.0.1)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    activejob (8.0.1)
      activesupport (= 8.0.1)
      globalid (>= 0.3.6)
    activemodel (8.0.1)
      activesupport (= 8.0.1)
    activerecord (8.0.1)
      activemodel (= 8.0.1)
      activesupport (= 8.0.1)
      timeout (>= 0.4.0)
    activestorage (8.0.1)
      actionpack (= 8.0.1)
      activejob (= 8.0.1)
      activerecord (= 8.0.1)
      activesupport (= 8.0.1)
      marcel (~> 1.0)
    activesupport (8.0.1)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
      uri (>= 0.13.1)
    apipie-rails (1.4.2)
      actionpack (>= 5.0)
      activesupport (>= 5.0)
    ast (2.4.2)
    athar_commons (0.3.4)
      apipie-rails
      athar_rpc
      caxlsx
      csv
      jsonapi-serializer
      jsonapi.rb
      maruku
      pagy
      prawn
      prawn-table
      rails (>= 7.0)
      ransack
      rexml
    athar_rpc (0.3.11)
      activesupport (>= 6.0)
      dry-configurable (~> 1.3.0)
      gruf (~> 2.21)
      singleton
    base64 (0.2.0)
    bcrypt (3.1.20)
    benchmark (0.4.0)
    bigdecimal (3.1.9)
    builder (3.3.0)
    caxlsx (4.2.0)
      htmlentities (~> 4.3, >= 4.3.4)
      marcel (~> 1.0)
      nokogiri (~> 1.10, >= 1.10.4)
      rubyzip (>= 1.3.0, < 3)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.0)
    crass (1.0.6)
    csv (3.3.2)
    date (3.4.1)
    devise (4.9.4)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    diff-lcs (1.6.0)
    drb (2.2.1)
    dry-configurable (1.3.0)
      dry-core (~> 1.1)
      zeitwerk (~> 2.6)
    dry-core (1.1.0)
      concurrent-ruby (~> 1.0)
      logger
      zeitwerk (~> 2.6)
    erubi (1.13.1)
    ffi (1.17.1)
    ffi (1.17.1-arm64-darwin)
    fileutils (1.7.3)
    globalid (1.2.1)
      activesupport (>= 6.1)
    google-protobuf (4.31.1)
      bigdecimal
      rake (>= 13)
    google-protobuf (4.31.1-arm64-darwin)
      bigdecimal
      rake (>= 13)
    googleapis-common-protos-types (1.20.0)
      google-protobuf (>= 3.18, < 5.a)
    grpc (1.73.0)
      google-protobuf (>= 3.25, < 5.0)
      googleapis-common-protos-types (~> 1.0)
    grpc (1.73.0-arm64-darwin)
      google-protobuf (>= 3.25, < 5.0)
      googleapis-common-protos-types (~> 1.0)
    grpc-tools (1.73.0)
    gruf (2.21.1)
      activesupport (> 4)
      concurrent-ruby (> 1)
      grpc (~> 1.10)
      grpc-tools (~> 1.10)
      json (>= 2.3)
      slop (>= 4.6)
      zeitwerk (>= 2)
    htmlentities (4.3.4)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    io-console (0.8.0)
    irb (1.15.1)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    json (2.10.1)
    jsonapi-serializer (2.2.0)
      activesupport (>= 4.2)
    jsonapi.rb (2.1.1)
      jsonapi-serializer
      rack
    jsonpath (1.1.5)
      multi_json
    jwt (2.10.1)
      base64
    language_server-protocol (3.17.0.4)
    lint_roller (1.1.0)
    listen (3.9.0)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    logger (1.6.6)
    loofah (2.24.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    maruku (0.7.3)
    matrix (0.4.3)
    mini_mime (1.1.5)
    mini_portile2 (2.8.9)
    minitest (5.25.4)
    multi_json (1.16.0)
    net-imap (0.5.6)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.1)
      net-protocol
    nio4r (2.7.4)
    nokogiri (1.18.3)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    nokogiri (1.18.3-arm64-darwin)
      racc (~> 1.4)
    orm_adapter (0.5.0)
    ostruct (0.6.1)
    pagy (9.3.5)
    parallel (1.26.3)
    parser (3.3.7.1)
      ast (~> 2.4.1)
      racc
    pdf-core (0.10.0)
    pp (0.6.2)
      prettyprint
    prawn (2.5.0)
      matrix (~> 0.4)
      pdf-core (~> 0.10.0)
      ttfunk (~> 1.8)
    prawn-table (0.2.2)
      prawn (>= 1.3.0, < 3.0.0)
    prettyprint (0.2.0)
    prism (1.4.0)
    psych (5.2.3)
      date
      stringio
    racc (1.8.1)
    rack (3.1.10)
    rack-session (2.1.0)
      base64 (>= 0.1.0)
      rack (>= 3.0.0)
    rack-test (2.2.0)
      rack (>= 1.3)
    rackup (2.2.1)
      rack (>= 3)
    rails (8.0.1)
      actioncable (= 8.0.1)
      actionmailbox (= 8.0.1)
      actionmailer (= 8.0.1)
      actionpack (= 8.0.1)
      actiontext (= 8.0.1)
      actionview (= 8.0.1)
      activejob (= 8.0.1)
      activemodel (= 8.0.1)
      activerecord (= 8.0.1)
      activestorage (= 8.0.1)
      activesupport (= 8.0.1)
      bundler (>= 1.15.0)
      railties (= 8.0.1)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    railties (8.0.1)
      actionpack (= 8.0.1)
      activesupport (= 8.0.1)
      irb (~> 1.13)
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (13.2.1)
    ransack (4.3.0)
      activerecord (>= 6.1.5)
      activesupport (>= 6.1.5)
      i18n
    rb-fsevent (0.11.2)
    rb-inotify (0.11.1)
      ffi (~> 1.0)
    rbs (3.8.1)
      logger
    rdoc (6.12.0)
      psych (>= 4.0.0)
    regexp_parser (2.10.0)
    reline (0.6.0)
      io-console (~> 0.5)
    responders (3.1.1)
      actionpack (>= 5.2)
      railties (>= 5.2)
    rexml (3.4.1)
    rspec (3.13.0)
      rspec-core (~> 3.13.0)
      rspec-expectations (~> 3.13.0)
      rspec-mocks (~> 3.13.0)
    rspec-core (3.13.3)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.3)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.2)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-support (3.13.2)
    rubocop (1.73.1)
      json (~> 2.3)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= *******)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.38.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.38.1)
      parser (>= *******)
    ruby-lsp (0.24.2)
      language_server-protocol (~> 3.17.0)
      prism (>= 1.2, < 2.0)
      rbs (>= 3, < 5)
      sorbet-runtime (>= 0.5.10782)
    ruby-lsp-rspec (0.1.25)
      ruby-lsp (~> 0.24.0)
    ruby-progressbar (1.13.0)
    rubyzip (2.4.1)
    securerandom (0.4.1)
    singleton (0.3.0)
    slop (4.10.1)
    sorbet-runtime (0.5.12225)
    sqlite3 (2.7.2)
      mini_portile2 (~> 2.8.0)
    sqlite3 (2.7.2-arm64-darwin)
    steep (1.9.4)
      activesupport (>= 5.1)
      concurrent-ruby (>= 1.1.10)
      csv (>= 3.0.9)
      fileutils (>= 1.1.0)
      json (>= 2.1.0)
      language_server-protocol (>= 3.15, < 4.0)
      listen (~> 3.0)
      logger (>= 1.3.0)
      parser (>= 3.1)
      rainbow (>= 2.2.2, < 4.0)
      rbs (~> 3.8)
      securerandom (>= 0.1)
      strscan (>= 1.0.0)
      terminal-table (>= 2, < 4)
      uri (>= 0.12.0)
    stringio (3.1.5)
    strscan (3.1.2)
    terminal-table (3.0.2)
      unicode-display_width (>= 1.1.1, < 3)
    thor (1.3.2)
    timeout (0.4.3)
    ttfunk (1.8.0)
      bigdecimal (~> 3.1)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unicode-display_width (2.6.0)
    uri (1.0.2)
    useragent (0.16.11)
    warden (1.2.9)
      rack (>= 2.0.9)
    websocket-driver (0.7.7)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    zeitwerk (2.7.2)

PLATFORMS
  arm64-darwin-24
  ruby

DEPENDENCIES
  athar_auth!
  irb
  rake (~> 13.0)
  rspec (~> 3.0)
  rubocop (~> 1.21)
  ruby-lsp-rspec
  sqlite3 (>= 2.1)
  steep

BUNDLED WITH
   2.6.5
