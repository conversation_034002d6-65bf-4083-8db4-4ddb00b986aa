# frozen_string_literal: true

# Example controller showing the new secure-by-default authorization features

class Api::FormTemplatesController < ApplicationController
  include AtharAuth::ResourceAuthorization

  before_action :authenticate_session!

  # NEW: Secure by default - authorizes ALL actions
  # authorize_resources

  # OR: Authorize all actions except specific ones
  # authorize_resources except: [:health, :status]

  # OR: Existing behavior - explicit action list
  authorize_resources only: [:index, :show, :validate_data]
  
  # OPTION 1: Simple permission mapping
  # Maps render_data action to use :read permission instead of :render_data
  map_action_permission :render_data, :read
  
  # OPTION 2: Custom authorization logic with lambda/proc
  # For complex authorization scenarios
  map_action_permission :advanced_action, -> { 
    can?(:read, :form_template) && 
    current_user.admin? && 
    params[:special_flag] == 'true' 
  }
  
  # OPTION 3: Collection action with custom permission
  map_action_permission :export_templates, :manage  # Maps to manage:form_template
  
  def index
    # @form_templates automatically loaded and authorized with :read permission
    render json: @form_templates
  end

  def show
    # @form_template automatically loaded and authorized with :read permission
    render json: @form_template
  end

  def render_data
    # @form_template automatically loaded and authorized with :read permission (not :render_data)
    # This solves the original problem where render_data would check for render_data:form_template
    # but the user only has read:form_template permission
    
    case_context = nil
    if params[:case_id].present?
      case_context = Case.find(params[:case_id])
    end

    render json: {
      form_template: @form_template.as_json(include: {
        form_sections: {
          include: {
            form_fields: {
              include: [:form_field_options, :form_field_validations]
            }
          }
        }
      }),
      existing_submission: case_context&.form_submissions&.find_by(form_template: @form_template),
      case_context: case_context&.approval_context,
      prerequisites_met: case_context ? @form_template.prerequisites_met?(case_context) : true,
      can_start: case_context ? case_context.can_start_form?(@form_template) : true
    }
  end

  def advanced_action
    # @form_template automatically loaded after custom authorization passes
    # Custom authorization checks:
    # 1. User has read:form_template permission
    # 2. User is an admin
    # 3. Special flag is set in params
    
    render json: { 
      message: "Advanced action authorized!", 
      form_template: @form_template,
      user: current_user.name 
    }
  end

  def export_templates
    # @form_templates automatically loaded and authorized with :manage permission
    # This would check for manage:form_template permission
    
    render json: {
      templates: @form_templates,
      export_timestamp: Time.current,
      exported_by: current_user.name
    }
  end

  def validate_data
    # @form_template automatically loaded and authorized with :read permission (standard behavior)
    form_data = params[:form_data] || {}
    validation_result = @form_template.validate_form_data(form_data)
    
    render json: {
      form_template_id: @form_template.id,
      is_valid: validation_result[:is_valid],
      completion_percentage: validation_result[:completion_percentage],
      validation_errors: validation_result[:errors],
      missing_required_fields: validation_result[:missing_required],
      field_validations: validation_result[:field_validations]
    }
  end

  private

  def some_custom_condition?
    # Example custom condition
    Time.current.hour.between?(9, 17) # Only allow during business hours
  end
end

# Benefits of map_action_permission:

# 1. **Flexible Permission Mapping**: Map actions to different permissions
#    - render_data uses :read instead of :render_data
#    - export_templates uses :manage instead of :export_templates

# 2. **Custom Authorization Logic**: Use procs for complex authorization
#    - Multiple conditions can be combined
#    - Access to all controller methods (can?, current_user, params, etc.)

# 3. **Automatic Resource Loading**: Resources are still loaded automatically
#    - @form_template or @form_templates set as usual
#    - Project filtering and other security measures still apply

# 4. **Backward Compatibility**: Existing authorize_resources behavior unchanged
#    - Standard actions work exactly as before
#    - Only mapped actions use custom logic

# 5. **Automatic Callback Setup**: No need to manually add before_actions
#    - Actions with mappings automatically get authorization callbacks
#    - Can be called before or after authorize_resources

# Usage patterns:

# Simple permission mapping:
# map_action_permission :action_name, :permission_name

# Custom authorization with proc:
# map_action_permission :action_name, -> { custom_authorization_logic }

# The proc has access to all controller methods:
# - can?(permission, subject)
# - current_user
# - params
# - Any custom controller methods

# =============================================================================
# NEW SECURE-BY-DEFAULT AUTHORIZATION PATTERNS
# =============================================================================

# Pattern 1: Secure by default (NEW - recommended for new controllers)
class Api::SecureController < ApplicationController
  include AtharAuth::ResourceAuthorization

  authorize_resources  # Authorizes ALL actions automatically

  def index; end
  def show; end
  def custom_action; end  # This gets authorized too!
end

# Pattern 2: Exclude specific actions (NEW - for health checks, etc.)
class Api::HealthController < ApplicationController
  include AtharAuth::ResourceAuthorization

  authorize_resources except: [:ping, :status, :health]

  def index; end        # Authorized
  def show; end         # Authorized
  def ping; end         # NOT authorized (public endpoint)
  def status; end       # NOT authorized (public endpoint)
  def health; end       # NOT authorized (public endpoint)
end

# Pattern 3: Explicit actions (EXISTING - unchanged for backward compatibility)
class Api::LegacyController < ApplicationController
  include AtharAuth::ResourceAuthorization

  authorize_resources only: [:index, :show, :create]

  def index; end        # Authorized
  def show; end         # Authorized
  def create; end       # Authorized
  def custom; end       # NOT authorized (not in list)
end

# Pattern 4: Combined (EXISTING - unchanged)
class Api::CombinedController < ApplicationController
  include AtharAuth::ResourceAuthorization

  authorize_resources only: [:index, :show, :create], except: [:show]

  def index; end        # Authorized
  def show; end         # NOT authorized (excluded)
  def create; end       # Authorized
  def custom; end       # NOT authorized (not in list)
end

# =============================================================================
# MIGRATION GUIDE
# =============================================================================

# OLD (less secure):
# authorize_resources only: [:index, :show, :create, :update, :destroy, :custom1, :custom2, ...]

# NEW (secure by default):
# authorize_resources  # All actions authorized automatically

# OR if you have public endpoints:
# authorize_resources except: [:health, :ping, :status]
