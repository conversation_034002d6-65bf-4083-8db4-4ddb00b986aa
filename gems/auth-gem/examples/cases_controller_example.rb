# frozen_string_literal: true

# Example controller showing how to use authorize_resources method
# This demonstrates the method call style authorization with automatic collection preparation

class Api::CasesController < ApplicationController
  include AtharAuth::Authorization

  # GET /api/cases
  # Automatically authorizes and prepares @cases collection with project filtering
  def index
    authorize_resources(:index, Case)

    # @cases is now available and contains only cases the user can access
    # For project-based users: filtered by accessible_project_ids
    # For global users: all cases (or custom scope if Case.accessible_to exists)

    render json: { cases: CaseSerializer.new(@cases).serializable_hash }
  end

  # GET /api/cases/:id
  # Automatically authorizes and prepares @case resource with project validation
  def show
    authorize_resources(:show, Case)

    # @case is now available and validated for project access
    # Will raise 404 if case doesn't exist in accessible projects

    render json: { case: CaseSerializer.new(@case).serializable_hash }
  end

  # POST /api/cases
  # Automatically authorizes and prepares new @case with current project
  def create
    authorize_resources(:create, Case)

    # @case is now available as new Case instance with project_id set
    # For project-based users: project_id = current_project.id
    # For global users: project_id = nil (or from params)

    if @case.update(case_params)
      render json: { case: CaseSerializer.new(@case).serializable_hash }, status: :created
    else
      render json: { errors: @case.errors }, status: :unprocessable_entity
    end
  end

  # PUT /api/cases/:id
  # Automatically authorizes and prepares @case for update with project validation
  def update
    authorize_resources(:update, Case)

    # @case is loaded and validated for project access

    if @case.update(case_params)
      render json: { case: CaseSerializer.new(@case).serializable_hash }
    else
      render json: { errors: @case.errors }, status: :unprocessable_entity
    end
  end

  # DELETE /api/cases/:id
  # Automatically authorizes and prepares @case for deletion with project validation
  def destroy
    authorize_resources(:destroy, Case)

    # @case is loaded and validated for project access

    @case.destroy
    head :no_content
  end

  private

  def case_params
    params.require(:case).permit(:title, :description, :status, :beneficiary_id, :case_manager_id)
  end
end

# Alternative usage examples:

class Api::AdvancedCasesController < ApplicationController
  include AtharAuth::Authorization

  # Custom collection name
  def index
    authorize_resources(:index, Case, collection_name: "@my_cases")

    # @my_cases is available instead of @cases
    render json: { cases: CaseSerializer.new(@my_cases).serializable_hash }
  end

  # Custom resource name
  def show
    authorize_resources(:show, Case, resource_name: "@current_case")

    # @current_case is available instead of @case
    render json: { case: CaseSerializer.new(@current_case).serializable_hash }
  end

  # With additional scoping
  def active_cases
    authorize_resources(:index, Case, scope: { status: "active" })

    # @cases contains only active cases within accessible projects
    render json: { cases: CaseSerializer.new(@cases).serializable_hash }
  end

  # With proc-based scoping
  def recent_cases
    scope_proc = ->(scope) { scope.where("created_at > ?", 1.week.ago).order(created_at: :desc) }
    authorize_resources(:index, Case, scope: scope_proc)

    # @cases contains recent cases within accessible projects, ordered by creation date
    render json: { cases: CaseSerializer.new(@cases).serializable_hash }
  end

  # Using existing resource (e.g., from nested routes)
  def show_nested
    # Find case through parent resource
    beneficiary = Beneficiary.find(params[:beneficiary_id])
    case_resource = beneficiary.cases.find(params[:id])

    authorize_resources(:show, Case, resource: case_resource)

    # @case is the pre-found resource, validated for authorization
    render json: { case: CaseSerializer.new(@case).serializable_hash }
  end
end

# Example with custom accessible_to scope in the model:

class Case < ApplicationRecord
  belongs_to :project
  belongs_to :beneficiary
  belongs_to :case_manager, class_name: "User"

  # Custom scope for authorize_resources to use
  scope :accessible_to, lambda { |user|
    if user.global_user?
      # Global users see all cases
      all
    else
      # Project-based users see cases in their accessible projects
      where(project_id: user.accessible_project_ids)
    end
  }

  # Project-based filtering is automatically applied because:
  # 1. Case has project_id column
  # 2. User is not global
  # 3. authorize_resources detects this and applies filtering
end

# Benefits of authorize_resources method:

# 1. **Automatic Authorization**: Checks permissions before proceeding
# 2. **Automatic Collection Preparation**: Sets up @cases, @case variables
# 3. **Project-Based Filtering**: Automatically filters by accessible projects
# 4. **Consistent Error Handling**: Standard 401/403 responses
# 5. **Flexible Scoping**: Support for additional filtering conditions
# 6. **Resource Detection**: Handles index vs show vs create actions appropriately
# 7. **Custom Naming**: Override default instance variable names
# 8. **Performance**: Only loads resources user can actually access

# Usage patterns:

# Basic usage (most common):
# authorize_resources(:index, Case)    # Sets @cases with project filtering
# authorize_resources(:show, Case)     # Sets @case, validates project access
# authorize_resources(:create, Case)   # Sets @case as new instance with project_id

# Advanced usage:
# authorize_resources(:index, Case, collection_name: "@my_cases")
# authorize_resources(:show, Case, resource: existing_case)
# authorize_resources(:index, Case, scope: { status: "active" })
# authorize_resources(:index, Case, scope: ->(s) { s.includes(:beneficiary) })

# The method replaces this common pattern:
# def index
#   return render_unauthorized unless user_signed_in?
#   return render_forbidden unless can?(:index, :case)
#
#   @cases = Case.all
#   @cases = @cases.where(project_id: accessible_project_ids) unless global_user?
#   @cases = @cases.where(status: "active") # additional filtering
#
#   render json: { cases: CaseSerializer.new(@cases).serializable_hash }
# end

# With just:
# def index
#   authorize_resources(:index, Case, scope: { status: "active" })
#   render json: { cases: CaseSerializer.new(@cases).serializable_hash }
# end
