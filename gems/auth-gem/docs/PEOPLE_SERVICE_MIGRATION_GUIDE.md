# People Service Migration Guide

## Overview

This guide provides step-by-step instructions for migrating AtharPeople models from the People service to the auth gem. The migration maintains **100% backward compatibility** while enabling enhanced authorization features.

## Migration Steps

### Phase 1: Update People Service Dependencies

#### 1. Update Gemfile

```ruby
# services/people/Gemfile
gem "athar_auth", "2.0.2"  # Update to version with AtharPeople models
```

#### 2. Update Type Registrations

```ruby
# services/people/config/initializers/active_model_types.rb
Rails.application.config.to_prepare do
  require Rails.root.join('app/models/types/avatar_attributes_type')

  # ✅ NEW: Use auth gem models instead of local models
  require 'athar_auth/models/types/user_role_collection_type'
  require 'athar_auth/models/types/project_type'
  require 'athar_auth/models/types/role_type'

  # Register the types with auth gem classes
  ActiveModel::Type.register(:avatar_attributes_type, Types::AvatarAttributesType)
  ActiveModel::Type.register(:user_role_collection, AtharAuth::Models::Types::UserRoleCollectionType)
  ActiveModel::Type.register(:project, AtharAuth::Models::Types::ProjectType)
  ActiveModel::Type.register(:role, AtharAuth::Models::Types::RoleType)
  ActiveModel::Type.register(:metric_card, Statistics::Types::MetricCardType)

  ActiveRecord::Type.register(:avatar_attributes_type, Types::AvatarAttributesType)
  ActiveRecord::Type.register(:user_role_collection, AtharAuth::Models::Types::UserRoleCollectionType)
  ActiveRecord::Type.register(:project, AtharAuth::Models::Types::ProjectType)
  ActiveRecord::Type.register(:role, AtharAuth::Models::Types::RoleType)
  ActiveRecord::Type.register(:metric_card, Statistics::Types::MetricCardType)
end
```

#### 3. Update UserRoleCollection Reference

```ruby
# services/people/app/models/employees/user_role_collection.rb

# ❌ OLD: Local reference
# class UserRoleCollection
#   include Athar::Commons::ActiveStruct::Collection
#   collection_item_class UserRole  # ← This will break

# ✅ NEW: Use auth gem models
class UserRoleCollection < AtharAuth::Models::UserRoleCollection
  # Inherits all functionality from auth gem
  # Add any People-service-specific methods here if needed
end
```

#### 4. Update Employee Model ActiveRpc Configuration

```ruby
# services/people/app/models/employee.rb
class Employee < ApplicationRecord
  include ActiveRpc

  # ✅ ActiveRpc configuration remains the same - types are handled by registrations
  active_rpc :core, :User, foreign_key: :user_id do
    attribute :name, :string, default: "Local User"
    attribute :email, :string, default: "<EMAIL>"
    attribute :phone, :string, default: "+962000000000"
    attribute :password, :string, default: DEFAULT_PASSWORD
    attribute :user_roles_list, :user_role_collection, default: []  # ← Type registration handles this
    attribute :avatar_attributes, :avatar_attributes_type, default: {}

    query_config(
      searchable: [:name, :email, :status],
      filterable: [:name, :email, :status],
      sortable: [:name, :email, :status],
      includable: [:user_roles, :avatar_attributes]
    )
  end

  # ✅ Associations remain the same
  has_many :projects, through: :user_roles_list
  has_many :roles, through: :user_roles_list
end
```

### Phase 2: Remove Local Model Files

#### 1. Remove Local Model Files (After Testing)

```bash
# ❌ Remove these files after confirming migration works:
rm services/people/app/models/employees/project.rb
rm services/people/app/models/employees/role.rb
rm services/people/app/models/employees/user_role.rb
# Keep user_role_collection.rb as inheritance wrapper

# ❌ Remove local type files:
rm services/people/app/models/employees/types/project_type.rb
rm services/people/app/models/employees/types/role_type.rb
rm services/people/app/models/employees/types/user_role_collection_type.rb
```

#### 2. Update Imports (If Any)

```ruby
# ❌ OLD: Local imports
# require_relative 'employees/project'
# require_relative 'employees/role'
# require_relative 'employees/user_role'

# ✅ NEW: Auth gem imports (automatic via gem loading)
# No explicit requires needed - handled by gem
```

### Phase 3: Testing and Validation

#### 1. Test Employee Model Loading

```ruby
# Test in Rails console
employee = Employee.first
puts employee.user_roles_list.class
# Should output: Employees::UserRoleCollection (inheriting from AtharAuth::Models::UserRoleCollection)

puts employee.user_roles_list.first.class
# Should output: AtharAuth::Models::UserRole
```

#### 2. Test Role/Project Operations

```ruby
# Test role operations
role = employee.user_roles_list.first.role
puts role.class
# Should output: AtharAuth::Models::Role

puts role.global_role?
puts role.global  # Backward compatibility method

# Test project operations
project = employee.user_roles_list.first.project
puts project.class
# Should output: AtharAuth::Models::Project

puts project.active?
```

#### 3. Test Frontend Integration

```bash
# Test role/project forms still work
# Test employee profile pages
# Test role/project assignment functionality
```

## Backward Compatibility Features

### 1. Role Model Compatibility

```ruby
# ✅ Both old and new syntax work:
role.global          # Old People service syntax
role.global_role?    # New auth gem syntax

role.global = true   # Old People service syntax
role.scope = 'global_role'  # New auth gem syntax
```

### 2. UserRoleCollection Compatibility

```ruby
# ✅ All old methods still work:
collection.user_role_ids
collection.user_role_ids = [1, 2, 3]
collection.project_roles  # Alias for project_based_roles
collection.find_by_role_id(123)  # Returns nil for compatibility
```

### 3. UserRole Compatibility

```ruby
# ✅ All old methods still work:
user_role.global?
user_role.project_based?
user_role.default?
```

## Rollback Plan

If issues arise, rollback is straightforward:

#### 1. Revert Gemfile

```ruby
# Revert to previous athar_auth version
gem "athar_auth", "2.0.2"
```

#### 2. Revert Type Registrations

```ruby
# Restore original type registrations
ActiveModel::Type.register(:project, Employees::Types::ProjectType)
ActiveModel::Type.register(:role, Employees::Types::RoleType)
ActiveModel::Type.register(:user_role_collection, Employees::Types::UserRoleCollectionType)
```

#### 3. Restore Local Model Files

```bash
# Restore from git if needed
git checkout HEAD~1 -- services/people/app/models/employees/
```

## Benefits After Migration

### 1. Enhanced Authorization

- Rich object models with domain methods
- Compressed token integration
- Project-based authorization support

### 2. Consistency Across Services

- Same models used in all services
- Consistent behavior and methods
- Centralized maintenance

### 3. Future-Proof Architecture

- Ready for enhanced authorization features
- Supports compressed tokens
- Enables cross-service consistency

## Validation Checklist

- [ ] Employee model loads without errors
- [ ] user_roles_list returns UserRoleCollection
- [ ] Role operations work (global?, project_based?)
- [ ] Project operations work (active?, inactive?)
- [ ] Frontend role/project forms work
- [ ] Employee profile pages load correctly
- [ ] Role/project assignment works
- [ ] No breaking changes in API responses
- [ ] Performance remains acceptable

## Support

If you encounter issues during migration:

1. Check the migration impact assessment: `MIGRATION_IMPACT_ASSESSMENT.md`
2. Review test results: `test_athar_people_models.rb`
3. Verify type registrations are correct
4. Ensure auth gem version is compatible
5. Test in development environment first

The migration maintains 100% backward compatibility, so existing code should continue to work without changes.
