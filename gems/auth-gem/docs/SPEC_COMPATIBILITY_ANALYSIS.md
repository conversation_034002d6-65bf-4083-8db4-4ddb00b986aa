# Spec Compatibility Analysis for AtharAuth Authorization

## Overview

This document analyzes the compatibility between `spec/concerns/authorize_resources_spec.rb` and the actual authorization logic in the AtharAuth gem.

## ✅ Compatible Areas

1. **Basic Authorization Flow**: Specs correctly test the main `authorize_resources` method
2. **Action Type Detection**: Collection vs single resource actions are handled correctly
3. **Instance Variable Setting**: Proper testing of `@mock_cases` and `@mock_case` variables
4. **Error Handling**: Unauthorized and forbidden responses are tested appropriately
5. **Custom Naming**: Tests for custom `collection_name` and `resource_name` options

## ⚠️ Critical Compatibility Issues

### 1. **Project Filtering Logic Mismatch**

**Issue**: The specs mock project filtering but don't actually test the real implementation.

**Current Spec (Lines 139-147)**:

```ruby
it "applies project filtering for project-based resources" do
  allow(controller).to receive(:accessible_project_ids).and_return([2, 3])
  result = controller.authorize_resources(:index, MockCase)
  expect(result).to be_a(MockScope)
  # In a real implementation, this would filter by project_id
end
```

**Actual Implementation**:

- Uses `supports_project_filtering?(klass)` which checks `klass.column_names.include?('project_id') && !global_user?`
- Applies `scope.where(project_id: accessible_project_ids)` filtering
- The MockScope doesn't implement the actual filtering logic

### 2. **Permission Checking Inconsistency**

**Issue**: Specs mock `can?` method but don't test the actual permission logic.

**Current Spec (Line 129)**:

```ruby
allow(controller).to receive(:can?).with(:index, "mock_case").and_return(true)
```

**Actual Implementation**:

- Uses complex permission checking with `can_general_action?` and `can_instance_action?`
- Checks for `"manage:#{base}"` or `"#{action}:#{base}"` permissions
- Validates project access for instances with `project_id`

### 3. **Resource Finding Logic Gap**

**Issue**: The `find_resource` method has complex logic not reflected in specs.

**Missing Test Coverage**:

- Project-based filtering during resource finding: `klass.where(project_id: accessible_project_ids).find(params[:id])`
- New resource creation with project assignment: `klass.new(project_id: current_project&.id)`
- The `accessible_to` scope support: `klass.accessible_to(current_user)`

### 4. **Additional Scoping Not Properly Tested**

**Issue**: Hash and Proc scoping tests are incomplete.

**Current Spec (Lines 278-292)**:

```ruby
it "applies hash-based additional scoping" do
  scope_conditions = { status: "active" }
  result = controller.authorize_resources(:index, MockCase, scope: scope_conditions)
  expect(result).to be_a(MockScope)
  # In real implementation, this would apply the where conditions
end
```

**Missing**: Actual verification that `scope.where(additional_scope)` is called

## 🔧 Required Mock Improvements

### 1. **Enhanced MockScope**

The MockScope needs to properly simulate ActiveRecord behavior:

```ruby
class MockScope
  def initialize(records)
    @records = records
    @conditions = {}
  end

  def where(conditions)
    new_scope = MockScope.new(@records)
    new_scope.instance_variable_set(:@conditions, @conditions.merge(conditions))
    new_scope
  end

  def applied_conditions
    @conditions
  end
end
```

### 2. **Realistic Permission Testing**

Instead of mocking `can?`, test with actual token data:

```ruby
before do
  controller.decoded_token_value = case_manager_token
  # Remove the can? mock to test real permission logic
end
```

### 3. **Project Filtering Verification**

Add assertions to verify project filtering is applied:

```ruby
it "applies project filtering for project-based resources" do
  allow(controller).to receive(:accessible_project_ids).and_return([2, 3])
  result = controller.authorize_resources(:index, MockCase)

  expect(result.applied_conditions).to include(project_id: [2, 3])
end
```

## 📋 Missing Test Coverage

### 1. **Global User Behavior**

- No tests for global users bypassing project filtering
- Missing tests for `global_user?` method impact

### 2. **Token Compression Integration**

- No tests verifying `TokenCompression.dig_token` usage
- Missing tests for compressed token permission extraction

### 3. **Error Message Accuracy**

- Specs test generic error messages but actual implementation may have different formats
- Missing tests for specific error scenarios (expired tokens, malformed tokens)

### 4. **Resource Class Normalization**

- Limited testing of `normalize_resource_class` method
- Missing edge cases for invalid resource classes

## 🎯 Recommendations

### Immediate Fixes

1. **Update MockScope** to properly simulate ActiveRecord where clauses
2. **Remove can? mocking** and test with real permission logic
3. **Add project filtering assertions** to verify actual filtering behavior
4. **Test with real token data** instead of mocking everything

### Enhanced Test Coverage

1. **Add global user tests** with `global: true` in MockUser
2. **Test token compression** integration with real compressed tokens
3. **Add edge case tests** for malformed inputs and error conditions
4. **Test accessible_to scope** support when available

### Long-term Improvements

1. **Integration tests** with real ActiveRecord models
2. **Performance tests** for large permission sets
3. **Security tests** for authorization bypass attempts
4. **Compatibility tests** with different Rails versions

## 🔍 Detailed Code Comparisons

### Permission Checking Logic

**Spec Expectation (Line 129)**:

```ruby
allow(controller).to receive(:can?).with(:index, "mock_case").and_return(true)
```

**Actual Implementation (authorization.rb:231)**:

```ruby
unless can?(action, klass.name.underscore)
  render_forbidden("You are not allowed to #{action} #{klass.name.underscore.pluralize}")
  return false
end
```

**Real can? method (authorization.rb:23-31)**:

```ruby
def can?(action, subject, options = {})
  if subject.is_a?(Symbol) || subject.is_a?(String)
    can_general_action?(action, subject, options)
  else
    can_instance_action?(action, subject, options)
  end
end
```

### Project Filtering Implementation

**Spec Mock (Lines 140-141)**:

```ruby
allow(controller).to receive(:accessible_project_ids).and_return([2, 3])
# No actual verification of filtering
```

**Actual Implementation (authorization.rb:287-293)**:

```ruby
def apply_project_filtering(scope, klass)
  if supports_project_filtering?(klass)
    scope.where(project_id: accessible_project_ids)
  else
    scope
  end
end
```

### Resource Finding Logic

**Spec Assumption (Lines 174-180)**:

```ruby
it "authorizes single resource and sets instance variable" do
  result = controller.authorize_resources(:show, MockCase)
  expect(result).to be_a(MockCaseInstance)
  expect(result.id).to eq("123")
end
```

**Actual Implementation (authorization.rb:308-326)**:

```ruby
def find_resource(klass, options)
  if options[:resource]
    options[:resource]
  elsif params[:id]
    if supports_project_filtering?(klass)
      klass.where(project_id: accessible_project_ids).find(params[:id])
    else
      klass.find(params[:id])
    end
  else
    if supports_project_filtering?(klass)
      klass.new(project_id: current_project&.id)
    else
      klass.new
    end
  end
end
```

## 🚨 Critical Gaps

1. **No testing of actual permission string matching** (`"read:mock_case"`, `"manage:mock_case"`)
2. **No verification of project_id filtering in where clauses**
3. **No testing of global_user? bypass logic**
4. **No testing of TokenCompression.dig_token integration**
5. **MockScope doesn't track applied conditions**

## Conclusion

While the specs cover the basic functionality well, they rely too heavily on mocking and don't test the actual authorization logic implementation. The main issues are around project filtering, permission checking, and resource finding logic that need more realistic testing approaches.

**Compatibility Score: 6/10** - Basic structure is compatible but critical implementation details are not properly tested.
