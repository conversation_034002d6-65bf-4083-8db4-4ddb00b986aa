# AtharPeople Model Migration Impact Assessment

## Executive Summary

**Risk Level: MEDIUM-HIGH** 🟡

The migration of Project, Role, UserRole models from People service to auth gem has **significant but manageable impact**. The main risks are around type registrations, ActiveStruct collections, and frontend dependencies.

## Current State Analysis

### 1. Existing Models in People Service

#### **Employees::Project** (`services/people/app/models/employees/project.rb`)
```ruby
class Project < Athar::Commons::ActiveStruct::Base
  with_id(auto_generate: false)
  attribute :name, :string
  attribute :description, :string
  attribute :status, :string
end
```

#### **Employees::Role** (`services/people/app/models/employees/role.rb`)
```ruby
class Role < Athar::Commons::ActiveStruct::Base
  with_id(auto_generate: false)
  attribute :name, :string
  attribute :global, :boolean, default: false  # ⚠️ Different from Core service
  attribute :level, :integer
end
```

#### **Employees::UserRole** (`services/people/app/models/employees/user_role.rb`)
```ruby
class UserRole < Athar::Commons::ActiveStruct::Base
  with_id(auto_generate: false)
  attribute :is_default, :boolean
  belongs_to :project # ActiveStruct object
  belongs_to :role # ActiveStruct object
end
```

### 2. Critical Dependencies

#### **Type Registrations** (`services/people/config/initializers/active_model_types.rb`)
```ruby
# ⚠️ BREAKING: These registrations will fail after migration
ActiveModel::Type.register(:user_role_collection, Employees::Types::UserRoleCollectionType)
ActiveModel::Type.register(:project, Employees::Types::ProjectType)
ActiveModel::Type.register(:role, Employees::Types::RoleType)
```

#### **Employee Model ActiveRpc Configuration**
```ruby
# ⚠️ BREAKING: Uses :user_role_collection type
active_rpc :core, :User, foreign_key: :user_id do
  attribute :user_roles_list, :user_role_collection, default: []  # ← Depends on type registration
end
```

#### **UserRoleCollection** (`services/people/app/models/employees/user_role_collection.rb`)
```ruby
# ⚠️ BREAKING: References Employees::UserRole
class UserRoleCollection
  include Athar::Commons::ActiveStruct::Collection
  collection_item_class UserRole  # ← Will break after migration
end
```

## Breaking Changes Analysis

### 1. **HIGH IMPACT: Type Registration Failures**

**Problem:**
```ruby
# This will fail after migration:
ActiveModel::Type.register(:role, Employees::Types::RoleType)
# Because Employees::Role no longer exists
```

**Impact:**
- Employee model won't load
- ActiveRpc attributes will fail
- All People service functionality breaks

**Solution:**
```ruby
# Update type registrations to use auth gem models:
ActiveModel::Type.register(:role, AtharAuth::Models::Types::RoleType)
ActiveModel::Type.register(:project, AtharAuth::Models::Types::ProjectType)
ActiveModel::Type.register(:user_role_collection, AtharAuth::Models::Types::UserRoleCollectionType)
```

### 2. **HIGH IMPACT: UserRoleCollection Class Reference**

**Problem:**
```ruby
# This will fail:
collection_item_class UserRole  # Employees::UserRole no longer exists
```

**Impact:**
- Employee.user_roles_list will fail
- All role/project operations break
- Frontend role/project management breaks

**Solution:**
```ruby
# Update collection to use auth gem model:
collection_item_class AtharAuth::Models::UserRole
```

### 3. **MEDIUM IMPACT: Attribute Inconsistencies**

**Problem:**
```ruby
# People service Role model:
attribute :global, :boolean, default: false

# Core service Role model:
enum :scope, { project_based: false, global_role: true }

# Auth gem Role model (planned):
attribute :scope, :string  # 'global_role' or 'project_based'
```

**Impact:**
- Role.global? method calls will break
- Frontend role scope checking breaks
- Role filtering logic breaks

**Solution:**
```ruby
# Add compatibility methods in auth gem:
def global?
  scope == 'global_role'
end

def global
  scope == 'global_role'
end
```

### 4. **MEDIUM IMPACT: Frontend Dependencies**

**Problem:**
Frontend code expects specific role/project structure:
```typescript
// services/frontend/src/app/[locale]/_modules/people/schemas/roleProjectSchema.ts
const scope = getScopeByRoleId(data.roleId);
if (scope === "project_based") {  // ← Expects this exact value
```

**Impact:**
- Role/project forms break
- Validation logic fails
- User management UI breaks

**Solution:**
- Ensure auth gem models return compatible values
- Update frontend if necessary

## Migration Strategy

### Phase 1: Preparation (Low Risk)
1. **Create auth gem models** with backward compatibility
2. **Create auth gem type classes** 
3. **Test auth gem models** in isolation

### Phase 2: People Service Update (High Risk)
1. **Update type registrations** to use auth gem models
2. **Update UserRoleCollection** to use auth gem models
3. **Add compatibility methods** for attribute differences
4. **Test Employee model** loading and functionality

### Phase 3: Validation (Medium Risk)
1. **Test frontend functionality** with new models
2. **Validate gRPC integration** still works
3. **Test role/project operations** end-to-end

## Risk Mitigation

### 1. **Backward Compatibility Layer**
```ruby
# In auth gem models, provide compatibility:
module AtharAuth::Models::Role
  alias_method :global, :global_role?  # For People service compatibility
end
```

### 2. **Gradual Migration**
- Keep old models temporarily with deprecation warnings
- Migrate one service at a time
- Rollback plan ready

### 3. **Comprehensive Testing**
- Test Employee model loading
- Test role/project CRUD operations
- Test frontend role/project forms
- Test gRPC integration

## Estimated Timeline

- **Phase 1 (Preparation)**: 1-2 days
- **Phase 2 (Migration)**: 2-3 days  
- **Phase 3 (Validation)**: 1-2 days
- **Total**: 4-7 days

## Success Criteria

✅ Employee model loads without errors
✅ Role/project CRUD operations work
✅ Frontend role/project forms work
✅ gRPC integration maintains functionality
✅ No breaking changes for other services
✅ Performance remains acceptable

## Rollback Plan

1. Revert type registrations to use Employees:: models
2. Revert UserRoleCollection to use Employees::UserRole
3. Remove auth gem model dependencies
4. Restore original model files if needed

## Conclusion

**The migration is feasible but requires careful execution.** The main risks are around type registrations and collection references, which can be mitigated with proper preparation and testing.

**Recommendation: Proceed with migration in Phase 0 validation to confirm assumptions before full implementation.**
