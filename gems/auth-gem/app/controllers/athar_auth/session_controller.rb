# frozen_string_literal: true

module AtharAuth
  class SessionController < ActionController::API
    include AtharAuth::ControllerHelpers
    include AtharAuth::Authorization

    before_action :authenticate_api_user!

    api :GET, '/session/permissions', 'Get current session permissions and capabilities'
    description <<~DESC
      **Endpoint Details**

      Returns comprehensive session information including user context, permissions,
      project access capabilities, and system-specific capabilities.

      This endpoint provides all the information needed for frontend authorization
      and permission-based UI rendering.

      ### Response Structure

      ```json
      {
        "permissions": {
          "session": {
            "user": { /* Current user info */ },
            "role": { /* Current role info */ },
            "project": { /* Current project info */ },
            "permissions": [ /* Array of permission strings */ ],
            "scope": "cm" /* Current system scope */
          },
          "access": {
            "type": "global|project",
            "projects": { /* Accessible projects */ },
            "can_access_other_projects": true|false
          },
          "capabilities": {
            /* System-specific capabilities based on current scope */
          }
        }
      }
      ```
    DESC
    header 'Authorization', 'Session token as Bearer token', required: true
    returns code: 200, desc: 'Session permissions and capabilities' do
      property :permissions, Hash, desc: 'Complete session permissions data'
    end

    def permissions
      render json: {
        data: {
          id: "session-#{current_user.id}-#{current_scope}",
          type: 'session_permissions',
          attributes: {
            user: build_user_attributes,
            role: build_role_attributes,
            project: build_project_attributes,
            permissions: current_permissions,
            scope: current_scope,
            access_type: global_user? ? 'global' : 'project'
          },
          relationships: {
            accessible_projects: {
              data: build_accessible_projects_data
            }
          }
        },
        included: build_included_projects,
        meta: {
          capabilities: extract_system_capabilities,
          session_info: {
            authenticated: true,
            token_type: 'session',
            expires_at: extract_token_expiry,
            permissions_source: 'jwt_token',
            permissions_scope: current_scope
          },
          organization_info: {
            organization_project_name: AtharAuth.organization_project_name,
            is_organization_project: organization_project?,
            is_global_user_in_organization: global_user_in_organization?
          }
        }
      }
    end

    private

    def build_user_attributes
      {
        id: current_user.id,
        name: current_user.name,
        email: current_user.email,
        global: global_user?,
        user_type: global_user? ? 'global' : 'project',
        status: current_user_status
      }
    end

    def build_role_attributes
      return {} unless respond_to?(:current_role) && current_role

      {
        id: current_role_id,
        name: current_role_name,
        level: current_role_level,
        scope: current_role_scope
      }
    end

    def build_project_attributes
      return {} unless respond_to?(:current_project) && current_project

      {
        id: current_project_id,
        name: current_project_name,
        is_organization_project: organization_project?
      }
    end

    def build_accessible_projects_data
      return [] unless respond_to?(:accessible_project_ids)

      accessible_project_ids.map do |project_id|
        { id: project_id.to_s, type: 'project' }
      end
    rescue
      []
    end

    def build_included_projects
      return [] unless respond_to?(:accessible_projects_with_names)

      accessible_projects_with_names.map do |project|
        {
          id: project[:id].to_s,
          type: 'project',
          attributes: {
            name: project[:name],
            status: project[:status] || 'active'
          }
        }
      end
    rescue
      []
    end

    def extract_token_expiry
      return nil unless respond_to?(:decoded_token) && decoded_token

      decoded_token['exp'] || decoded_token['expires_at']
    end

    def current_role_data
      return {} unless respond_to?(:current_role) && current_role

      if current_role.respond_to?(:attributes)
        current_role.attributes
      else
        {
          name: current_role_name,
          level: current_role_level,
          scope: current_role_scope
        }
      end
    end

    def current_project_data
      return {} unless respond_to?(:current_project) && current_project

      if current_project.respond_to?(:attributes)
        current_project.attributes
      else
        {
          id: current_project_id,
          name: current_project_name
        }
      end
    end

    def accessible_projects_data
      return {} unless respond_to?(:accessible_projects_with_names)

      accessible_projects_with_names
    rescue
      {}
    end

    def can_access_other_projects?
      return false unless respond_to?(:accessible_project_ids)

      accessible_project_ids.size > 1
    rescue
      false
    end

    def extract_system_capabilities
      case current_scope
      when 'cm'
        extract_cm_capabilities
      when 'people'
        extract_people_capabilities
      when 'procure'
        extract_procure_capabilities
      else
        {}
      end
    end

    def extract_cm_capabilities
      {
        can_manage_cases: current_permissions.any? { |p| p.include?('manage:case') },
        can_supervise: current_permissions.any? { |p| p.include?('supervise') },
        can_export_data: current_permissions.any? { |p| p.include?('export:case') },
        can_manage_beneficiaries: current_permissions.any? { |p| p.include?('manage:beneficiary') },
        can_create_cases: current_permissions.any? { |p| p.include?('create:case') },
        can_update_cases: current_permissions.any? { |p| p.include?('update:case') }
      }
    end

    def extract_people_capabilities
      {
        can_manage_employees: current_permissions.any? { |p| p.include?('manage:employee') },
        can_manage_attendance: current_permissions.any? { |p| p.include?('manage:attendance') },
        can_manage_payroll: current_permissions.any? { |p| p.include?('manage:salary') },
        can_create_employees: current_permissions.any? { |p| p.include?('create:employee') },
        can_update_employees: current_permissions.any? { |p| p.include?('update:employee') },
        can_manage_leaves: current_permissions.any? { |p| p.include?('manage:leave') }
      }
    end

    def extract_procure_capabilities
      {
        can_manage_procurement: current_permissions.any? { |p| p.include?('manage:procurement') },
        can_approve_requests: current_permissions.any? { |p| p.include?('approve:request') },
        can_create_requests: current_permissions.any? { |p| p.include?('create:request') },
        can_update_requests: current_permissions.any? { |p| p.include?('update:request') }
      }
    end

    # Helper methods to extract token data safely
    def current_user_type
      current_user.respond_to?(:user_type) ? current_user.user_type : 'unknown'
    end

    def current_user_status
      current_user.respond_to?(:status) ? current_user.status : 'active'
    end

    def current_role_id
      respond_to?(:current_role) && current_role&.respond_to?(:id) ? current_role.id : nil
    end

    def current_role_name
      respond_to?(:current_role) && current_role&.respond_to?(:name) ? current_role.name : nil
    end

    def current_role_level
      respond_to?(:current_role) && current_role&.respond_to?(:level) ? current_role.level : nil
    end

    def current_role_scope
      respond_to?(:current_role) && current_role&.respond_to?(:scope) ? current_role.scope : nil
    end

    def current_project_id
      respond_to?(:current_project) && current_project&.respond_to?(:id) ? current_project.id : nil
    end

    def current_project_name
      respond_to?(:current_project) && current_project&.respond_to?(:name) ? current_project.name : nil
    end

    def current_scope
      # Extract scope from token or default to 'unknown'
      if respond_to?(:decoded_token) && decoded_token
        decoded_token['scope'] || decoded_token['sc'] || 'unknown'
      else
        'unknown'
      end
    end
  end
end
