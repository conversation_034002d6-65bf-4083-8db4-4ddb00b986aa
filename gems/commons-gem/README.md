# Athar Commons

This gem provides common functionality for Athar microservices, including:

- JSON:API serialization and error handling
- Parameter type conversion for API requests
- Filtering and sorting
- Pagination
- API documentation

## Installation

Add this line to your application's Gemfile:

```ruby
gem 'athar_commons', path: 'https://github.com/athar/athar-commons.git'
```

And then execute:

```bash
$ bundle install
```

## Usage

### Installation

After adding the gem to your Gemfile and running `bundle install`, you can run the generator to set up the necessary files:

```bash
$ rails generate athar:commons:install
```

This will create:
- A base API controller that includes all the concerns
- Initializers for JSONAPI, Apipie, and Pagy
- Routes for the API documentation

### Controller Concerns

The gem provides several controller concerns that can be included in your API controllers:

#### Serializable

Provides methods for serializing resources and errors in JSON:API format.

```ruby
def show
  @resource = YourModel.find(params[:id])
  serialize_response(@resource)
end

def create
  @resource = YourModel.new(resource_params)
  if @resource.save
    serialize_response(@resource, status: :created)
  else
    serialize_errors(@resource.errors)
  end
end
```

#### FilterableSortable

Provides methods for filtering and sorting collections based on query parameters.

```ruby
def index
  @collection = YourModel.all
  
  apply_filters(@collection) do |filtered_collection|
    records, meta = paginate(filtered_collection)
    serialize_response(records, meta: meta)
  end
end
```

#### Paginatable

Provides methods for paginating collections.

```ruby
def index
  @collection = YourModel.all
  
  records, meta = paginate(@collection)
  serialize_response(records, meta: meta)
end
```

#### ParameterTypeConverter

Automatically converts parameters to their correct types based on Apipie documentation.

```ruby
# Declarative approach (recommended)
convert_params do
  use_apipie_types
  convert :custom_param, as: :array
end

# Or traditional approach
before_action :convert_param_types

api :GET, '/your_resources', 'List resources'
param :page, Hash do
  param :number, :number
  param :size, :number
end
param :filter, Hash do
  param :active, :boolean
  param :ids, Array
end
param :user_roles_list, Array, validator: EnhancedArrayValidator
```

The Parameter Type Converter handles:
- Basic types (integers, booleans, dates)
- Nested parameters (like `filter[active]=true`)
- Form-submitted arrays (like `user_roles_list[0][role_id]=1`)

See [Parameter Type Converter Documentation](docs/parameter_type_converter.md) for more details.

#### JsonapiDocs

Provides helpers for documenting JSON:API endpoints with Apipie.

```ruby
api! "List resources"
param_group :pagination_params
param_group :filter_params
param_group :sort_params
description self.jsonapi_with_docs("Endpoint details here")
```

## Customization

Each service can customize the behavior of the shared functionality:

### Customizing Serialization

```ruby
# Override to customize serialization options
def prepare_serialization_options(resource, options = {})
  default_options = super
  default_options[:meta][:service_name] = "your-service"
  default_options
end
```

### Customizing Filtering

```ruby
# Override to customize search behavior
def apply_search_filter(collection)
  search_query = params.dig(:filter, :search)
  return collection unless search_query.present?
  
  collection.where("name ILIKE ?", "%#{search_query}%")
end

# Override to restrict allowed filters
def get_allowed_filters(model_class)
  %w[name status created_at]
end
```

### Customizing Pagination

```ruby
# Override to customize pagination options
def get_pagination_options
  { jsonapi: true, items: 50, max_items: 200 }
end
```

### Customizing Parameter Type Conversion

```ruby
# Register custom validators
register_validator "MyCustomValidator", :integer

# Override type extraction if needed
def extract_type_from_validator(validator)
  if validator.class.name == "MySpecialValidator"
    :special_type
  else
    super
  end
end
```

See the CUSTOMIZATION.md file for more detailed examples.

## License

The gem is available as open source under the terms of the [MIT License](https://opensource.org/licenses/MIT).
