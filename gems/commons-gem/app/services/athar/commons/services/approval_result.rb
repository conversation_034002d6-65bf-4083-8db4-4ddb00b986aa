module Athar
  module Commons
    module Services
      class ApprovalResult
        attr_reader :success, :message, :errors, :data
        
        def initialize(success, message = nil, errors = {}, data = {})
          @success = success
          @message = message
          @errors = errors
          @data = data
        end
        
        def success?
          @success
        end
        
        def failure?
          !@success
        end
        
        # Create a success result
        def self.success(message = nil, data = {})
          new(true, message, {}, data)
        end
        
        # Create a failure result
        def self.failure(message = nil, errors = {}, data = {})
          new(false, message, errors, data)
        end
        
        # Convert to hash
        def to_h
          {
            success: @success,
            message: @message,
            errors: @errors,
            data: @data
          }
        end
        
        # For backward compatibility, convert to boolean
        def to_bool
          @success
        end
        
        # Allow using in boolean contexts
        def to_b
          @success
        end
        
        # Allow using in boolean contexts
        alias_method :to_boolean, :to_b
      end
    end
  end
end
