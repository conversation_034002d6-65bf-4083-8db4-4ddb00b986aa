module Athar
  module Commons
    module Api
      class BaseController < ActionController::API
        include Athar::Commons::Api::Concerns::ParameterTypeConverter
        include Athar::Commons::Api::Concerns::FilterableSortable
        include Athar::Commons::Api::Concerns::Paginatable
        include Athar::Commons::Api::Concerns::Serializable
        include Athar::Commons::Api::Concerns::JsonapiDocs
        include Athar::Commons::Api::Concerns::Exportable

        # Enable parameter type conversion using Apipie documentation
        convert_params do
          use_apipie_types
        end

        # Common error handling
        rescue_from ActiveRecord::RecordNotFound do |_e|
          serialize_errors({ detail: 'Resource not found' }, :not_found)
        end

        rescue_from ActiveRecord::RecordInvalid do |e|
          serialize_errors({ detail: e.record.errors }, :unprocessable_entity)
        end

        rescue_from ActionController::ParameterMissing do |e|
          serialize_errors({ detail: e.message }, :bad_request)
        end

        rescue_from Athar::Commons::Api::Errors::UnauthorizedError do |e|
          serialize_errors({ detail: e.message }, :unauthorized)
        end

        # Handle UncaughtThrowError exceptions (e.g., from throw(:abort) in callbacks)
        rescue_from UncaughtThrowError do |exception|
          raise exception unless exception.tag == :abort

          # If the record has errors, serialize them
          if instance_variable_defined?('@' + controller_name.singularize) &&
             (record = instance_variable_get('@' + controller_name.singularize)) &&
             record.respond_to?(:errors) &&
             !record.errors.empty?
            serialize_errors(record.errors)
          else
            # Generic error if no specific errors are available
            serialize_errors({ detail: 'Operation aborted' })
          end

          # Re-raise if it's not an :abort throw
        end

        # Override inherited to define parameter groups in subclasses
        def self.inherited(subclass)
          super
          # Only define parameter groups if the subclass is in the Api namespace
          return unless subclass.name&.start_with?('Api::')

          subclass.class_eval do
            jsonapi_param_groups
          end
        end
      end
    end
  end
end
