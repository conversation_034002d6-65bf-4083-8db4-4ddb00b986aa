module Athar
  module Commons
    module Api
      module Concerns
        module <PERSON>sonapiDocs
          extend ActiveSupport::Concern

          # Module for class methods
          module ClassMethods
            # Define parameter groups for common parameters
            def jsonapi_param_groups
              # Pagination parameters
              def_param_group :pagination_params do
                param :page, Hash, desc: "Pagination parameters (see main docs for details)" do
                  param :number, :number, desc: "Page number (default: 1)"
                  param :size, :number, desc: "Items per page (default: 20, max: 100)"
                end
              end

              # Filtering parameters
              def_param_group :filter_params do
                param :filter, Hash, desc: "Filtering parameters (see main docs for details)" do
                  param :field_eq, String, desc: "Filter by exact match (e.g., 'status_eq=active')"
                  param :field_in, Array, desc: "Filter by multiple values (e.g., 'department_in=hr,finance' or 'department_in[]=hr&department_in[]=finance')"
                  param :field_cont, String, desc: "Filter by contains (e.g., 'name_cont=john')"
                  param :field_gteq, [ Date, :number ], desc: "Filter by greater than or equal to (e.g., 'created_at_gteq=2023-01-01')"
                  param :field_lteq, [ Date, :number ], desc: "Filter by less than or equal to (e.g., 'created_at_lteq=2023-12-31')"
                  param :field_true, :boolean, desc: "Filter for true values (e.g., 'active_true=true')"
                  param :field_false, :boolean, desc: "Filter for false values (e.g., 'active_false=true')"
                end
              end

              # Sorting parameters
              def_param_group :sort_params do
                param :sort, String, desc: "Sorting parameters (e.g., 'sort=created_at,-name' for ascending created_at and descending name)"
              end

              # Include parameters
              def_param_group :include_params do
                param :include, String, desc: "Related resources to include (e.g., 'include=user,department')"
              end

              # Fields parameters
              def_param_group :fields_params do
                param :fields, Hash, desc: "Sparse fieldsets (e.g., 'fields[employee]=id,name,department')"
              end
            end

            # Common JSON:API documentation
            def common_jsonapi_docs
              <<-HTML
              <b>JSON:API Format</b>

              This API follows the JSON:API specification (https://jsonapi.org). All responses are formatted according to this standard.

              <b>See the main documentation page for details about pagination, filtering, sorting, includes, and sparse fieldsets.</b>
              HTML
            end

            # Define a method that combines common docs with endpoint-specific docs
            def jsonapi_with_docs(endpoint_docs)
              <<-HTML
#{common_jsonapi_docs}

#{endpoint_docs}
              HTML
            end
          end

          # When this module is included in a controller
          included do
            extend Athar::Commons::Apipie::DSL::Concern
            extend ClassMethods
          end
        end
      end
    end
  end
end
