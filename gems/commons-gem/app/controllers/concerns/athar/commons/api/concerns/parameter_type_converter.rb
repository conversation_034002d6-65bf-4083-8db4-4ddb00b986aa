module Athar
  module Commons
    module Api
      module Concerns
        module ParameterTypeConverter
          extend ActiveSupport::Concern

          included do
            class_attribute :param_converters, default: {}
            class_attribute :validator_handlers, default: {}

            # Register default validator handlers
            validator_handlers["Apipie::Validator::IntegerValidator"] = :integer
            validator_handlers["Apipie::Validator::NumberValidator"] = :integer
            validator_handlers["Apipie::Validator::DecimalValidator"] = :decimal
            validator_handlers["BooleanValidator"] = :boolean
            validator_handlers["Apipie::Validator::ArrayValidator"] = :array
            validator_handlers["EnhancedArrayValidator"] = :array_of_objects
            
            # Automatically call convert_param_types before each action
            before_action :convert_param_types
          end

          # Class methods for declarative setup
          module ClassMethods
            # Define parameter conversion settings
            # 
            # @example
            #   convert_params do
            #     use_apipie_types
            #     convert :custom_param, as: :array
            #   end
            def convert_params(&block)
              converter_config = ConverterConfig.new
              converter_config.instance_eval(&block)
              self.param_converters = converter_config.converters
            end

            # Register a custom validator handler
            # 
            # @param validator_class [String] The validator class name
            # @param type [Symbol] The type to convert to (:integer, :decimal, :boolean, :array, etc.)
            def register_validator(validator_class, type)
              validator_handlers[validator_class.to_s] = type
            end
          end

          # Convert parameters based on their expected types
          # This method is automatically called before each action
          def convert_param_types
            # Skip if no converters defined
            return if self.class.param_converters.blank?
            
            # Skip if not using Apipie and no custom converters
            return unless self.class.param_converters[:use_apipie] || self.class.param_converters.except(:use_apipie).present?

            # Process Apipie-defined parameters if enabled
            if self.class.param_converters[:use_apipie]
              process_apipie_parameters
            end

            # Process custom converters
            process_custom_converters
          rescue => e
            # Log error but don't fail the request
            Rails.logger.error("Error in parameter type conversion: #{e.message}")
            Rails.logger.error(e.backtrace.join("\n")) if Rails.env.development?
          end

          private

          # Process parameters based on Apipie documentation
          def process_apipie_parameters
            # Get controller and action
            controller_name = params[:controller].split("/").last
            action_name = params[:action]

            # Find API documentation
            resource_desc = ::Apipie.get_resource_description(self.class)
            return unless resource_desc

            method_desc = find_method_description(resource_desc, action_name)
            return unless method_desc

            # Build parameter type map from documentation
            param_type_map = build_param_type_map(method_desc)

            # Apply conversions based on the type map
            apply_param_conversions(param_type_map)
          end

          # Find the method description from Apipie
          def find_method_description(resource_desc, action_name)
            if resource_desc.respond_to?(:method_descriptions) && resource_desc.method_descriptions.is_a?(Hash)
              resource_desc.method_descriptions[action_name.to_sym]
            elsif resource_desc.respond_to?(:method_descriptions) && resource_desc.method_descriptions.is_a?(Array)
              resource_desc.method_descriptions.find { |md| md.method == action_name }
            elsif resource_desc.respond_to?(:get_method_description)
              resource_desc.get_method_description(action_name.to_sym)
            end
          end

          # Build a map of parameter paths to their types
          def build_param_type_map(method_desc)
            param_type_map = {}

            # Process each parameter defined in Apipie
            if method_desc.respond_to?(:params)
              if method_desc.params.is_a?(Hash)
                method_desc.params.each do |param_name, param_desc|
                  process_param_for_type_map(param_type_map, [param_name.to_s], param_desc)
                end
              elsif method_desc.params.respond_to?(:each)
                method_desc.params.each do |param_desc|
                  if param_desc.respond_to?(:name)
                    process_param_for_type_map(param_type_map, [param_desc.name.to_s], param_desc)
                  end
                end
              end
            end

            param_type_map
          end

          # Process a parameter for the type map, including nested parameters
          def process_param_for_type_map(type_map, path, param_desc)
            # Add this parameter to the type map
            if param_desc.validator
              type_map[path.dup] = extract_type_from_validator(param_desc.validator)
            end

            # Process nested parameters if any
            if param_desc.respond_to?(:validator) && 
               param_desc.validator.respond_to?(:params_ordered) &&
               param_desc.validator.params_ordered.is_a?(Array)

              param_desc.validator.params_ordered.each do |nested_param|
                # Create a new path array that includes the current path plus the nested parameter
                nested_path = path.dup + [nested_param.name.to_s]
                # Recursively process this nested parameter
                process_param_for_type_map(type_map, nested_path, nested_param)
              end
            end
          end

          # Extract the type from a validator
          def extract_type_from_validator(validator)
            return nil unless validator

            # Get the validator class name
            validator_class = validator.class.name

            # Check if we have a registered handler for this validator
            if self.class.validator_handlers.key?(validator_class)
              return self.class.validator_handlers[validator_class]
            end

            # If not registered, try to determine from TypeValidator
            if validator_class == "Apipie::Validator::TypeValidator" && validator.respond_to?(:instance_variable_get)
              type = validator.instance_variable_get(:@type)
              case type
              when ->(t) { t == Date } then :date
              when ->(t) { t == Integer } then :integer
              when ->(t) { t == Float } then :decimal
              when ->(t) { t == Array } then :array
              else :string
              end
            # Or try to determine from expected_type
            elsif validator.respond_to?(:expected_type)
              type_str = validator.expected_type.to_s.downcase
              if type_str.include?('numeric') || type_str.include?('integer')
                :integer
              elsif type_str.include?('decimal') || type_str.include?('float')
                :decimal
              elsif type_str.include?('boolean')
                :boolean
              elsif type_str.include?('array')
                :array
              elsif type_str.include?('date')
                :date
              else
                :string
              end
            else
              :string
            end
          end

          # Apply parameter conversions based on the type map
          def apply_param_conversions(param_type_map)
            # Process each parameter in the type map
            param_type_map.each do |path, type|
              # Skip if parameter doesn't exist in request
              next unless parameter_exists?(params, path)

              # Get the parameter value
              value = get_parameter_value(params, path)

              # Skip nil values
              next if value.nil?

              # Convert the value based on its type
              converted_value = convert_value(value, type)

              # Set the converted value back in params
              set_parameter_value(params, path, converted_value)
            end
          end

          # Check if a parameter exists at the given path
          def parameter_exists?(param_hash, path)
            current = param_hash

            # Navigate through each segment of the path
            path.each_with_index do |segment, index|
              # If we're at the last segment, just check if it exists
              return current.key?(segment) if index == path.length - 1

              # Otherwise, ensure the current segment exists and is a hash
              return false unless current.key?(segment) && 
                                 (current[segment].is_a?(Hash) || current[segment].is_a?(ActionController::Parameters))

              # Move deeper into the nested structure
              current = current[segment]
            end

            false
          end

          # Get a parameter value at the given path
          def get_parameter_value(param_hash, path)
            current = param_hash

            # Navigate through all but the last segment
            path[0..-2].each do |segment|
              return nil unless current.key?(segment) && 
                               (current[segment].is_a?(Hash) || current[segment].is_a?(ActionController::Parameters))
              current = current[segment]
            end

            # Return the value at the final segment
            current[path.last] if current.key?(path.last)
          end

          # Set a parameter value at the given path
          def set_parameter_value(param_hash, path, value)
            current = param_hash

            # Navigate through all but the last segment, creating hashes as needed
            path[0..-2].each do |segment|
              current[segment] = {} unless current.key?(segment) && 
                                        (current[segment].is_a?(Hash) || current[segment].is_a?(ActionController::Parameters))
              current = current[segment]
            end

            # Set the value at the final segment
            current[path.last] = value
          end

          # Convert a value based on its type
          def convert_value(value, type)
            case type
            when :integer, :number
              value.to_i
            when :decimal, :float
              value.to_f
            when :boolean
              convert_to_boolean(value)
            when :date
              convert_to_date(value)
            when :array, :array_of_objects
              # Handle any array-like parameter
              if value.is_a?(Hash) || value.is_a?(ActionController::Parameters)
                # Check if this looks like a form-submitted array (numeric keys)
                if value.keys.all? { |k| k.to_s =~ /\A\d+\z/ }
                  convert_hash_to_array(value)
                else
                  # Not a numeric-keyed hash, return as is
                  value
                end
              else
                convert_to_array(value)
              end
            else
              value
            end
          end

          # Process custom converters defined in the controller
          def process_custom_converters
            self.class.param_converters.except(:use_apipie).each do |param_name, options|
              # Skip if parameter doesn't exist
              next unless params.key?(param_name)

              if options[:as]
                # Convert using predefined type
                params[param_name] = convert_value(params[param_name], options[:as])
              elsif options[:using].respond_to?(:call)
                # Convert using custom lambda/proc
                params[param_name] = options[:using].call(params[param_name])
              end
            end
          end

          # Convert a value to boolean
          def convert_to_boolean(value)
            return true if value == true || value.to_s =~ /^(true|t|yes|y|1)$/i
            return false if value == false || value.to_s =~ /^(false|f|no|n|0)$/i
            value
          end

          # Convert a value to array
          def convert_to_array(value)
            return value if value.is_a?(Array)
            # Handle hash with numeric string keys (array of objects from form data)
            return convert_hash_to_array(value) if value.is_a?(Hash) || value.is_a?(ActionController::Parameters)
            return value.split(",").map(&:strip) if value.is_a?(String) && value.include?(",")
            [ value ]
          end

          # Convert a hash with numeric string keys to an array
          # Example: {"0" => {"role_id" => "1"}, "1" => {"role_id" => "2"}} => [{"role_id" => "1"}, {"role_id" => "2"}]
          def convert_hash_to_array(hash)
            # Convert ActionController::Parameters to a regular hash if needed
            hash_to_sort = hash.is_a?(ActionController::Parameters) ? hash.to_unsafe_h : hash
            
            # Check if all keys are numeric strings
            if hash_to_sort.keys.all? { |k| k.to_s =~ /\A\d+\z/ }
              # Sort by numeric value and extract just the values
              hash_to_sort.sort_by { |k, _| k.to_i }.map { |_, v| v }
            else
              # Not a numeric-keyed hash, return as is
              hash
            end
          end

          # Convert a value to date
          def convert_to_date(value)
            return value if value.is_a?(Date)

            # Try to parse the string as a date if it's a string
            if value.is_a?(String) && value.present?
              begin
                return Date.parse(value)
              rescue Date::Error => e
                Rails.logger.error("Error converting to date: #{e.message}")
              end
            end

            # Return original value if conversion failed
            value
          end

          # Configuration class for declarative setup
          class ConverterConfig
            attr_reader :converters

            def initialize
              @converters = {}
            end

            def use_apipie_types
              @converters[:use_apipie] = true
            end

            def convert(param_name, options = {})
              @converters[param_name.to_s] = options
            end
          end
        end
      end
    end
  end
end
