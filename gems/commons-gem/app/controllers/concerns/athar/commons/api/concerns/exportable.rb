# frozen_string_literal: true

module Athar
  module Commons
    module Api
      module Concerns
        # Concern for handling data exports in various formats (CSV, PDF, XLSX)
        # 
        # This concern provides export functionality that can be included in controllers
        # to enable exporting data in multiple formats with dynamic column selection
        # and relationship inclusion.
        #
        # @example Basic usage
        #   class EmployeesController < ApplicationController
        #     include Athar::Commons::Api::Concerns::Exportable
        #     
        #     def index
        #       @employees = Employee.all
        #       if export_format = detect_export_format
        #         handle_export(@employees, export_format)
        #       else
        #         # Regular JSON response
        #       end
        #     end
        #   end
        #
        # @example Export URLs
        #   GET /api/employees.csv?columns=id,name,email
        #   GET /api/employees.csv?columns[]=id&columns[]=name&columns[]=email
        #   GET /api/employees.pdf?include=department,role
        #   GET /api/employees.xlsx?columns=id,name&include=user_roles.role
        module Exportable
          extend ActiveSupport::Concern

          # Override serialize_response to add export functionality
          def serialize_response(resource, options = {})
            export_format = detect_export_format

            if export_format
              handle_export(resource, export_format, options)
            else
              super  # ← Call the original serialize_response method
            end
          end

          # Detects if the current request is for an export format
          #
          # Only allows exports on GET requests to prevent accidental exports
          # on write operations (POST, PUT, DELETE).
          #
          # @return [Symbol, nil] The export format (:csv, :pdf, :xlsx) or nil
          def detect_export_format
            # Only allow exports on GET requests
            return nil unless request.get?
            return nil unless request.path =~ /\.(\w+)$/

            extension = $1.to_sym
            return extension if Athar::Commons::Services::Export::ExportFactory.supported_format?(extension)

            nil
          end

          # Handles export requests
          #
          # @param resource [Object, Array] The resource to export
          # @param format [Symbol] The export format (:csv, :pdf, :xlsx)
          # @param options [Hash] Export options
          # @return [void]
          def handle_export(resource, format, options = {})
            begin
              # Parse export-specific parameters
              export_options = extract_export_options(options)
              
              export_service = Athar::Commons::Services::Export::ExportFactory.create(resource, format, export_options)
              file_content = export_service.generate
              
              send_data file_content,
                        filename: export_service.filename,
                        type: export_service.mime_type,
                        disposition: 'attachment'
            rescue StandardError => e
              Rails.logger&.error("Export failed: #{e.message}")
              Rails.logger&.error(e.backtrace.join("\n")) if Rails.env.test?
              serialize_errors({ detail: "Export failed: #{e.message}" }, :unprocessable_entity)
            end
          end

          private

          # Extract export-specific options from request parameters
          #
          # @param options [Hash] Base options
          # @return [Hash] Enhanced options with export parameters
          def extract_export_options(options = {})
            export_options = options.dup

            # Column selection
            if params[:columns].present?
              export_options[:columns] = params[:columns]
            end

            # Column aliases
            if params[:aliases].present?
              export_options[:aliases] = params[:aliases]
            end

            # Custom filename
            if params[:filename].present?
              export_options[:filename] = params[:filename]
            end

            # Title for PDF exports
            if params[:title].present?
              export_options[:title] = params[:title]
            end

            export_options
          end
        end
      end
    end
  end
end
