module Athar
  module Commons
    module Models
      module Concerns
        module ApprovalActionCallbacks
          extend ActiveSupport::Concern

          included do
            after_create :update_request_status, if: -> { %w[approve reject].include?(action) }
          end

          private

          # Update the request status based on the action
          def update_request_status
            case action
            when 'approve'
              handle_approval
            when 'reject'
              handle_rejection
            end
          end

          # Handle approval action
          def handle_approval
            # Check if this completes the step
            if approval_request.is_step_complete?(approval_step)
              # Get the next step
              next_step = approval_request.next_step(approval_step)
              
              if next_step
                # There's another step, nothing more to do
                Rails.logger.info "Moving to next step: #{next_step.name} (#{next_step.id})"
              else
                # This was the final step, finalize the approval
                Rails.logger.info "Final step approved, finalizing approval"
                approval_request.finalize_approval
              end
            else
              Rails.logger.info "Step not yet complete, waiting for more approvals"
            end
          end

          # Handle rejection action
          def handle_rejection
            # Immediately reject the request
            Rails.logger.info "Request rejected, updating status"
            approval_request.handle_rejection
          end
        end
      end
    end
  end
end
