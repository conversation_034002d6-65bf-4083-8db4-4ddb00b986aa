require "ransack"

module Athar
  module Commons
    module Models
      module Concerns
        module Ransackable
          extend ActiveSupport::Concern

          included do
            # When this concern is included, hook into the enum method
            singleton_class.alias_method :original_enum, :enum
            singleton_class.define_method(:enum) do |*args, **kwargs|
              # Call the original enum method
              result = original_enum(*args, **kwargs)

              # Extract enum name and create ransacker
              enum_name = extract_enum_name_from_args(*args)
              ransackable_enum(enum_name) if enum_name

              result
            end
          end

          class_methods do
            def ransackable_attributes(auth_object = nil)
              # Define which attributes can be searched/filtered
              column_names
            end

            # TODO: Currently not un use! later we check filtration through association with ransnack
            def ransackable_associations(auth_object = nil)
              # Define which associations can be searched through
              reflect_on_all_associations.map { |a| a.name.to_s }
            end

            # Extract enum name from arguments passed to enum method
            def extract_enum_name_from_args(*args)
              first_arg = args.first

              if first_arg.is_a?(Hash)
                # Called like: enum department: { admin: 0, hr: 1 }
                first_arg.keys.first.to_s
              elsif first_arg.is_a?(Symbol) || first_arg.is_a?(String)
                # Called like: enum :department, { admin: 0, hr: 1 } or
                # enum :status, { active: 0, inactive: 1 }, prefix: true
                first_arg.to_s
              end
            end

            # Create a ransacker for a specific enum attribute
            def ransackable_enum(enum_name)
              return unless respond_to?(:defined_enums) && defined_enums.key?(enum_name.to_s)

              ransacker enum_name.to_sym do |parent|
                parent.table[enum_name.to_sym]
              end
            end
          end
        end
      end
    end
  end
end
