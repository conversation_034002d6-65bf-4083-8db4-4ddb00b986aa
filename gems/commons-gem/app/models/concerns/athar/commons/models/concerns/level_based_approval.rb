module Athar
  module Commons
    module Models
      module Concerns
        module LevelBasedApproval
          extend ActiveSupport::Concern

          # Get the context for level-based approver methods
          def level_context(context = {})
            # For level-based methods, we need to add the level to the context
            if dynamic_approver_method.to_s.include?('level')
              # Extract level from the method name or description if possible
              level = if name.to_s =~ /level\s*(\d+)/i
                        $1.to_i
                      elsif description.to_s =~ /level\s*(\d+)/i
                        $1.to_i
                      else
                        # Default levels based on common role patterns
                        case name.to_s.downcase
                        when /admin/
                          40
                        when /manager/, /head/, /supervisor/
                          30
                        when /officer/, /lead/
                          20
                        else
                          10
                        end
                      end
              
              # Add the level to the context
              context = context.dup
              context[:level] = level
            end
            
            context
          end
        end
      end
    end
  end
end
