[tools]
ruby = "3.4.2"

# Test and Development Tasks
[tasks.test]
description = "Run RSpec tests"
run = "bundle exec rspec"

[tasks.clean]
description = "Clean up built gem files"
run = [
  "rm -f *.gem",
  "rm -rf pkg/"
]

# Version Management Tasks
[tasks.version]
description = "Show current gem version"
run = "ruby -r ./lib/athar/commons/version -e 'puts Athar::Commons::VERSION'"

[tasks.bump-patch]
description = "Bump patch version (0.3.1 -> 0.3.2) and commit"
run = '''
#!/usr/bin/env bash
set -e

# Bump version with Ruby
ruby -e "
require_relative 'lib/athar/commons/version'

current = Athar::Commons::VERSION
parts = current.split('.').map(&:to_i)
parts[2] += 1
new_version = parts.join('.')

content = File.read('lib/athar/commons/version.rb')
content.gsub!(/\"#{current}\"/, \"\\\"#{new_version}\\\"\")
File.write('lib/athar/commons/version.rb', content)

puts \"Version bumped from #{current} to #{new_version}\"
"

# Commit the version change
VERSION=$(ruby -r ./lib/athar/commons/version -e 'puts Athar::Commons::VERSION')
git add lib/athar/commons/version.rb
git commit -m "Bump version to ${VERSION}" || echo "No changes to commit"
echo "✅ Version ${VERSION} committed"
'''

[tasks.bump-minor]
description = "Bump minor version (0.3.1 -> 0.4.0) and commit"
run = '''
#!/usr/bin/env bash
set -e

# Bump version with Ruby
ruby -e "
require_relative 'lib/athar/commons/version'

current = Athar::Commons::VERSION
parts = current.split('.').map(&:to_i)
parts[1] += 1
parts[2] = 0
new_version = parts.join('.')

content = File.read('lib/athar/commons/version.rb')
content.gsub!(/\"#{current}\"/, \"\\\"#{new_version}\\\"\")
File.write('lib/athar/commons/version.rb', content)

puts \"Version bumped from #{current} to #{new_version}\"
"

# Commit the version change
VERSION=$(ruby -r ./lib/athar/commons/version -e 'puts Athar::Commons::VERSION')
git add lib/athar/commons/version.rb
git commit -m "Bump version to ${VERSION}" || echo "No changes to commit"
echo "✅ Version ${VERSION} committed"
'''

[tasks.bump-major]
description = "Bump major version (0.3.1 -> 1.0.0) and commit"
run = '''
#!/usr/bin/env bash
set -e

# Bump version with Ruby
ruby -e "
require_relative 'lib/athar/commons/version'

current = Athar::Commons::VERSION
parts = current.split('.').map(&:to_i)
parts[0] += 1
parts[1] = 0
parts[2] = 0
new_version = parts.join('.')

content = File.read('lib/athar/commons/version.rb')
content.gsub!(/\"#{current}\"/, \"\\\"#{new_version}\\\"\")
File.write('lib/athar/commons/version.rb', content)

puts \"Version bumped from #{current} to #{new_version}\"
"

# Commit the version change
VERSION=$(ruby -r ./lib/athar/commons/version -e 'puts Athar::Commons::VERSION')
git add lib/athar/commons/version.rb
git commit -m "Bump version to ${VERSION}" || echo "No changes to commit"
echo "✅ Version ${VERSION} committed"
'''

# Build Tasks
[tasks.build]
description = "Build the gem"
depends = ["test", "clean"]
run = "bundle exec rake build"



# Fury Publishing Tasks
[tasks.publish-fury]
description = "Push to Fury via Git"
depends = ["test"]
run = '''
#!/usr/bin/env bash
set -e

echo "📦 Publishing to Fury via Git..."

# Check if fury remote exists
if ! git remote | grep -q "fury"; then
  echo "❌ Fury remote not found"
  echo "Please add fury remote first: git remote add fury <your-fury-git-url>"
  exit 1
fi

# Push to fury
echo "🚀 Pushing to fury master..."
git push fury master

if [ $? -eq 0 ]; then
  echo "✅ Successfully published to Fury!"
else
  echo "❌ Failed to publish to Fury"
  exit 1
fi
'''

[tasks.release]
description = "Complete release workflow (test, publish)"
depends = ["publish-fury"]
run = '''
echo "🎉 Release completed successfully!"
echo "📦 Changes pushed to Fury via Git"
echo "🔗 Check your Fury dashboard for the new version"
'''
