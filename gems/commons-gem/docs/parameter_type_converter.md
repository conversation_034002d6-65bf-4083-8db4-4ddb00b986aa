# Parameter Type Converter

The Parameter Type Converter is a Rails concern that automatically converts API parameters to their appropriate Ruby types based on Apipie documentation.

## Overview

When building APIs, parameters often come in as strings, but your application needs them as integers, booleans, dates, or arrays. The Parameter Type Converter handles this conversion automatically by:

1. Reading the Apipie documentation for your controller actions
2. Determining the expected types for each parameter
3. Converting parameters to their appropriate types

## Features

- **Automatic Type Conversion**: Converts parameters based on Apipie documentation
- **Declarative Interface**: Simple DSL for configuring parameter conversion
- **Support for Nested Parameters**: Handles complex nested parameter structures
- **Form-Submitted Arrays**: Automatically converts form-submitted arrays (with numeric keys) to proper arrays
- **Custom Converters**: Allows defining custom conversion logic for specific parameters
- **Extensible**: Supports registering handlers for custom validators

## Installation and Setup

### 1. Include the Concern

First, include the concern in your controller:

```ruby
class Api::EmployeesController < Api::BaseController
  include Athar::Commons::Api::Concerns::ParameterTypeConverter
  
  # Rest of your controller...
end
```

### 2. Enable Parameter Conversion

You must explicitly enable parameter conversion using one of these methods:

#### Method 1: Declarative Approach (Recommended)

Use the `convert_params` method with `use_apipie_types` to enable conversion based on Apipie documentation:

```ruby
class Api::EmployeesController < Api::BaseController
  include Athar::Commons::Api::Concerns::ParameterTypeConverter

  convert_params do
    use_apipie_types  # This enables Apipie-based parameter conversion
  end
  
  # Your controller actions...
end
```

#### Method 2: Manual Approach

Alternatively, you can call the `convert_param_types` method directly in a before_action:

```ruby
class Api::EmployeesController < Api::BaseController
  include Athar::Commons::Api::Concerns::ParameterTypeConverter
  
  before_action :convert_param_types
  
  # Your controller actions...
end
```

### 3. Define Apipie Documentation

Make sure your controller actions have Apipie documentation that specifies parameter types:

```ruby
api :GET, '/employees', 'List employees'
param :page, Hash do
  param :number, :number
  param :size, :number
end
param :filter, Hash do
  param :active, :boolean
  param :department_id, :number
end
def index
  # Parameters will be automatically converted based on the documentation
  # params[:page][:number] will be an integer
  # params[:filter][:active] will be a boolean
  # ...
end
```

## Usage Examples

### Basic Usage

Include the concern and enable Apipie-based conversion:

```ruby
class Api::EmployeesController < Api::BaseController
  include Athar::Commons::Api::Concerns::ParameterTypeConverter

  convert_params do
    use_apipie_types
  end

  # Your controller actions with Apipie documentation...
end
```

### Custom Parameter Conversion

You can define custom conversion for specific parameters:

```ruby
class Api::EmployeesController < Api::BaseController
  include Athar::Commons::Api::Concerns::ParameterTypeConverter

  convert_params do
    use_apipie_types
    
    # Convert a parameter to a specific type
    convert :start_date, as: :date
    
    # Use a custom conversion function
    convert :tags, using: ->(value) { value.to_s.split(',').map(&:strip) }
  end

  # Your controller actions...
end
```

### Registering Custom Validators

If you have custom Apipie validators, you can register them with the converter:

```ruby
class Api::BaseController < ApplicationController
  include Athar::Commons::Api::Concerns::ParameterTypeConverter
  
  # Register a custom validator
  register_validator "MyCustomValidator", :integer
end
```

## Supported Types

The converter supports the following parameter types:

- `:integer` - Converts to Integer using `to_i`
- `:decimal` - Converts to Float using `to_f`
- `:boolean` - Converts strings like "true", "1", "yes" to `true` and "false", "0", "no" to `false`
- `:date` - Converts to Date using `Date.parse`
- `:array` - Converts comma-separated strings to arrays and handles form-submitted arrays
- `:array_of_objects` - Special handling for arrays of objects (like `user_roles_list`)

## How It Works

1. The converter reads the Apipie documentation for the current controller action
2. It builds a map of parameter paths to their expected types
3. For each parameter in the map, it checks if the parameter exists in the request
4. If the parameter exists, it converts it to the expected type
5. The converted value is set back in the params hash

## Form-Submitted Arrays

HTML forms encode arrays of objects in a special way:

```
user_roles_list[0][role_id]=1&user_roles_list[0][project_id]=100&user_roles_list[1][role_id]=2
```

Rails parses this as:

```ruby
{
  "user_roles_list" => {
    "0" => { "role_id" => "1", "project_id" => "100" },
    "1" => { "role_id" => "2" }
  }
}
```

The converter automatically detects this pattern and converts it to a proper array:

```ruby
{
  "user_roles_list" => [
    { "role_id" => "1", "project_id" => "100" },
    { "role_id" => "2" }
  ]
}
```

## Nested Parameters

The converter handles nested parameters at any depth. For example, with Apipie documentation like:

```ruby
param :filter, Hash do
  param :status, :number
  param :nested, Hash do
    param :deep, :boolean
  end
end
```

Parameters like `filter[status]=123&filter[nested][deep]=true` will be properly converted.

## Error Handling

The converter includes robust error handling to ensure that parameter conversion issues don't cause API requests to fail. If a conversion fails, the original value is preserved and an error is logged.

## Testing

When testing controllers that use the Parameter Type Converter, you may need to mock the Apipie documentation. See the `parameter_type_converter_test.rb` file for examples of how to do this.
