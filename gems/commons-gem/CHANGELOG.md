# Changelog

## [0.2.0] - 2023-04-20

### Added
- ParameterTypeConverter concern for automatic type conversion of query parameters
- Custom Apipie validators for boolean and enhanced array types
- Tests for parameter type conversion
- Install generator for easy setup of configuration files

### Changed
- Updated default pagination size from 25 to 20
- Simplified Pagy metadata fields for cleaner responses
- Improved JSON:API documentation with more accurate parameter types
- Enhanced Base API controller with parameter type conversion

## [0.1.0] - 2023-04-20

### Added
- Initial release
- Ransackable concern for models
- FilterableSortable concern for controllers
- Paginatable concern for controllers
- Serializable concern for controllers
- JsonapiDocs concern for controllers
- Base API controller
