# Approval System Generator

This generator creates the necessary models, migrations, controllers, and serializers for implementing the approval system in a service.

## Usage

```bash
bin/rails g athar:commons:approval [options]
```

## Options

- `--user_table=NAME`: Name of the users table for requestor reference (default: "users")
- `--project_based=BOOLEAN`: Whether the system is project-based (default: true)
- `--project_table=NAME`: Name of the projects table if project_based is true (default: "projects")
- `--use_foreign_keys=BOOLEAN`: Whether to use foreign keys for references (default: false)

## Examples

### For a Project-Based Service with Direct DB Access

```bash
bin/rails g athar:commons:approval --user_table=users --project_based=true --project_table=projects --use_foreign_keys=true
```

### For a Non-Project-Based Service (like AtharPeople)

```bash
bin/rails g athar:commons:approval --user_table=users --project_based=false --use_foreign_keys=true
```

### For a Service without Direct DB Access to Users/Projects

```bash
bin/rails g athar:commons:approval --use_foreign_keys=false
```

## Generated Files

The generator creates the following files:

### Models
- `app/models/approval_request.rb`
- `app/models/approval_step.rb`
- `app/models/approval_action.rb`

### Migrations
- `db/migrate/TIMESTAMP_create_approval_requests.rb`
- `db/migrate/TIMESTAMP_create_approval_steps.rb`
- `db/migrate/TIMESTAMP_create_approval_actions.rb`

### Controllers
- `app/controllers/api/approval_requests_controller.rb`

### Serializers
- `app/serializers/approval_request_serializer.rb`
- `app/serializers/approval_step_serializer.rb`
- `app/serializers/approval_action_serializer.rb`

## Making a Model Approvable

To make a model approvable, simply call `acts_as_approvable` in your model and implement the required methods:

```ruby
class ProcurementRequest < ApplicationRecord
  # Call acts_as_approvable to set up the association and methods
  acts_as_approvable
  
  # ... other attributes and methods
  
  # Implement the required methods
  def approval_action
    "buy_new_item"
  end
  
  def system_name
    "procure"
  end
end
```

## Submitting for Approval

To submit a model for approval:

```ruby
procurement_request = ProcurementRequest.create!(...)
procurement_request.submit_for_approval(current_user, project)
```

## Approving and Rejecting Requests

You can approve or reject requests directly from the model instance:

```ruby
# Approve the request
result = procurement_request.approval_approve!(current_user.id, "Looks good!")

# Check if the approval was successful
if result.success?
  # Handle success
  puts "Request approved: #{result.message}"
else
  # Handle failure
  puts "Approval failed: #{result.message}"
  puts "Errors: #{result.errors}"
end

# Reject the request
result = procurement_request.approval_reject!(current_user.id, "Budget exceeded")

# Add a comment
result = procurement_request.approval_comment!(current_user.id, "Please provide more details")

# You can also use exception handling
begin
  result = procurement_request.approval_approve!(current_user.id, "Looks good!", raise_error: true)
  # Only reaches here if successful
  puts "Request approved: #{result.message}"
rescue StandardError => e
  # Handle the error
  puts "Approval failed: #{e.message}"
end
```

Alternatively, you can use the ApprovalService:

```ruby
# Create an approval service
service = Athar::Commons::Services::ApprovalService.new

# Approve the request
result = service.approve(procurement_request.approval_request, current_user.id, "Looks good!")

# Reject the request
result = service.reject(procurement_request.approval_request, current_user.id, "Budget exceeded")

# Add a comment
result = service.comment(procurement_request.approval_request, current_user.id, "Please provide more details")
```

### The ApprovalResult Object

All approval operations return an `ApprovalResult` object with the following methods:

```ruby
result.success?     # => true/false
result.failure?     # => true/false
result.message      # => String message describing the result
result.errors       # => Hash of errors
result.data         # => Hash of additional data

# Convert to hash
result.to_h         # => { success: true, message: "...", errors: {}, data: {} }

# For backward compatibility
if result            # Can be used in boolean contexts
  # Success case
else
  # Failure case
end
```

## Finding Approvable Objects

You can find approvable objects using the following scopes:

```ruby
# Find all objects pending approval for a specific user
ProcurementRequest.pending_approval_for(current_user.id)

# Find all objects approved in the approval workflow
ProcurementRequest.approval_workflow_approved

# Find all objects rejected in the approval workflow
ProcurementRequest.approval_workflow_rejected

# Find all objects pending approval in the approval workflow
ProcurementRequest.approval_workflow_pending
```

These scopes can be chained with other scopes and query methods:

```ruby
# Find all annual leaves pending approval for the current user, ordered by creation date
Leave.pending_approval_for(current_user.id).where(leave_type: :annual).order(created_at: :desc)
```

## Checking Approval Status

```ruby
# Use these methods to check approval workflow status
procurement_request.approval_approved?      # => true/false
procurement_request.approval_rejected?      # => true/false
procurement_request.approval_pending?       # => true/false

# Get the approval workflow status
procurement_request.approval_workflow_status  # => "pending", "approved", "rejected", etc.
```

## Note for Models with Existing Status Enum

If your model already has a status enum with methods like `approved?`, `rejected?`, etc., the Approvable concern uses different method names to avoid conflicts:

```ruby
# Model's own status methods
model.approved?  # => From the model's status enum
model.rejected?  # => From the model's status enum
model.pending?   # => From the model's status enum

# Approval workflow status methods
model.approval_approved?  # => From the approval workflow
model.approval_rejected?  # => From the approval workflow
model.approval_pending?   # => From the approval workflow

# Model's own scopes
Model.approved   # => From the model's status enum
Model.rejected   # => From the model's status enum
Model.pending    # => From the model's status enum

# Approval workflow scopes
Model.approval_workflow_approved   # => From the approval workflow
Model.approval_workflow_rejected   # => From the approval workflow
Model.approval_workflow_pending    # => From the approval workflow
```

This allows models to maintain their own status while also participating in the approval workflow.
