require 'rails/generators'
require 'rails/generators/base'

module Athar
  module Commons
    module Generators
      class ApprovalGenerator < Rails::Generators::Base
        source_root File.expand_path("templates", __dir__)

        desc "Generates approval system models and migrations"

        class_option :user_table, type: :string, default: "users",
                     desc: "Name of the users table for requestor reference"
        class_option :project_based, type: :boolean, default: true,
                     desc: "Whether the system is project-based"
        class_option :project_table, type: :string, default: "projects",
                     desc: "Name of the projects table (if project_based is true)"
        class_option :use_foreign_keys, type: :boolean, default: false,
                     desc: "Whether to use foreign keys for references"
        class_option :system_name, type: :string, default: nil,
                     desc: "The system name for the service (e.g., 'people', 'procure'). If not provided, will try to extract from the application."

        def create_approval_models
          template "approval_request.rb.tt", "app/models/approval_request.rb"
          template "approval_step.rb", "app/models/approval_step.rb"
          template "approval_action.rb", "app/models/approval_action.rb"
        end

        def create_approval_migrations
          template "create_approval_requests.rb.tt", "db/migrate/#{timestamp}_create_approval_requests.rb"
          template "create_approval_steps.rb", "db/migrate/#{timestamp(1)}_create_approval_steps.rb"
          template "create_approval_actions.rb", "db/migrate/#{timestamp(2)}_create_approval_actions.rb"
        end

        def create_approval_controllers
          template "approval_requests_controller.rb", "app/controllers/api/approval_requests_controller.rb"
        end

        def create_approval_serializers
          template "approval_request_serializer.rb.tt", "app/serializers/approval_request_serializer.rb"
          template "approval_step_serializer.rb", "app/serializers/approval_step_serializer.rb"
          template "approval_action_serializer.rb", "app/serializers/approval_action_serializer.rb"
        end

        def add_routes_instructions
          say "\n"
          say "=== MANUAL STEP REQUIRED ===", :yellow
          say "Please add the following routes to your config/routes.rb file:", :yellow
          say "\n"
          say "namespace :api do", :green
          say "  resources :approval_requests, only: [:index, :show] do", :green
          say "    member do", :green
          say "      post :approve", :green
          say "      post :reject", :green
          say "      post :cancel", :green
          say "    end", :green
          say "    collection do", :green
          say "      get :pending_approvals", :green
          say "    end", :green
          say "  end", :green
          say "end", :green
          say "\n"
        end

        private

        def timestamp(offset = 0)
          (Time.now.utc + offset.seconds).strftime("%Y%m%d%H%M%S")
        end

        # Extract the system name from the Rails application
        def system_name
          return options[:system_name] if options[:system_name].present?

          # Try to extract from Rails application module name
          if defined?(Rails) && Rails.application
            app_name = Rails.application.class.module_parent_name

            # Convert CamelCase to snake_case and remove "Athar" prefix
            extracted_name = app_name.gsub(/^Athar/, '').underscore

            return extracted_name if extracted_name.present?
          end

          # Try to extract from Apipie configuration
          if defined?(::Apipie) && Apipie.configuration.app_name
            app_name = ::Apipie.configuration.app_name

            # Convert CamelCase to snake_case and remove "Athar" prefix
            extracted_name = app_name.gsub(/^Athar/, '').underscore

            return extracted_name if extracted_name.present?
          end

          # Try to extract from Rails application config
          if defined?(Rails) && Rails.application.config.respond_to?(:system_name)
            return Rails.application.config.system_name if Rails.application.config.system_name.present?
          end

          # Default fallback
          say "Could not automatically determine system name. Using 'unknown' as fallback.", :yellow
          say "Please update the system_name method in your models.", :yellow
          'unknown'
        end
      end
    end
  end
end
