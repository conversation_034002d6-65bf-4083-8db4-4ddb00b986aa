# Approval System Setup

## Overview

The approval system has been successfully generated for your service. This system allows you to implement multi-step approval workflows for any model in your application.

## Next Steps

### 1. Add Routes

Add the following routes to your `config/routes.rb` file:

```ruby
namespace :api do
  resources :approval_requests, only: [:index, :show] do
    member do
      post :approve
      post :reject
      post :cancel
    end
    collection do
      get :pending_approvals
    end
  end
end
```

### 2. Make Models Approvable

To make a model approvable, add the `acts_as_approvable` method to your model and implement the required methods:

```ruby
class YourModel < ApplicationRecord
  # Call acts_as_approvable to set up the association and methods
  acts_as_approvable
  
  # ... other attributes and methods
  
  # Implement the required methods
  def approval_action
    "your_action_name"  # Must match a workflow action in Core
  end
  
  def system_name
    "<%= system_name %>"  # Your service name (core, procure, people, case_manager)
  end
  
  # Optional: Handle approval status changes
  def on_approval_status_change(new_status, previous_status)
    case new_status.to_sym
    when :approved
      update_column(:status, :approved)
      # Additional logic for approved state
    when :rejected
      update_column(:status, :rejected)
      # Additional logic for rejected state
    end
  end
end
```

### 3. Submit for Approval

To submit a record for approval:

```ruby
your_model = YourModel.create!(...)
your_model.submit_for_approval(current_user, project)  # project is optional
```

### 4. Check Approval Status

```ruby
# Check if approved in the approval workflow
your_model.approval_approved?  # => true/false

# Check if rejected in the approval workflow
your_model.approval_rejected?  # => true/false

# Check if pending approval in the approval workflow
your_model.approval_pending?  # => true/false
```

### 5. Approve or Reject

```ruby
# Approve the request
result = your_model.approval_approve!(current_user.id, "Looks good!")

# Reject the request
result = your_model.approval_reject!(current_user.id, "Needs revision")

# Add a comment
result = your_model.approval_comment!(current_user.id, "Please provide more details")
```

### 6. Find Approvable Objects

```ruby
# Find all objects pending approval for a specific user
YourModel.pending_approval_for(current_user.id)

# Find all objects approved in the approval workflow
YourModel.approval_workflow_approved

# Find all objects rejected in the approval workflow
YourModel.approval_workflow_rejected

# Find all objects pending approval in the approval workflow
YourModel.approval_workflow_pending
```

### 7. Customize Status Change Callbacks

You can customize how your model responds to approval status changes:

```ruby
# Using a custom method name
class CustomModel < ApplicationRecord
  acts_as_approvable(on_status_change: :handle_approval_status)
  
  def handle_approval_status(new_status, previous_status)
    # Your custom logic here
  end
end

# Using a proc or lambda
class SimpleModel < ApplicationRecord
  acts_as_approvable(on_status_change: ->(new_status, previous_status) {
    update_column(:status, new_status)
  })
end
```

## Documentation

For more detailed documentation, please refer to:

1. The [Approval System Documentation](/services/core/approval_system_documentation.md) in the Core service
2. The [README.md](/gems/commons-gem/lib/generators/athar/commons/approval/README.md) in the commons-gem

## Support

If you encounter any issues or have questions, please contact the Core team.
