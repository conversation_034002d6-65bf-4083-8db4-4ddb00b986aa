class ApprovalRequestSerializer
  include JSONAPI::Serializer

  attributes :workflow_id, :workflow_name, :status, :created_at, :updated_at

  attribute :approvable do |object|
    {
      id: object.approvable_id,
      type: object.approvable_type
    }
  end

  attribute :requestor do |object|
<% if options[:use_foreign_keys] -%>
    {
      id: object.requestor_id,
      name: object.requestor.name
    }
<% else -%>
    # Load requestor from Core service if needed
    object.load_requestor unless object.requestor
    if object.requestor
      {
        id: object.requestor_id,
        name: object.requestor.name
      }
    else
      {
        id: object.requestor_id,
        name: "Unknown User"
      }
    end
<% end -%>
  end
<% if options[:project_based] -%>
  attribute :project do |object|
<% if options[:use_foreign_keys] -%>
    if object.project
      {
        id: object.project_id,
        name: object.project.name
      }
    else
      nil
    end
<% else -%>
    # Load project from Core service if needed
    object.load_project if object.project_id && !object.project
    if object.project
      {
        id: object.project_id,
        name: object.project.name
      }
    else
      nil
    end
<% end -%>
  end
<% end -%>

  attribute :current_step do |object|
    step = object.current_step
    if step
      {
        id: step.id,
        name: step.name,
        sequence: step.sequence,
        approval_type: step.approval_type,
        approver_ids: step.approver_ids
      }
    else
      nil
    end
  end

  attribute :steps do |object|
    object.approval_steps.order(sequence: :asc).map do |step|
      {
        id: step.id,
        name: step.name,
        sequence: step.sequence,
        approval_type: step.approval_type,
        complete: step.complete?,
        rejected: step.rejected?,
        actions: step.actions.map do |action|
          {
            id: action.id,
            user_id: action.user_id,
            action: action.action,
            comment: action.comment,
            created_at: action.created_at
          }
        end
      }
    end
  end
end
