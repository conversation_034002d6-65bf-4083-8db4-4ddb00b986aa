class CreateApprovalRequests < ActiveRecord::Migration[8.0]
  def change
    create_table :approval_requests do |t|
      t.string :workflow_id, null: false
      t.string :workflow_name, null: false
<% if options[:use_foreign_keys] -%>
      t.references :requestor, null: false, foreign_key: { to_table: :<%= options[:user_table] %> }
<% else -%>
      t.string :requestor_id, null: false
      t.index :requestor_id
<% end -%>
<% if options[:project_based] -%><% if options[:use_foreign_keys] -%>
      t.references :project, null: true, foreign_key: { to_table: :<%= options[:project_table] %> }
<% else -%>
      t.string :project_id, null: true
      t.index :project_id
<% end -%><% end -%>
      t.references :approvable, polymorphic: true, null: false
      t.integer :status, null: false, default: 0
      t.json :steps_data

      t.timestamps
    end
  end
end
