class ApprovalStep < ApplicationRecord
  belongs_to :approval_request
  has_many :approval_actions, dependent: :destroy

  include Athar::Commons::Models::Concerns::LevelBasedApproval

  validates :step_id, presence: true
  validates :name, presence: true
  validates :sequence, presence: true, numericality: { only_integer: true }
  validates :approval_type, presence: true, inclusion: { in: %w[any all] }

  # Check if all approvers have acted on this step
  def complete?
    if approval_type == "any"
      # For 'any' type, one approval/rejection completes the step
      approval_request.approval_actions.exists?(approval_step: self, action: %w[approve reject])
    else
      # For 'all' type, all potential approvers must have acted
      acted_approvers = approval_request.approval_actions
                                        .where(approval_step: self, action: %w[approve reject])
                                        .pluck(:user_id)

      approver_ids.all? { |id| acted_approvers.include?(id) }
    end
  end

  # Check if this step was rejected
  def rejected?
    approval_request.approval_actions.exists?(approval_step: self, action: "reject")
  end

  # Get all actions for this step
  def actions
    approval_request.approval_actions.where(approval_step: self).order(created_at: :asc)
  end
end
