# frozen_string_literal: true

# Custom validators for Apipie are automatically registered by the Athar::Commons gem.
# This file is for any additional custom validators you might want to add.

# Example of a custom validator:
#
# class CustomValidator < Apipie::Validator::BaseValidator
#   def initialize(param_description, argument)
#     super(param_description)
#     @type = argument
#   end
#
#   def validate(value)
#     # Your validation logic here
#     true
#   end
#
#   def self.build(param_description, argument, options, block)
#     if argument == :custom_type
#       self.new(param_description, argument)
#     end
#   end
#
#   def description
#     "Must be a valid custom type."
#   end
# end
#
# Apipie.app.validator_engine.register_validator(CustomValidator)
