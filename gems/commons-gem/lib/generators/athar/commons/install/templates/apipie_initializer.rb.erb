# frozen_string_literal: true

::Apipie.configure do |config|
  config.app_name = "  <%= Rails.application.class.module_parent_name %>   "
  config.api_base_url = "/api"
  config.doc_base_url = "/apipie"
  config.default_version = "1.0"

  # Disable validation to avoid issues with JSON:API parameters like page[size]
  # config.validate = false

  # where is your API defined?
  config.api_controllers_matcher = "#{Rails.root}/app/controllers/api/**/*.rb"

  # Add a custom introduction to the documentation
  config.app_info = <<-HTML
    <h2>JSON:API Format</h2>
    <p>This API follows the <a href="https://jsonapi.org" target="_blank">JSON:API specification</a>. All responses are formatted according to this standard.</p>

    <h3>Response Format</h3>
    <p>Responses include:</p>
    <ul>
      <li><code>data</code>: The primary resource(s) being returned</li>
      <li><code>included</code>: Related resources included in the response (when requested)</li>
      <li><code>meta</code>: Additional information about the response (e.g., pagination details)</li>
    </ul>

    <h3>Common Features</h3>

    <h4>Authentication</h4>
    <p>Most endpoints require authentication using a Bearer token in the Authorization header:</p>
    <pre>Authorization: Bearer your-token-here</pre>

    <h4>Error Responses</h4>
    <p>Error responses follow the JSON:API specification:</p>
    <pre>{
  "errors": [
    {
      "status": "422",
      "title": "Validation Error",
      "detail": "Name can't be blank"
    }
  ]
}</pre>

    <h4>Includes</h4>
    <p>You can include related resources using the <code>include</code> query parameter:</p>
    <pre>?include=relationship1,relationship2</pre>

    <h4>Sparse Fieldsets</h4>
    <p>You can request specific fields using the <code>fields</code> query parameter:</p>
    <pre>?fields[resource_type]=field1,field2</pre>

    <h4>Filtering</h4>
    <p>You can filter resources using the <code>filter</code> query parameter:</p>
    <pre>?filter[attribute]=value</pre>
    <p>Advanced filtering with Ransack operators is supported:</p>
    <pre>?filter[attribute_cont]=value</pre>
    <pre>?filter[attribute_eq]=value</pre>
    <pre>?filter[attribute_in]=value1,value2</pre>
    <p>For a complete list of available search matchers, see the <a href="https://activerecord-hackery.github.io/ransack/getting-started/search-matches/" target="_blank">Ransack documentation</a>.</p>

    <h4>Sorting</h4>
    <p>You can sort resources using the <code>sort</code> query parameter:</p>
    <pre>?sort=attribute</pre>
    <p>For descending order, prefix the attribute with a minus sign:</p>
    <pre>?sort=-attribute</pre>
    <p>Multiple sort criteria can be specified:</p>
    <pre>?sort=attribute1,-attribute2</pre>

    <h4>Pagination</h4>
    <p>This API supports JSON:API compliant pagination using the <code>page</code> query parameters:</p>
    <pre>?page[number]=1&amp;page[size]=20</pre>

    <p><strong>Parameters:</strong></p>
    <ul>
      <li><code>page[number]</code>: The page number to retrieve (default: 1)</li>
      <li><code>page[size]</code>: The number of items per page (default: 20, max: 100)</li>
    </ul>

    <p><strong>Example Request:</strong></p>
    <pre>GET /api/employees?page[number]=2&amp;page[size]=10</pre>

    <p><strong>Pagination Metadata:</strong></p>
    <p>Pagination information is included in the <code>meta.pagination</code> section of the response:</p>
    <pre>{
      "data": [...],
      "meta": {
        "pagination": {
          "count": 10,       // Total number of records
          "page": 1,         // Current page number
          "limit": 20,       // Items per page
          "from": 1,         // First record number on this page
          "to": 10          // Last record number on this page
        }
      }
    }</pre>

    <p><strong>Notes:</strong></p>
    <ul>
      <li>If <code>page[size]</code> exceeds the maximum allowed value (100), it will be capped at that maximum.</li>
      <li>If <code>page[number]</code> exceeds the total number of pages, an empty data array will be returned.</li>
      <li>Both parameters are optional. If omitted, defaults will be used (page=1, size=20).</li>
      <li>All query parameters are received as strings (HTTP limitation) but are automatically converted to their proper types. For example, <code>page[number]</code> and <code>page[size]</code> are converted to integers.</li>
    </ul>
  HTML

  # Customize the layout
  # You can choose between different markup processors:
  # - For Markdown (recommended): config.markup = ::Apipie::Markup::Markdown.new
  # - For RDoc: config.markup = ::Apipie::Markup::RDoc.new
  config.markup = ::Apipie::Markup::Markdown.new
end
