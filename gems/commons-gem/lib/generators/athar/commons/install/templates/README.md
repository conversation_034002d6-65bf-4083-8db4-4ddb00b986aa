# Athar Commons Installation

The Athar Commons gem has been installed in your application. This gem provides common functionality for JSON:API-based Rails applications.

## Next Steps:

1. **Create Serializers**:
   Create serializers for your models using JSONAPI::Serializer:

   ```ruby
   class YourModelSerializer
     include JSONAPI::Serializer
     
     attributes :attribute1, :attribute2
     
     # Add any custom attributes or relationships
     attribute :custom_attribute do |resource|
       resource.calculate_something
     end
     
     belongs_to :related_model
   end
   ```

2. **Create Resource Controllers**:
   Create controllers for your resources inheriting from the base controller:

   ```ruby
   module Api
     class YourResourcesController < ApplicationController
       include Athar::Commons::Api::JsonapiDocs
       
       # Your controller actions...
     end
   end
   ```

3. **Document Your API**:
   Use Apipie to document your API endpoints:

   ```ruby
   module Api
     class YourResourcesController < ApplicationController
       api! "List resources"
       param_group :pagination_params
       param_group :filter_params
       param_group :sort_params
       description self.jsonapi_with_docs(<<-HTML
         <b>Endpoint Details</b>
         Returns a list of resources.
       HTML
       )
       def index
         # ...
       end
     end
   end
   ```

4. **Configure Ransack for Filtering**:
   If you're using filtering, make sure to configure Ransack in your models:

   ```ruby
   class YourModel < ApplicationRecord
     def self.ransackable_attributes(auth_object = nil)
       %w[id name created_at updated_at]
     end
     
     def self.ransackable_associations(auth_object = nil)
       %w[related_model]
     end
   end
   ```

5. **Implement Full-Text Search**:
   To enable full-text search, implement one of these methods in your model:

   ```ruby
   class YourModel < ApplicationRecord
     def self.search(query)
       where("name ILIKE ?", "%#{query}%")
     end
     
     # Or for PostgreSQL full-text search:
     def self.full_text_search(query)
       where("to_tsvector('english', name) @@ plainto_tsquery('english', ?)", query)
     end
   end
   ```

## Documentation

Visit `/apipie` in your browser to see the API documentation.
