# frozen_string_literal: true

module Athar
  module Commons
    module Generators
      class InstallGenerator < Rails::Generators::Base
        source_root File.expand_path('templates', __dir__)

        desc "Installs Athar Commons configuration files"

        def copy_initializers
          template "jsonapi_initializer.rb.erb", "config/initializers/jsonapi.rb"
          template "apipie_initializer.rb.erb", "config/initializers/apipie.rb"
          template "pagy_initializer.rb.erb", "config/initializers/pagy.rb"
          # template "apipie_validators_initializer.rb.erb", "config/initializers/apipie_validators.rb"
        end

        def add_routes
          route "apipie"
        end

        def show_readme
          readme "README.md"
        end
      end
    end
  end
end
