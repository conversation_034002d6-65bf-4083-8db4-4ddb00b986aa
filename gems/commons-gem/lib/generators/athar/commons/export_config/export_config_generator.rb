# frozen_string_literal: true

module Athar
  module Commons
    module Generators
      class ExportConfigGenerator < Rails::Generators::Base
        desc "Generate export configuration and MIME types for Athar Commons"

        def self.source_root
          @source_root ||= File.expand_path("templates", __dir__)
        end

        def create_export_config_initializer
          template "export_config.rb", "config/initializers/athar_commons_export.rb"
        end

        def create_mime_types_initializer
          template "mime_types.rb", "config/initializers/mime_types.rb"
        end

        def show_completion_message
          say "\n✅ Export functionality configured successfully!", :green
          say "   • Export configuration: config/initializers/athar_commons_export.rb", :green
          say "   • MIME types: config/initializers/mime_types.rb", :green
          say "\n📝 Next steps:", :yellow
          say "   1. Restart your Rails server"
          say "   2. Export endpoints are now available (e.g., /api/employees.csv)"
          say "   3. Customize export limits in athar_commons_export.rb if needed"
        end
      end
    end
  end
end
