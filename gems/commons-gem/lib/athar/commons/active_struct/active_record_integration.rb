# frozen_string_literal: true

module Athar
  module Commons
    module ActiveStruct
      # Module to integrate ActiveStruct associations with ActiveRecord reflection
      # This allows ActiveRecord models that use ActiveStruct associations to be
      # properly recognized by systems that rely on ActiveRecord reflection
      module ActiveRecordIntegration
        extend ActiveSupport::Concern

        # When this module is included in an ActiveRecord model,
        # it enhances the reflection methods to include ActiveStruct associations
        included do
          # Store original reflection methods to avoid infinite recursion
          class << self
            alias_method :original_reflect_on_association, :reflect_on_association unless method_defined?(:original_reflect_on_association)
            alias_method :original_reflect_on_all_associations, :reflect_on_all_associations unless method_defined?(:original_reflect_on_all_associations)
          end
        end

        class_methods do
          # Enhanced reflect_on_association that includes ActiveStruct associations
          def reflect_on_association(name)
            # First try the original ActiveRecord reflection
            association = original_reflect_on_association(name)
            return association if association

            # If not found, check if it's an ActiveStruct association method
            activestruct_association = find_activestruct_association(name)
            return activestruct_association if activestruct_association

            nil
          end

          # Enhanced reflect_on_all_associations that includes ActiveStruct associations
          def reflect_on_all_associations(macro = nil)
            # Get original ActiveRecord associations
            associations = original_reflect_on_all_associations(macro)

            # Add ActiveStruct associations
            activestruct_associations = find_all_activestruct_associations(macro)
            associations + activestruct_associations
          end

          private

          # Find a specific ActiveStruct association by name
          def find_activestruct_association(name)
            method_name = name.to_s

            # Check if the method exists and returns an ActiveStruct collection or object
            return nil unless method_defined?(method_name)

            # Try to determine the association type by examining the method
            association_info = detect_activestruct_association_info(method_name)
            return nil unless association_info

            # Create a mock ActiveRecord association reflection
            create_mock_association_reflection(name, association_info)
          end

          # Find all ActiveStruct associations
          def find_all_activestruct_associations(macro = nil)
            associations = []

            # Only check known ActiveStruct association methods to avoid infinite recursion
            known_activestruct_methods = ['user_roles', 'user_roles_list']

            known_activestruct_methods.each do |method_name|
              next unless method_defined?(method_name)

              association_info = detect_activestruct_association_info(method_name)
              next unless association_info

              # Filter by macro if specified
              if macro.nil? || association_info[:macro] == macro
                mock_association = create_mock_association_reflection(method_name.to_sym, association_info)
                associations << mock_association if mock_association
              end
            end

            associations
          end

          # Detect if a method is an ActiveStruct association and determine its type
          def detect_activestruct_association_info(method_name)
            # Special handling for known ActiveStruct association patterns
            case method_name
            when 'user_roles'
              # Known ActiveStruct collection via ActiveRpc
              {
                macro: :has_many,
                class_name: 'Employees::UserRole',
                klass: defined?(Employees::UserRole) ? Employees::UserRole : nil
              }
            when 'user_roles_list'
              # Known ActiveRpc attribute that returns ActiveStruct collection
              {
                macro: :has_many,
                class_name: 'Employees::UserRole',
                klass: defined?(Employees::UserRole) ? Employees::UserRole : nil
              }
            else
              # Try to detect based on method name patterns
              if method_name.end_with?('_list') || method_name.pluralize == method_name
                # Likely a collection
                {
                  macro: :has_many,
                  class_name: method_name.singularize.camelize,
                  klass: nil
                }
              elsif method_name.singularize != method_name
                # Likely a single association
                {
                  macro: :belongs_to,
                  class_name: method_name.camelize,
                  klass: nil
                }
              else
                nil
              end
            end
          end

          # Create a mock ActiveRecord association reflection object
          def create_mock_association_reflection(name, association_info)
            OpenStruct.new(
              name: name.to_sym,
              macro: association_info[:macro],
              options: { class_name: association_info[:class_name] },
              klass: association_info[:klass],
              active_record: self,
              plural_name: association_info[:macro] == :has_many ? name.to_s : name.to_s.pluralize,
              collection?: association_info[:macro] == :has_many,
              belongs_to?: association_info[:macro] == :belongs_to,
              has_many?: association_info[:macro] == :has_many,
              has_one?: association_info[:macro] == :has_one
            )
          end
        end
      end
    end
  end
end
