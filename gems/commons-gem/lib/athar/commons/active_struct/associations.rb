# frozen_string_literal: true

require 'ostruct'

module Athar
  module Commons
    module ActiveStruct
      # Associations module for ActiveStruct objects
      # Provides ActiveRecord-like association methods
      module Associations
        extend ActiveSupport::Concern

        included do
          class_attribute :_associations, instance_writer: false, default: {}
        end

        module ClassMethods
          # Define a belongs_to association
          def belongs_to(name, options = {})
            # Store association metadata for reflection
            _associations[name] = { type: :belongs_to, options: options }

            # Determine if the target is an ActiveRecord model or an ActiveStruct
            target_class_name = options[:class_name] || name.to_s.classify
            target_class = target_class_name.safe_constantize

            if target_class && defined?(ActiveRecord::Base) && target_class < ActiveRecord::Base
              # ActiveRecord target
              belongs_to_active_record(name, options)
            else
              # ActiveStruct target
              belongs_to_active_struct(name, options)
            end
          end

          # Define a has_many association
          def has_many(name, options = {})
            # Store association metadata for reflection
            _associations[name] = { type: :has_many, options: options }

            if options[:through]
              # Handle has_many through
              has_many_through_implementation(name, options)
            else
              # Handle regular has_many
              has_many_regular_implementation(name, options)
            end
          end

          # Define a has_one association
          def has_one(name, options = {})
            # Store association metadata for reflection
            _associations[name] = { type: :has_one, options: options }

            if options[:through]
              # Handle has_one through
              has_one_through_implementation(name, options)
            else
              # Handle regular has_one
              has_one_regular_implementation(name, options)
            end
          end

          def has_one_regular_implementation(name, options)
            # Store association configuration
            association_name = name.to_s
            class_name = options[:class_name] || association_name.classify

            # Define the getter method
            define_method(association_name) do
              # Lazy-initialize the association
              @association_cache ||= {}
              @association_cache[association_name]
            end

            # Define the setter method
            define_method("#{association_name}=") do |value|
              @association_cache ||= {}

              @association_cache[association_name] = if value.nil?
                                                       nil
                                                     elsif value.is_a?(class_name.constantize)
                                                       value
                                                     else
                                                       # Create a new instance
                                                       class_name.constantize.new(value)
                                                     end
            end

            # Define _id getter method
            define_method("#{association_name}_id") do
              send(association_name)&.id
            end

            # Define _id setter method
            define_method("#{association_name}_id=") do |id|
              if id.present?
                # Create a new instance with just the ID
                send("#{association_name}=", { id: id })
              else
                send("#{association_name}=", nil)
              end
            end
          end

          # Support for reflection-like API
          def reflect_on_all_associations(macro = nil)
            associations = _associations.map do |name, config|
              next if macro && config[:type] != macro

              OpenStruct.new(
                name: name,
                macro: config[:type],
                options: config[:options],
                klass: begin
                  class_name = config[:options][:class_name] || name.to_s.classify
                  class_name.constantize
                rescue StandardError
                  nil
                end
              )
            end

            associations.compact
          end

          def reflect_on_association(name)
            config = _associations[name.to_sym]
            return nil unless config

            OpenStruct.new(
              name: name,
              macro: config[:type],
              options: config[:options],
              klass: begin
                class_name = config[:options][:class_name] || name.to_s.classify
                class_name.constantize
              rescue StandardError
                nil
              end
            )
          end

          private

          def has_many_through_implementation(name, options)
            through_association = options[:through]
            source_association = options[:source] || name.to_s

            association_name = name.to_s
            singular_name = association_name.singularize

            # Define the getter method that returns ActiveRecord relation or Collection
            define_method(association_name) do
              # Get the through association
              begin
                through_object = send(through_association)
              rescue NoMethodError
                # Through association doesn't exist
                return []
              end

              return [] unless through_object

              # If through_object is ActiveRecord, return its association directly
              if defined?(ActiveRecord::Base) && through_object.class < ActiveRecord::Base
                through_object.send(source_association)
              elsif through_object.respond_to?(:flat_map)
                # Handle ActiveStruct through objects
                through_object.flat_map { |obj| Array(obj.send(source_association)) }.compact
              else
                Array(through_object.send(source_association))
              end
            end

            # Define _ids getter method
            define_method("#{singular_name}_ids") do
              association = send(association_name)
              if association.respond_to?(:pluck)
                # ActiveRecord relation
                association.pluck(:id)
              else
                # Array or Collection
                association.map(&:id).compact
              end
            end

            # NOTE: We don't define setter methods for through associations
            # as they should be managed through the through association
          end

          def has_one_through_implementation(name, options)
            through_association = options[:through]
            source_association = options[:source] || name.to_s
            conditions = options[:conditions] || {}

            association_name = name.to_s

            # Define the getter method
            define_method(association_name) do
              # Get the through association
              begin
                through_object = send(through_association)
              rescue NoMethodError
                # Through association doesn't exist
                return nil
              end

              return nil unless through_object

              # If through_object is ActiveRecord, return its association directly
              if defined?(ActiveRecord::Base) && through_object.class < ActiveRecord::Base
                result = through_object.send(source_association)

                # Apply conditions if specified
                if conditions.any?
                  # Evaluate conditions in the context of this object
                  evaluated_conditions = conditions.transform_values do |value|
                    if value.is_a?(Symbol)
                      send(value)
                    elsif value.respond_to?(:call)
                      instance_eval(&value)
                    else
                      value
                    end
                  end

                  result = result.where(evaluated_conditions)
                end

                # For has_one, get the first record if it's a relation
                if result.respond_to?(:first)
                  result.first
                else
                  result
                end
              else
                # Handle ActiveStruct through objects
                result = through_object.send(source_association)

                # Apply conditions for non-ActiveRecord objects
                if conditions.any? && result.respond_to?(:find)
                  evaluated_conditions = conditions.transform_values do |value|
                    if value.is_a?(Symbol)
                      send(value)
                    elsif value.respond_to?(:call)
                      instance_eval(&value)
                    else
                      value
                    end
                  end

                  result.find do |item|
                    evaluated_conditions.all? do |key, val|
                      item_val = item.respond_to?(key) ? item.send(key) : item[key]
                      item_val == val
                    end
                  end
                else
                  result
                end
              end
            end

            # Define _id getter method
            define_method("#{association_name}_id") do
              association = send(association_name)
              association&.id
            end

            # NOTE: We don't define setter methods for through associations
            # as they should be managed through the through association
          end

          def has_many_regular_implementation(name, options)
            # Store association configuration
            association_name = name.to_s
            singular_name = association_name.singularize
            class_name = options[:class_name] || singular_name.classify

            # Check if the target class is an ActiveRecord model
            target_class = class_name.constantize

            if defined?(ActiveRecord::Base) && target_class < ActiveRecord::Base
              # Handle has_many to ActiveRecord models
              has_many_active_record(name, options)
            else
              # Handle has_many to ActiveStruct models (existing logic)
              # Define the getter method
              define_method(association_name) do
                # Lazy-initialize the collection
                @association_cache ||= {}
                @association_cache[association_name] ||= begin
                  # Create a collection instance
                  collection_class = "#{class_name}Collection".constantize
                  collection_class.new(id, self.class.name.demodulize.underscore)
                end
              end

              # Define the setter method
              define_method("#{association_name}=") do |value|
                collection = send(association_name)
                collection.clear

                Array(value).each do |item|
                  collection << item
                end
              end

              # Define _ids getter method
              define_method("#{singular_name}_ids") do
                send(association_name).ids
              end

              # Define _ids setter method
              define_method("#{singular_name}_ids=") do |ids|
                send(association_name).ids = ids
              end
            end
          rescue NameError => e
            if defined?(target_class) && defined?(ActiveRecord::Base) && target_class < ActiveRecord::Base
              raise "ActiveRecord association setup failed: #{e.message}"
            end

            raise "Collection class #{class_name}Collection not found. Please define it or use a different association type."
          end

          def has_many_active_record(name, options = {})
            # Store association configuration
            association_name = name.to_s
            singular_name = association_name.singularize
            class_name = options[:class_name] || singular_name.classify
            foreign_key = options[:foreign_key] || "#{self.name.demodulize.underscore}_id"
            primary_key = options[:primary_key] || 'id'

            # Define the getter method
            define_method(association_name) do
              # Get the foreign key value from this ActiveStruct instance
              primary_key_value = send(primary_key)
              return [] if primary_key_value.blank?

              # Query the ActiveRecord model
              klass = class_name.constantize
              klass.where(foreign_key => primary_key_value)
            end

            # Define the setter method (basic implementation)
            define_method("#{association_name}=") do |_value|
              # This is a simplified setter - in practice, you might want more sophisticated handling
              Rails.logger.warn("Setting #{association_name} on ActiveStruct is not fully implemented")
            end

            # Define _ids getter method
            define_method("#{singular_name}_ids") do
              send(association_name).pluck(:id)
            end

            # Define _ids setter method
            define_method("#{singular_name}_ids=") do |_ids|
              Rails.logger.warn("Setting #{singular_name}_ids on ActiveStruct is not fully implemented")
            end
          end

          def belongs_to_active_record(name, options = {})
            # Support custom foreign key and primary key
            foreign_key_attr = options[:foreign_key] || "#{name}_id"
            primary_key = options[:primary_key] || 'id'
            class_name = options[:class_name] || name.to_s.classify

            # Determine the foreign key data type based on the target model's primary key
            foreign_key_type = if options[:primary_key]
                                 # For custom primary keys, determine type from the target column
                                 determine_custom_foreign_key_type(class_name, primary_key)
                               else
                                 # Standard ID foreign key
                                 determine_foreign_key_type(class_name)
                               end

            # Define the foreign key attribute with the appropriate type
            attribute foreign_key_attr, foreign_key_type

            # Define the getter method
            define_method(name) do
              foreign_key_value = send(foreign_key_attr)
              return nil if foreign_key_value.blank?

              # Determine the class
              klass = class_name.constantize

              # Fetch from the database using custom primary key
              klass.find_by(primary_key => foreign_key_value)
            end

            # Define the setter method
            define_method("#{name}=") do |value|
              if value.nil?
                send("#{foreign_key_attr}=", nil)
              elsif value.respond_to?(primary_key)
                send("#{foreign_key_attr}=", value.send(primary_key))
              elsif value.is_a?(Hash) && value.key?(primary_key)
                send("#{foreign_key_attr}=", value[primary_key])
              elsif value.is_a?(Hash) && value.key?(primary_key.to_s)
                send("#{foreign_key_attr}=", value[primary_key.to_s])
              end
            end
          end

          def belongs_to_active_struct(name, options = {})
            # Auto-infer foreign key type from target model's primary key
            target_class_name = options[:class_name] || name.to_s.classify
            target_class = target_class_name.safe_constantize

            foreign_key_type = if target_class && target_class.respond_to?(:attribute_types)
                                 primary_key = options[:primary_key] || 'id'
                                 target_class.attribute_types[primary_key]&.type || :string
                               else
                                 options[:foreign_key_type] || :string
                               end

            # Define the foreign key attribute with the inferred type
            attribute "#{name}_id", foreign_key_type

            # Define the getter method
            define_method(name) do
              # Store the referenced object in an instance variable
              # This is a simplified approach - in a more complex implementation,
              # you might want to use a registry or other lookup mechanism
              instance_variable_get("@#{name}")
            end

            # Define the setter method
            define_method("#{name}=") do |value|
              if value.nil?
                send("#{name}_id=", nil)
                instance_variable_set("@#{name}", nil)
              elsif value.respond_to?(:id)
                send("#{name}_id=", value.id)
                instance_variable_set("@#{name}", value)
              elsif value.is_a?(Hash)
                # Create a new instance
                class_name = options[:class_name] || name.to_s.classify

                # Try to find the class in the current namespace first
                klass = begin
                  # Get the module parent of the current class
                  module_parent = self.class.name.deconstantize.constantize
                  # Try to get the constant from the parent module
                  module_parent.const_get(class_name)
                rescue NameError
                  # If not found, try to constantize the full name
                  class_name.constantize
                end

                instance = klass.new(value)
                send("#{name}_id=", instance.id)
                instance_variable_set("@#{name}", instance)
              end
            end
          end

          def determine_foreign_key_type(class_name)
            klass = class_name.constantize

            if klass.respond_to?(:primary_key) && klass.columns_hash.key?(klass.primary_key)
              # Get the ActiveRecord column type
              column = klass.columns_hash[klass.primary_key]

              # Map the SQL type to an ActiveModel type
              case column.type
              when :integer, :bigint
                :integer
              when :uuid, :string, :text
                :string
              else
                # Default to string for unknown types
                :string
              end
            else
              # Default to integer for ActiveRecord models (most common)
              :integer
            end
          rescue StandardError => e
            # If anything goes wrong, default to string (most flexible)
            Rails.logger.warn("Error determining foreign key type for #{class_name}: #{e.message}")
            :string
          end

          def determine_custom_foreign_key_type(class_name, column_name)
            klass = class_name.constantize

            if klass.respond_to?(:columns_hash) && klass.columns_hash.key?(column_name.to_s)
              # Get the ActiveRecord column type for the custom primary key
              column = klass.columns_hash[column_name.to_s]

              # Map the SQL type to an ActiveModel type
              case column.type
              when :integer, :bigint
                :integer
              when :uuid, :string, :text
                :string
              else
                # Default to string for unknown types
                :string
              end
            else
              # Default to string for custom primary keys
              :string
            end
          rescue StandardError => e
            # If anything goes wrong, default to string (most flexible)
            Rails.logger.warn("Error determining custom foreign key type for #{class_name}.#{column_name}: #{e.message}")
            :string
          end
        end
      end
    end
  end
end
