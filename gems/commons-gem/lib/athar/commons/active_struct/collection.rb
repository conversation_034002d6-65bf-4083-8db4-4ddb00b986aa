# frozen_string_literal: true

module Athar
  module Commons
    module ActiveStruct
      # === Filter Matcher ===
      # Unified filtering logic for collections
      class FilterMatcher
        OPERATORS = {
          'eq' => :equals,
          'not_eq' => :not_equals,
          'in' => :in_array,
          'not_in' => :not_in_array,
          'matches' => :matches,
          'like' => :matches,
          'gt' => :greater_than,
          'lt' => :less_than,
          'gteq' => :greater_than_or_equal,
          'lteq' => :less_than_or_equal
        }.freeze

        def self.matches?(item, key, value)
          if key.to_s.include?('_')
            field, operator = key.to_s.split('_', 2)
            return false unless OPERATORS.key?(operator)
            return false unless item.respond_to?(field)

            item_value = item.public_send(field)
            apply_operator(item_value, value, OPERATORS[operator])
          else
            return false unless item.respond_to?(key)

            item_value = item.public_send(key)
            apply_operator(item_value, value, :equals)
          end
        rescue StandardError
          false
        end

        def self.apply_operator(item_value, filter_value, operator)
          case operator
          when :equals
            item_value == filter_value
          when :not_equals
            item_value != filter_value
          when :in_array
            Array(filter_value).include?(item_value)
          when :not_in_array
            !Array(filter_value).include?(item_value)
          when :matches
            item_value.to_s.include?(filter_value.to_s)
          when :greater_than
            item_value > filter_value
          when :less_than
            item_value < filter_value
          when :greater_than_or_equal
            item_value >= filter_value
          when :less_than_or_equal
            item_value <= filter_value
          else
            false
          end
        rescue StandardError
          false
        end
      end

      # === Query Engine ===
      # Handles lazy evaluation and optimization of collection queries
      class QueryEngine
        attr_reader :items, :filters, :sorts, :offset_value, :limit_value

        def initialize(items)
          @items = items
          @filters = []
          @sorts = []
          @offset_value = 0
          @limit_value = nil
          @cached_result = nil
          @cache_valid = false
        end

        def where(conditions)
          new_engine = dup_engine
          conditions.each { |key, value| new_engine.add_filter(key, value) }
          new_engine
        end

        def order(field_or_hash)
          new_engine = dup_engine
          if field_or_hash.is_a?(Hash)
            field, direction = field_or_hash.first
          else
            field = field_or_hash.to_s
            direction = :asc
          end

          if field.start_with?('-')
            field = field[1..]
            direction = :desc
          end

          new_engine.add_sort(field, direction)
          new_engine
        end

        def offset(value)
          new_engine = dup_engine
          new_engine.set_offset(value.to_i)
          new_engine
        end

        def limit(value)
          new_engine = dup_engine
          new_engine.set_limit(value.to_i)
          new_engine
        end

        def to_a
          return @cached_result.dup if @cache_valid && @cached_result

          result = apply_filters(@items)
          result = apply_sorts(result)
          result = apply_pagination(result)

          @cached_result = result.freeze
          @cache_valid = true
          result.dup
        end

        def each(&block)
          return to_enum(:each) unless block_given?

          to_a.each(&block)
        end

        delegate :size, to: :to_a

        delegate :first, to: :to_a

        delegate :last, to: :to_a

        delegate :empty?, to: :to_a

        protected

        def add_filter(key, value)
          @filters << [key, value]
          invalidate_cache
        end

        def add_sort(field, direction)
          @sorts << [field, direction]
          invalidate_cache
        end

        def set_offset(value)
          @offset_value = value
          invalidate_cache
        end

        def set_limit(value)
          @limit_value = value
          invalidate_cache
        end

        private

        def dup_engine
          new_engine = self.class.new(@items)
          new_engine.instance_variable_set(:@filters, @filters.dup)
          new_engine.instance_variable_set(:@sorts, @sorts.dup)
          new_engine.instance_variable_set(:@offset_value, @offset_value)
          new_engine.instance_variable_set(:@limit_value, @limit_value)
          new_engine
        end

        def apply_filters(items)
          return items if @filters.empty?

          items.select do |item|
            @filters.all? { |key, value| FilterMatcher.matches?(item, key, value) }
          end
        end

        def apply_sorts(items)
          return items if @sorts.empty?

          @sorts.reduce(items) do |sorted_items, (field, direction)|
            sorted = sorted_items.sort_by do |item|
              item.respond_to?(field) ? item.public_send(field) : nil
            end
            direction.to_s.downcase == 'desc' ? sorted.reverse : sorted
          end
        end

        def apply_pagination(items)
          return items if @offset_value == 0 && @limit_value.nil?

          start_index = @offset_value
          end_index = @limit_value ? start_index + @limit_value - 1 : -1

          items[start_index..end_index] || []
        end

        def invalidate_cache
          @cache_valid = false
          @cached_result = nil
        end
      end

      # Collection module for groups of structured data objects
      # Implements Enumerable and provides methods for finding, filtering, and manipulating collections
      module Collection
        extend ActiveSupport::Concern

        included do
          include Enumerable
          attr_reader :items, :owner_id, :owner_type
          attr_accessor :changed
        end

        # === Initialization ===
        def initialize(owner_id = nil, owner_type = nil, items = [])
          @owner_id = owner_id
          @owner_type = owner_type
          @items = []
          @query_engine = nil
          @changed = false
          Array(items).each { |item| self << item }
          @changed = false
        end

        # === Enumerable Implementation ===
        def each(&block)
          return to_enum(:each) unless block_given?

          if @query_engine
            @query_engine.each(&block)
          else
            @items.each(&block)
          end
        end

        # === Collection Manipulation ===
        def <<(item)
          item_obj = if item.is_a?(item_class)
                       item
                     elsif item.is_a?(Array) && item.first.is_a?(Hash)
                       item_class.new(item.first.merge(owner_attributes))
                     elsif item.is_a?(Hash)
                       item_class.new(item.merge(owner_attributes))
                     elsif item.is_a?(ActionController::Parameters)
                       item_class.new(item.to_unsafe_hash.merge(owner_attributes))
                     else
                       item_class.new(owner_attributes)
                     end
          @items << item_obj
          @changed = true
          invalidate_query_engine
          self
        end

        def clear
          old_size = @items.size
          @items = []
          @changed = old_size > 0
          invalidate_query_engine
          self
        end

        # === Query Methods ===

        # Override Enumerable's find to support both behaviors
        def find(id_or_block = nil, &block)
          # If block is given, use Enumerable's find behavior
          if block_given?
            each.find(&block)
          elsif id_or_block.nil?
            # If no arguments, return enumerator (Enumerable behavior)
            each.find
          else
            # If argument is given and no block, treat as ID lookup
            find_by_id(id_or_block)
          end
        end

        def where(conditions = {})
          if @query_engine
            # Chain with existing query engine
            new_collection = self.class.new(owner_id, owner_type)
            new_collection.instance_variable_set(:@items, @items)
            new_collection.instance_variable_set(:@query_engine, @query_engine.where(conditions))
            new_collection
          else
            # Create new query engine
            new_collection = self.class.new(owner_id, owner_type)
            new_collection.instance_variable_set(:@items, @items)
            new_collection.instance_variable_set(:@query_engine, QueryEngine.new(@items).where(conditions))
            new_collection
          end
        end

        def find_by(conditions = {})
          where(conditions).first
        end

        def last
          if @query_engine
            @query_engine.last
          else
            @items.last
          end
        end

        def to_a
          if @query_engine
            @query_engine.to_a
          else
            @items.dup
          end
        end

        def empty?
          if @query_engine
            @query_engine.empty?
          else
            @items.empty?
          end
        end

        def present?
          !empty?
        end

        def size
          if @query_engine
            @query_engine.size
          else
            @items.size
          end
        end

        def method_missing(method_name, *args, &block)
          if method_name.to_s.start_with?('find_by_')
            attribute_name_str = method_name.to_s.delete_prefix('find_by_')

            if attribute_name_str.empty? || args.length != 1
              return super # Not a valid find_by_attribute call
            end

            value_to_find = args.first
            attribute_name_sym = attribute_name_str.to_sym

            return @items.find do |item|
              if item.respond_to?(attribute_name_sym)
                item_value = item.public_send(attribute_name_sym)
                # Compare as strings, consistent with original find_by_id behavior
                item_value.to_s == value_to_find.to_s
              else
                false # Item does not respond to the attribute
              end
            end
          end
          super # Call super for methods not matching find_by_
        end

        def respond_to_missing?(method_name, include_private = false)
          if method_name.to_s.start_with?('find_by_')
            attribute_name_str = method_name.to_s.delete_prefix('find_by_')
            # Respond true if there is an attribute name after 'find_by_'
            !attribute_name_str.empty? || super
          else
            super
          end
        end

        # === ID Management ===
        def ids
          map { |item| item.respond_to?(:id) ? item.id : nil }.compact
        end

        def ids=(ids)
          # Only allow setting IDs if the item class supports IDs
          return unless item_class.respond_to?(:has_id_attribute) && item_class.has_id_attribute

          old_ids = self.ids
          clear
          Array(ids).each { |id| self << { id: id } if id.present? }
          @changed = (old_ids.sort != Array(ids).map(&:to_s).sort)
        end

        # === Pagination & Limiting ===
        def offset(value)
          if @query_engine
            new_collection = self.class.new(owner_id, owner_type)
            new_collection.instance_variable_set(:@items, @items)
            new_collection.instance_variable_set(:@query_engine, @query_engine.offset(value))
            new_collection
          else
            new_collection = self.class.new(owner_id, owner_type)
            new_collection.instance_variable_set(:@items, @items)
            new_collection.instance_variable_set(:@query_engine, QueryEngine.new(@items).offset(value))
            new_collection
          end
        end

        def limit(value)
          if @query_engine
            new_collection = self.class.new(owner_id, owner_type)
            new_collection.instance_variable_set(:@items, @items)
            new_collection.instance_variable_set(:@query_engine, @query_engine.limit(value))
            new_collection
          else
            new_collection = self.class.new(owner_id, owner_type)
            new_collection.instance_variable_set(:@items, @items)
            new_collection.instance_variable_set(:@query_engine, QueryEngine.new(@items).limit(value))
            new_collection
          end
        end

        def page(number)
          PaginationAdapter.new(self, number.to_i, @per_page || 25)
        end

        def per(size)
          new_collection = dup
          new_collection.instance_variable_set(:@per_page, size.to_i)
          new_collection
        end

        # === Serialization ===
        def as_json(_options = nil)
          to_a.map(&:as_json)
        end

        # === Sorting ===
        def order(field_or_hash)
          new_collection = self.class.new(owner_id, owner_type)
          new_collection.instance_variable_set(:@items, @items)

          if @query_engine
            new_collection.instance_variable_set(:@query_engine, @query_engine.order(field_or_hash))
          else
            new_collection.instance_variable_set(:@query_engine, QueryEngine.new(@items).order(field_or_hash))
          end

          new_collection
        end

        # === Miscellaneous ===
        def model
          item_class
        end

        def ransack(params = {})
          RansackAdapter.new(self, params)
        end

        def owner_attributes
          attrs = {}
          attrs["#{owner_type.to_s.underscore}_id".to_sym] = owner_id if owner_id && owner_type
          attrs
        end

        def changed?
          @changed
        end

        def item_class
          raise NotImplementedError, 'Collection classes must implement item_class method'
        end

        # === Class Methods ===
        def self.from_api_params(params, owner_id = nil, owner_type = nil)
          collection = new(owner_id, owner_type)
          Array(params).each { |param| collection << param }
          collection
        end

        module ClassMethods
          def collection_item_class(klass)
            define_method(:item_class) { klass }
          end
        end

        def find_by_id(id)
          return nil unless id.present?

          if @query_engine
            @query_engine.to_a.find { |item| item.respond_to?(:id) && item.id.to_s == id.to_s }
          else
            @items.find { |item| item.respond_to?(:id) && item.id.to_s == id.to_s }
          end
        end

        private

        def invalidate_query_engine
          @query_engine = nil
        end

        # Legacy method - now uses FilterMatcher
        def item_matches?(item, key, value)
          FilterMatcher.matches?(item, key, value)
        end
      end

      # === Ransack Adapter ===
      class RansackAdapter
        attr_accessor :sorts
        attr_reader :collection, :params

        def initialize(collection, params)
          @collection = collection
          @params = params
          @sorts = params.delete(:sorts)
        end

        def result
          filtered_collection = filter_collection
          sorted_collection = sort_collection(filtered_collection)
          @collection.class.new(@collection.owner_id, @collection.owner_type, sorted_collection)
        end

        def filter_collection
          filtered_collection = @collection
          @params.each do |key, value|
            next if value.blank?

            filtered_collection = filtered_collection.select do |item|
              FilterMatcher.matches?(item, key, value)
            end
          end
          filtered_collection
        end

        def sort_collection(collection)
          return collection unless @sorts.present?

          sorted = collection
          Array(@sorts).each do |sort_str|
            field, direction = sort_str.split(' ')
            direction = direction.to_s.downcase == 'desc' ? :desc : :asc
            sorted = sorted.sort_by { |item| item.send(field) if item.respond_to?(field) }
            sorted = sorted.reverse if direction == :desc
          end
          sorted
        end

        def method_missing(method, *args, &block)
          @collection.respond_to?(method) ? @collection.send(method, *args, &block) : super
        end

        def respond_to_missing?(method, include_private = false)
          @collection.respond_to?(method, include_private) || super
        end
      end

      # === Pagination Adapter ===
      class PaginationAdapter
        include Enumerable
        attr_reader :collection, :current_page, :per_page

        def initialize(collection, page, per_page = 25)
          @collection = collection
          @current_page = [ page.to_i, 1 ].max
          @per_page = [ per_page.to_i, 1 ].max
        end

        def total_count
          @collection.size
        end

        def total_pages
          (total_count.to_f / per_page).ceil
        end

        def offset
          (current_page - 1) * per_page
        end

        def limit
          per_page
        end

        def to_a
          @collection.to_a[offset, limit] || []
        end

        def each(&block)
          to_a.each(&block)
        end

        def method_missing(method, *args, &block)
          @collection.respond_to?(method) ? @collection.send(method, *args, &block) : super
        end

        def respond_to_missing?(method, include_private = false)
          @collection.respond_to?(method, include_private) || super
        end
      end
    end
  end
end
