# frozen_string_literal: true

module Athar
  module Commons
    module ActiveStruct
      # Custom ActiveModel type for handling serialization and deserialization of structured data collections
      class Type < ActiveModel::Type::Value
        attr_reader :collection_class_name

        def initialize(collection_class = nil, precision: nil, limit: nil, scale: nil)
          super(precision: precision, limit: limit, scale: scale)

          if collection_class.is_a?(String)
            @collection_class_name = collection_class
          elsif collection_class.is_a?(Class)
            @collection_class = collection_class
            @collection_class_name = collection_class.name
          end
        end

        # Get the collection class, lazily loading it if necessary
        def collection_class
          return @collection_class if @collection_class
          return nil if @collection_class_name.blank?

          begin
            @collection_class = @collection_class_name.constantize
          rescue NameError => e
            Rails.logger.error("Failed to load collection class: #{e.message}")
            nil
          end
        end

        # Cast input to the appropriate format
        # This is called when assigning a value to an attribute
        def cast(value)
          return nil if value.nil?
          return nil if collection_class.nil?

          # If it's already the right type, return it
          return value if value.is_a?(collection_class)

          # If it's an array or hash, convert it
          return collection_class.new(nil, nil, value) if value.is_a?(Array) || value.is_a?(Hash)

          # Default fallback
          collection_class.new
        end

        # Serialize the value for database storage
        # This is called when saving to the database
        def serialize(value)
          return [] if value.nil?

          # Convert to array of hashes
          return value.as_json if value.respond_to?(:as_json)

          # If it's already an array, return it
          return value if value.is_a?(Array)

          # Default fallback
          []
        end

        # Deserialize from database format to Ruby object
        # This is called when loading from the database
        def deserialize(value)
          return nil if value.nil?
          return nil if collection_class.nil?

          # If it's already the right type, return it
          return value if value.is_a?(collection_class)

          # If the value is a JSON string, parse it
          if value.is_a?(String)
            begin
              value = JSON.parse(value)
            rescue JSON::ParserError
              return collection_class.new
            end
          end

          # Convert array to collection
          return collection_class.new(nil, nil, value) if value.is_a?(Array)

          # Default fallback
          collection_class.new
        end

        # Check if two values are equal
        # This is important for dirty tracking
        def changed_in_place?(raw_old_value, new_value)
          # If either value is nil, use standard comparison
          return true if raw_old_value.nil? != new_value.nil?
          return false if raw_old_value.nil? && new_value.nil?

          # Deserialize the old value if needed
          old_value = raw_old_value.is_a?(String) ? deserialize(raw_old_value) : raw_old_value

          # If they're different types, they're different
          return true if old_value.class != new_value.class

          # If they're collections, compare their serialized forms
          if old_value.respond_to?(:as_json) && new_value.respond_to?(:as_json)
            return serialize(old_value) != serialize(new_value)
          end

          # Default to standard comparison
          old_value != new_value
        end
      end
    end
  end
end
