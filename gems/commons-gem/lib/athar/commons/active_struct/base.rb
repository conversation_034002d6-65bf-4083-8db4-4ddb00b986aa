# frozen_string_literal: true

require 'ostruct'

module Athar
  module Commons
    module ActiveStruct
      # Base class for structured data objects
      # Provides common functionality for attribute handling, serialization, and nested attribute support
      class Base
        include ActiveModel::Model
        include ActiveModel::Attributes
        include ActiveModel::Dirty
        include ActiveModel::Serialization
        include Athar::Commons::ActiveStruct::Associations

        # Class attributes with clearer names
        class_attribute :has_id_attribute, default: false
        class_attribute :auto_generate_id, default: true

        # Class attribute for ransackable attributes
        class_attribute :ransackable_attributes_list, instance_writer: false, default: nil

        # Class methods for ActiveRecord compatibility
        class << self
          # Class method to disable ID attribute entirely
          def without_id
            self.has_id_attribute = false
            self.auto_generate_id = false
            # Remove ID attribute if it exists
            return unless attribute_types.key?('id')

            attribute_types.delete('id')
            undef_method(:id) if method_defined?(:id)
            undef_method(:id=) if method_defined?(:id=)
          end

          # Override attribute_types to filter out ID when disabled
          def attribute_types
            types = super
            if !has_id_attribute && types.key?('id')
              types = types.dup
              types.delete('id')
            end
            types
          end

          # Class method to configure ID behavior
          def with_id(auto_generate: true, type: :string)
            self.has_id_attribute = true
            self.auto_generate_id = auto_generate
            # Define ID attribute immediately with specified type
            attribute :id, type
          end

          # Class method to check if ID attribute is enabled
          def id_enabled?
            has_id_attribute && attribute_types.key?('id')
          end

          # Return attribute names (similar to ActiveRecord's column_names)
          def column_names
            attribute_types.keys
          end

          # Return attributes that can be used for filtering
          def ransackable_attributes(_auth_object = nil)
            ransackable_attributes_list || column_names
          end

          # Allow specifying which attributes are ransackable
          def ransackable(*attributes)
            self.ransackable_attributes_list = attributes.map(&:to_s)
          end

          # Support for model_name (used by form builders and routes)
          def model_name
            @model_name ||= begin
              namespace = name.to_s.deconstantize
              name_without_namespace = name.to_s.demodulize
              ActiveModel::Name.new(self, namespace.presence, name_without_namespace)
            rescue StandardError
              # Fallback for tests
              ActiveModel::Name.new(self)
            end
          end
        end

        def initialize(attributes = {})
          # Handle array input
          attributes = attributes.first if attributes.is_a?(Array) && attributes.first.is_a?(Hash)

          # Generate ID if needed and not provided
          if self.class.has_id_attribute && self.class.auto_generate_id && !attributes.key?(:id) && !attributes.key?('id')
            attributes = attributes.dup
            attributes[:id] = SecureRandom.uuid
          end

          # Initialize instance variables for associations
          @association_cache = {}

          # Initialize with processed attributes
          processed_attributes = process_attributes(attributes || {})
          super(processed_attributes)

          # Mark all attributes as clean after initialization
          clear_changes_information
        end

        # Process nested attributes based on attribute definitions
        def process_attributes(attributes)
          return {} if attributes.nil?

          processed = attributes.dup
          self.class.nested_attributes.each do |nested_attr, config|
            next unless attributes[nested_attr] || attributes[nested_attr.to_s]

            nested_data = attributes[nested_attr] || attributes[nested_attr.to_s]
            config[:fields].each do |field|
              field_key = "#{nested_attr}_#{field}".to_sym
              processed[field_key] = nested_data[field] || nested_data[field.to_s]
            end
          end
          processed
        end

        # Override attribute setter to track changes
        def write_attribute(attr_name, value)
          attr_name = attr_name.to_s

          # Track the change
          attribute_will_change!(attr_name) if value != read_attribute(attr_name)

          # Call the original method
          super
        end

        # Define nested attributes configuration
        def self.nested_attributes
          @nested_attributes ||= {}
        end

        # DSL method to define nested attributes
        def self.define_nested_attribute(name, fields)
          nested_attributes[name] = { fields: fields }

          # Define attributes for each nested field
          fields.each do |field|
            attribute "#{name}_#{field}".to_sym
          end

          # Define a method to access the nested attribute as a whole
          define_method(name) do
            result = {}
            fields.each do |field|
              field_key = "#{name}_#{field}".to_sym
              result[field] = send(field_key) if respond_to?(field_key)
            end
            OpenStruct.new(result)
          end
        end

        # Convert to JSON representation
        def as_json(_options = nil)
          result = attributes.compact

          # Process nested attributes for JSON output
          self.class.nested_attributes.each do |nested_attr, config|
            nested_result = {}
            config[:fields].each do |field|
              field_key = "#{nested_attr}_#{field}".to_sym
              value = send(field_key)
              nested_result[field] = value if value.present?
            end

            next if nested_result.blank?

            result[nested_attr] = nested_result
            config[:fields].each do |field|
              result.delete("#{nested_attr}_#{field}")
            end
          end

          # Add associated objects based on configuration
          self.class.instance_methods.each do |method_name|
            # Check if this is an association getter method
            next unless method_name.to_s.end_with?('_id') &&
                        method_name.to_s != 'id' &&
                        respond_to?(method_name.to_s.sub(/_id$/, ''))

            # Get the association name
            association_name = method_name.to_s.sub(/_id$/, '')

            # Get the associated object
            associated_object = send(association_name)

            # Add to result if present
            if associated_object && associated_object.respond_to?(:as_json)
              result[association_name] = associated_object.as_json
            end
          end

          result
        end

        # Check if the struct is empty
        def empty?
          attributes.values.compact.empty?
        end

        # Check if the struct has content
        def present?
          !empty?
        end

        # Allow hash-like access
        def [](key)
          send(key) if respond_to?(key)
        end

        # Allow hash-like assignment
        def []=(key, value)
          send("#{key}=", value) if respond_to?("#{key}=")
        end

        # Convert to hash
        def to_h
          as_json
        end

        # Support for form builders
        def to_key
          self.class.has_id_attribute && persisted? ? [id] : nil
        end

        # Support for form builders and URL generation
        def to_param
          self.class.has_id_attribute && persisted? ? id.to_s : nil
        end

        # Override respond_to? to hide ID methods when disabled
        def respond_to?(method_name, include_private = false)
          return false if !self.class.has_id_attribute && ['id', 'id='].include?(method_name.to_s)

          super
        end

        # Support for form builders
        def persisted?
          self.class.has_id_attribute && respond_to?(:id) && id.present?
        end

        # Support for form builders
        def new_record?
          !persisted?
        end
      end
    end
  end
end
