# frozen_string_literal: true

module Athar
  module Commons
    # Configuration class for Athar Commons
    # Allows services to customize export limits and other settings
    class Configuration
      attr_accessor :export_limits

      def initialize
        @export_limits = {
          csv: 50_000,
          pdf: 5_000,
          xlsx: 10_000
        }
      end
    end

    # Global configuration instance
    def self.configuration
      @configuration ||= Configuration.new
    end

    # Configure Athar Commons
    # 
    # @example
    #   Athar::Commons.configure do |config|
    #     config.export_limits = {
    #       csv: 100_000,
    #       pdf: 10_000,
    #       xlsx: 20_000
    #     }
    #   end
    def self.configure
      yield(configuration)
    end
  end
end
