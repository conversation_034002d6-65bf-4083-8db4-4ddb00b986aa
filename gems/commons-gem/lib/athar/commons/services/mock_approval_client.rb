module Athar
  module Commons
    module Services
      class MockApprovalClient
        # Mock response for workflow
        class MockWorkflowResponse
          attr_reader :id, :name, :action, :subject_class, :system_name, :description, :active
          
          def initialize(id, name, action, subject_class, system_name)
            @id = id
            @name = name
            @action = action
            @subject_class = subject_class
            @system_name = system_name
            @description = "Mock workflow for #{action} on #{subject_class}"
            @active = true
          end
        end
        
        # Mock response for step
        class MockStepResponse
          attr_reader :step_id, :name, :sequence, :approval_type, :approver_ids
          
          def initialize(step_id, name, sequence, approval_type, approver_ids)
            @step_id = step_id
            @name = name
            @sequence = sequence
            @approval_type = approval_type
            @approver_ids = approver_ids
          end
          
          def to_h
            {
              'step_id' => @step_id,
              'name' => @name,
              'sequence' => @sequence,
              'approval_type' => @approval_type,
              'approver_ids' => @approver_ids
            }
          end
        end
        
        # Mock response for sequence
        class MockSequenceResponse
          attr_reader :workflow_id, :workflow_name, :steps
          
          def initialize(workflow_id, workflow_name, steps)
            @workflow_id = workflow_id
            @workflow_name = workflow_name
            @steps = steps
          end
        end
        
        def get_workflow(action, subject_class, system_name)
          MockWorkflowResponse.new(
            "mock-workflow-#{action}-#{subject_class}",
            "#{action.titleize} #{subject_class} Workflow",
            action,
            subject_class,
            system_name
          )
        end
        
        def generate_sequence(action, subject_class, system_name, requestor_id, project_id = nil)
          workflow_id = "mock-workflow-#{action}-#{subject_class}"
          workflow_name = "#{action.titleize} #{subject_class} Workflow"
          
          # Create mock steps
          steps = [
            MockStepResponse.new(
              "mock-step-1",
              "Manager Approval",
              1,
              "any",
              ["1", "2", "3"] # Mock approver IDs
            ),
            MockStepResponse.new(
              "mock-step-2",
              "HR Approval",
              2,
              "any",
              ["4", "5"] # Mock approver IDs
            )
          ]
          
          MockSequenceResponse.new(workflow_id, workflow_name, steps)
        end
        
        def validate_approver(user_id, approver_ids)
          # Mock response that always returns true
          OpenStruct.new(can_approve: true)
        end
        
        def list_workflows(system_name)
          # Return a list of mock workflows
          [
            MockWorkflowResponse.new(
              "mock-workflow-1",
              "Leave Request Workflow",
              "request_leave",
              "Leave",
              system_name
            ),
            MockWorkflowResponse.new(
              "mock-workflow-2",
              "Procurement Request Workflow",
              "buy_new_item",
              "ProcurementRequest",
              system_name
            )
          ]
        end
      end
    end
  end
end
