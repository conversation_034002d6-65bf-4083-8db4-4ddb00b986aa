require 'athar_rpc'

module Athar
  module Commons
    module Services
      class ApprovalClient
        def initialize
          @client = AtharRpc::GrpcClientFactory.instance.client_for('core', 'ApprovalWorkflows')
        rescue => e
          Rails.logger.error "Failed to initialize Core::ApprovalWorkflows::Client: #{e.message}"
          @client = nil
        end

        def get_workflow(action, subject_class, system_name)
          return nil unless @client

          # Create a proper request object
          request = Core::GetWorkflowRequest.new(
            action: action,
            subject_class: subject_class,
            system_name: system_name
          )

          @client.get_workflow(request)
        rescue => e
          Rails.logger.error "Error getting approval workflow: #{e.message}"
          nil
        end

        # Generate an approval sequence for a specific request
        # @param action [String] The approval action (e.g., "buy_new_item")
        # @param subject_class [String] The class name of the approvable object
        # @param system_name [String] The system name (e.g., "procure")
        # @param requestor_id [String, Integer] The ID of the user requesting approval
        # @param project_id [String, Integer, nil] The ID of the project (if applicable)
        # @param context [Hash] Additional context for the approval workflow
        # @return [Core::ApprovalSequenceResponse, nil] The approval sequence or nil if an error occurred
        # @raise [StandardError] If there's a validation error from the server
        def generate_sequence(action, subject_class, system_name, requestor_id, project_id = nil, context = {})
          return nil unless @client

          # Create a proper request object
          request_params = {
            action: action,
            subject_class: subject_class,
            system_name: system_name,
            requestor_id: requestor_id.to_s
          }

          # Only include project_id if it's present
          request_params[:project_id] = project_id.to_s if project_id.present?
          
          # Add context if provided
          if context.present?
            # Convert all keys and values to strings
            string_context = {}
            context.each do |key, value|
              string_context[key.to_s] = value.to_s
            end
            request_params[:context] = string_context
          end

          request = Core::GenerateSequenceRequest.new(request_params)

          begin
            @client.generate_sequence(request)
          rescue GRPC::NotFound => e
            # Handle specific error cases
            case e.message
            when /workflow_not_found/
              Rails.logger.error "Workflow not found for action: #{action}, subject: #{subject_class}, system: #{system_name}"
              raise StandardError, "No approval workflow found for this action"
            when /requestor_not_found/
              Rails.logger.error "Requestor not found with ID: #{requestor_id}"
              raise StandardError, "Requestor not found"
            when /project_not_found/
              Rails.logger.error "Project not found with ID: #{project_id}"
              raise StandardError, "Project not found"
            else
              Rails.logger.error "Not found error: #{e.message}"
              raise StandardError, "Resource not found: #{e.message}"
            end
          rescue GRPC::InvalidArgument => e
            if e.message =~ /missing_context_keys/
              Rails.logger.error "Missing required context keys: #{e.message}"
              raise StandardError, "Missing required context for approval workflow"
            else
              Rails.logger.error "Invalid argument error: #{e.message}"
              raise StandardError, "Invalid argument: #{e.message}"
            end
          rescue GRPC::FailedPrecondition => e
            if e.message =~ /workflow_not_applicable/
              Rails.logger.error "Workflow not applicable with given context"
              raise StandardError, "This approval workflow is not applicable with the provided context"
            else
              Rails.logger.error "Failed precondition error: #{e.message}"
              raise StandardError, "Failed precondition: #{e.message}"
            end
          rescue => e
            Rails.logger.error "Error generating approval sequence: #{e.message}"
            Rails.logger.error e.backtrace.join("\n") if e.backtrace
            nil
          end
        end

        def validate_approver(user_id, approver_ids)
          return nil unless @client

          # Create a proper request object
          request = Core::ValidateApproverRequest.new(
            user_id: user_id.to_s,
            approver_ids: approver_ids.map(&:to_s)
          )

          @client.validate_approver(request)
        rescue => e
          Rails.logger.error "Error validating approver: #{e.message}"
          nil
        end

        def list_workflows(system_name)
          return [] unless @client

          # Create a proper request object
          request = Core::ListWorkflowsRequest.new(
            system_name: system_name
          )

          response = @client.list_workflows(request)
          response.items
        rescue => e
          Rails.logger.error "Error listing workflows: #{e.message}"
          []
        end
      end
    end
  end
end
