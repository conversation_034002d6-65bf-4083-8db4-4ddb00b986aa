module Athar
  module Commons
    module Services
      class ApprovalService
        # Approve a request
        # @param request [ApprovalRequest] The approval request to approve
        # @param user_id [String, Integer] The ID of the user approving the request
        # @param comment [String, nil] Optional comment for the approval
        # @return [ApprovalResult] The result of the approval operation
        def approve(request, user_id, comment = nil)
          # Check if the request exists
          unless request
            return ApprovalResult.failure("Approval request not found")
          end

          # Delegate to the request's approve! method
          request.approve!(user_id, comment)
        end

        # Reject a request
        # @param request [ApprovalRequest] The approval request to reject
        # @param user_id [String, Integer] The ID of the user rejecting the request
        # @param comment [String, nil] Optional comment for the rejection
        # @return [ApprovalResult] The result of the rejection operation
        def reject(request, user_id, comment = nil)
          # Check if the request exists
          unless request
            return ApprovalResult.failure("Approval request not found")
          end

          # Delegate to the request's reject! method
          request.reject!(user_id, comment)
        end

        # Add a comment to a request
        # @param request [ApprovalRequest] The approval request to comment on
        # @param user_id [String, Integer] The ID of the user adding the comment
        # @param comment_text [String] The comment text
        # @return [ApprovalResult] The result of the comment operation
        def comment(request, user_id, comment_text)
          # Check if the request exists
          unless request
            return ApprovalResult.failure("Approval request not found")
          end

          # Delegate to the request's comment! method
          request.comment!(user_id, comment_text)
        end

        # Cancel a request (only the requestor or an admin can do this)
        # @param request [ApprovalRequest] The approval request to cancel
        # @param user_id [String, Integer] The ID of the user canceling the request
        # @return [ApprovalResult] The result of the cancellation operation
        def cancel(request, user_id)
          # Check if the request exists
          unless request
            return ApprovalResult.failure("Approval request not found")
          end

          # Delegate to the request's cancel! method
          request.cancel!(user_id)
        end

        # Get all requests that a user can approve
        # @param user_id [String, Integer] The ID of the user
        # @return [Array<ApprovalRequest>] The requests that the user can approve
        def approvable_by(user_id)
          # Get all pending requests
          pending_requests = ApprovalRequest.where(status: :pending)

          # Filter to only those where the user is a potential approver for the current step
          pending_requests.select do |request|
            request.can_be_approved_by?(user_id)
          end
        end

        # Get all requests created by a user
        # @param user_id [String, Integer] The ID of the user
        # @return [ActiveRecord::Relation] The requests created by the user
        def requested_by(user_id)
          ApprovalRequest.where(requestor_id: user_id)
        end
      end
    end
  end
end
