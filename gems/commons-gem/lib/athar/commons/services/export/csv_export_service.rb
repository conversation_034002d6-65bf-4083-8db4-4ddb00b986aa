# frozen_string_literal: true

require 'csv'

module Athar
  module Commons
    module Services
      module Export
        # CSV export service
        # Generates CSV files from ActiveRecord resources
        class CsvExportService < BaseExportService
          # Generate CSV content
          # @return [String] The CSV content
          def generate
            validate_export!
            
            CSV.generate(headers: true) do |csv|
              # Add header row
              csv << formatted_column_names
              
              # Add data rows
              export_data.each do |record|
                csv << extract_values(record)
              end
            end
          end

          # Get the MIME type for CSV
          # @return [String] The MIME type
          def mime_type
            'text/csv'
          end

          # Get the file extension for CSV
          # @return [String] The file extension
          def file_extension
            'csv'
          end

          private

          # This method is now inherited from BaseExportService
          # and handles column aliases automatically
        end
      end
    end
  end
end
