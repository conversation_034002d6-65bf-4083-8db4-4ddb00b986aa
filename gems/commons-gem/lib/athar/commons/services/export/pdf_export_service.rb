# frozen_string_literal: true

require 'prawn'
require 'prawn/table'

module Athar
  module Commons
    module Services
      module Export
        # PDF export service
        # Generates PDF files from ActiveRecord resources using Prawn
        class PdfExportService < BaseExportService
          # Generate PDF content
          # @return [String] The PDF content
          def generate
            validate_export!
            
            Prawn::Document.new(page_layout: :landscape) do |pdf|
              # Add title
              pdf.text title, size: 16, style: :bold
              pdf.move_down 20
              
              # Prepare table data
              table_data = prepare_table_data
              
              if table_data.any?
                # Create table with styling
                pdf.table(table_data, 
                  header: true,
                  row_colors: ["FFFFFF", "F0F0F0"],
                  cell_style: { size: 8, padding: 5 }
                ) do
                  # Style header row
                  row(0).font_style = :bold
                  row(0).background_color = "DDDDDD"
                end
              else
                pdf.text "No data available for export."
              end
              
              # Add footer with timestamp
              pdf.number_pages "Page <page> of <total> - Generated on #{Time.current.strftime('%Y-%m-%d %H:%M:%S')}", 
                at: [pdf.bounds.left, 0], 
                width: pdf.bounds.width, 
                align: :center,
                size: 8
            end.render
          end

          # Get the MIME type for PDF
          # @return [String] The MIME type
          def mime_type
            'application/pdf'
          end

          # Get the file extension for PDF
          # @return [String] The file extension
          def file_extension
            'pdf'
          end

          private

          # Generate title for the PDF
          # @return [String] The title
          def title
            options[:title] || "#{resource_name.humanize} Export"
          end

          # Prepare data for the table
          # @return [Array<Array>] Table data with headers
          def prepare_table_data
            data = []
            
            # Add header row
            data << formatted_column_names
            
            # Add data rows
            export_data.each do |record|
              data << extract_values(record).map { |value| format_value_for_pdf(value) }
            end
            
            data
          end

          # This method is now inherited from BaseExportService
          # and handles column aliases automatically

          # Format a value for PDF display
          # @param value [Object] The value to format
          # @return [String] The formatted value
          def format_value_for_pdf(value)
            case value
            when nil
              ""
            when Time, DateTime, Date
              value.strftime('%Y-%m-%d %H:%M:%S')
            when TrueClass, FalseClass
              value ? "Yes" : "No"
            else
              value.to_s.truncate(50) # Limit cell content length
            end
          end
        end
      end
    end
  end
end
