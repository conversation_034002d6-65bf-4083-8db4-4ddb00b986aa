# frozen_string_literal: true

require 'caxlsx'

module Athar
  module Commons
    module Services
      module Export
        # XLSX export service
        # Generates Excel files from ActiveRecord resources using Caxlsx
        class XlsxExportService < BaseExportService
          # Generate XLSX content
          # @return [String] The XLSX content
          def generate
            validate_export!
            
            package = Axlsx::Package.new
            workbook = package.workbook
            
            # Create worksheet
            worksheet = workbook.add_worksheet(name: worksheet_name)
            
            # Define styles
            header_style = workbook.styles.add_style(
              bg_color: "DDDDDD",
              fg_color: "000000",
              b: true,
              alignment: { horizontal: :center }
            )
            
            date_style = workbook.styles.add_style(
              format_code: "yyyy-mm-dd hh:mm:ss"
            )
            
            # Add header row
            worksheet.add_row(formatted_column_names, style: header_style)
            
            # Add data rows
            export_data.each do |record|
              row_data = extract_values(record).map { |value| format_value_for_xlsx(value) }
              styles = determine_cell_styles(extract_values(record), date_style)
              worksheet.add_row(row_data, style: styles)
            end
            
            # Auto-fit columns
            worksheet.column_widths(*calculate_column_widths)
            
            # Return the generated content
            package.to_stream.read
          end

          # Get the MIME type for XLSX
          # @return [String] The MIME type
          def mime_type
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          end

          # Get the file extension for XLSX
          # @return [String] The file extension
          def file_extension
            'xlsx'
          end

          private

          # Generate worksheet name
          # @return [String] The worksheet name
          def worksheet_name
            options[:worksheet_name] || resource_name.humanize.truncate(31) # Excel limit
          end

          # This method is now inherited from BaseExportService
          # and handles column aliases automatically

          # Format a value for XLSX display
          # @param value [Object] The value to format
          # @return [Object] The formatted value
          def format_value_for_xlsx(value)
            case value
            when nil
              ""
            when TrueClass, FalseClass
              value ? "Yes" : "No"
            else
              value
            end
          end

          # Determine cell styles for a row
          # @param values [Array] The row values
          # @param date_style [Object] The date style object
          # @return [Array] Array of styles for each cell
          def determine_cell_styles(values, date_style)
            values.map do |value|
              case value
              when Time, DateTime, Date
                date_style
              else
                nil
              end
            end
          end

          # Calculate optimal column widths
          # @return [Array<Integer>] Array of column widths
          def calculate_column_widths
            # Start with header widths
            widths = formatted_column_names.map { |header| [header.length + 2, 10].max }

            # Sample first few rows to determine content width
            sample_data = export_data.respond_to?(:limit) ? export_data.limit(10) : export_data.first(10)
            sample_data.each do |record|
              extract_values(record).each_with_index do |value, index|
                content_length = format_value_for_xlsx(value).to_s.length + 2
                widths[index] = [[widths[index], content_length].max, 50].min # Cap at 50
              end
            end

            widths
          end
        end
      end
    end
  end
end
