# frozen_string_literal: true

module Athar
  module Commons
    module Services
      module Export
        # Base class for all export services
        # Provides common functionality for exporting data in different formats
        class BaseExportService
          attr_reader :resource, :options, :format

          def initialize(resource, format, options = {})
            @resource = resource
            @format = format.to_sym
            @options = options
            @all_columns = parse_unified_columns(options)
            @regular_columns, @association_columns = separate_column_types(@all_columns)
            @column_aliases = parse_column_aliases(options)
          end

          # Generate the export file
          # Must be implemented by subclasses
          # @return [String] The generated file content
          def generate
            raise NotImplementedError, "Subclasses must implement #generate"
          end

          # Get the MIME type for this export format
          # Must be implemented by subclasses
          # @return [String] The MIME type
          def mime_type
            raise NotImplementedError, "Subclasses must implement #mime_type"
          end

          # Get the file extension for this export format
          # Must be implemented by subclasses
          # @return [String] The file extension
          def file_extension
            raise NotImplementedError, "Subclasses must implement #file_extension"
          end

          # Generate a filename for the export
          # @return [String] The generated filename
          def filename
            timestamp = Time.current.strftime("%Y%m%d_%H%M%S")
            base_name = options[:filename] || resource_name
            "#{base_name}_#{timestamp}.#{file_extension}"
          end

          protected

          # Get the resource name for filename generation
          # @return [String] The resource name
          def resource_name
            if resource.respond_to?(:model_name)
              resource.model_name.plural
            elsif resource.is_a?(Array) && resource.first.respond_to?(:model_name)
              resource.first.model_name.plural
            else
              "export"
            end
          end

          # Parse unified columns from options (handles both regular and association fields)
          # @return [Array<String>] Selected columns or default columns
          def parse_unified_columns(options)
            columns = options[:columns]

            if columns.present?
              case columns
              when String
                columns.split(',').map(&:strip)
              when Array
                columns.map(&:to_s)
              else
                get_default_columns
              end
            else
              get_default_columns
            end
          end

          # Separate columns into regular and association types using ActiveRecord reflection
          # @param columns [Array<String>] All columns
          # @return [Array<Array<String>, Array<String>>] [regular_columns, association_columns]
          def separate_column_types(columns)
            regular = []
            associations = []

            columns.each do |col|
              if is_valid_association?(col)
                associations << col
              else
                regular << col
              end
            end

            [regular, associations]
          end

          # Validate if a column path represents a valid association using ActiveRecord reflection
          # @param column_path [String] The column path to validate (e.g., "employee.name")
          # @return [Boolean] True if it's a valid association
          def is_valid_association?(column_path)
            Rails.logger&.debug("EXPORT DEBUG: Validating association: #{column_path}")

            return false unless column_path.include?('.')

            # Split the path and validate the first part is a real association
            parts = column_path.split('.')
            return false if parts.length < 2

            # Get the first association name
            first_part = parts[0]
            Rails.logger&.debug("EXPORT DEBUG: Testing first part: #{first_part}")

            # Get the model class to check associations
            model_class = get_model_class
            Rails.logger&.debug("EXPORT DEBUG: Model class: #{model_class}")
            return false unless model_class

            # First try ActiveRecord reflection
            association = model_class.reflect_on_association(first_part.to_sym)
            if association.present?
              Rails.logger&.debug("EXPORT DEBUG: Found ActiveRecord association: #{association}")
              return true
            end

            # Special case for ActiveRpc user_roles - always treat as valid association
            if first_part == 'user_roles'
              # Get an actual model instance, not the relation
              test_object = @resource.is_a?(Array) ? @resource.first : @resource
              if test_object.respond_to?(:first) # It's a relation
                test_object = test_object.first
              end

              Rails.logger&.debug("EXPORT DEBUG: Test object class: #{test_object.class}")
              Rails.logger&.debug("EXPORT DEBUG: Test object methods containing 'user': #{test_object&.methods&.grep(/user/)}")
              result = test_object&.respond_to?(:user_roles)
              Rails.logger&.debug("EXPORT DEBUG: user_roles special case - responds_to?: #{result}")

              # Try user_roles_list as fallback
              if !result && test_object&.respond_to?(:user_roles_list)
                Rails.logger&.debug("EXPORT DEBUG: Found user_roles_list instead - treating as valid")
                return true
              end

              return result
            end

            # If ActiveRecord reflection fails, check if it's an ActiveRpc/ActiveStruct association
            # by testing if the method exists and returns a navigable object
            test_object = @resource.is_a?(Array) ? @resource.first : @resource
            if test_object&.respond_to?(first_part)
              # Test if the method returns something that looks like an association
              begin
                result = test_object.send(first_part)

                # Check if it's a collection that can be navigated
                if result.respond_to?(:each) && !result.is_a?(String)
                  # For collections, check if items have the next part in the path
                  if parts.length > 1 && result.respond_to?(:first)
                    first_item = result.first
                    return true if first_item&.respond_to?(parts[1])
                  end
                  return true # It's a navigable collection
                end

                # Check if it's a single object that can be navigated
                if parts.length > 1
                  return true if result&.respond_to?(parts[1])
                end

                # If it responds to the method but we can't navigate further, it might still be valid
                return true if result

              rescue => e
                # If calling the method fails, it's probably not a valid association
                Rails.logger&.debug("Association test failed for #{first_part}: #{e.message}")
              end
            end

            false
          end

          # Get the model class from the resource
          # @return [Class, nil] The ActiveRecord model class
          def get_model_class
            if @resource.respond_to?(:klass)
              # ActiveRecord relation
              @resource.klass
            elsif @resource.is_a?(Array) && @resource.first
              # Array of model instances
              @resource.first.class
            elsif @resource.respond_to?(:class) && @resource.class.respond_to?(:reflect_on_association)
              # Single model instance
              @resource.class
            else
              nil
            end
          end

          # Get default columns when none are specified
          # @return [Array<String>] Default column names
          def get_default_columns
            base_columns = get_base_column_names
            excluded = %w[created_at updated_at password_digest remember_token]
            base_columns - excluded
          end

          # Parse column aliases from options
          # @return [Hash] Column aliases mapping
          def parse_column_aliases(options)
            aliases = options[:aliases] || options[:column_aliases] || {}

            # Handle string format: "Name,Email,Department"
            if aliases.is_a?(String) && @all_columns
              alias_array = aliases.split(',').map(&:strip)
              aliases = @all_columns.zip(alias_array).to_h
            end

            aliases.stringify_keys
          end

          # Get the export limit for the current format
          # @return [Integer] The export limit
          def export_limit
            Athar::Commons.configuration.export_limits[format] || 10_000
          end

          # Check if the resource collection exceeds the export limit
          # @return [Boolean] True if the limit is exceeded
          def exceeds_limit?
            return false unless resource.respond_to?(:count)
            resource.count > export_limit
          end

          # Validate the export request
          # @raise [StandardError] If validation fails
          def validate_export!
            if exceeds_limit?
              raise StandardError, "Export limit exceeded. Maximum #{export_limit} records allowed for #{format.upcase} format."
            end
          end

          # Get the data to export
          # Handles both single resources and collections
          # @return [Array] Array of records to export
          def export_data
            case resource
            when ActiveRecord::Relation
              resource.limit(export_limit)
            when Array
              resource.take(export_limit)
            else
              [resource]
            end
          end

          # Get all column names (unified approach)
          # @return [Array<String>] Array of all column names
          def column_names
            @all_columns
          end

          # Get base column names from the resource
          # @return [Array<String>] Array of base column names
          def get_base_column_names
            if resource.respond_to?(:column_names)
              resource.column_names
            elsif resource.is_a?(Array) && resource.first.respond_to?(:attributes)
              resource.first.attributes.keys
            elsif resource.respond_to?(:attributes)
              resource.attributes.keys
            else
              []
            end
          end



          # Extract values for a record using unified column approach
          # @param record [Object] The record to extract values from
          # @return [Array] Array of values
          def extract_values(record)
            @all_columns.map do |col|
              if @association_columns.include?(col)
                extract_association_value(record, col)
              else
                record.respond_to?(col) ? record.send(col) : nil
              end
            end
          end

          # Extract value from an association using dot notation
          # @param record [Object] The main record
          # @param column_path [String] The association path (e.g., "employee.name", "user_roles.role.name")
          # @return [Object] The extracted value
          def extract_association_value(record, column_path)
            # Navigate association path using dot notation
            # e.g., "employee.name" -> record.employee.name
            parts = column_path.split('.')
            current_object = record

            Rails.logger&.debug("EXPORT DEBUG: Starting extraction for #{column_path} on #{record.class}##{record.id}")

            parts.each_with_index do |part, index|
              Rails.logger&.debug("EXPORT DEBUG: Step #{index}: accessing '#{part}' on #{current_object.class}")

              unless current_object&.respond_to?(part)
                Rails.logger&.debug("EXPORT DEBUG: Object does not respond to '#{part}' - returning nil")
                return nil
              end

              current_object = current_object.send(part)
              Rails.logger&.debug("EXPORT DEBUG: Step #{index}: got #{current_object.class} - #{current_object.inspect}")

              # Handle collections by extracting remaining path from each item
              if current_object.respond_to?(:each) && !current_object.is_a?(String)
                Rails.logger&.debug("EXPORT DEBUG: Found collection with #{current_object.count} items")

                # Get remaining parts after this one
                remaining_parts = parts[(index + 1)..-1]
                if remaining_parts.any?
                  Rails.logger&.debug("EXPORT DEBUG: Extracting remaining path: #{remaining_parts.join('.')}")
                  values = current_object.map do |item|
                    Rails.logger&.debug("EXPORT DEBUG: Processing collection item: #{item.class} - #{item.inspect}")
                    extract_nested_value(item, remaining_parts)
                  end.compact.uniq
                  result = values.any? ? values.join(', ') : nil
                  Rails.logger&.debug("EXPORT DEBUG: Collection result: #{result.inspect}")
                  return result
                else
                  values = current_object.map(&:to_s).compact.reject(&:empty?)
                  result = values.any? ? values.join(', ') : nil
                  Rails.logger&.debug("EXPORT DEBUG: Collection toString result: #{result.inspect}")
                  return result
                end
              end
            end

            Rails.logger&.debug("EXPORT DEBUG: Final result: #{current_object.inspect}")
            current_object
          rescue => e
            Rails.logger&.warn("EXPORT DEBUG: Failed to extract association value for #{column_path}: #{e.message}")
            Rails.logger&.warn("EXPORT DEBUG: Backtrace: #{e.backtrace.first(3).join("\n")}")
            nil
          end

          # Extract nested value from an object following a path
          # @param object [Object] The object to extract from
          # @param parts [Array<String>] The remaining path parts
          # @return [Object] The extracted value
          def extract_nested_value(object, parts)
            current = object
            parts.each do |part|
              return nil unless current&.respond_to?(part)
              current = current.send(part)
            end
            current
          end

          # Check if the related object is a collection
          # @param related_object [Object] The related object
          # @return [Boolean] True if it's a collection
          def is_collection?(related_object)
            related_object.respond_to?(:each) &&
            !related_object.is_a?(String) &&
            !related_object.respond_to?(:attributes)
          end

          # Extract values from a collection and concatenate them
          # @param collection [Object] The collection object
          # @param attribute_name [String] The attribute to extract
          # @return [String] Concatenated values
          def extract_collection_values(collection, attribute_name)
            values = []

            collection.each do |item|
              value = extract_single_value(item, attribute_name)
              values << value if value.present?
            end

            values.compact.uniq.join(', ')
          rescue => e
            Rails.logger&.warn("Failed to extract collection values: #{e.message}")
            ""
          end

          # Extract values from a collection with nested path navigation
          # @param collection [Object] The collection object
          # @param path_parts [Array] Remaining path parts to navigate
          # @return [String] Concatenated values
          def extract_collection_values_with_path(collection, path_parts)
            values = []

            collection.each do |item|
              if path_parts.empty?
                # No more path parts - use the item itself
                value = extract_single_value(item, nil)
                values << value if value.present?
              else
                # Navigate through remaining path parts
                current_object = item
                path_parts.each do |part|
                  break unless current_object&.respond_to?(part)
                  current_object = current_object.send(part)
                end

                # Add the final value
                if current_object.present?
                  value = extract_single_value(current_object, nil)
                  values << value if value.present?
                end
              end
            end

            values.compact.uniq.join(', ')
          rescue => e
            Rails.logger&.warn("Failed to extract collection values with path: #{e.message}")
            ""
          end



          # Extract value from a single related object
          # @param related_object [Object] The related object
          # @param attribute_name [String] The attribute to extract (nil means use the object itself)
          # @return [Object] The extracted value
          def extract_single_value(related_object, attribute_name)
            return nil unless related_object

            if attribute_name.present?
              # Extract specific attribute
              if related_object.respond_to?(attribute_name)
                related_object.send(attribute_name)
              elsif related_object.respond_to?(:attributes)
                related_object.attributes[attribute_name]
              else
                nil
              end
            else
              # No attribute specified - try to get a meaningful string representation
              if related_object.respond_to?(:name)
                related_object.name
              elsif related_object.respond_to?(:title)
                related_object.title
              elsif related_object.respond_to?(:to_s)
                related_object.to_s
              else
                related_object
              end
            end
          end

          # Get formatted column names for display (with aliases)
          # @return [Array<String>] Array of formatted column names
          def formatted_column_names
            column_names.map do |col|
              # Use alias if provided, otherwise format the column name
              @column_aliases[col] || format_column_name(col)
            end
          end

          # Format a column name for display
          # @param col [String] The column name
          # @return [String] The formatted column name
          def format_column_name(col)
            # Check for custom mappings first
            custom_name = get_custom_column_name(col)
            return custom_name if custom_name

            if @association_columns.include?(col)
              # Handle association columns with smart formatting
              format_association_column(col)
            else
              # Handle regular columns
              format_part(col)
            end
          end

          # Get custom column name mapping
          # @param col [String] The column name
          # @return [String, nil] Custom name or nil
          def get_custom_column_name(col)
            # Common association patterns with better names
            custom_mappings = {
              'user_roles.role.name' => 'Role',
              'user_roles.project.name' => 'Project',
              'employee.name' => 'Employee Name',
              'employee.email' => 'Employee Email',
              'employee.department' => 'Employee Department',
              'manager.name' => 'Manager',
              'manager.email' => 'Manager Email',
              'department.name' => 'Department',
              'role.name' => 'Role',
              'project.name' => 'Project',
              'device.name' => 'Device',
              'device.location' => 'Device Location',
              'approver.name' => 'Approver',
              'created_by.name' => 'Created By',
              'updated_by.name' => 'Updated By'
            }

            custom_mappings[col]
          end

          # Format association column with smart logic
          # @param col [String] The association column
          # @return [String] Formatted name
          def format_association_column(col)
            parts = col.split('.')

            # For paths ending with common attributes, use the entity name + attribute
            if parts.last == 'name'
              # Get the entity we're fetching the name from
              entity_part = parts[-2] # e.g., 'role' from 'user_roles.role.name'
              return format_part(entity_part) # 'role' -> 'Role'
            elsif parts.last == 'email'
              entity_part = parts[-2]
              return "#{format_part(entity_part)} Email" # 'employee.email' -> 'Employee Email'
            elsif parts.last == 'title'
              entity_part = parts[-2]
              return "#{format_part(entity_part)} Title"
            elsif parts.last == 'department'
              entity_part = parts[-2]
              return "#{format_part(entity_part)} Department"
            else
              # For other attributes, use entity + attribute
              if parts.length >= 2
                entity_part = parts[-2]
                attribute_part = parts[-1]
                return "#{format_part(entity_part)} #{format_part(attribute_part)}"
              end
            end

            # Fallback to full formatting
            parts.map { |part| format_part(part) }.join(' ')
          end

          private

          # Format a single part of a column name
          # @param part [String] The part to format
          # @return [String] The formatted part
          def format_part(part)
            # Convert snake_case to Title Case
            part.split('_').map(&:capitalize).join(' ')
          end
        end
      end
    end
  end
end
