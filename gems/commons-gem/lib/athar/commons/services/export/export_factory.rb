# frozen_string_literal: true

module Athar
  module Commons
    module Services
      module Export
        # Factory for creating export services
        # Handles the creation of appropriate export service based on format
        class ExportFactory
          # Supported export formats
          SUPPORTED_FORMATS = %i[csv pdf xlsx].freeze

          # Create an export service for the given format
          # @param resource [Object] The resource to export
          # @param format [String, Symbol] The export format
          # @param options [Hash] Additional options
          # @return [BaseExportService] The export service instance
          # @raise [ArgumentError] If format is not supported
          def self.create(resource, format, options = {})
            format = format.to_sym
            
            unless SUPPORTED_FORMATS.include?(format)
              raise ArgumentError, "Unsupported export format: #{format}. Supported formats: #{SUPPORTED_FORMATS.join(', ')}"
            end

            service_class = case format
                           when :csv
                             CsvExportService
                           when :pdf
                             PdfExportService
                           when :xlsx
                             XlsxExportService
                           end

            service_class.new(resource, format, options)
          end

          # Check if a format is supported
          # @param format [String, Symbol] The format to check
          # @return [Boolean] True if supported
          def self.supported_format?(format)
            return false if format.nil?
            SUPPORTED_FORMATS.include?(format.to_sym)
          end

          # Get list of supported formats
          # @return [Array<Symbol>] Array of supported formats
          def self.supported_formats
            SUPPORTED_FORMATS.dup
          end
        end
      end
    end
  end
end
