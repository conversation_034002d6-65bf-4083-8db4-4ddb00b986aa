#!/usr/bin/env ruby
require 'erb'
require 'ostruct'
require 'active_support'
require 'active_support/core_ext/string'

# Test cases
test_cases = [
  {
    name: "Project-based with foreign keys",
    options: {
      user_table: "users",
      project_based: true,
      project_table: "projects",
      use_foreign_keys: true
    }
  },
  {
    name: "Non-project-based without foreign keys",
    options: {
      user_table: "users",
      project_based: false,
      project_table: "projects",
      use_foreign_keys: false
    }
  }
]

# Templates to test
templates = [
  {
    name: "ApprovalRequest Model",
    path: "lib/generators/athar/commons/approval/templates/approval_request.rb.tt"
  },
  {
    name: "ApprovalRequests Migration",
    path: "lib/generators/athar/commons/approval/templates/create_approval_requests.rb.tt"
  }
]

# Test each template with each test case
templates.each do |template|
  puts "Testing template: #{template[:name]}"
  puts "=" * 80
  
  template_content = File.read(template[:path])
  
  test_cases.each do |test_case|
    puts "\nTest case: #{test_case[:name]}"
    puts "-" * 40
    
    # Create a binding with the options
    options = OpenStruct.new(test_case[:options])
    binding_obj = OpenStruct.new(options: options).instance_eval { binding }
    
    # Render the template
    erb = ERB.new(template_content, trim_mode: '-')
    result = erb.result(binding_obj)
    
    # Check for consecutive empty lines
    consecutive_empty_lines = result.scan(/\n\s*\n\s*\n/)
    if consecutive_empty_lines.any?
      puts "WARNING: Found #{consecutive_empty_lines.size} instances of consecutive empty lines"
    else
      puts "No consecutive empty lines found"
    end
    
    # Print the result
    puts "\nOutput:"
    puts result
  end
  
  puts "\n\n"
end
