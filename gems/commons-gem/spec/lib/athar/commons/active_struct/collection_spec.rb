require 'active_model'
require 'active_support'
require 'active_support/core_ext'
require 'securerandom'

# Load our ActiveStruct files directly
require_relative '../../../../../lib/athar/commons/active_struct/associations'
require_relative '../../../../../lib/athar/commons/active_struct/base'
require_relative '../../../../../lib/athar/commons/active_struct/collection'

RSpec.describe Athar::Commons::ActiveStruct::Collection do
  let(:id_enabled_model_class) do
    Class.new(Athar::Commons::ActiveStruct::Base) do
      attribute :name, :string
    end
  end

  let(:id_disabled_model_class) do
    Class.new(Athar::Commons::ActiveStruct::Base) do
      without_id
      attribute :name, :string
    end
  end

  let(:id_enabled_collection_class) do
    model_class = id_enabled_model_class
    Class.new do
      include Athar::Commons::ActiveStruct::Collection
      define_method(:item_class) { model_class }
    end
  end

  let(:id_disabled_collection_class) do
    model_class = id_disabled_model_class
    Class.new do
      include Athar::Commons::ActiveStruct::Collection
      define_method(:item_class) { model_class }
    end
  end

  describe 'with ID-enabled models' do
    let(:collection) { id_enabled_collection_class.new }

    before do
      collection << id_enabled_model_class.new(name: 'Item 1')
      collection << id_enabled_model_class.new(name: 'Item 2')
    end

    it 'provides ID management functionality' do
      expect(collection.size).to eq(2)
      expect(collection.ids.size).to eq(2)
      expect(collection.ids.all?(&:present?)).to be true
    end

    it 'supports finding by ID' do
      first_id = collection.ids.first
      found_item = collection.find_by_id(first_id)
      expect(found_item).not_to be_nil
      expect(found_item.id).to eq(first_id)
    end

    it 'supports setting IDs' do
      new_ids = [ 'id1', 'id2' ]
      collection.ids = new_ids
      expect(collection.size).to eq(2)
      expect(collection.ids).to eq(new_ids)
    end

    it 'tracks changes when IDs are modified' do
      # Reset changed state first
      collection.instance_variable_set(:@changed, false)
      expect(collection.changed?).to be false
      collection.ids = [ 'new1', 'new2' ]
      expect(collection.changed?).to be true
    end
  end

  describe 'with ID-disabled models' do
    let(:collection) { id_disabled_collection_class.new }

    before do
      collection << id_disabled_model_class.new(name: 'Item 1')
      collection << id_disabled_model_class.new(name: 'Item 2')
    end

    it 'handles models without IDs gracefully' do
      expect(collection.size).to eq(2)
      expect(collection.ids).to be_empty
    end

    it 'returns nil when finding by ID' do
      found_item = collection.find_by_id('any-id')
      expect(found_item).to be_nil
    end

    it 'ignores ID setting attempts' do
      original_size = collection.size
      collection.ids = [ 'id1', 'id2' ]
      expect(collection.size).to eq(original_size)
      expect(collection.ids).to be_empty
    end

    it 'does not track changes for ID operations' do
      # Reset changed state first
      collection.instance_variable_set(:@changed, false)
      expect(collection.changed?).to be false
      collection.ids = [ 'new1', 'new2' ]
      expect(collection.changed?).to be false
    end
  end

  describe 'mixed functionality' do
    it 'handles empty collections' do
      empty_collection = id_enabled_collection_class.new
      expect(empty_collection.ids).to be_empty
      expect(empty_collection.find_by_id('any-id')).to be_nil
    end

    it 'supports enumerable methods' do
      collection = id_enabled_collection_class.new
      collection << id_enabled_model_class.new(name: 'Item 1')
      collection << id_enabled_model_class.new(name: 'Item 2')

      names = collection.map(&:name)
      expect(names).to eq([ 'Item 1', 'Item 2' ])
    end

    it 'supports filtering and querying' do
      collection = id_enabled_collection_class.new
      collection << id_enabled_model_class.new(name: 'Item 1')
      collection << id_enabled_model_class.new(name: 'Item 2')

      filtered = collection.where(name: 'Item 1')
      expect(filtered.size).to eq(1)
      expect(filtered.first.name).to eq('Item 1')
    end
  end

  describe 'serialization' do
    it 'serializes collections with ID-enabled models' do
      collection = id_enabled_collection_class.new
      collection << id_enabled_model_class.new(name: 'Item 1')

      json = collection.as_json
      expect(json).to be_an(Array)
      expect(json.first).to have_key('id')
      expect(json.first).to have_key('name')
    end

    it 'serializes collections with ID-disabled models' do
      collection = id_disabled_collection_class.new
      collection << id_disabled_model_class.new(name: 'Item 1')

      json = collection.as_json
      expect(json).to be_an(Array)
      expect(json.first).not_to have_key('id')
      expect(json.first).to have_key('name')
    end
  end
end
