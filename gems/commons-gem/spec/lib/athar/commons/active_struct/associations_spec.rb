# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Athar::Commons::ActiveStruct::Associations do
  # Create test ActiveRecord models for testing associations
  before(:all) do
    # Create test tables
    ActiveRecord::Schema.define do
      create_table :test_users, force: true do |t|
        t.string :name
        t.string :email
        t.string :uuid
        t.timestamps
      end

      create_table :test_projects, force: true do |t|
        t.string :name
        t.references :owner, foreign_key: { to_table: :test_users }
        t.timestamps
      end

      create_table :test_user_roles, force: true do |t|
        t.references :user, foreign_key: { to_table: :test_users }
        t.references :project, foreign_key: { to_table: :test_projects }
        t.string :role_name
        t.boolean :is_default, default: false
        t.timestamps
      end

      create_table :test_roles, force: true do |t|
        t.string :name
        t.timestamps
      end
    end

    # Define test ActiveRecord models
    Object.const_set('TestUser', Class.new(ActiveRecord::Base) do
      self.table_name = 'test_users'
      has_many :test_projects, foreign_key: 'owner_id'
      has_many :test_user_roles, foreign_key: 'user_id'
      has_many :roles, through: :test_user_roles, source: :role
    end)

    Object.const_set('TestProject', Class.new(ActiveRecord::Base) do
      self.table_name = 'test_projects'
      belongs_to :owner, class_name: 'TestUser'
      has_many :test_user_roles, foreign_key: 'project_id'
    end)

    Object.const_set('TestUserRole', Class.new(ActiveRecord::Base) do
      self.table_name = 'test_user_roles'
      belongs_to :user, class_name: 'TestUser'
      belongs_to :project, class_name: 'TestProject'
      belongs_to :role, class_name: 'TestRole', foreign_key: 'role_name', primary_key: 'name'
    end)

    Object.const_set('TestRole', Class.new(ActiveRecord::Base) do
      self.table_name = 'test_roles'
      has_many :test_user_roles, foreign_key: 'role_name', primary_key: 'name'
    end)
  end

  after(:all) do
    # Clean up
    ActiveRecord::Schema.define do
      drop_table :test_user_roles if ActiveRecord::Base.connection.table_exists?(:test_user_roles)
      drop_table :test_projects if ActiveRecord::Base.connection.table_exists?(:test_projects)
      drop_table :test_users if ActiveRecord::Base.connection.table_exists?(:test_users)
      drop_table :test_roles if ActiveRecord::Base.connection.table_exists?(:test_roles)
    end
    
    %w[TestUser TestProject TestUserRole TestRole].each do |const|
      Object.send(:remove_const, const) if Object.const_defined?(const)
    end
  end

  # Test data setup - use let instead of let! to avoid cross-test pollution
  let(:user) { TestUser.create!(name: 'John Doe', email: '<EMAIL>', uuid: 'user-123') }
  let(:project) { TestProject.create!(name: 'Test Project', owner: user) }
  let(:role) { TestRole.create!(name: 'admin') }
  let(:user_role) { TestUserRole.create!(user: user, project: project, role_name: 'admin', is_default: true) }

  # Clean up after each test
  after(:each) do
    TestUserRole.delete_all
    TestProject.delete_all
    TestUser.delete_all
    TestRole.delete_all
  end

  describe 'belongs_to associations' do
    context 'with ActiveRecord target' do
      let(:profile_class) do
        Class.new(Athar::Commons::ActiveStruct::Base) do
          belongs_to :user, class_name: 'TestUser'
          attribute :bio, :string
        end
      end

      it 'defines foreign key attribute with correct type' do
        expect(profile_class.attribute_types).to have_key('user_id')
        expect(profile_class.attribute_types['user_id']).to be_a(ActiveModel::Type::Integer)
      end

      it 'creates getter and setter methods' do
        profile = profile_class.new
        expect(profile).to respond_to(:user)
        expect(profile).to respond_to(:user=)
        expect(profile).to respond_to(:user_id)
        expect(profile).to respond_to(:user_id=)
      end

      it 'handles association assignment and retrieval' do
        profile = profile_class.new
        profile.user = user
        
        expect(profile.user_id).to eq(user.id)
        expect(profile.user).to eq(user)
      end

      it 'handles nil assignment' do
        profile = profile_class.new(user: user)
        profile.user = nil
        
        expect(profile.user_id).to be_nil
        expect(profile.user).to be_nil
      end
    end

    context 'with custom foreign key and primary key' do
      let(:profile_class) do
        Class.new(Athar::Commons::ActiveStruct::Base) do
          belongs_to :manager, class_name: 'TestUser', 
                     foreign_key: 'manager_uuid', primary_key: 'uuid'
          attribute :bio, :string
        end
      end

      it 'defines custom foreign key attribute' do
        expect(profile_class.attribute_types).to have_key('manager_uuid')
        expect(profile_class.attribute_types['manager_uuid']).to be_a(ActiveModel::Type::String)
      end

      it 'handles custom key association' do
        profile = profile_class.new
        profile.manager = user
        
        expect(profile.manager_uuid).to eq(user.uuid)
        expect(profile.manager).to eq(user)
      end
    end

    context 'with ActiveStruct target' do
      let(:department_class) do
        Class.new(Athar::Commons::ActiveStruct::Base) do
          attribute :name, :string
        end
      end

      let(:employee_class) do
        Class.new(Athar::Commons::ActiveStruct::Base) do
          belongs_to :department
          attribute :name, :string
        end
      end

      before do
        stub_const('Department', department_class)
        stub_const('Employee', employee_class)
      end

      it 'defines foreign key with string type for ActiveStruct' do
        expect(employee_class.attribute_types).to have_key('department_id')
        expect(employee_class.attribute_types['department_id']).to be_a(ActiveModel::Type::String)
      end

      it 'handles ActiveStruct association' do
        department = department_class.new(name: 'Engineering')
        employee = employee_class.new(name: 'Jane Doe')
        
        employee.department = department
        expect(employee.department_id).to eq(department.id)
        expect(employee.department).to eq(department)
      end
    end
  end

  describe 'has_many associations' do
    context 'regular has_many with ActiveStruct targets' do
      let(:post_class) do
        Class.new(Athar::Commons::ActiveStruct::Base) do
          attribute :title, :string
          attribute :user_id, :string
        end
      end

      let(:post_collection_class) do
        Class.new do
          include Athar::Commons::ActiveStruct::Collection

          def item_class
            Post
          end
        end
      end

      let(:user_class) do
        Class.new(Athar::Commons::ActiveStruct::Base) do
          has_many :posts
          attribute :name, :string
        end
      end

      before do
        stub_const('Post', post_class)
        stub_const('PostCollection', post_collection_class)
        stub_const('User', user_class)
      end

      it 'defines association methods' do
        user = user_class.new(name: 'John')
        expect(user).to respond_to(:posts)
        expect(user).to respond_to(:posts=)
        expect(user).to respond_to(:post_ids)
        expect(user).to respond_to(:post_ids=)
      end

      it 'lazy-initializes collection' do
        user = user_class.new(name: 'John')
        posts_collection = user.posts
        expect(posts_collection).to respond_to(:<<)
        expect(posts_collection).to respond_to(:clear)
        expect(posts_collection).to respond_to(:size)
      end
    end

    context 'has_many with ActiveRecord targets' do
      let(:user_struct_class) do
        Class.new(Athar::Commons::ActiveStruct::Base) do
          has_many :test_projects, class_name: 'TestProject', foreign_key: 'owner_id'
          attribute :name, :string
        end
      end

      it 'defines association methods for ActiveRecord targets' do
        user_struct = user_struct_class.new(name: 'John')
        expect(user_struct).to respond_to(:test_projects)
        expect(user_struct).to respond_to(:test_projects=)
        expect(user_struct).to respond_to(:test_project_ids)
        expect(user_struct).to respond_to(:test_project_ids=)
      end

      it 'returns ActiveRecord relation for associated records' do
        # Create a user_struct with the same ID as the project owner
        user_struct = user_struct_class.new(name: 'John', id: project.owner_id)

        # Should return an ActiveRecord relation
        projects = user_struct.test_projects
        expect(projects).to respond_to(:where)
        expect(projects).to respond_to(:pluck)
        expect(projects).to include(project)
      end

      it 'returns empty relation when no foreign key value' do
        user_struct = user_struct_class.new(name: 'John')  # No ID set
        projects = user_struct.test_projects
        expect(projects).to eq([])
      end

      it 'returns project IDs using pluck for performance' do
        # Create a user_struct with the same ID as the project owner
        user_struct = user_struct_class.new(name: 'John', id: project.owner_id)

        project_ids = user_struct.test_project_ids
        expect(project_ids).to include(project.id)
        expect(project_ids).to be_an(Array)
      end

      it 'handles custom foreign key and primary key' do
        custom_class = Class.new(Athar::Commons::ActiveStruct::Base) do
          has_many :test_user_roles, class_name: 'TestUserRole',
                   foreign_key: 'user_id', primary_key: 'user_id'
          attribute :user_id, :integer
        end

        user_struct = custom_class.new(user_id: user.id)
        roles = user_struct.test_user_roles
        expect(roles).to include(user_role)
      end
    end

    context 'has_many through' do
      let(:user_struct_class) do
        Class.new(Athar::Commons::ActiveStruct::Base) do
          belongs_to :user, class_name: 'TestUser'
          has_many :projects, through: :user, source: :test_projects
          has_many :roles, through: :user, source: :roles
          attribute :profile_data, :string
        end
      end

      it 'defines through association methods' do
        user_struct = user_struct_class.new
        expect(user_struct).to respond_to(:projects)
        expect(user_struct).to respond_to(:project_ids)
        expect(user_struct).to respond_to(:roles)
        expect(user_struct).to respond_to(:role_ids)
      end

      it 'retrieves associated records through ActiveRecord' do
        user_struct = user_struct_class.new
        user_struct.user = user
        
        projects = user_struct.projects
        expect(projects).to include(project)
        
        project_ids = user_struct.project_ids
        expect(project_ids).to include(project.id)
      end

      it 'handles empty through associations' do
        user_struct = user_struct_class.new
        # No user assigned

        expect(user_struct.projects).to eq([])
        expect(user_struct.project_ids).to eq([])
      end
    end
  end

  describe 'has_one associations' do
    context 'regular has_one' do
      let(:user_class) do
        Class.new(Athar::Commons::ActiveStruct::Base) do
          has_one :profile
          attribute :name, :string
        end
      end

      it 'defines association methods' do
        user = user_class.new(name: 'John')
        expect(user).to respond_to(:profile)
        expect(user).to respond_to(:profile=)
        expect(user).to respond_to(:profile_id)
        expect(user).to respond_to(:profile_id=)
      end
    end

    context 'has_one through' do
      let(:user_struct_class) do
        Class.new(Athar::Commons::ActiveStruct::Base) do
          belongs_to :user, class_name: 'TestUser'
          has_one :default_role, through: :user, source: :test_user_roles,
                  conditions: { is_default: true }
          attribute :profile_data, :string
        end
      end

      it 'defines through association methods' do
        user_struct = user_struct_class.new
        expect(user_struct).to respond_to(:default_role)
        expect(user_struct).to respond_to(:default_role_id)
      end

      it 'retrieves single record through ActiveRecord with conditions' do
        user_struct = user_struct_class.new
        user_struct.user = user

        # Ensure the user_role is created and associated
        user_role # This triggers the lazy creation

        default_role = user_struct.default_role
        expect(default_role).to be_a(TestUserRole)
        expect(default_role.is_default).to be true

        expect(user_struct.default_role_id).to eq(default_role.id)
      end

      it 'handles conditions with symbol values' do
        condition_class = Class.new(Athar::Commons::ActiveStruct::Base) do
          belongs_to :user, class_name: 'TestUser'
          has_one :user_project, through: :user, source: :test_user_roles,
                  conditions: { user_id: :user_id }
          attribute :user_id, :integer
        end

        user_struct = condition_class.new
        user_struct.user = user
        user_struct.user_id = user.id

        # Ensure the user_role is created and associated
        user_role # This triggers the lazy creation

        # This should find the user_role that matches the user_id condition
        result = user_struct.user_project
        expect(result).to be_a(TestUserRole)
        expect(result.user_id).to eq(user.id)
      end

      it 'returns nil when no matching record found' do
        user_struct = user_struct_class.new
        # No user assigned

        expect(user_struct.default_role).to be_nil
        expect(user_struct.default_role_id).to be_nil
      end
    end
  end

  describe 'association metadata and reflection' do
    let(:test_class) do
      Class.new(Athar::Commons::ActiveStruct::Base) do
        belongs_to :user, class_name: 'TestUser'
        has_many :posts, through: :user, source: :test_projects
        has_one :profile, class_name: 'UserProfile'
        attribute :name, :string
      end
    end

    it 'stores association metadata' do
      associations = test_class._associations

      expect(associations).to have_key(:user)
      expect(associations[:user][:type]).to eq(:belongs_to)
      expect(associations[:user][:options][:class_name]).to eq('TestUser')

      expect(associations).to have_key(:posts)
      expect(associations[:posts][:type]).to eq(:has_many)
      expect(associations[:posts][:options][:through]).to eq(:user)

      expect(associations).to have_key(:profile)
      expect(associations[:profile][:type]).to eq(:has_one)
    end
  end

  describe 'error handling and edge cases' do
    context 'when target class does not exist' do
      let(:test_class) do
        Class.new(Athar::Commons::ActiveStruct::Base) do
          belongs_to :nonexistent_model
          attribute :name, :string
        end
      end

      it 'handles missing target class gracefully' do
        instance = test_class.new(name: 'Test')
        expect(instance).to respond_to(:nonexistent_model)
        expect(instance).to respond_to(:nonexistent_model_id)

        # Should not raise error when accessing
        expect { instance.nonexistent_model }.not_to raise_error
      end
    end

    context 'with invalid through association' do
      let(:test_class) do
        Class.new(Athar::Commons::ActiveStruct::Base) do
          has_many :items, through: :nonexistent_association, source: :items
          attribute :name, :string
        end
      end

      it 'handles missing through association gracefully' do
        instance = test_class.new(name: 'Test')
        expect(instance).to respond_to(:items)

        # Should return empty array when through association doesn't exist
        expect(instance.items).to eq([])
      end
    end

    context 'with ActiveRecord has_many associations' do
      let(:user_struct_class) do
        Class.new(Athar::Commons::ActiveStruct::Base) do
          has_many :test_projects, class_name: 'TestProject', foreign_key: 'owner_id'
          attribute :name, :string
        end
      end

      it 'properly detects and handles ActiveRecord targets' do
        # Should create the association without errors
        user_struct = user_struct_class.new(name: 'John', id: project.owner_id)

        # Should respond to all association methods
        expect(user_struct).to respond_to(:test_projects)
        expect(user_struct).to respond_to(:test_projects=)
        expect(user_struct).to respond_to(:test_project_ids)
        expect(user_struct).to respond_to(:test_project_ids=)

        # Should return ActiveRecord relation
        projects = user_struct.test_projects
        expect(projects).to respond_to(:where)
        expect(projects).to respond_to(:pluck)
      end
    end
  end

  describe 'type detection for foreign keys' do
    context 'with integer primary key' do
      it 'detects integer type for standard ActiveRecord models' do
        profile_class = Class.new(Athar::Commons::ActiveStruct::Base) do
          belongs_to :user, class_name: 'TestUser'
        end

        expect(profile_class.attribute_types['user_id']).to be_a(ActiveModel::Type::Integer)
      end
    end

    context 'with string primary key' do
      it 'detects string type for custom primary keys' do
        profile_class = Class.new(Athar::Commons::ActiveStruct::Base) do
          belongs_to :manager, class_name: 'TestUser',
                     foreign_key: 'manager_uuid', primary_key: 'uuid'
        end

        expect(profile_class.attribute_types['manager_uuid']).to be_a(ActiveModel::Type::String)
      end
    end

    context 'with unknown target class' do
      it 'defaults to string type when class cannot be determined' do
        profile_class = Class.new(Athar::Commons::ActiveStruct::Base) do
          belongs_to :unknown_model, class_name: 'NonExistentModel'
        end

        expect(profile_class.attribute_types['unknown_model_id']).to be_a(ActiveModel::Type::String)
      end
    end
  end
end
