# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Athar::Commons::Services::Export::ExportFactory do
  let(:resource) { double('Resource') }

  describe '.create' do
    context 'with supported formats' do
      it 'creates CSV export service' do
        service = described_class.create(resource, :csv)
        expect(service).to be_a(Athar::Commons::Services::Export::CsvExportService)
      end

      it 'creates PDF export service' do
        service = described_class.create(resource, :pdf)
        expect(service).to be_a(Athar::Commons::Services::Export::PdfExportService)
      end

      it 'creates XLSX export service' do
        service = described_class.create(resource, :xlsx)
        expect(service).to be_a(Athar::Commons::Services::Export::XlsxExportService)
      end

      it 'accepts string format' do
        service = described_class.create(resource, 'csv')
        expect(service).to be_a(Athar::Commons::Services::Export::CsvExportService)
      end
    end

    context 'with unsupported format' do
      it 'raises ArgumentError' do
        expect {
          described_class.create(resource, :unsupported)
        }.to raise_error(ArgumentError, /Unsupported export format/)
      end
    end
  end

  describe '.supported_format?' do
    it 'returns true for supported formats' do
      expect(described_class.supported_format?(:csv)).to be true
      expect(described_class.supported_format?(:pdf)).to be true
      expect(described_class.supported_format?(:xlsx)).to be true
    end

    it 'returns false for unsupported formats' do
      expect(described_class.supported_format?(:unsupported)).to be false
    end

    it 'accepts string format' do
      expect(described_class.supported_format?('csv')).to be true
    end
  end

  describe '.supported_formats' do
    it 'returns array of supported formats' do
      formats = described_class.supported_formats
      expect(formats).to contain_exactly(:csv, :pdf, :xlsx)
    end
  end
end
