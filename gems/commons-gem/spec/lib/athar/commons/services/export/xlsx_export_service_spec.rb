# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Athar::Commons::Services::Export::XlsxExportService do
  let(:records) do
    [
      double('Record', attributes: { 'id' => 1, 'name' => '<PERSON>', 'email' => '<EMAIL>' }),
      double('Record', attributes: { 'id' => 2, 'name' => '<PERSON>', 'email' => '<EMAIL>' })
    ]
  end
  let(:service) { described_class.new(records, :xlsx) }

  before do
    allow(Athar::Commons.configuration).to receive(:export_limits).and_return({ xlsx: 10_000 })
  end

  describe '#generate' do
    it 'generates XLSX content' do
      expect { service.generate }.not_to raise_error
    end

    it 'returns binary content' do
      content = service.generate
      expect(content).to be_a(String)
      expect(content.length).to be > 0
    end
  end

  describe '#mime_type' do
    it 'returns XLSX MIME type' do
      expect(service.mime_type).to eq('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    end
  end

  describe '#file_extension' do
    it 'returns xlsx extension' do
      expect(service.file_extension).to eq('xlsx')
    end
  end
end
