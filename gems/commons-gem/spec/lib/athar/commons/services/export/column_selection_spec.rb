# frozen_string_literal: true

require 'rails_helper'

RSpec.describe "Export Column Selection", type: :service do
  # Create test models with associations
  before(:all) do
    ActiveRecord::Schema.define do
      create_table :test_employees, force: true do |t|
        t.string :name
        t.string :email
        t.string :department
        t.integer :salary
        t.integer :manager_id
        t.timestamps
      end

      create_table :test_managers, force: true do |t|
        t.string :name
        t.string :title
        t.timestamps
      end
    end

    Object.const_set('TestManager', Class.new(ActiveRecord::Base) do
      self.table_name = 'test_managers'
      has_many :test_employees, foreign_key: 'manager_id'
    end)

    Object.const_set('TestEmployee', Class.new(ActiveRecord::Base) do
      self.table_name = 'test_employees'
      belongs_to :manager, class_name: 'TestManager', optional: true

      # Add computed methods to test method support
      def full_name
        "#{name} (#{department})"
      end

      def display_email
        email&.downcase
      end
    end)
  end

  after(:all) do
    ActiveRecord::Schema.define do
      drop_table :test_employees if ActiveRecord::Base.connection.table_exists?(:test_employees)
      drop_table :test_managers if ActiveRecord::Base.connection.table_exists?(:test_managers)
    end
    Object.send(:remove_const, 'TestEmployee') if Object.const_defined?('TestEmployee')
    Object.send(:remove_const, 'TestManager') if Object.const_defined?('TestManager')
  end

  let(:manager) { TestManager.create!(name: 'Alice Johnson', title: 'Engineering Manager') }
  let(:records) do
    [
      TestEmployee.create!(name: 'John Doe', email: '<EMAIL>', department: 'Engineering', salary: 75000, manager: manager),
      TestEmployee.create!(name: 'Jane Smith', email: '<EMAIL>', department: 'Marketing', salary: 65000, manager: manager)
    ]
  end

  before do
    allow(Athar::Commons.configuration).to receive(:export_limits).and_return({ csv: 50_000 })
  end

  describe "Column Selection" do
    context "with specific columns (string format)" do
      let(:service) { Athar::Commons::Services::Export::CsvExportService.new(records, :csv, columns: 'id,name,email') }

      it "exports only selected columns" do
        csv_content = service.generate
        lines = csv_content.split("\n")

        expect(lines[0]).to eq('Id,Name,Email')
        expect(lines[1]).to match(/^\d+,John Doe,JOHN@EXAMPLE\.COM$/)
        expect(lines[2]).to match(/^\d+,Jane Smith,JANE@EXAMPLE\.COM$/)
      end

      it "excludes non-selected columns" do
        csv_content = service.generate
        expect(csv_content).not_to include('Department')
        expect(csv_content).not_to include('Salary')
      end
    end

    context "with specific columns (array format)" do
      let(:service) { Athar::Commons::Services::Export::CsvExportService.new(records, :csv, columns: ['id', 'name', 'department']) }

      it "exports only selected columns" do
        csv_content = service.generate
        lines = csv_content.split("\n")

        expect(lines[0]).to eq('Id,Name,Department')
        expect(lines[1]).to match(/^\d+,John Doe,Engineering$/)
        expect(lines[2]).to match(/^\d+,Jane Smith,Marketing$/)
      end

      it "excludes non-selected columns" do
        csv_content = service.generate
        expect(csv_content).not_to include('Email')
        expect(csv_content).not_to include('Salary')
      end
    end

    context "with column aliases" do
      let(:service) do
        Athar::Commons::Services::Export::CsvExportService.new(
          records,
          :csv,
          columns: 'name,email,salary',
          aliases: 'Employee Name,Email Address,Annual Salary'
        )
      end

      it "uses custom column names" do
        csv_content = service.generate
        lines = csv_content.split("\n")

        expect(lines[0]).to eq('Employee Name,Email Address,Annual Salary')
        expect(lines[1]).to eq('John Doe,<EMAIL>,75000')
      end
    end

    context "with hash-based aliases" do
      let(:service) do
        Athar::Commons::Services::Export::CsvExportService.new(
          records,
          :csv,
          columns: ['name', 'email', 'salary'],
          aliases: { 'name' => 'Full Name', 'email' => 'Email', 'salary' => 'Salary ($)' }
        )
      end

      it "uses hash-based aliases" do
        csv_content = service.generate
        lines = csv_content.split("\n")

        expect(lines[0]).to eq('Full Name,Email,Salary ($)')
        expect(lines[1]).to eq('John Doe,<EMAIL>,75000')
      end
    end

    context "with model methods" do
      let(:service) do
        Athar::Commons::Services::Export::CsvExportService.new(
          records,
          :csv,
          columns: 'name,full_name,display_email'
        )
      end

      it "supports computed model methods" do
        csv_content = service.generate
        lines = csv_content.split("\n")

        expect(lines[0]).to eq('Name,Full Name,Display Email')
        expect(lines[1]).to eq('John Doe,John Doe (Engineering),<EMAIL>')
        expect(lines[2]).to eq('Jane Smith,Jane Smith (Marketing),<EMAIL>')
      end
    end

    context "without column selection" do
      let(:service) { Athar::Commons::Services::Export::CsvExportService.new(records, :csv) }

      it "excludes timestamps by default" do
        csv_content = service.generate
        expect(csv_content).not_to include('Created at')
        expect(csv_content).not_to include('Updated at')
      end

      it "includes main data fields" do
        csv_content = service.generate
        expect(csv_content).to include('Id,Name,Email,Department,Salary')
      end
    end

    context "with association fields (unified columns)" do
      let(:service) do
        Athar::Commons::Services::Export::CsvExportService.new(
          records,
          :csv,
          columns: 'name,email,manager.name,manager.title'
        )
      end

      it "exports association fields using dot notation" do
        csv_content = service.generate
        lines = csv_content.split("\n")

        expect(lines[0]).to eq('Name,Email,Manager,Manager Title')
        expect(lines[1]).to eq('John Doe,<EMAIL>,Alice Johnson,Engineering Manager')
        expect(lines[2]).to eq('Jane Smith,<EMAIL>,Alice Johnson,Engineering Manager')
      end

      it "validates associations using ActiveRecord reflection" do
        # Test that the service correctly identifies manager.name as an association
        expect(service.send(:is_valid_association?, 'manager.name')).to be true
        expect(service.send(:is_valid_association?, 'invalid_association.field')).to be false
        expect(service.send(:is_valid_association?, 'name')).to be false
      end
    end

    context "with mixed regular and association fields" do
      let(:service) do
        Athar::Commons::Services::Export::CsvExportService.new(
          records,
          :csv,
          columns: 'id,name,department,manager.name'
        )
      end

      it "handles both regular and association fields correctly" do
        csv_content = service.generate
        lines = csv_content.split("\n")

        expect(lines[0]).to eq('Id,Name,Department,Manager')
        expect(lines[1]).to match(/^\d+,John Doe,Engineering,Alice Johnson$/)
        expect(lines[2]).to match(/^\d+,Jane Smith,Marketing,Alice Johnson$/)
      end
    end
  end

  describe "Multiple Export Formats" do
    let(:columns) { 'name,email,department' }

    it "works with PDF export" do
      service = Athar::Commons::Services::Export::PdfExportService.new(records, :pdf, columns: columns)
      expect { service.generate }.not_to raise_error
    end

    it "works with XLSX export" do
      service = Athar::Commons::Services::Export::XlsxExportService.new(records, :xlsx, columns: columns)
      expect { service.generate }.not_to raise_error
    end
  end

  describe "Error Handling" do
    context "with invalid columns" do
      let(:service) do
        Athar::Commons::Services::Export::CsvExportService.new(
          records,
          :csv,
          columns: 'id,invalid_method,name'
        )
      end

      it "handles invalid columns gracefully" do
        csv_content = service.generate
        lines = csv_content.split("\n")

        # Should include valid columns and handle invalid ones
        expect(lines[0]).to include('Id')
        expect(lines[0]).to include('Name')
        expect(lines[1]).to match(/^\d+,.*,John Doe$/)
      end
    end
  end
end
