# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Athar::Commons::Services::Export::CsvExportService do
  # Create a simple test model for testing
  before(:all) do
    ActiveRecord::Schema.define do
      create_table :test_employees, force: true do |t|
        t.string :name
        t.string :email
        t.string :department
        t.timestamps
      end
    end

    # Define the test model class
    Object.const_set('TestEmployee', Class.new(ActiveRecord::Base) do
      self.table_name = 'test_employees'
    end)
  end

  after(:all) do
    # Clean up
    ActiveRecord::Schema.define do
      drop_table :test_employees if ActiveRecord::Base.connection.table_exists?(:test_employees)
    end
    Object.send(:remove_const, 'TestEmployee') if Object.const_defined?('TestEmployee')
  end

  let(:records) do
    [
      TestEmployee.create!(name: '<PERSON>', email: '<EMAIL>', department: 'Engineering'),
      TestEmployee.create!(name: '<PERSON>', email: '<EMAIL>', department: 'Marketing')
    ]
  end
  let(:resource) { records }
  let(:service) { described_class.new(resource, :csv, columns: 'id,name,email') }

  before do
    allow(Athar::Commons.configuration).to receive(:export_limits).and_return({ csv: 50_000 })
  end

  describe '#generate' do
    it 'generates CSV content with headers' do
      csv_content = service.generate
      lines = csv_content.split("\n")

      expect(lines[0]).to eq('Id,Name,Email')
      expect(lines[1]).to eq('1,John Doe,<EMAIL>')
      expect(lines[2]).to eq('2,Jane Smith,<EMAIL>')
      expect(lines.length).to eq(3)
    end

    context 'when export limit is exceeded' do
      let(:large_resource) { double('Resource', count: 60_000) }
      let(:large_service) { described_class.new(large_resource, :csv) }

      it 'raises an error' do
        expect { large_service.generate }.to raise_error(StandardError, /Export limit exceeded/)
      end
    end
  end

  describe '#mime_type' do
    it 'returns CSV MIME type' do
      expect(service.mime_type).to eq('text/csv')
    end
  end

  describe '#file_extension' do
    it 'returns csv extension' do
      expect(service.file_extension).to eq('csv')
    end
  end

  describe '#filename' do
    it 'generates filename with timestamp' do
      allow(Time).to receive(:current).and_return(Time.parse('2024-01-15 10:30:00'))
      filename = service.filename
      expect(filename).to match(/test_employees_20240115_103000\.csv/)
    end
  end
end
