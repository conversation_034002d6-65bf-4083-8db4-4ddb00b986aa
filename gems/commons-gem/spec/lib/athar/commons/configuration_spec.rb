# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Athar::Commons::Configuration do
  describe '#initialize' do
    it 'sets default export limits' do
      config = described_class.new
      expect(config.export_limits).to eq({
        csv: 50_000,
        pdf: 5_000,
        xlsx: 10_000
      })
    end
  end

  describe 'Athar::Commons.configure' do
    it 'allows configuration of export limits' do
      Athar::Commons.configure do |config|
        config.export_limits = {
          csv: 100_000,
          pdf: 10_000,
          xlsx: 20_000
        }
      end

      expect(Athar::Commons.configuration.export_limits).to eq({
        csv: 100_000,
        pdf: 10_000,
        xlsx: 20_000
      })
    end
  end

  describe 'Athar::Commons.configuration' do
    it 'returns the same instance' do
      config1 = Athar::Commons.configuration
      config2 = Athar::Commons.configuration
      expect(config1).to be(config2)
    end
  end
end
