require "spec_helper"

RSpec.describe Athar::Commons::Api::Concerns::ParameterTypeConverter do
  let(:controller_class) do
    Class.new(ActionController::API) do
      include Athar::Commons::Api::Concerns::ParameterTypeConverter

      def params
        @params ||= ActionController::Parameters.new({})
      end

      def request
        @request ||= double("Request",
                            get?: true,
                            parameters: {},
                            query_parameters: {}
        )
      end
    end
  end

  let(:controller) { controller_class.new }

  describe "#convert_to_boolean" do
    it "converts various string representations to boolean" do
      expect(controller.send(:convert_to_boolean, "true")).to eq(true)
      expect(controller.send(:convert_to_boolean, "t")).to eq(true)
      expect(controller.send(:convert_to_boolean, "yes")).to eq(true)
      expect(controller.send(:convert_to_boolean, "y")).to eq(true)
      expect(controller.send(:convert_to_boolean, "1")).to eq(true)

      expect(controller.send(:convert_to_boolean, "false")).to eq(false)
      expect(controller.send(:convert_to_boolean, "f")).to eq(false)
      expect(controller.send(:convert_to_boolean, "no")).to eq(false)
      expect(controller.send(:convert_to_boolean, "n")).to eq(false)
      expect(controller.send(:convert_to_boolean, "0")).to eq(false)
    end

    it "returns the original value for non-boolean strings" do
      expect(controller.send(:convert_to_boolean, "something")).to eq("something")
    end
  end

  describe "#convert_to_array" do
    it "returns the original value if it's already an array" do
      array = [ 1, 2, 3 ]
      expect(controller.send(:convert_to_array, array)).to eq(array)
    end

    it "converts comma-separated strings to arrays" do
      expect(controller.send(:convert_to_array, "a,b,c")).to eq([ "a", "b", "c" ])
      expect(controller.send(:convert_to_array, "1, 2, 3")).to eq([ "1", "2", "3" ])
    end

    it "wraps non-array, non-comma-separated values in an array" do
      expect(controller.send(:convert_to_array, "single")).to eq([ "single" ])
      expect(controller.send(:convert_to_array, 123)).to eq([ 123 ])
    end
  end
end
