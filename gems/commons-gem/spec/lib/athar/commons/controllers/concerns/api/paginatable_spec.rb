require "spec_helper"

RSpec.describe Athar::Commons::Api::Concerns::Paginatable do
  let(:controller_class) do
    Class.new(ActionController::API) do
      include Athar::Commons::Api::Concerns::Paginatable

      def params
        @params ||= {}
      end
    end
  end

  let(:controller) { controller_class.new }
  let(:pagy) { double("Pagy", page: 2, pages: 10, count: 100, vars: { items: 25 }) }
  let(:collection) { double("Collection") }
  let(:records) { double("Records") }

  describe "#paginate" do
    it "paginates the collection and returns records with metadata" do
      expect(controller).to receive(:pagy).with(collection, jsonapi: true).and_return([ pagy, records ])
      expect(controller).to receive(:pagy_metadata).with(pagy).and_return({ current_page: 2 })

      result_records, result_meta = controller.paginate(collection)

      expect(result_records).to eq(records)
      expect(result_meta).to eq({ pagination: { current_page: 2 } })
    end

    it "uses limit from params if provided" do
      # Set up params to include page limit parameter
      controller.params[:page] = { Pagy::DEFAULT[:limit_param] => 50 }

      expect(controller).to receive(:pagy).with(collection, jsonapi: true, limit: 50).and_return([ pagy, records ])
      expect(controller).to receive(:pagy_metadata).with(pagy).and_return({ current_page: 2 })

      controller.paginate(collection)
    end
  end

  describe "#pagy_metadata" do
    it "has access to pagy_metadata method from Pagy::Backend" do
      # Just verify the private method exists - testing Pagy's implementation is not our responsibility
      expect(controller.private_methods).to include(:pagy_metadata)

      # Verify that the concern includes Pagy::Backend
      expect(controller.class.ancestors).to include(Pagy::Backend)
    end
  end
end
