# frozen_string_literal: true

require "active_support"
require "active_support/core_ext"
require "ostruct"

# Load Rails components in the correct order for Rails 8.0 compatibility
require "rails"
require "active_model"
require "active_record"
require "action_controller"

# Load ActionView but skip ERB handler to avoid Rails 8.0 compatibility issues
begin
  require "action_view"
rescue NameError, ArgumentError => e
  # Ignore Rails 8.0 ActionView issues
  puts "Warning: ActionView loading issue: #{e.message}" if ENV['DEBUG']
end

require "rspec"
require "factory_bot"

# Load the main gem
require "athar_commons"

# Manually load concerns since autoloading doesn't work in gem tests
gem_root = File.expand_path("..", __dir__)

# Load all controller concerns
Dir[File.join(gem_root, "app/controllers/concerns/**/*.rb")].sort.each do |file|
  require file
end

# Load all model concerns
Dir[File.join(gem_root, "app/models/concerns/**/*.rb")].sort.each do |file|
  require file
end

# Set up database connection for ActiveRecord
ActiveRecord::Base.establish_connection(
  adapter: 'sqlite3',
  database: ':memory:'
)

# Create a minimal schema for testing
ActiveRecord::Schema.define do
  create_table :employees, force: true do |t|
    t.string :name
    t.integer :department, default: 0
    t.integer :status, default: 0
    t.timestamps
  end
end

RSpec.configure do |config|
  # Enable flags like --only-failures and --next-failure
  config.example_status_persistence_file_path = ".rspec_status"

  # Disable RSpec exposing methods globally on `Module` and `main`
  config.disable_monkey_patching!

  config.expect_with :rspec do |c|
    c.syntax = :expect
  end

  config.include FactoryBot::Syntax::Methods
end
