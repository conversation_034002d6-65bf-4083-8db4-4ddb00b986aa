# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Athar::Commons::Api::Concerns::Serializable, type: :controller do
  let(:test_records) do
    records = [
      double('Record',
        attributes: { 'id' => 1, 'name' => '<PERSON>', 'email' => '<EMAIL>' },
        id: 1,
        name: '<PERSON>',
        email: '<EMAIL>'
      ),
      double('Record',
        attributes: { 'id' => 2, 'name' => '<PERSON>', 'email' => '<EMAIL>' },
        id: 2,
        name: '<PERSON>',
        email: '<EMAIL>'
      )
    ]

    # Mock the array to respond to first for column_names detection
    allow(records).to receive(:first).and_return(records[0])
    allow(records).to receive(:count).and_return(2)
    records
  end

  controller(ActionController::API) do
    include Athar::Commons::Api::Concerns::Serializable
    include Athar::Commons::Api::Concerns::Exportable

    def index
      serialize_response(@test_records)
    end

    attr_accessor :test_records
  end

  before do
    routes.draw do
      get 'index' => 'anonymous#index'
      get 'index.:format' => 'anonymous#index'
    end
    allow(Athar::Commons.configuration).to receive(:export_limits).and_return({ csv: 50_000, pdf: 5_000, xlsx: 10_000 })
    controller.instance_variable_set(:@test_records, test_records)

    # Register MIME types for testing
    Mime::Type.register "text/csv", :csv unless Mime::Type.lookup_by_extension(:csv)
    Mime::Type.register "application/pdf", :pdf unless Mime::Type.lookup_by_extension(:pdf)
    Mime::Type.register "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", :xlsx unless Mime::Type.lookup_by_extension(:xlsx)
  end

  describe '#serialize_response' do
    context 'when request format is JSON' do
      it 'renders JSON:API response' do
        get :index, format: :json
        expect(response).to have_http_status(:ok)
        # The content type might be text/plain in test environment, but the important thing is that it doesn't export
        expect(response.headers['Content-Disposition']).to be_nil
      end
    end

    context 'when request format is CSV' do
      it 'renders CSV file' do
        get :index, format: :csv
        expect(response).to have_http_status(:ok)
        # In test environment, content type might be text/plain, but check for attachment header
        expect(response.headers['Content-Disposition']).to include('attachment')
        expect(response.headers['Content-Disposition']).to include('.csv')
      end
    end

    context 'when request format is PDF' do
      it 'renders PDF file' do
        get :index, format: :pdf
        expect(response).to have_http_status(:ok)
        # In test environment, content type might be text/plain, but check for attachment header
        expect(response.headers['Content-Disposition']).to include('attachment')
        expect(response.headers['Content-Disposition']).to include('.pdf')
      end
    end

    context 'when request format is XLSX' do
      it 'renders XLSX file' do
        get :index, format: :xlsx
        expect(response).to have_http_status(:ok)
        # In test environment, content type might be text/plain, but check for attachment header
        expect(response.headers['Content-Disposition']).to include('attachment')
        expect(response.headers['Content-Disposition']).to include('.xlsx')
      end
    end

    context 'when export fails' do
      before do
        # Mock the export service to raise an error during generation
        export_service = double('ExportService')
        allow(export_service).to receive(:generate).and_raise(StandardError, 'Export failed')
        allow(Athar::Commons::Services::Export::ExportFactory).to receive(:create).and_return(export_service)

        # Mock serialize_errors to render a simple JSON response since jsonapi_errors
        # might not be available in the test environment
        allow(controller).to receive(:serialize_errors) do |errors, status|
          controller.render json: { errors: [errors] }, status: status
        end
      end

      it 'renders error response' do
        get :index, format: :csv
        expect(response).to have_http_status(:unprocessable_entity)

        # Verify that serialize_errors was called with the correct error message
        expect(controller).to have_received(:serialize_errors).with(
          { detail: "Export failed: Export failed" },
          :unprocessable_entity
        )

        # Verify the response contains the error
        response_body = JSON.parse(response.body)
        expect(response_body['errors'].first['detail']).to include('Export failed')
      end
    end
  end

  describe '#detect_export_format' do
    it 'detects format from request format' do
      get :index, format: :csv
      expect(controller.send(:detect_export_format)).to eq(:csv)
    end
  end
end
