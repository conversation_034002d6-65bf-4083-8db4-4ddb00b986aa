# frozen_string_literal: true

require 'rails_helper'

# Configure Apipie for testing
::Apipie.configure do |config|
  config.validate = false
  config.app_info = 'Test API'
  config.api_base_url = '/api'
  config.doc_base_url = '/apidoc'
  config.default_version = '1.0'
  config.validate_value = false
  config.validate_presence = false
  config.validate_key = false
  config.process_params = false
  config.app_name = 'Test App'
  config.copyright = ''
  config.markup = ::Apipie::Markup::RDoc.new
  config.namespaced_resources = false
  config.show_all_examples = true
  config.use_cache = false
end

# Test controller for parameter conversion (without <PERSON><PERSON>pie DSL to avoid complexity)
class ParameterTypeConverterTestController < ActionController::Base
  include Athar::Commons::Api::Concerns::ParameterTypeConverter

  # Configure parameter conversion using custom converters instead of Apipie
  convert_params do
    convert :id, as: :integer
    convert :page, as: :hash_with_conversions
    convert :filter, as: :hash_with_conversions
  end

  def index
    render json: params.to_unsafe_h
  end

  def create
    render json: params.to_unsafe_h
  end

  private

  # Mock the Apipie method definitions that the concern expects
  def self.apipie_method_definitions
    {
      'index' => {
        'params' => {
          'id' => { 'type' => :number },
          'page' => {
            'type' => Hash,
            'params' => {
              'number' => { 'type' => :number },
              'size' => { 'type' => :number }
            }
          },
          'filter' => {
            'type' => Hash,
            'params' => {
              'active' => { 'type' => :boolean },
              'ids' => { 'type' => Array },
              'nested' => {
                'type' => Hash,
                'params' => {
                  'field1' => { 'type' => :number },
                  'field2' => { 'type' => :boolean },
                  'deep' => {
                    'type' => Hash,
                    'params' => {
                      'very_deep' => { 'type' => :number }
                    }
                  }
                }
              }
            }
          }
        }
      },
      'create' => {
        'params' => {
          'user' => {
            'type' => Hash,
            'params' => {
              'name' => { 'type' => String },
              'age' => { 'type' => :number }
            }
          }
        }
      }
    }
  end
end

RSpec.describe Athar::Commons::Api::Concerns::ParameterTypeConverter do
  # Test the individual conversion methods directly
  let(:controller_class) do
    Class.new(ActionController::Base) do
      include Athar::Commons::Api::Concerns::ParameterTypeConverter
    end
  end

  let(:controller) { controller_class.new }

  describe "conversion methods" do
    describe "#convert_to_boolean" do
      it "converts various string representations to boolean" do
        expect(controller.send(:convert_to_boolean, "true")).to eq(true)
        expect(controller.send(:convert_to_boolean, "t")).to eq(true)
        expect(controller.send(:convert_to_boolean, "yes")).to eq(true)
        expect(controller.send(:convert_to_boolean, "y")).to eq(true)
        expect(controller.send(:convert_to_boolean, "1")).to eq(true)

        expect(controller.send(:convert_to_boolean, "false")).to eq(false)
        expect(controller.send(:convert_to_boolean, "f")).to eq(false)
        expect(controller.send(:convert_to_boolean, "no")).to eq(false)
        expect(controller.send(:convert_to_boolean, "n")).to eq(false)
        expect(controller.send(:convert_to_boolean, "0")).to eq(false)
      end

      it "returns the original value for non-boolean strings" do
        expect(controller.send(:convert_to_boolean, "maybe")).to eq("maybe")
        expect(controller.send(:convert_to_boolean, "123")).to eq("123")
      end
    end

    describe "#convert_to_array" do
      it "returns the original value if it's already an array" do
        array = [ 1, 2, 3 ]
        expect(controller.send(:convert_to_array, array)).to eq(array)
      end

      it "converts comma-separated strings to arrays" do
        expect(controller.send(:convert_to_array, "1,2,3")).to eq([ "1", "2", "3" ])
        expect(controller.send(:convert_to_array, "a, b, c")).to eq([ "a", "b", "c" ])
      end

      it "wraps non-array, non-comma-separated values in an array" do
        expect(controller.send(:convert_to_array, "single")).to eq([ "single" ])
        expect(controller.send(:convert_to_array, 123)).to eq([ 123 ])
      end
    end

    describe "#convert_value" do
      it "converts integers" do
        expect(controller.send(:convert_value, "123", :integer)).to eq(123)
        expect(controller.send(:convert_value, "456", :number)).to eq(456)
      end

      it "converts decimals" do
        expect(controller.send(:convert_value, "12.34", :decimal)).to eq(12.34)
        expect(controller.send(:convert_value, "56.78", :float)).to eq(56.78)
      end

      it "converts booleans" do
        expect(controller.send(:convert_value, "true", :boolean)).to eq(true)
        expect(controller.send(:convert_value, "false", :boolean)).to eq(false)
      end

      it "converts arrays" do
        expect(controller.send(:convert_value, "1,2,3", :array)).to eq([ "1", "2", "3" ])
      end

      it "returns original value for unknown types" do
        expect(controller.send(:convert_value, "test", :unknown)).to eq("test")
      end
    end
  end
end
