require 'spec_helper'

RSpec.describe "Template Files" do
  let(:templates) do
    [
      "lib/generators/athar/commons/approval/templates/approval_request.rb.tt",
      "lib/generators/athar/commons/approval/templates/create_approval_requests.rb.tt",
      "lib/generators/athar/commons/approval/templates/create_approval_steps.rb.tt",
      "lib/generators/athar/commons/approval/templates/approval_request_serializer.rb.tt"
    ]
  end

  it "are valid ERB template files" do
    templates.each do |template_path|
      full_path = File.join(File.dirname(__FILE__), "../../#{template_path}")
      expect(File.exist?(full_path)).to be true

      content = File.read(full_path)

      # Check that the template can be parsed as ERB
      expect { ERB.new(content) }.not_to raise_error

      # Check for excessive consecutive empty lines (more than 3)
      excessive_empty_lines = content.scan(/\n\s*\n\s*\n\s*\n\s*\n/)
      expect(excessive_empty_lines).to be_empty,
                                       "Template #{template_path} has excessive consecutive empty lines"
    end
  end
end
