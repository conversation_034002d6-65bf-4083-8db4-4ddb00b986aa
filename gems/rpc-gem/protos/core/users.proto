syntax = "proto3";

package core;

option ruby_package = "Core";

service Users {
  rpc GetUser (UserRequest) returns (UserResponse);
  rpc ListUsers (UserListRequest) returns (UserListResponse);
  rpc UpdateUser (UpdateUserRequest) returns (UserResponse);
  rpc CreateUser (CreateUserRequest) returns (UserResponse);
}

message UserRequest {
  int64 id = 1;
}

message AvatarAttributesResponse {
  string url = 1; // URL for the user's avatar
  string filename = 2; // Original filename with extension
  string content_type = 3; // MIME type of the image
  int64 bytes_size = 4; // Size of the avatar image in bytes
}

message UserResponse {
  int64 id = 1;
  string name = 2;
  string email = 3;
  AvatarAttributesResponse avatar_attributes = 4; // Avatar information
  repeated UserRoleResponse user_roles_list = 5; // List of user roles with associated projects
  string status = 6; // User's status: active or inactive

  // Response metadata - separate from resource data
  ResponseMetadata _metadata = 99; // Response metadata including success/error info
}

message UserRoleResponse {
  int64 id = 1; // User role ID
  RoleResponse role = 2; // Role information
  ProjectResponse project = 3; // Project associated with this role (if applicable)
  bool is_default = 4; // Whether this is the default role for the user
}

message RoleResponse {
  int64 id = 1; // Role ID
  string name = 2; // Role name
  bool global = 3; // Whether this is a global role
  int32 level = 4; // Level of the role (optional)
}

message ProjectResponse {
  int64 id = 1; // Project ID
  string name = 2; // Project name,
  string description = 3; // Project description
  string status = 4; // Project status: active or inactive
}

message UserListRequest {
  repeated int64 ids = 1; // List of user IDs to retrieve
  map<string, string> q = 2; // Ransack-style search parameters
  repeated string include = 3; // Associations to include in the response
  int32 page = 4; // Pagination page number
  int32 per_page = 5; // Items per page
  string sort = 6; // Sort field and direction (e.g., "name asc")
  map<string, string> scope = 7; // Named scopes to apply
  map<string, string> where = 8; // ActiveRecord-style where conditions
  repeated string _fields = 99; // Internal: tracks which fields were originally sent
}

message UserListResponse {
  repeated UserResponse items = 1; // List of user details
  int32 total_count = 2; // Total number of records matching the query
  int32 page = 3; // Current page number
  int32 per_page = 4; // Items per page
  int32 total_pages = 5; // Total number of pages
}

message AvatarAttributesRequest {
  bytes image = 1; // Binary image data for avatar
  string filename = 2; // Original filename with extension
  string content_type = 3; // MIME type of the image
  int64 bytes_size = 4; // Size of the avatar image in bytes
}

message UserRoleRequest {
  int64 id = 1; // User role request ID
  int64 role_id = 2; // ID of the role to assign
  int64 project_id = 3; // Project ID (optional for global roles)
  bool is_default = 4; // Whether this is the default role for the user
  int32 level = 5; // Level of the role (optional)
  RoleResponse role = 6; // Role information
  ProjectResponse project = 7; // Project associated with this role (if applicable)
}

message UpdateUserRequest {
  int64 id = 1; // ID of the user to update
  optional string name = 2; // Updated name (optional)
  optional string email = 3; // Updated email (optional)
  optional string status = 4; // Updated status (optional)
  optional string password = 5; // Updated password (optional)
  optional AvatarAttributesRequest avatar_attributes = 6; // Avatar information (optional)
  repeated UserRoleRequest user_roles_list = 7; // User roles to assign (optional)
  repeated string _fields = 99; // Internal: tracks which fields were originally sent
}

message CreateUserRequest {
  string name = 1; // User name (required)
  string email = 2; // User email (required)
  string status = 3; // Status (optional)
  string password = 4; // Password (required)
  AvatarAttributesRequest avatar_attributes = 5; // Avatar information (optional)
  repeated UserRoleRequest user_roles_list = 6; // User roles to assign (optional)
  repeated string _fields = 99; // Internal: tracks which fields were originally sent
}

// Response metadata - keeps validation/error info separate from resource data
message ResponseMetadata {
  bool success = 1; // Whether the operation was successful
  map<string, ErrorMessages> errors = 2; // Rails errors object (when success = false)
}

// Simple structure to hold error messages for a field
message ErrorMessages {
  repeated string messages = 1; // Error messages for this field
}
