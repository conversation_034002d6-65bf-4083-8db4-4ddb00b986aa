syntax = "proto3";

package core;

service ApprovalWorkflows {
  rpc GetWorkflow (GetWorkflowRequest) returns (ApprovalWorkflowResponse);
  rpc GenerateSequence (GenerateSequenceRequest) returns (ApprovalSequenceResponse);
  rpc ValidateApprover (ValidateApproverRequest) returns (ValidateApproverResponse);
  rpc ListWorkflows (ListWorkflowsRequest) returns (ApprovalWorkflowListResponse);
}

message GetWorkflowRequest {
  string action = 1;
  string subject_class = 2;
  string system_name = 3;
  repeated string _fields = 99; // Internal: tracks which fields were originally sent
}

message GenerateSequenceRequest {
  string action = 1;
  string subject_class = 2;
  string system_name = 3;
  string requestor_id = 4;
  string project_id = 5;
  map<string, string> context = 6;
  repeated string _fields = 99; // Internal: tracks which fields were originally sent
}

message ValidateApproverRequest {
  string user_id = 1;
  repeated string approver_ids = 2;
  repeated string _fields = 99; // Internal: tracks which fields were originally sent
}

message ListWorkflowsRequest {
  string system_name = 1;
  repeated string ids = 2; // List of workflow IDs to retrieve
  map<string, string> q = 3; // Ransack-style search parameters
  repeated string include = 4; // Associations to include in the response
  int32 page = 5; // Pagination page number
  int32 per_page = 6; // Items per page
  string sort = 7; // Sort field and direction (e.g., "name asc")
  map<string, string> scope = 8; // Named scopes to apply
  repeated string _fields = 99; // Internal: tracks which fields were originally sent
}

message ApprovalStepResponse {
  string id = 1;
  string workflow_id = 2;
  string name = 3;
  int32 sequence = 4;
  string approval_type = 5;
  string dynamic_approver_method = 6;
  string description = 7;
}

message ApprovalWorkflowResponse {
  string id = 1;
  string name = 2;
  string action = 3;
  string subject_class = 4;
  string system_name = 5;
  string description = 6;
  bool active = 7;
  repeated ApprovalStepResponse steps = 8;
}

message ApprovalStepSequence {
  string step_id = 1;
  string name = 2;
  int32 sequence = 3;
  string approval_type = 4;
  repeated string approver_ids = 5;
  string selection_explanation = 6;
}

message ApprovalSequenceResponse {
  string workflow_id = 1;
  string workflow_name = 2;
  repeated ApprovalStepSequence steps = 3;
  bool auto_approved = 4;
  string auto_approval_reason = 5;
}

message ValidateApproverResponse {
  bool can_approve = 1;
}

message ApprovalWorkflowListResponse {
  repeated ApprovalWorkflowResponse items = 1;
  int32 total_count = 2; // Total number of records matching the query
  int32 page = 3; // Current page number
  int32 per_page = 4; // Items per page
  int32 total_pages = 5; // Total number of pages
}
