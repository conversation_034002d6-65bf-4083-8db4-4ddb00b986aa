# athar_rpc

🚀 **Athar RPC** is a shared gRPC service definitions gem for the Athar project.
It provides gRPC message structures and service interfaces for various subsystems.

## 📌 Features
- ✅ gRPC-based inter-service communication
- ✅ Supports multiple subsystems (`core`, `people`, etc.)
- ✅ Organized message & service definitions
- ✅ Easy integration in Ruby applications
- ✅ Automated setup for clients and servers
- ✅ ActiveRpc concern for seamless ActiveRecord integration
- ✅ Support for creating, reading, and updating data via gRPC
- ✅ Validation error handling for seamless integration with ActiveRecord validations
- ✅ Image upload support for user avatars

---

## 📺 Installation

### **1️⃣ Add to Your `Gemfile`**
```ruby
source "https://gem.fury.io/athar11/" do
  gem 'athar_rpc', '~> 0.1.0'
end
```
Then install:
```bash
bundle install
```

---

## 🛠 Usage

### **1️⃣ Require the Generated gRPC Services**
```ruby
require 'athar_rpc_services/core/user_services_pb'
```

### **2️⃣ Running the gRPC Server with Gruf**
Athar RPC is designed to work with [Gruf](https://github.com/bigcommerce/gruf), a framework for running gRPC services in a Rails environment.

#### **Step 1: Create a Gruf Initializer**
You can generate an initializer using the setup Rake task:
```bash
bundle exec rails generate athar_rpc:server_setup
```
This will create an initializer file in `config/initializers/gruf.rb`:
```ruby
# frozen_string_literal: true

require 'athar_rpc'

Gruf.configure do |config|
  config.server_binding_url = "#{ENV['GRPC_SERVER_HOST']}:#{ENV['GRPC_SERVER_PORT']}"
  config.rpc_server_options = config.rpc_server_options.merge(
    pool_size: ENV['GRPC_SERVER_POOL_SIZE'].to_i,
    pool_keep_alive: ENV['GRPC_SERVER_POOL_KEEP_ALIVE'].to_i,
    poll_period: ENV['GRPC_SERVER_POLL_PERIOD'].to_i
  )

  config.interceptors.use Gruf::Interceptors::Instrumentation::RequestLogging::Interceptor, logger: Rails.logger
  config.error_serializer = Gruf::Serializers::Errors::Json
  config.use_ssl = false
end
```

#### **Step 2: Generate Gruf Controllers for gRPC Services**
You can auto-generate controllers for the defined proto services using:
```bash
bundle exec rails generate athar_rpc:gruf_controller core/users.proto
```
This will generate controllers like:
```ruby
require "athar_rpc_services/core/users_services_pb"

module Core
  class UsersController < Gruf::Controllers::Base
    bind ::Core::Users::Service

    def get_user
      request_payload = request.message
      # Implement business logic here
      # Example response:
      ::Core::UserResponse.new(message: "Success")
    rescue StandardError => e
      fail!(:internal, :unexpected_error, "Error: #{e.message}")
    end
  end
end
```

#### **Step 3: Start the gRPC Server**
Run the Gruf server with:
```bash
bundle exec gruf
```
This will start the gRPC server and load all available Gruf controllers.

---

## 🌐 Using Athar RPC in Client Applications
If you want to **consume** the gRPC services from another application, follow these steps:

### **Step 1: Install Athar RPC in the Client**
Add Athar RPC to your `Gemfile`:
```ruby
source "https://gem.fury.io/athar11/" do
  gem 'athar_rpc', '~> 0.1.0'
end
```
Then install:
```bash
bundle install
```

### **Step 2: Require the gRPC Service Stub**
```ruby
require 'athar_rpc_services/core/user_services_pb'
```

### **Step 3: Setup your gRPC Client**

#### **Generate Client Configuration**
To set up gRPC configurations, run:
```bash
bundle exec rails generate athar_rpc:client_setup
```
This will create `config/grpc_clients.yml`:
```yaml
default: &default
  core:
    grpc_host: "core-app-grpc"
    grpc_port: 10541
    use_ssl: true
    services:
      - Users
...

development:
  <<: *default
```

### **Step 4: Use the gRPC Client Factory**
After setting up, you can make calls to the gRPC services easily using the `GrpcClientFactory`:
```ruby
core_users_client = AtharRpc::GrpcClientFactory.instance.client_for("core", "Users")
response = core_users_client.call(:GetUser, id: '1')
puts response.message.inspect
```

### **Step 5: Using ActiveRpc with ActiveRecord Models**
The gem provides an `ActiveRpc` concern that makes it easy to integrate gRPC services with your ActiveRecord models:

```ruby
class Employee < ApplicationRecord
  include AtharRpc::ActiveRpc

  # Configure the gRPC integration
  # Parameters:
  # - service: The service namespace (e.g., :core)
  # - resource: The resource name (e.g., :User)
  # - attributes: Array of attributes to expose from the gRPC response
  # - options: Additional options (foreign_key, method_name, etc.)
  active_rpc :core, :User, [:name, :email], foreign_key: :user_id

  # This will automatically create methods for each attribute
  # and handle fetching data from the gRPC service
 end
```

After setting this up, you can access the gRPC attributes directly on your model:

```ruby
employee = Employee.find(1)
puts employee.name  # Fetches from the gRPC service
puts employee.email # Fetches from the gRPC service

# Force reload the data from the gRPC service
employee.reload_user
```

#### **Updating Attributes with ActiveRpc**
The ActiveRpc concern now supports updating attributes and saving the changes to the gRPC service:

```ruby
employee = Employee.find(1)

# Update attributes locally (no RPC calls made yet)
employee.name = "Jane Smith"
employee.email = "<EMAIL>"

# Check if there are pending updates
if employee.has_user_updates?
  puts "Pending updates: #{employee.pending_user_updates.inspect}"
end

# Save the updates to the gRPC service
if employee.update_user
  puts "Updates saved successfully!"
else
  puts "Failed to save updates"
end

# Or discard the updates without saving
employee.discard_user_updates
```

#### **Creating Records with ActiveRpc**
The ActiveRpc concern also supports creating new records via the gRPC service:

```ruby
# Create a new user and get its ID
user_id = Employee.create_user(
  name: "New User",
  email: "<EMAIL>",
  roles: ["user"],
  permissions: ["read"]
)

# Create a new employee with this user ID
employee = Employee.create!(user_id: user_id, department: "IT")

# Or create and associate a new user with an existing employee
employee = Employee.new(department: "IT")
employee.save

if employee.create_and_associate_user(name: "New User", email: "<EMAIL>")
  puts "User created and associated successfully!"
else
  puts "Failed to create and associate user"
end
```

#### **Implicit Integration with ActiveRpc**
The ActiveRpc concern now provides seamless integration with ActiveRecord, making delegated attributes behave just like regular model attributes:

```ruby
# Create a new employee and user in one go
employee = Employee.new(department: "IT")
employee.build_user(
  name: "New User",
  email: "<EMAIL>",
  roles: ["user"],
  permissions: ["read"]
)

# Save both the employee and user in one step
employee.save

# Update both the employee and user in one step
employee = Employee.find(1)
employee.department = "HR"  # Employee attribute
employee.name = "Updated Name"  # Delegated user attribute
employee.save  # This will update both the employee and the user
```

#### **Validations for Delegated Attributes**
You can add validations for delegated attributes just like regular ActiveRecord validations:

```ruby
class Employee < ApplicationRecord
  include AtharRpc::ActiveRpc

  active_rpc :core, :User, [:name, :email, :roles, :permissions], foreign_key: :user_id

  # Add validations for delegated attributes
  validates_user_attribute :email, presence: true, format: /\A[^@\s]+@[^@\s]+\z/
  validates_user_attribute :name, presence: true, length: { minimum: 2, maximum: 50 }

  # Custom validation with a Proc
  validates_user_attribute :roles, validate: ->(roles) {
    roles.include?('user') || 'must include the user role'
  }
 end
```

#### **Change Tracking for Delegated Attributes**
You can track changes to delegated attributes just like regular ActiveRecord attributes:

```ruby
employee = Employee.find(1)
employee.name = "New Name"

# Check if delegated attributes have changed
if employee.user_changed?
  puts "User attributes have changed"
  puts "Changes: #{employee.user_changes.inspect}"
end
```

This ensures that:
1. When you save a model, any pending updates to its delegated record are also saved
2. If the delegated record update fails, the model save is aborted
3. When you create a new model, you can also create its delegated record in one step
4. Validations are applied to delegated attributes before saving
5. Changes to delegated attributes can be tracked

#### **Validation Error Handling**
The ActiveRpc concern now includes comprehensive validation error handling. When a gRPC service returns validation errors, they are automatically applied to the model's errors collection, making them available just like regular ActiveRecord validation errors:

```ruby
employee = Employee.find(1)
employee.email = 'invalid-email'

# Try to save the model
if employee.save
  puts "Employee saved successfully!"
else
  # Display validation errors from the gRPC service
  employee.errors.full_messages.each do |message|
    puts message
  end
  # Output: Email must be a valid email address
 end
```

This works with both update and create operations:

```ruby
# Creating a new user with invalid data
employee = Employee.new(department: "IT")
employee.build_user(
  name: "A", # Too short
  email: "invalid-email" # Invalid format
)

# Try to save the model
if employee.save
  puts "Employee saved successfully!"
else
  # Display validation errors from the gRPC service
  employee.errors.full_messages.each do |message|
    puts message
  end
  # Output: Name is too short (minimum is 2 characters)
  #         Email must be a valid email address
end
```

The validation errors are properly propagated from the gRPC service to the model, ensuring a consistent experience for developers.

#### **Lazy Loading with ActiveRpc**
The ActiveRpc concern uses lazy loading to avoid unnecessary gRPC calls. When you load a collection of records, no gRPC calls are made until you actually access an attribute that requires gRPC data:

```ruby
# This won't trigger any gRPC calls
employees = Employee.all

# This still won't trigger any gRPC calls
employees.each do |employee|
  puts employee.id  # No gRPC call needed
end

# This will trigger gRPC calls for each employee
employees.each do |employee|
  puts employee.name  # gRPC call needed
end
```

#### **Attribute Caching for Serialization**
The ActiveRpc concern includes an attribute caching mechanism to prevent individual RPC calls during serialization. When you use the bulk loading scope, all attribute values are cached on the model instances:

```ruby
# Load all employees with user data in a single RPC call
employees = Employee.with_user_data.all

# Serialize the employees - no additional RPC calls will be made
# because attribute values are cached on each employee instance
serialize_response(employees)

# Even when accessing attributes directly, cached values are used
employees.each do |employee|
  puts employee.name  # Uses cached value, no RPC call
  puts employee.email # Uses cached value, no RPC call
end
```

This is particularly useful when serializing collections of records, as it prevents the N+1 query problem with gRPC calls.

#### **Bulk Retrieval with ActiveRpc**
The ActiveRpc concern also supports bulk retrieval of records for better performance. This requires your gRPC service to implement a `List{Resource}s` method that accepts a list of IDs and returns a collection of items:

```protobuf
service Users {
  rpc GetUser (UserRequest) returns (UserResponse);
  rpc ListUsers (UserListRequest) returns (UserListResponse);
}

message UserListRequest {
  repeated string ids = 1; // List of user IDs to retrieve
}

message UserListResponse {
  repeated UserResponse items = 1; // List of user details
}
```

Once your proto file is set up correctly, you can use bulk retrieval in your models:

```ruby
# Fetch multiple users at once
user_data = Employee.list_users([1, 2, 3])

# Use the eager loading scope to preload all user data in a single request
employees = Employee.with_user_data.where(department_id: 5)
employees.each do |employee|
  puts employee.name  # No additional gRPC calls - data already loaded
  puts employee.email
end

# The scope is idempotent - calling it multiple times won't trigger duplicate RPC calls
# This is useful in complex query chains with pagination
employees = Employee.with_user_data.all

# Apply filters and pagination
filtered_employees = employees.where(active: true).limit(10)

# Call with_user_data again - only loads data for records that don't have it yet
filtered_employees.with_user_data

# Attribute values are cached to prevent individual RPC calls during serialization
filtered_employees.each do |employee|
  puts employee.name  # Uses cached value, no RPC call
  puts employee.email # Uses cached value, no RPC call
end

# Eager loading works with map(&:attribute) to avoid individual RPC calls
names = filtered_employees.map(&:name)  # Uses cached values, no additional RPC calls

# For even better performance, use the specialized map methods
names = filtered_employees.map_name  # Directly accesses cached values
```

#### **Custom RPC Calls with ActiveRpc**
You can also make custom RPC calls using the exposed client:

```ruby
# Access the gRPC client directly at the class level
client = Employee.user_client
response = client.call(:SearchUsers, query: "John")

# Make custom RPC calls from an instance
employee = Employee.find(1)
response = employee.call_user_rpc(:UpdateUserStatus, id: employee.user_id, status: "active")
```

#### **Image Upload with ActiveRpc**
You can upload avatar images for users directly through the `CreateUser` and `UpdateUser` RPC methods:

```ruby
# Get the client
client = AtharRpc::GrpcClientFactory.instance.client_for("core", "Users")

# Read the image file
image_path = '/path/to/avatar.jpg'
image_data = File.binread(image_path)
filename = File.basename(image_path)
content_type = 'image/jpeg'

# Create a new user with an avatar
request = Core::CreateUserRequest.new(
  name: 'John Doe',
  email: '<EMAIL>',
  password: 'secure_password',
  status: 'active',
  avatar_image: image_data,
  avatar_filename: filename,
  avatar_content_type: content_type
)

# Make the RPC call
response = client.call(:CreateUser, request)

# Handle the response
user = response.message
puts "User created with ID: #{user.id}"
puts "Avatar URL: #{user.avatar_url}"

# Update an existing user's avatar
update_request = Core::UpdateUserRequest.new(
  id: 'user123',
  avatar_image: image_data,
  avatar_filename: filename,
  avatar_content_type: content_type
)

# Make the RPC call
response = client.call(:UpdateUser, update_request)

# Handle the response
user = response.message
puts "User updated successfully"
puts "New avatar URL: #{user.avatar_url}"
```

For more detailed information on image upload, see the [Image Upload Guide](docs/image_upload_guide.md).

---

## 🔨 Development

### **1️⃣ Clone the Repository**
```bash
<NAME_EMAIL>:athar-association/rpc-protos.git
cd rpc-protos
```

### **2️⃣ Install Dependencies**
```bash
bundle install
```

### **3️⃣ Generate gRPC Files**
```bash
make generate
```

### **4️⃣ Build and Install the Gem**
```bash
make build
make install
```

### **5️⃣ Clean Generated Files**
```bash
make clean
```

---

## 🔗 Resources
- 📚 [gRPC Ruby Docs](https://grpc.io/docs/languages/ruby/)
- 📚 [Protocol Buffers Guide](https://protobuf.dev/)
- 📚 [Gruf](https://github.com/bigcommerce/gruf/)
