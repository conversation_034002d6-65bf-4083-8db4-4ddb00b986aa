# frozen_string_literal: true

require 'spec_helper'
require 'ostruct'
require 'active_support/all'
require 'concerns/active_rpc'

RSpec.describe "ActiveRpc with ActiveModel::Dirty" do
  before(:each) do
    # Clear the database
    TestUser.delete_all
    TestEmployee.delete_all

    # Create test data in the TestUser table
    @user = TestUser.create!(
      name: '<PERSON>',
      email: '<EMAIL>',
      roles: ['admin'],
      permissions: ['read', 'write'],
      status: 'active'
    )

    # Create an employee associated with the user
    @employee = TestEmployee.create!(
      department: 'Engineering',
      status: 'active',
      user_id: @user.id.to_s
    )

    # Set up a mock client for the TestEmployee model
    @client = RpcTestHelper::TestRpcClient::GrpcClient.new('core', 'Users')
    TestEmployee.grpc_client = @client
  end

  it "tracks changes to remote attributes" do
    # Find the employee
    employee = TestEmployee.find(@employee.id)

    # Verify that there are no changes initially
    expect(employee.changed?).to be false
    expect(employee.changes).to be_empty

    # Set a remote attribute
    employee.name = '<PERSON>'

    # Verify that the change is tracked
    expect(employee.changed?).to be true
    expect(employee.changes).to include('name')
    expect(employee.name_change).to eq(['<PERSON> Doe', '<PERSON> Doe'])
    expect(employee.name_was).to eq('John Doe')

    # Save the model to trigger the RPC call
    employee.save

    # Verify that the changes are cleared after save
    expect(employee.changed?).to be false
    expect(employee.changes).to be_empty

    # Verify that the attribute was updated
    expect(employee.name).to eq('Jane Doe')
  end

  it "tracks changes to empty hash values" do
    # Find the employee
    employee = TestEmployee.find(@employee.id)

    # Set roles to an empty hash
    employee.roles = {}

    # Verify that the change is tracked
    expect(employee.changed?).to be true
    expect(employee.changes).to include('roles')
    expect(employee.roles_change).to eq([['admin'], {}])
    expect(employee.roles_was).to eq(['admin'])

    # Save the model to trigger the RPC call
    employee.save

    # Verify that the changes are cleared after save
    expect(employee.changed?).to be false
    expect(employee.changes).to be_empty

    # Verify that the attribute was updated
    expect(employee.roles).to eq({})
  end

  it "tracks changes to nil values" do
    # Find the employee
    employee = TestEmployee.find(@employee.id)

    # Set roles to nil
    employee.roles = nil

    # Verify that the change is tracked
    expect(employee.changed?).to be true
    expect(employee.changes).to include('roles')
    expect(employee.roles_change).to eq([['admin'], nil])
    expect(employee.roles_was).to eq(['admin'])

    # Save the model to trigger the RPC call
    employee.save

    # Verify that the changes are cleared after save
    expect(employee.changed?).to be false
    expect(employee.changes).to be_empty

    # Verify that the attribute was updated
    expect(employee.roles).to eq([])
  end

  it "tracks changes to non-empty hash values" do
    # Find the employee
    employee = TestEmployee.find(@employee.id)

    # Set roles to a non-empty hash
    employee.roles = { admin: true, user: true }

    # Verify that the change is tracked
    expect(employee.changed?).to be true
    expect(employee.changes).to include('roles')
    expect(employee.roles_change).to eq([['admin'], { 'admin' => true, 'user' => true }])
    expect(employee.roles_was).to eq(['admin'])

    # Save the model to trigger the RPC call
    employee.save

    # Verify that the changes are cleared after save
    expect(employee.changed?).to be false
    expect(employee.changes).to be_empty

    # Verify that the attribute was updated
    expect(employee.roles).to eq({ 'admin' => true, 'user' => true })
  end

  it "tracks changes to array values" do
    # Find the employee
    employee = TestEmployee.find(@employee.id)

    # Set roles to a different array
    employee.roles = ['user', 'manager']

    # Verify that the change is tracked
    expect(employee.changed?).to be true
    expect(employee.changes).to include('roles')
    expect(employee.roles_change).to eq([['admin'], ['user', 'manager']])
    expect(employee.roles_was).to eq(['admin'])

    # Save the model to trigger the RPC call
    employee.save

    # Verify that the changes are cleared after save
    expect(employee.changed?).to be false
    expect(employee.changes).to be_empty

    # Verify that the attribute was updated
    expect(employee.roles).to eq(['user', 'manager'])
  end

  it "doesn't track changes when the value doesn't change" do
    # Find the employee
    employee = TestEmployee.find(@employee.id)

    # Set name to the same value
    employee.name = 'John Doe'

    # Verify that no change is tracked
    expect(employee.changed?).to be false
    expect(employee.changes).to be_empty

    # Set roles to the same value
    employee.roles = ['admin']

    # Verify that no change is tracked
    expect(employee.changed?).to be false
    expect(employee.changes).to be_empty
  end

  it "clears changes when reloading" do
    # Find the employee
    employee = TestEmployee.find(@employee.id)

    # Set a remote attribute
    employee.name = 'Jane Doe'

    # Verify that the change is tracked
    expect(employee.changed?).to be true
    expect(employee.changes).to include('name')

    # Reload the model
    employee.reload

    # Verify that the changes are cleared after reload
    expect(employee.changed?).to be false
    expect(employee.changes).to be_empty

    # Verify that the attribute was not updated
    expect(employee.name).to eq('John Doe')
  end
end
