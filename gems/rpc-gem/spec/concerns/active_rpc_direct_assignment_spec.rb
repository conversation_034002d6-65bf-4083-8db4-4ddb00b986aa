# frozen_string_literal: true

require 'spec_helper'
require 'ostruct'
require 'active_support/all'
require 'concerns/active_rpc'

RSpec.describe "ActiveRpc with direct assignments" do
  before(:each) do
    # Clear the database
    TestUser.delete_all
    TestEmployee.delete_all

    # Create test data in the TestUser table
    @user = TestUser.create!(
      name: '<PERSON>',
      email: '<EMAIL>',
      roles: ['admin'],
      permissions: ['read', 'write'],
      status: 'active'
    )

    # Create an employee associated with the user
    @employee = TestEmployee.create!(
      department: 'Engineering',
      status: 'active',
      user_id: @user.id.to_s
    )

    # Set up a mock client for the TestEmployee model
    @client = RpcTestHelper::TestRpcClient::GrpcClient.new('core', 'Users')
    TestEmployee.grpc_client = @client
  end

  it "handles direct assignments of empty hash" do
    # Find the employee
    employee = TestEmployee.find(@employee.id)

    # Set roles to an empty hash
    employee.roles = []

    # Verify that we can access the empty hash immediately
    expect(employee.roles).to eq([])

    # Save the model to trigger the RPC call
    employee.save

    # Reload the employee to get fresh data
    employee.reload

    # Verify that we can still access the empty hash
    expect(employee.roles).to eq([])
  end

  it "handles direct assignments of nil" do
    # Find the employee
    employee = TestEmployee.find(@employee.id)

    # Set roles to nil
    employee.roles = nil

    # Verify that we can access the empty array immediately
    expect(employee.roles).to eq(nil)

    # Save the model to trigger the RPC call
    employee.save

    # Reload the employee to get fresh data
    employee.reload

    # Verify that we can still access the empty array
    expect(employee.roles).to eq([])
  end

  it "handles direct assignments of non-empty hash" do
    # Find the employee
    employee = TestEmployee.find(@employee.id)

    # Set roles to a non-empty hash
    employee.roles = %i[admin user]

    # Verify that we can access the hash immediately
    expect(employee.roles).to eq(%w[admin user])

    # Save the model to trigger the RPC call
    employee.save

    # Reload the employee to get fresh data
    employee.reload

    # Verify that we can still access the hash
    expect(employee.roles).to eq(%w[admin user])
  end

  it "handles direct assignments of array" do
    # Find the employee
    employee = TestEmployee.find(@employee.id)

    # Set roles to an array
    employee.roles = ['admin', 'user']

    # Verify that we can access the array immediately
    expect(employee.roles).to eq(['admin', 'user'])

    # Save the model to trigger the RPC call
    employee.save

    # Reload the employee to get fresh data
    employee.reload

    # Verify that we can still access the array
    expect(employee.roles).to eq(['admin', 'user'])
  end

  it "handles direct assignments of empty array" do
    # Find the employee
    employee = TestEmployee.find(@employee.id)

    # Set roles to an empty array
    employee.roles = []

    # Verify that we can access the empty array immediately
    expect(employee.roles).to eq([])

    # Save the model to trigger the RPC call
    employee.save

    # Reload the employee to get fresh data
    employee.reload

    # Verify that we can still access the empty array
    expect(employee.roles).to eq([])
  end
end
