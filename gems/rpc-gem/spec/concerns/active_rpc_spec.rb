# frozen_string_literal: true

require 'spec_helper'
require 'ostruct'
require 'active_support/all'
require 'concerns/active_rpc'

# Mock GrpcClientFactory for testing
module AtharRpc
  class GrpcClientFactory
    def self.instance
      @instance ||= new
    end

    def client_for(service, resource)
      nil # Will be stubbed in tests
    end
  end
end

RSpec.describe AtharRpc::ActiveRpc do
  # Create a mock ActiveRecord model class for testing
  let(:mock_model_class) do
    Class.new do
      include ActiveModel::Model
      include ActiveModel::Attributes
      include ActiveModel::Dirty
      include ActiveModel::Validations
      include ActiveModel::Callbacks

      define_model_callbacks :save, :create, :initialize

      # Mock collection methods
      def self.all
        []
      end

      # Track callback registrations
      class << self
        attr_accessor :registered_callbacks

        def reset_callbacks
          @registered_callbacks = { after_save: [], after_find: [], after_initialize: [] }
        end

        def after_save(method_name, options = {})
          @registered_callbacks[:after_save] << method_name
        end

        def after_find(method_name)
          @registered_callbacks[:after_find] << method_name
        end

        def after_initialize(method_name)
          @registered_callbacks[:after_initialize] << method_name
        end
      end

      # Initialize callback tracking
      reset_callbacks

      # Include the concern
      include AtharRpc::ActiveRpc

      # Mock the active_rpc method to test its behavior
      def self.active_rpc(service, resource, attributes, options = {})
        @service = service
        @resource = resource
        @attributes = attributes
        @options = {
          foreign_key: :"#{resource.to_s.underscore}_id",
          method_name: :"#{resource.to_s.underscore}_data",
          reload_method: :"reload_#{resource.to_s.underscore}",
          cache_expires_in: 30.minutes,
          bulk_method: :"list_#{resource.to_s.pluralize.underscore}"
        }.merge(options)

        # Store the client
        self.grpc_client = AtharRpc::GrpcClientFactory.instance.client_for(service.to_s, "#{resource}s")

        # Define client accessor method
        client_method_name = "#{resource.to_s.underscore}_client"
        define_singleton_method(client_method_name) do
          grpc_client
        end

        # Define bulk method
        bulk_method_name = "list_#{resource.to_s.pluralize.underscore}"
        define_singleton_method(bulk_method_name) do |ids = []|
          return [] if ids.empty?
          response = grpc_client.call(:"List#{resource}s", ids: ids.map(&:to_s))
          response&.message&.respond_to?(:items) ? response.message.items : []
        end
      end

      # Accessors for testing
      def self.service; @service; end
      def self.resource; @resource; end
      def self.attributes; @attributes; end
      def self.options; @options; end

      # Mock class_attribute
      class_attribute :grpc_client
    end
  end

  # Mock gRPC client
  let(:mock_client) do
    double('GrpcClient').tap do |client|
      allow(client).to receive(:call).and_return(mock_response)
    end
  end

  # Mock gRPC response
  let(:mock_response) do
    OpenStruct.new(
      message: OpenStruct.new(
        id: '123',
        name: 'John Doe',
        email: '<EMAIL>'
      )
    )
  end

  # Mock bulk response
  let(:mock_bulk_response) do
    items = [
      OpenStruct.new(id: '123', name: 'John Doe', email: '<EMAIL>'),
      OpenStruct.new(id: '456', name: 'Jane Smith', email: '<EMAIL>')
    ]
    OpenStruct.new(message: OpenStruct.new(items: items))
  end

  before do
    # Mock the GrpcClientFactory
    allow(AtharRpc::GrpcClientFactory).to receive(:instance).and_return(
      double('GrpcClientFactoryInstance').tap do |factory|
        allow(factory).to receive(:client_for).and_return(mock_client)
      end
    )
  end

  describe '.active_rpc' do
    it 'configures the model with the correct options' do
      # Call active_rpc on the mock model
      mock_model_class.active_rpc(:core, :User, [:name, :email], foreign_key: :user_id)

      # Verify the configuration
      expect(mock_model_class.service).to eq(:core)
      expect(mock_model_class.resource).to eq(:User)
      expect(mock_model_class.attributes).to eq([:name, :email])
      expect(mock_model_class.options[:foreign_key]).to eq(:user_id)
    end

    it 'sets default options when not provided' do
      # Call active_rpc without custom options
      mock_model_class.active_rpc(:core, :User, [:name, :email])

      # Verify default options
      expect(mock_model_class.options[:foreign_key]).to eq(:user_id)
      expect(mock_model_class.options[:method_name]).to eq(:user_data)
      expect(mock_model_class.options[:reload_method]).to eq(:reload_user)
      expect(mock_model_class.options[:bulk_method]).to eq(:list_users)
    end

    it 'initializes the gRPC client' do
      # Call active_rpc
      mock_model_class.active_rpc(:core, :User, [:name, :email])

      # Verify the client factory was called
      expect(AtharRpc::GrpcClientFactory.instance).to have_received(:client_for).with('core', 'Users')
    end

    it 'uses after_find callback to load remote data when a model is loaded from the database' do
      # Create a test class that includes the concern
      test_class = Class.new(ActiveRecord::Base) do
        self.table_name = 'test_employees'
        include AtharRpc::ActiveRpc

        # Set up ActiveRpc with the User resource using the new DSL
        active_rpc :core, :User, foreign_key: :user_id do
          define_rpc_attribute :name, :string
          define_rpc_attribute :email, :string
        end
      end

      # Check if the class has the after_find callback
      callbacks = test_class._find_callbacks.map(&:filter)
      expect(callbacks).to include(:load_user_data)
    end
  end

  # Test class methods
  describe 'class methods' do
    before do
      # Setup the model class
      mock_model_class.active_rpc(:core, :User, [:name, :email])

      # Setup mock for bulk response
      allow(mock_client).to receive(:call).with(:ListUsers, ids: ['123', '456']).and_return(mock_bulk_response)

      # Define the with_user_data method for testing
      mock_model_class.define_singleton_method(:with_user_data) do
        collection = all

        # Get all IDs that need loading
        ids = collection.map(&:user_id).compact.uniq

        # Skip if no IDs to fetch
        return collection if ids.empty?

        # Fetch all records in bulk
        bulk_data = list_users(ids)

        # Create a lookup hash
        lookup = {}
        bulk_data.each do |item|
          lookup[item.id.to_s] = item if item.respond_to?(:id)
        end

        # Preload data into each record
        collection.each do |record|
          foreign_key_value = record.user_id
          next unless foreign_key_value.present? && lookup[foreign_key_value.to_s]

          # Create a mock response object with the item as message
          mock_response = OpenStruct.new(message: lookup[foreign_key_value.to_s])
          record.instance_variable_set("@user_data", mock_response)
          record.instance_variable_set("@user_data_loaded", true)

          # Pre-cache all attribute values to prevent individual RPC calls
          [:name, :email].each do |attr|
            if mock_response&.message&.respond_to?(attr)
              # Create a cached value for each attribute
              record.instance_variable_set("@#{attr}_cached", mock_response.message.send(attr))
              record.instance_variable_set("@#{attr}_cached_loaded", true)

              # Define the attribute method on the record
              record.define_singleton_method(attr) do
                instance_variable_get("@#{attr}_cached")
              end
            end
          end
        end

        # Add specialized map methods
        [:name, :email].each do |attr|
          collection.define_singleton_method("map_#{attr}") do
            map { |record| record.instance_variable_get("@#{attr}_cached") }
          end
        end

        collection
      end
    end

    it 'provides access to the gRPC client' do
      expect(mock_model_class.user_client).to eq(mock_client)
    end

    it 'supports bulk retrieval' do
      # Test the bulk method
      result = mock_model_class.list_users(['123', '456'])
      expect(result.size).to eq(2)
      expect(result.first.name).to eq('John Doe')
      expect(result.last.name).to eq('Jane Smith')
    end

    it 'supports map(&:attribute) with bulk loading' do
      # Setup the model class
      mock_model_class.active_rpc(:core, :User, [:name, :email])

      # Create a collection of model instances
      instances = [
        mock_model_class.new.tap { |m| m.define_singleton_method(:user_id) { '123' } },
        mock_model_class.new.tap { |m| m.define_singleton_method(:user_id) { '456' } }
      ]

      # Create a collection-like object
      collection = instances.dup
      def collection.define_singleton_method(name, &block)
        singleton_class.define_method(name, &block)
      end

      # Apply the with_user_data scope
      allow(mock_model_class).to receive(:all).and_return(collection)
      result = mock_model_class.with_user_data

      # Verify that the data is cached
      expect(instances[0].instance_variable_get("@user_data_loaded")).to be_truthy
      expect(instances[1].instance_variable_get("@user_data_loaded")).to be_truthy

      # Test map(&:name)
      names = result.map(&:name)
      expect(names).to eq(['John Doe', 'Jane Smith'])

      # Verify that no additional RPC calls were made
      expect(mock_client).to have_received(:call).with(:ListUsers, ids: ['123', '456']).once
      expect(mock_client).not_to have_received(:call).with(:GetUser, anything)
    end

    it 'supports specialized map methods for better performance' do
      # Create a collection of model instances
      instances = [
        mock_model_class.new.tap { |m| m.define_singleton_method(:user_id) { '123' } },
        mock_model_class.new.tap { |m| m.define_singleton_method(:user_id) { '456' } }
      ]

      # Create a collection-like object that can have singleton methods
      collection = instances.dup
      def collection.define_singleton_method(name, &block)
        singleton_class.define_method(name, &block)
      end

      # Apply the with_user_data scope
      allow(mock_model_class).to receive(:all).and_return(collection)
      result = mock_model_class.with_user_data

      # Verify that the specialized map methods are defined
      expect(result).to respond_to(:map_name)
      expect(result).to respond_to(:map_email)

      # Test the specialized map methods
      names = result.map_name
      expect(names).to eq(['John Doe', 'Jane Smith'])

      emails = result.map_email
      expect(emails).to eq(['<EMAIL>', '<EMAIL>'])

      # Verify that no additional RPC calls were made
      expect(mock_client).to have_received(:call).with(:ListUsers, ids: ['123', '456']).once
      expect(mock_client).not_to have_received(:call).with(:GetUser, anything)
    end
  end

  # Test instance methods - simplified for testing purposes
  describe 'instance methods' do
    let(:model_instance) { mock_model_class.new }

    before do
      # Setup the model class
      mock_model_class.active_rpc(:core, :User, [:name, :email])

      # Create local variables for the closures
      response = mock_response
      client = mock_client

      # Add user_id method to the instance
      model_instance.define_singleton_method(:user_id) { '123' }

      # Add user_data method to the instance
      model_instance.define_singleton_method(:user_data) { response }

      # Add call_user_rpc method to the instance
      model_instance.define_singleton_method(:call_user_rpc) do |method_name, params = {}|
        client.call(method_name, params)
      end

      # Add name and email methods to the instance
      model_instance.define_singleton_method(:name) do
        data = user_data
        return nil unless data&.message
        data.message.respond_to?(:name) ? data.message.send(:name) : nil
      end

      model_instance.define_singleton_method(:email) do
        data = user_data
        return nil unless data&.message
        data.message.respond_to?(:email) ? data.message.send(:email) : nil
      end
    end

    it 'provides access to gRPC attributes' do
      expect(model_instance.name).to eq('John Doe')
      expect(model_instance.email).to eq('<EMAIL>')
    end

    it 'allows custom RPC calls' do
      expect(model_instance.call_user_rpc(:UpdateUser, id: '123', name: 'New Name')).to eq(mock_response)
    end
  end
end
