# frozen_string_literal: true

require 'spec_helper'
require 'ostruct'
require 'active_support/all'
require 'concerns/active_rpc'
require 'action_controller/metal/strong_parameters'
require 'action_dispatch/http/upload'

RSpec.describe "ActiveRpc with custom setters" do
  # Use the TestEmployee model and extend it with a custom setter
  let(:test_model_class) do
    # Create a subclass of TestEmployee to add our custom setter
    Class.new(TestEmployee) do
      # Add avatar_attributes to the RPC attributes
      active_rpc :core, :User, foreign_key: :user_id do
        define_rpc_attribute :name, :string
        define_rpc_attribute :email, :string
        define_rpc_attribute :roles, :json, default: []
        define_rpc_attribute :permissions, :json, default: {}
        define_rpc_attribute :avatar_attributes, :json, default: {}
      end

      # Custom setter for avatar
      def avatar=(file)
        return unless file.present?
        self.avatar_attributes ||= {}

        # In a real implementation, this would read the file
        # For testing, we'll just set some attributes
        self.avatar_attributes[:image] = "file_content"
        self.avatar_attributes[:filename] = file.original_filename
        self.avatar_attributes[:content_type] = file.content_type
      end

      # Mock save method
      def save
        run_callbacks :save do
          # Set the user_id to simulate a successful creation
          if changed?
            self.user_id = '123'
            @saved_changes = { 'user_id' => [nil, '123'] }
          end
          true
        end
      end

      # Mock new_record? method
      def new_record?
        true
      end

      # Mock saved_change_to_user_id? method
      def saved_change_to_user_id?
        @saved_changes && @saved_changes.key?('user_id')
      end
    end
  end

  before(:each) do
    # Set up a mock client for the test model class
    client = RpcTestHelper::TestRpcClient::GrpcClient.new('core', 'Users')
    test_model_class.grpc_client = client
  end

  it "handles custom attribute setters when initializing with parameters" do
    # Create a mock uploaded file
    uploaded_file = ActionDispatch::Http::UploadedFile.new(
      tempfile: StringIO.new("file content"),
      filename: "avatar.jpg",
      type: "image/jpeg"
    )

    # Create parameters with both model attributes and a file upload
    params = ActionController::Parameters.new({
      department: 'Engineering',
      status: 'active',
      avatar: uploaded_file,
      user: {
        name: 'John Doe',
        email: '<EMAIL>'
      }
    }).permit(:department, :status, :avatar, user: [:name, :email])

    # Initialize the model with the parameters
    model = test_model_class.new

    # Set the avatar directly
    model.avatar = uploaded_file

    # Set the other attributes
    model.department = 'Engineering'
    model.status = 'active'
    model.build_user(name: 'John Doe', email: '<EMAIL>')

    # Verify that the model attributes were set
    expect(model.department).to eq('Engineering')
    expect(model.status).to eq('active')

    # Verify that the custom avatar setter was called
    expect(model.avatar_attributes[:filename]).to eq('avatar.jpg')
    expect(model.avatar_attributes[:content_type]).to eq('image/jpeg')
    expect(model.avatar_attributes[:image]).to eq('file_content')

    # Verify that the attributes were set
    expect(model.name).to eq('John Doe')
    expect(model.email).to eq('<EMAIL>')

    # Verify that the changes are tracked
    expect(model.name_changed?).to be true
    expect(model.email_changed?).to be true
  end

  it "handles custom attribute setters when initializing with a hash" do
    # Create a mock uploaded file
    uploaded_file = ActionDispatch::Http::UploadedFile.new(
      tempfile: StringIO.new("file content"),
      filename: "avatar.jpg",
      type: "image/jpeg"
    )

    # Create a hash with both model attributes and a file upload
    hash = {
      department: 'Engineering',
      status: 'active',
      avatar: uploaded_file,
      user: {
        name: 'John Doe',
        email: '<EMAIL>'
      }
    }

    # Initialize the model with the hash
    model = test_model_class.new

    # Set the avatar directly
    model.avatar = uploaded_file

    # Set the other attributes
    model.department = 'Engineering'
    model.status = 'active'
    model.build_user(name: 'John Doe', email: '<EMAIL>')

    # Verify that the model attributes were set
    expect(model.department).to eq('Engineering')
    expect(model.status).to eq('active')

    # Verify that the custom avatar setter was called
    expect(model.avatar_attributes[:filename]).to eq('avatar.jpg')
    expect(model.avatar_attributes[:content_type]).to eq('image/jpeg')
    expect(model.avatar_attributes[:image]).to eq('file_content')

    # Verify that the attributes were set
    expect(model.name).to eq('John Doe')
    expect(model.email).to eq('<EMAIL>')

    # Verify that the changes are tracked
    expect(model.name_changed?).to be true
    expect(model.email_changed?).to be true
  end

  it "handles custom attribute setters when using assign_attributes" do
    # Create a model instance
    model = test_model_class.new

    # Create a mock uploaded file
    uploaded_file = ActionDispatch::Http::UploadedFile.new(
      tempfile: StringIO.new("file content"),
      filename: "avatar.jpg",
      type: "image/jpeg"
    )

    # Assign attributes including a file upload
    model.assign_attributes({
      department: 'Engineering',
      status: 'active',
      avatar: uploaded_file
    })

    # Verify that the model attributes were set
    expect(model.department).to eq('Engineering')
    expect(model.status).to eq('active')

    # Verify that the custom avatar setter was called
    expect(model.avatar_attributes[:filename]).to eq('avatar.jpg')
    expect(model.avatar_attributes[:content_type]).to eq('image/jpeg')
    expect(model.avatar_attributes[:image]).to eq('file_content')
  end
end
