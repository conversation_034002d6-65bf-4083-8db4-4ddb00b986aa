# frozen_string_literal: true

require 'spec_helper'
require 'ostruct'
require 'active_support/all'
require 'concerns/active_rpc'

RSpec.describe "ActiveRpc with empty hash values" do
  before(:each) do
    # Clear the database
    TestUser.delete_all
    TestEmployee.delete_all

    # Create test data in the TestUser table
    @user = TestUser.create!(
      name: '<PERSON>',
      email: '<EMAIL>',
      roles: ['admin'],
      permissions: ['read', 'write'],
      status: 'active'
    )

    # Create an employee associated with the user
    @employee = TestEmployee.create!(
      department: 'Engineering',
      status: 'active',
      user_id: @user.id.to_s
    )

    # Set up a mock client for the TestEmployee model
    @client = RpcTestHelper::TestRpcClient::GrpcClient.new('core', 'Users')
    TestEmployee.grpc_client = @client
  end

  it "handles empty hash assignments" do
    # Find the employee
    employee = TestEmployee.find(@employee.id)

    # Set roles to an empty hash
    employee.roles = {}

    # Verify that the attribute was set
    expect(employee.roles).to eq({})
    expect(employee.roles_changed?).to be true

    # Verify that we can access the empty hash
    expect(employee.roles).to eq({})

    # Save the model to trigger the RPC call
    employee.save

    # Reload the employee to get fresh data
    employee.reload

    # Verify that the roles were updated in the database
    user = TestUser.find(@user.id)
    expect(user.roles).to eq({})

    # Verify that we can still access the empty hash
    expect(employee.roles).to eq({})
  end

  it "handles nil assignments" do
    # Find the employee
    employee = TestEmployee.find(@employee.id)

    # Set roles to nil
    employee.roles = nil

    # Verify that the attribute was set
    expect(employee.roles).to eq(nil)
    expect(employee.roles_changed?).to be true

    # Save the model to trigger the RPC call
    employee.save

    # Reload the employee to get fresh data
    employee.reload

    # Verify that the roles were updated in the database
    user = TestUser.find(@user.id)
    expect(user.roles).to eq([])

    # Verify that we can still access the empty array
    expect(employee.roles).to eq([])
  end

  it "handles non-empty hash assignments" do
    # Find the employee
    employee = TestEmployee.find(@employee.id)

    # Set roles to a non-empty hash
    employee.roles = { "admin": true, "user": true }

    # Verify that the attribute was set
    expect(employee.roles).to eq({ "admin" => true, "user" => true })
    expect(employee.roles_changed?).to be true

    # Verify that we can access the hash
    expect(employee.roles).to eq({ "admin" => true, "user" => true })

    # Save the model to trigger the RPC call
    employee.save

    # Reload the employee to get fresh data
    employee.reload

    # Verify that the roles were updated in the database
    user = TestUser.find(@user.id)
    expect(user.roles).to eq({ "admin" => true, "user" => true })

    # Verify that we can still access the hash
    expect(employee.roles).to eq({ "admin" => true, "user" => true })
  end
end
