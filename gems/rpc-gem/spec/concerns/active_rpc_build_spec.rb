# frozen_string_literal: true

require 'spec_helper'
require 'ostruct'
require 'active_support/all'
require 'concerns/active_rpc'

RSpec.describe "ActiveRpc build_user method" do
  let(:model) { TestEmployee.new }

  before(:each) do
    # Clear the database
    TestUser.delete_all
    TestEmployee.delete_all

    # Create test data in the TestUser table
    TestUser.create!(
      id: 1,
      name: '<PERSON>',
      email: '<EMAIL>',
      roles: ['admin'],
      permissions: ['read', 'write'],
      status: 'active'
    )

    # Set up a mock client for the TestEmployee model
    client = RpcTestHelper::TestRpcClient::GrpcClient.new('core', 'Users')
    TestEmployee.grpc_client = client
  end

  it "stores attributes for later creation" do
    # Call build_user with attributes
    model.build_user(name: "Test User", email: "<EMAIL>")

    # Verify that the attributes were properly set
    expect(model.name).to eq("Test User")
    expect(model.email).to eq("<EMAIL>")
    expect(model.name_changed?).to be true
    expect(model.email_changed?).to be true

    # Verify that no user_id is set yet
    expect(model.user_id).to be_nil

    # Verify that no user was created in the database yet
    expect(TestUser.find_by(email: "<EMAIL>")).to be_nil
  end

  it "creates and associates a user when the model is saved" do
    # Build the user
    model.build_user(name: "Test User", email: "<EMAIL>")

    # Save the model
    result = model.save
    expect(result).to be true

    # Verify that the user_id was set on the model
    expect(model.user_id).to be_present

    # Verify that the pending delegated attributes were cleared
    expect(model.instance_variable_get(:@pending_delegated_attributes)).to be_nil

    # Verify that a user was created in the database
    user = TestUser.find_by(email: "<EMAIL>")
    expect(user).to be_present
    expect(user.name).to eq("Test User")
  end

  it "properly assigns the foreign key when creating a new resource" do
    # Build the user
    model.build_user(name: "Test User 2", email: "<EMAIL>")

    # Create the model
    model.save

    # Verify that the user_id was set correctly
    expect(model.user_id).to be_present

    # Verify that we can access the user data
    expect(model.name).to eq("Test User 2")
    expect(model.email).to eq("<EMAIL>")

    # Verify that a user was created in the database
    user = TestUser.find_by(email: "<EMAIL>")
    expect(user).to be_present
    expect(user.name).to eq("Test User 2")
  end

  it "initializes with both model attributes and remote attributes" do
    # Create params with both model attributes and remote attributes
    create_params = {
      department: "Engineering",
      status: "active",
      name: "New Employee",
      email: "<EMAIL>"
    }

    # Initialize the model with the params
    employee = TestEmployee.new(create_params)

    # Verify that the model attributes were set
    expect(employee.department).to eq("Engineering")
    expect(employee.status).to eq("active")

    # Check that the attributes were properly set on the model
    expect(employee.name).to eq("New Employee")
    expect(employee.email).to eq("<EMAIL>")

    # Save the model to trigger the RPC call
    employee.save

    # Verify that the user_id was set
    expect(employee.user_id).to be_present

    # Verify that the pending delegated attributes were cleared
    expect(employee.instance_variable_get(:@pending_delegated_attributes)).to be_nil

    # Verify that a user was created in the database
    user = TestUser.find_by(email: "<EMAIL>")
    expect(user).to be_present
    expect(user.name).to eq("New Employee")

    # Verify that we can access the user data through the employee
    expect(employee.name).to eq("New Employee")
    expect(employee.email).to eq("<EMAIL>")
  end
end
