# frozen_string_literal: true

require 'spec_helper'
require 'ostruct'
require 'active_support/all'
require 'concerns/active_rpc'
require 'action_controller/metal/strong_parameters'

RSpec.describe "ActiveRpc update with existing record" do
  before(:each) do
    # Clear the database
    TestUser.delete_all
    TestEmployee.delete_all

    # Create test data in the TestUser table
    @user = TestUser.create!(
      name: '<PERSON>',
      email: '<EMAIL>',
      roles: ['admin'],
      permissions: ['read', 'write'],
      status: 'active'
    )

    # Create an employee associated with the user
    @employee = TestEmployee.create!(
      department: 'Engineering',
      status: 'active',
      user_id: @user.id.to_s
    )

    # Set up a mock client for the TestEmployee model
    @client = RpcTestHelper::TestRpcClient::GrpcClient.new('core', 'Users')
    TestEmployee.grpc_client = @client
  end

  it "updates an existing user when initialized with parameters" do
    # Find the employee
    employee = TestEmployee.find(@employee.id)

    # Update user attributes directly
    employee.name = '<PERSON>'

    # Verify that the attribute was set
    expect(employee.name).to eq('<PERSON>')
    expect(employee.name_changed?).to be true

    # Save the model to trigger the RPC call
    employee.save

    # Reload the employee to get fresh data
    employee.reload

    # Reload the user from the database
    user = TestUser.find(@user.id)
    expect(user.name).to eq('John Smith')

    # Verify that we can access the updated user data through the employee
    expect(employee.name).to eq('John Smith')
  end

  it "updates an existing user when initialized with direct attributes" do
    # Find the employee
    employee = TestEmployee.find(@employee.id)

    # Update attributes directly
    employee.name = 'John Johnson'

    # Verify that the attribute was set
    expect(employee.name).to eq('John Johnson')
    expect(employee.name_changed?).to be true

    # Save the model to trigger the RPC call
    employee.save

    # Reload the employee to get fresh data
    employee.reload

    # Reload the user from the database
    user = TestUser.find(@user.id)
    expect(user.name).to eq('John Johnson')

    # Verify that we can access the updated user data through the employee
    expect(employee.name).to eq('John Johnson')
  end

  it "updates an existing user when attributes are set directly" do
    # Find the employee
    employee = TestEmployee.find(@employee.id)

    # Update attributes directly
    employee.name = 'John Williams'

    # Verify that the attribute was set
    expect(employee.name).to eq('John Williams')
    expect(employee.name_changed?).to be true

    # Save the model to trigger the RPC call
    employee.save

    # Reload the employee to get fresh data
    employee.reload

    # Reload the user from the database
    user = TestUser.find(@user.id)
    expect(user.name).to eq('John Williams')

    # Verify that we can access the updated user data through the employee
    expect(employee.name).to eq('John Williams')
  end
end
