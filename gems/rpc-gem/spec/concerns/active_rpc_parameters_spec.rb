# frozen_string_literal: true

require 'spec_helper'
require 'ostruct'
require 'active_support/all'
require 'concerns/active_rpc'
require 'action_controller/metal/strong_parameters'

RSpec.describe AtharRpc::ActiveRpc do
  before(:each) do
    # Clear the database
    TestUser.delete_all
    TestEmployee.delete_all

    # Create test data in the TestUser table
    TestUser.create!(
      id: 1,
      name: '<PERSON>',
      email: '<EMAIL>',
      roles: ['admin'],
      permissions: ['read', 'write'],
      status: 'active'
    )

    # Set up a mock client for the TestEmployee model
    client = RpcTestHelper::TestRpcClient::GrpcClient.new('core', 'Users')
    TestEmployee.grpc_client = client
  end

  describe '#initialize' do
    context 'with ActiveController::Parameters' do
      it 'handles parameters with remote attributes' do
        # Create parameters similar to what would come from a controller
        params = ActionController::Parameters.new({
          department: 'Engineering',
          status: 'active',
          name: '<PERSON>',
          email: '<EMAIL>'
        }).permit(:department, :status, :name, :email)

        # Initialize the model with the parameters
        model = TestEmployee.new(params)

        # Verify that the model attributes were set
        expect(model.department).to eq('Engineering')
        expect(model.status).to eq('active')

        # Check that the attributes were properly set on the model
        expect(model.name).to eq('John Doe')
        expect(model.email).to eq('<EMAIL>')
      end

      it 'handles parameters with direct resource attributes' do
        # Create parameters with direct resource attributes
        params = ActionController::Parameters.new({
          department: 'Marketing',
          status: 'active',
          name: 'Jane Smith',
          email: '<EMAIL>'
        }).permit(:department, :status, :name, :email)

        # Initialize the model with the parameters
        model = TestEmployee.new(params)

        # Verify that the model attributes were set
        expect(model.department).to eq('Marketing')
        expect(model.status).to eq('active')

        # Check that the attributes were properly set on the model
        expect(model.name).to eq('Jane Smith')
        expect(model.email).to eq('<EMAIL>')

        # Save the model to trigger the RPC call
        model.save

        # Now the remote attributes should be accessible
        expect(model.name).to eq('Jane Smith')
        expect(model.email).to eq('<EMAIL>')
      end

      it 'handles parameters with string keys' do
        # Create parameters with string keys
        params = ActionController::Parameters.new({
          'department' => 'Sales',
          'status' => 'active',
          'name' => 'Bob Johnson',
          'email' => '<EMAIL>'
        }).permit(:department, :status, :name, :email)

        # Initialize the model with the parameters
        model = TestEmployee.new(params)

        # Verify that the model attributes were set
        expect(model.department).to eq('Sales')
        expect(model.status).to eq('active')

        # Check that the attributes were properly set on the model
        expect(model.name).to eq('Bob Johnson')
        expect(model.email).to eq('<EMAIL>')

        # Save the model to trigger the RPC call
        model.save

        # Now the remote attributes should be accessible
        expect(model.name).to eq('Bob Johnson')
        expect(model.email).to eq('<EMAIL>')
      end

      it 'handles saving with parameters' do
        # Create parameters with remote attributes
        params = ActionController::Parameters.new({
          department: 'IT',
          status: 'active',
          name: 'Alice Brown',
          email: '<EMAIL>'
        }).permit(:department, :status, :name, :email)

        # Initialize and save the model
        model = TestEmployee.new(params)
        model.save

        # Verify that the user was created in the database
        user = TestUser.find_by(email: '<EMAIL>')
        expect(user).to be_present
        expect(user.name).to eq('Alice Brown')

        # Verify that the user_id was set on the model
        expect(model.user_id).to be_present
      end
    end

    context 'with regular Hash' do
      it 'still works with regular hash arguments' do
        # Create a hash with both model and remote attributes
        hash = {
          department: 'Research',
          status: 'active',
          name: 'Charlie Davis',
          email: '<EMAIL>'
        }

        # Initialize the model with the hash
        model = TestEmployee.new(hash)

        # Verify that the model attributes were set
        expect(model.department).to eq('Research')
        expect(model.status).to eq('active')

        # Check that the attributes were properly set on the model
        expect(model.name).to eq('Charlie Davis')
        expect(model.email).to eq('<EMAIL>')
      end
    end
  end
end
