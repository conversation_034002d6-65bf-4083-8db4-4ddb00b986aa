syntax = "proto3";

package test;

// User service for testing
service Users {
  // Get a single user by ID
  rpc GetUser(UserRequest) returns (UserResponse) {}

  // Get multiple users by IDs
  rpc ListUsers(UserListRequest) returns (UserListResponse) {}

  // Update a user
  rpc UpdateUser(UpdateUserRequest) returns (UserResponse) {}

  // Create a new user
  rpc CreateUser(CreateUserRequest) returns (UserResponse) {}
}

// Request message for GetUser
message UserRequest {
  string id = 1;
}

// Response message for GetUser
message UserResponse {
  string id = 1;
  string name = 2;
  string email = 3;
  repeated string roles = 4;
  repeated string permissions = 5;
  string avatar_url = 6;
  string status = 7;

  // Response metadata - separate from resource data
  ResponseMetadata _metadata = 99; // Response metadata including success/error info
}

// Request message for ListUsers
message UserListRequest {
  repeated string ids = 1;
}

// Response message for ListUsers
message UserListResponse {
  repeated UserResponse items = 1;
}

// Request message for UpdateUser
message UpdateUserRequest {
  string id = 1; // ID of the user to update
  string name = 2; // Updated name (optional)
  string email = 3; // Updated email (optional)
  string avatar_url = 4; // Updated avatar URL (optional)
  repeated string roles = 5; // Updated roles (optional)
  repeated string permissions = 6; // Updated permissions (optional)
  string status = 7; // Updated status (optional)
}

// Request message for CreateUser
message CreateUserRequest {
  string name = 1; // User name (required)
  string email = 2; // User email (required)
  string avatar_url = 3; // Avatar URL (optional)
  repeated string roles = 4; // Roles (optional)
  repeated string permissions = 5; // Permissions (optional)
  string status = 6; // Status (optional)
}

// Response metadata - keeps validation/error info separate from resource data
message ResponseMetadata {
  bool success = 1; // Whether the operation was successful
  map<string, ErrorMessages> errors = 2; // Rails errors object (when success = false)
}

// Simple structure to hold error messages for a field
message ErrorMessages {
  repeated string messages = 1; // Error messages for this field
}
