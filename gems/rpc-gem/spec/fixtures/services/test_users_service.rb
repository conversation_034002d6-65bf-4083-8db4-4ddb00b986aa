# frozen_string_literal: true

# This file contains the implementation of the Test::Users gRPC service for testing.
# It is used by the integration tests to simulate a real gRPC server.

require_relative '../models/test_user'

# For simplicity, we'll use the same TestUser model for the gRPC service
# The TestUser model is already configured to use the file-based SQLite database

module TestServices
  # Implementation of the Test::Users gRPC service
  # This class implements the methods defined in the users.proto file
  # and will be used by the real gRPC server in our test
  class UsersService < Test::Users::Service
    def get_user(request, _call)
      id = request.id

      begin
        # Find the user in our database
        user = TestUser.find(id)
        user.to_rpc_response
      rescue ActiveRecord::RecordNotFound => e
        # Return error if user not found
        raise GRPC::NotFound.new("User with ID #{id} not found")
      end
    end

    def list_users(request, _call)
      ids = request.ids

      # Find users in our database
      users = TestUser.where(id: ids)

      if users.any?
        items = users.map(&:to_rpc_response)
        Test::UserListResponse.new(items: items)
      else
        raise GRPC::NotFound.new("No users found for the provided IDs")
      end
    end

    def update_user(request, _call)
      # Get the user ID from the request
      id = request.id

      # Check for special test cases
      if request.name == 'Server Will Reject'
        # Simulate a validation error
        raise GRPC::InvalidArgument.new('Validation failed: Email is invalid, Name is too short')
      elsif request.name == 'Will Fail'
        # Simulate a client error by returning nil
        return nil
      end

      begin
        # Find the user in our database
        user = TestUser.find(id)

        # Update attributes if provided
        user.name = request.name if request.name.present?
        user.email = request.email if request.email.present?
        user.avatar_url = request.avatar_url if request.avatar_url.present?
        user.roles = request.roles.to_a if request.roles.present?
        user.permissions = request.permissions.to_a if request.permissions.present?
        user.status = request.status if request.status.present?

        # Save the user
        if user.save
          # Return the updated user with success metadata
          response = user.to_rpc_response
          response._metadata = create_success_metadata
          response
        else
          # Return structured error response with validation errors
          response = user.to_rpc_response
          response._metadata = create_validation_error_metadata(user)
          response
        end
      rescue ActiveRecord::RecordNotFound
        raise GRPC::NotFound.new("User with ID #{id} not found")
      end
    end

    def create_user(request, _call)
      # Create a new user with the provided attributes
      user = TestUser.new(
        name: request.name,
        email: request.email,
        avatar_url: request.avatar_url,
        roles: request.roles.to_a,
        permissions: request.permissions.to_a,
        status: request.status.present? ? request.status : 'active'
      )

      # Save the user
      if user.save
        # Return the created user with success metadata
        response = user.to_rpc_response
        response._metadata = create_success_metadata
        response
      else
        # Return structured error response with validation errors
        response = user.to_rpc_response
        response._metadata = create_validation_error_metadata(user)
        response
      end
    end

    private

    # Create structured response metadata for validation errors
    def create_validation_error_metadata(record)
      # Share the Rails errors object directly - no conversion needed!
      errors_map = {}
      record.errors.to_hash.each do |field, messages|
        errors_map[field.to_s] = Test::ErrorMessages.new(messages: messages)
      end

      Test::ResponseMetadata.new(
        success: false,
        errors: errors_map
      )
    end

    # Create successful response metadata
    def create_success_metadata
      Test::ResponseMetadata.new(success: true)
    end
  end
end
