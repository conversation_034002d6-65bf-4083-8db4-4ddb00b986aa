# frozen_string_literal: true
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: test/users.proto

require 'google/protobuf'


descriptor_data = "\n\x10test/users.proto\x12\x04test\"\x19\n\x0bUserRequest\x12\n\n\x02id\x18\x01 \x01(\t\"\xaa\x01\n\x0cUserResponse\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\r\n\x05\x65mail\x18\x03 \x01(\t\x12\r\n\x05roles\x18\x04 \x03(\t\x12\x13\n\x0bpermissions\x18\x05 \x03(\t\x12\x12\n\navatar_url\x18\x06 \x01(\t\x12\x0e\n\x06status\x18\x07 \x01(\t\x12)\n\t_metadata\x18\x63 \x01(\x0b\x32\x16.test.ResponseMetadata\"\x1e\n\x0fUserListRequest\x12\x0b\n\x03ids\x18\x01 \x03(\t\"5\n\x10UserListResponse\x12!\n\x05items\x18\x01 \x03(\x0b\x32\x12.test.UserResponse\"\x84\x01\n\x11UpdateUserRequest\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\r\n\x05\x65mail\x18\x03 \x01(\t\x12\x12\n\navatar_url\x18\x04 \x01(\t\x12\r\n\x05roles\x18\x05 \x03(\t\x12\x13\n\x0bpermissions\x18\x06 \x03(\t\x12\x0e\n\x06status\x18\x07 \x01(\t\"x\n\x11\x43reateUserRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\r\n\x05\x65mail\x18\x02 \x01(\t\x12\x12\n\navatar_url\x18\x03 \x01(\t\x12\r\n\x05roles\x18\x04 \x03(\t\x12\x13\n\x0bpermissions\x18\x05 \x03(\t\x12\x0e\n\x06status\x18\x06 \x01(\t\"\x9b\x01\n\x10ResponseMetadata\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x32\n\x06\x65rrors\x18\x02 \x03(\x0b\x32\".test.ResponseMetadata.ErrorsEntry\x1a\x42\n\x0b\x45rrorsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\"\n\x05value\x18\x02 \x01(\x0b\x32\x13.test.ErrorMessages:\x02\x38\x01\"!\n\rErrorMessages\x12\x10\n\x08messages\x18\x01 \x03(\t2\xf3\x01\n\x05Users\x12\x32\n\x07GetUser\x12\x11.test.UserRequest\x1a\x12.test.UserResponse\"\x00\x12<\n\tListUsers\x12\x15.test.UserListRequest\x1a\x16.test.UserListResponse\"\x00\x12;\n\nUpdateUser\x12\x17.test.UpdateUserRequest\x1a\x12.test.UserResponse\"\x00\x12;\n\nCreateUser\x12\x17.test.CreateUserRequest\x1a\x12.test.UserResponse\"\x00\x62\x06proto3"

pool = Google::Protobuf::DescriptorPool.generated_pool
pool.add_serialized_file(descriptor_data)

module Test
  UserRequest = ::Google::Protobuf::DescriptorPool.generated_pool.lookup("test.UserRequest").msgclass
  UserResponse = ::Google::Protobuf::DescriptorPool.generated_pool.lookup("test.UserResponse").msgclass
  UserListRequest = ::Google::Protobuf::DescriptorPool.generated_pool.lookup("test.UserListRequest").msgclass
  UserListResponse = ::Google::Protobuf::DescriptorPool.generated_pool.lookup("test.UserListResponse").msgclass
  UpdateUserRequest = ::Google::Protobuf::DescriptorPool.generated_pool.lookup("test.UpdateUserRequest").msgclass
  CreateUserRequest = ::Google::Protobuf::DescriptorPool.generated_pool.lookup("test.CreateUserRequest").msgclass
  ResponseMetadata = ::Google::Protobuf::DescriptorPool.generated_pool.lookup("test.ResponseMetadata").msgclass
  ErrorMessages = ::Google::Protobuf::DescriptorPool.generated_pool.lookup("test.ErrorMessages").msgclass
end
