# Generated by the protocol buffer compiler.  DO NOT EDIT!
# Source: test/users.proto for package 'test'

require 'grpc'
require 'test/users_pb'

module Test
  module Users
    # User service for testing
    class Service

      include ::GRPC::GenericService

      self.marshal_class_method = :encode
      self.unmarshal_class_method = :decode
      self.service_name = 'test.Users'

      # Get a single user by ID
      rpc :GetUser, ::Test::UserRequest, ::Test::UserResponse
      # Get multiple users by IDs
      rpc :ListUsers, ::Test::UserListRequest, ::Test::UserListResponse
      # Update a user
      rpc :UpdateUser, ::Test::UpdateUserRequest, ::Test::UserResponse
      # Create a new user
      rpc :CreateUser, ::Test::CreateUserRequest, ::Test::UserResponse
    end

    Stub = Service.rpc_stub_class
  end
end
