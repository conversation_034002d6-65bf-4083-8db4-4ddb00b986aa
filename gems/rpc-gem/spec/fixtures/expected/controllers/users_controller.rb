module Core
  class UsersController < Gruf::Controllers::Base
    bind ::Core::Users::Service

    def get_user
      request_payload = request.message
      # Implement business logic here
      # Example response:
      ::Core::UserResponse.new(message: "Success")
    rescue StandardError => e
      fail!(:internal, :unexpected_error, "Error: #{e.message}")
    end

    def create_user
      request_payload = request.message
      # Implement business logic here
      # Example response:
      ::Core::UserResponse.new(message: "Success")
    rescue StandardError => e
      fail!(:internal, :unexpected_error, "Error: #{e.message}")
    end

  end
end
