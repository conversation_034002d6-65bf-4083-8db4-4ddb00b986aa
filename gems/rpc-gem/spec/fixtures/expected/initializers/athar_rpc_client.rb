# frozen_string_literal: true

require "athar_rpc"

# Load configuration before models are loaded
# This ensures the config is available in both development and production
AtharRpc::GrpcConfig.load_config

# Also keep the after_initialize hook for backward compatibility
Rails.application.config.after_initialize do
  # Reload config if needed
  AtharRpc::GrpcConfig.load_config unless AtharRpc::GrpcConfig.configs.present?
end
