# frozen_string_literal: true

# A dummy collection class to simulate ActiveRecord collections
class DummyCollection
  include Enumerable

  def initialize(records)
    @records = records
  end

  def each(&block)
    @records.each(&block)
  end

  def map(&block)
    @records.map(&block)
  end

  def size
    @records.size
  end

  # Simulate ActiveRecord's tap method for eager loading
  def tap(&block)
    block.call(self)
    self
  end
end

# A dummy ActiveRecord model for testing ActiveRpc
class DummyModel
  include ActiveModel::Model
  include ActiveModel::Attributes
  include ActiveModel::Dirty
  include ActiveModel::Validations
  include ActiveModel::Callbacks

  define_model_callbacks :save, :create, :initialize

  # Mock ActiveRecord methods
  def self.after_save(method_name, options = {})
    ;
  end

  def self.after_find(method_name)
    ;
  end

  def self.after_initialize(method_name)
    ;
  end

  def self.before_save(&block)
    @before_save_block = block
  end

  # Track RPC calls
  @@rpc_calls = []

  def self.rpc_calls
    @@rpc_calls
  end

  def self.reset_rpc_calls
    @@rpc_calls = []
  end

  # Mock save method
  def save
    if self.class.instance_variable_defined?(:@before_save_block) && self.class.instance_variable_get(:@before_save_block)
      result = self.class.instance_variable_get(:@before_save_block).call(self)
      return false if result == false
    end
    true
  end

  # Mock new_record? method
  def new_record?
    @new_record || false
  end

  # Mock collection methods
  def self.all
    collection = [
      new('101'),
      new('102'),
      new('103')
    ]
    DummyCollection.new(collection)
  end

  def self.where(conditions)
    collection = [
      new('201'),
      new('202')
    ]
    DummyCollection.new(collection)
  end

  # Mock the with_user_data scope
  def self.with_user_data
    # Return self to allow chaining
    self
  end

  # Include the ActiveRpc concern
  include AtharRpc::ActiveRpc

  # Override fetch method to track RPC calls
  def self.fetch_user_data_from_core_original(user_id)
    client = grpc_client
    client.call(:GetUser, id: user_id.to_s)
  end

  # Set up ActiveRpc with the User resource using the new DSL
  active_rpc :core, :User, foreign_key: :user_id do
    define_rpc_attribute :name, :string
    define_rpc_attribute :email, :string
    define_rpc_attribute :role, :string
  end

  # Override the fetch method to track calls
  def fetch_user_data_from_core
    # We don't need to track here since the client already tracks calls
    self.class.fetch_user_data_from_core_original(user_id)
  end

  # Mock instance methods
  attr_accessor :user_id

  def initialize(user_id = nil)
    @user_id = user_id
  end

  # Mock class attribute
  class_attribute :grpc_client
end
