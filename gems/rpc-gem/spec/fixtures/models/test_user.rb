# frozen_string_literal: true

# A TestUser model using ActiveRecord for testing
class TestUser < GrpcServerSideConnection
  # Add validations
  validates :name, presence: true, length: { minimum: 2, maximum: 50 }
  validates :email, presence: true, format: { with: /\A[^@\s]+@[^@\s]+\z/, message: "must be a valid email address" }
  validates :status, inclusion: { in: %w[active inactive suspended], message: "must be one of: active, inactive, suspended" }

  # Serialize arrays
  serialize :roles
  serialize :permissions

  # Convert string representations to arrays
  def roles
    super.is_a?(String) ? super.split(',') : (super || [])
  end

  def permissions
    super.is_a?(String) ? super.split(',') : (super || [])
  end

  def roles=(value)
    super(value.is_a?(Array) ? value.join(',') : value)
  end

  def permissions=(value)
    super(value.is_a?(Array) ? value.join(',') : value)
  end

  # Convert to RPC response
  def to_rpc_response
    Test::UserResponse.new(
      id: id.to_s,
      name: name,
      email: email,
      avatar_url: avatar_url || '',
      roles: roles || [],
      permissions: permissions || [],
      status: status || 'active'
    )
  end

  # Create the table if it doesn't exist
  def self.create_table
    ActiveRecord::Base.connection.create_table :test_users, force: true do |t|
      t.string :name
      t.string :email
      t.string :avatar_url
      t.text :roles
      t.text :permissions
      t.string :status, default: 'active'
      t.timestamps
    end
  end

  # Seed with test data
  def self.seed
    # Clear existing data
    delete_all

    # Create test users
    create!(
      id: 1,
      name: 'John Doe',
      email: '<EMAIL>',
      roles: ['admin'],
      permissions: ['read', 'write'],
      status: 'active'
    )

    create!(
      id: 2,
      name: 'Jane Smith',
      email: '<EMAIL>',
      roles: ['user'],
      permissions: ['read'],
      status: 'active'
    )

    create!(
      id: 3,
      name: 'New User',
      email: '<EMAIL>',
      roles: ['user'],
      permissions: ['read'],
      status: 'active'
    )
  end
end
