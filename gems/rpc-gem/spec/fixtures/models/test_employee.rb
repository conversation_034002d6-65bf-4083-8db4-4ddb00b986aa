# frozen_string_literal: true

# A TestEmployee model for testing
class TestEmployee < GrpcClientSideConnection
  include AtharRpc::ActiveRpc

  # Set up ActiveRpc with the User resource using the new DSL
  active_rpc :core, :User, foreign_key: :user_id do
    define_rpc_attribute :name, :string
    define_rpc_attribute :email, :string
    define_rpc_attribute :roles, :json, default: []
    define_rpc_attribute :permissions, :json, default: {}
    define_rpc_attribute :avatar_url, :string
  end

  # Define scope for bulk loading
  scope :with_user_data, -> { includes_rpc(:user) }

  # Define the includes_rpc method for the ActiveRecord::Relation class
  def self.includes_rpc(association)
    # This is a simplified version for testing
    # In a real application, this would be implemented in the ActiveRpc concern
    all
  end



  # Seed with test data
  def self.seed
    # Clear existing data
    delete_all

    # Create test employees
    create!(
      id: 1,
      user_id: 1,
      department: 'Engineering',
      status: 'active'
    )

    create!(
      id: 2,
      user_id: 2,
      department: 'Marketing',
      status: 'active'
    )
  end
end
