require 'spec_helper'
require 'athar_rpc/grpc/concerns/request_processor'

RSpec.describe AtharRpc::Grpc::Concerns::RequestProcessor do
  # Create a test class that includes the concern
  let(:test_class) do
    Class.new do
      include AtharRpc::Grpc::Concerns::RequestProcessor

      def fail!(code, app_code, message, metadata = {})
        raise "gRPC Error: #{code}, #{app_code}, #{message}"
      end
    end
  end

  let(:test_instance) { test_class.new }

  describe '#extract_params' do
    context 'with a payload that responds to to_h' do
      let(:payload) do
        # Create a mock gRPC message object
        Class.new do
          def to_h
            {
              'name' => 'John',
              'email' => '<EMAIL>',
              'address' => {
                'street' => '123 Main St',
                'city' => 'Anytown'
              },
              'phone_numbers' => [
                { 'type' => 'home', 'number' => '555-1234' },
                { 'type' => 'work', 'number' => '555-5678' }
              ]
            }
          end
        end.new
      end

      it 'extracts all parameters when no param_names are provided' do
        params = test_instance.extract_params(payload)

        expect(params).to include('name' => 'John')
        expect(params).to include('email' => '<EMAIL>')
        expect(params).to include('address' => { 'street' => '123 Main St', 'city' => 'Anytown' })
        expect(params).to include('phone_numbers' => [
          { 'type' => 'home', 'number' => '555-1234' },
          { 'type' => 'work', 'number' => '555-5678' }
        ])
      end

      it 'extracts only the requested parameters' do
        params = test_instance.extract_params(payload, :name, :email)

        expect(params.keys).to contain_exactly(:name, :email)
        expect(params[:name]).to eq('John')
        expect(params[:email]).to eq('<EMAIL>')
        expect(params).not_to have_key(:address)
        expect(params).not_to have_key(:phone_numbers)
      end

      it 'ignores parameters not in the payload' do
        params = test_instance.extract_params(payload, :name, :non_existent)

        expect(params.keys).to contain_exactly(:name)
        expect(params[:name]).to eq('John')
        expect(params).not_to have_key(:non_existent)
      end
    end

    context 'with a payload that fails to_h' do
      let(:payload) do
        # Create a mock gRPC message object that fails to_h
        Class.new do
          def to_h
            raise "Cannot convert to hash"
          end

          def name
            'John'
          end

          def email
            '<EMAIL>'
          end

          def has_name?
            true
          end

          def has_email?
            true
          end

          def descriptor
            [
              OpenStruct.new(name: 'name'),
              OpenStruct.new(name: 'email')
            ]
          end
        end.new
      end

      it 'falls back to the old method' do
        params = test_instance.extract_params(payload, :name, :email)

        expect(params.keys).to contain_exactly(:name, :email)
        expect(params[:name]).to eq('John')
        expect(params[:email]).to eq('<EMAIL>')
      end
    end

    context 'with a protobuf message containing empty strings' do
      let(:payload) do
        # Create a mock protobuf message object with empty string values
        mock_class = Class.new do
          def self.descriptor
            [
              OpenStruct.new(name: 'id'),
              OpenStruct.new(name: 'name'),
              OpenStruct.new(name: 'email'),
              OpenStruct.new(name: 'status'),
              OpenStruct.new(name: 'password'),
              OpenStruct.new(name: 'user_roles_list')
            ]
          end

          def to_h
            # Simulate protobuf to_h behavior that might exclude empty strings
            { 'id' => '22' }
          end

          def id
            '22'
          end

          def name
            ''
          end

          def email
            ''
          end

          def status
            ''
          end

          def password
            ''
          end

          def user_roles_list
            []
          end
        end

        mock_class.new
      end

      it 'extracts all parameters including empty strings when no param_names are provided' do
        params = test_instance.extract_params(payload)

        expect(params).to include(:id => '22')
        expect(params).to include(:name => '')
        expect(params).to include(:email => '')
        expect(params).to include(:status => '')
        expect(params).to include(:password => '')
        expect(params).to include(:user_roles_list => [])
      end

      it 'extracts requested parameters including empty strings' do
        params = test_instance.extract_params(payload, :name, :email, :status)

        expect(params.keys).to contain_exactly(:name, :email, :status)
        expect(params[:name]).to eq('')
        expect(params[:email]).to eq('')
        expect(params[:status]).to eq('')
      end
    end

    context 'with real Core::UpdateUserRequest protobuf message' do
      let(:payload) do
        # This requires the actual protobuf classes to be loaded
        if defined?(Core::UpdateUserRequest)
          Core::UpdateUserRequest.new(
            id: "22",
            name: "",
            email: "",
            status: "",
            password: "",
            user_roles_list: []
          )
        else
          # Skip this test if protobuf classes aren't available
          skip "Core::UpdateUserRequest not available"
        end
      end

      it 'extracts all parameters including empty strings from real protobuf message' do
        skip "Core::UpdateUserRequest not available" unless defined?(Core::UpdateUserRequest)

        params = test_instance.extract_params(payload)

        expect(params).to be_a(Hash)
        expect(params).not_to be_empty
        expect(params[:id]).to eq("22")
        expect(params[:name]).to eq("")
        expect(params[:email]).to eq("")
        expect(params[:status]).to eq("")
        expect(params[:password]).to eq("")
        expect(params[:user_roles_list]).to eq([])
      end
    end

    context 'with _fields metadata (dynamic field tracking)' do
      before do
        skip "Core::UpdateUserRequest not available" unless defined?(Core::UpdateUserRequest)
      end

      context 'original problem case: empty array should be preserved' do
        let(:payload) do
          payload = Core::UpdateUserRequest.new(id: "22", user_roles_list: [])
          payload._fields.clear
          payload._fields.push("id", "user_roles_list")
          payload
        end

        it 'extracts both id and user_roles_list when both are in _fields' do
          params = test_instance.extract_params(payload)

          expect(params).to eq({id: "22", user_roles_list: []})
        end

        it 'ignores param_names filter when _fields metadata exists' do
          # Even though we only request :user_roles_list, it should return both id and user_roles_list
          # because _fields is authoritative
          params = test_instance.extract_params(payload, :user_roles_list)

          expect(params).to eq({id: "22", user_roles_list: []})
        end

        it 'ignores param_names that are not in _fields' do
          # Request fields that weren't originally sent
          params = test_instance.extract_params(payload, :name, :email, :user_roles_list)

          # Should only return what was in _fields, not what was requested
          expect(params).to eq({id: "22", user_roles_list: []})
        end
      end

      context 'nested field tracking' do
        let(:payload) do
          avatar_attrs = Core::AvatarAttributesRequest.new(filename: "test.jpg", content_type: "image/jpeg")
          payload = Core::UpdateUserRequest.new(id: "22", avatar_attributes: avatar_attrs)
          payload._fields.clear
          payload._fields.push("id", "avatar_attributes", "avatar_attributes.filename", "avatar_attributes.content_type")
          payload
        end

        it 'extracts top-level fields from nested _fields metadata' do
          params = test_instance.extract_params(payload)

          expect(params.keys).to contain_exactly(:id, :avatar_attributes)
          expect(params[:id]).to eq("22")
          expect(params[:avatar_attributes]).to be_a(Hash)
          expect(params[:avatar_attributes]["filename"]).to eq("test.jpg")
          expect(params[:avatar_attributes]["content_type"]).to eq("image/jpeg")
        end
      end

      context 'array with nested objects' do
        let(:payload) do
          role1 = Core::UserRoleRequest.new(id: "role1", role_id: "admin")
          role2 = Core::UserRoleRequest.new(id: "role2", role_id: "user")
          payload = Core::UpdateUserRequest.new(id: "22", user_roles_list: [role1, role2])
          payload._fields.clear
          payload._fields.push(
            "id", "user_roles_list",
            "user_roles_list[0]", "user_roles_list[0].id", "user_roles_list[0].role_id",
            "user_roles_list[1]", "user_roles_list[1].id", "user_roles_list[1].role_id"
          )
          payload
        end

        it 'extracts top-level fields from array element _fields metadata' do
          params = test_instance.extract_params(payload)

          expect(params.keys).to contain_exactly(:id, :user_roles_list)
          expect(params[:id]).to eq("22")
          expect(params[:user_roles_list]).to respond_to(:length)
          expect(params[:user_roles_list].length).to eq(2)
          expect(params[:user_roles_list][0]["id"]).to eq("role1")
          expect(params[:user_roles_list][0]["role_id"]).to eq("admin")
          expect(params[:user_roles_list][1]["id"]).to eq("role2")
          expect(params[:user_roles_list][1]["role_id"]).to eq("user")
        end
      end

      context 'partial update (only some fields sent)' do
        let(:payload) do
          payload = Core::UpdateUserRequest.new(id: "22", name: "John Doe")
          payload._fields.clear
          payload._fields.push("id", "name")
          payload
        end

        it 'only extracts fields that were originally sent' do
          params = test_instance.extract_params(payload)

          expect(params).to eq({id: "22", name: "John Doe"})
          expect(params).not_to have_key(:user_roles_list)
          expect(params).not_to have_key(:email)
          expect(params).not_to have_key(:status)
        end
      end

      context 'empty _fields metadata' do
        let(:payload) do
          payload = Core::UpdateUserRequest.new(id: "22", name: "John")
          payload._fields.clear  # Empty _fields
          payload
        end

        it 'falls back to old behavior when _fields is empty' do
          # Should use the fallback method since _fields is empty
          params = test_instance.extract_params(payload)

          # This should use the optional field detection
          expect(params[:id]).to eq("22")
          expect(params[:name]).to eq("John")
        end
      end

      context 'mixed field types with _fields' do
        let(:payload) do
          avatar_attrs = Core::AvatarAttributesRequest.new(filename: "avatar.jpg")
          role1 = Core::UserRoleRequest.new(id: "role1", role_id: "admin")
          payload = Core::UpdateUserRequest.new(
            id: "22",
            name: "",
            email: "<EMAIL>",
            avatar_attributes: avatar_attrs,
            user_roles_list: [role1]
          )
          payload._fields.clear
          payload._fields.push(
            "id", "name", "email", "avatar_attributes", "user_roles_list",
            "avatar_attributes.filename", "user_roles_list[0]", "user_roles_list[0].id", "user_roles_list[0].role_id"
          )
          payload
        end

        it 'extracts all types of fields correctly' do
          params = test_instance.extract_params(payload)

          expect(params.keys).to contain_exactly(:id, :name, :email, :avatar_attributes, :user_roles_list)
          expect(params[:id]).to eq("22")
          expect(params[:name]).to eq("")  # Empty string preserved
          expect(params[:email]).to eq("<EMAIL>")
          expect(params[:avatar_attributes]["filename"]).to eq("avatar.jpg")
          expect(params[:user_roles_list][0]["id"]).to eq("role1")
          expect(params[:user_roles_list][0]["role_id"]).to eq("admin")
        end
      end

      context 'with CreateUserRequest _fields metadata' do
        let(:payload) do
          payload = Core::CreateUserRequest.new(
            name: "John Doe",
            email: "<EMAIL>",
            password: "secret123",
            user_roles_list: []
          )
          payload._fields.clear
          payload._fields.push("name", "email", "password", "user_roles_list")
          payload
        end

        it 'extracts all fields from CreateUserRequest with _fields' do
          params = test_instance.extract_params(payload)

          expect(params.keys).to contain_exactly(:name, :email, :password, :user_roles_list)
          expect(params[:name]).to eq("John Doe")
          expect(params[:email]).to eq("<EMAIL>")
          expect(params[:password]).to eq("secret123")
          expect(params[:user_roles_list]).to eq([])
        end
      end

      context 'with UserListRequest _fields metadata' do
        let(:payload) do
          payload = Core::UserListRequest.new(
            ids: ["1", "2"],
            page: 1,
            per_page: 10
          )
          payload._fields.clear
          payload._fields.push("ids", "page", "per_page")
          payload
        end

        it 'extracts all fields from UserListRequest with _fields' do
          params = test_instance.extract_params(payload)

          expect(params.keys).to contain_exactly(:ids, :page, :per_page)
          expect(params[:ids]).to eq(["1", "2"])
          expect(params[:page]).to eq(1)
          expect(params[:per_page]).to eq(10)
        end
      end

      context 'protobuf object conversion issue' do
        let(:payload) do
          # Create nested protobuf objects like what actually happens
          role1 = Core::RoleResponse.new(id: "22", name: "employee", global: false, level: 10)
          project1 = Core::ProjectResponse.new(id: "4", name: "Dare to Care", description: "Community care and support initiative", status: "active")
          user_role1 = Core::UserRoleRequest.new(id: "9", role_id: "22", project_id: "4", is_default: false, level: 0, role: role1, project: project1)

          role2 = Core::RoleResponse.new(id: "22", name: "employee", global: false, level: 10)
          project2 = Core::ProjectResponse.new(id: "3", name: "Tamkeen", description: "Empowerment and capacity building project", status: "active")
          user_role2 = Core::UserRoleRequest.new(id: "8", role_id: "22", project_id: "3", is_default: true, level: 0, role: role2, project: project2)

          user_role3 = Core::UserRoleRequest.new(id: "", role_id: "7", project_id: "2", is_default: false, level: 0)

          payload = Core::UpdateUserRequest.new(id: "6", user_roles_list: [user_role1, user_role2, user_role3])
          payload._fields.clear
          payload._fields.push(
            "id", "user_roles_list",
            # Add nested fields that the test expects to access
            "user_roles_list[0]", "user_roles_list[0].id", "user_roles_list[0].role_id", "user_roles_list[0].project_id", "user_roles_list[0].is_default",
            "user_roles_list[0].role", "user_roles_list[0].role.id", "user_roles_list[0].role.name",
            "user_roles_list[0].project", "user_roles_list[0].project.id", "user_roles_list[0].project.name"
          )
          payload
        end

        it 'should convert protobuf objects to Ruby hashes' do
          params = test_instance.extract_params(payload)

          expect(params[:id]).to eq("6")
          expect(params[:user_roles_list]).to be_a(Array)
          expect(params[:user_roles_list].length).to eq(3)

          # The extracted user_roles_list should be Ruby hashes, not protobuf objects
          first_role = params[:user_roles_list][0]
          expect(first_role).to be_a(Hash)
          expect(first_role).not_to be_a(Core::UserRoleRequest)

          # Check the structure is correct (protobuf to_h uses string keys)
          expect(first_role["id"]).to eq("9")
          expect(first_role["role_id"]).to eq("22")
          expect(first_role["project_id"]).to eq("4")
          expect(first_role["is_default"]).to eq(false)

          # Nested objects should also be converted to hashes
          expect(first_role["role"]).to be_a(Hash)
          expect(first_role["role"]["id"]).to eq("22")
          expect(first_role["role"]["name"]).to eq("employee")

          expect(first_role["project"]).to be_a(Hash)
          expect(first_role["project"]["id"]).to eq("4")
          expect(first_role["project"]["name"]).to eq("Dare to Care")
        end
      end

      context 'respecting _fields metadata for nested objects' do
        let(:payload) do
          # Create the exact scenario from the real issue:
          # user_roles_list[2] should only have project_id and role_id in _fields
          # but extract_params returns id, is_default, level, role, project as well
          role1 = Core::RoleResponse.new(id: "22", name: "employee", global: false, level: 10)
          project1 = Core::ProjectResponse.new(id: "4", name: "Dare to Care", description: "Community care and support initiative", status: "active")
          user_role1 = Core::UserRoleRequest.new(id: "9", role_id: "22", project_id: "4", is_default: false, level: 0, role: role1, project: project1)

          role2 = Core::RoleResponse.new(id: "22", name: "employee", global: false, level: 10)
          project2 = Core::ProjectResponse.new(id: "3", name: "Tamkeen", description: "Empowerment and capacity building project", status: "active")
          user_role2 = Core::UserRoleRequest.new(id: "8", role_id: "22", project_id: "3", is_default: true, level: 0, role: role2, project: project2)

          # This third role has NO nested objects and limited _fields tracking
          user_role3 = Core::UserRoleRequest.new(id: "", role_id: "7", project_id: "2", is_default: false, level: 0)

          payload = Core::UpdateUserRequest.new(id: "6", user_roles_list: [user_role1, user_role2, user_role3])
          payload._fields.clear
          payload._fields.push(
            "id", "user_roles_list",
            # Full tracking for first two items
            "user_roles_list[0]", "user_roles_list[0].id", "user_roles_list[0].project_id", "user_roles_list[0].role_id",
            "user_roles_list[1]", "user_roles_list[1].id", "user_roles_list[1].is_default", "user_roles_list[1].project_id", "user_roles_list[1].role_id",
            # LIMITED tracking for third item - only project_id and role_id, NO id, is_default, level, etc.
            "user_roles_list[2].project_id", "user_roles_list[2].role_id"
          )
          payload
        end

        it 'should only extract fields that were in _fields metadata' do
          params = test_instance.extract_params(payload)

          expect(params[:id]).to eq("6")
          expect(params[:user_roles_list]).to be_a(Array)
          expect(params[:user_roles_list].length).to eq(3)

          # First two items should have all their tracked fields
          first_role = params[:user_roles_list][0]
          expect(first_role.keys).to include("id", "project_id", "role_id")

          second_role = params[:user_roles_list][1]
          expect(second_role.keys).to include("id", "is_default", "project_id", "role_id")

          # CRITICAL TEST: Third item should ONLY have project_id and role_id
          # because those are the only fields in _fields for user_roles_list[2]
          third_role = params[:user_roles_list][2]
          expect(third_role.keys).to contain_exactly("project_id", "role_id")

          # These fields should NOT be present because they weren't in _fields
          expect(third_role).not_to have_key("id")
          expect(third_role).not_to have_key("is_default")
          expect(third_role).not_to have_key("level")
          expect(third_role).not_to have_key("role")
          expect(third_role).not_to have_key("project")

          # But the values that are present should be correct
          expect(third_role["project_id"]).to eq("2")
          expect(third_role["role_id"]).to eq("7")
        end
      end
    end
  end
end
