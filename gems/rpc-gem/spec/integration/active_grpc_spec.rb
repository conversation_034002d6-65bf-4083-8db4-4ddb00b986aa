# frozen_string_literal: true

# This test verifies that the ActiveRpc concern correctly handles Google::Protobuf::RepeatedField
# by running a real gRPC server and making actual gRPC calls.
#
# The test sets up:
# 1. A real gRPC server implementing the Test::Users service
# 2. A custom GrpcClient that makes real gRPC calls to this server
# 3. A test ActiveRecord model that uses the ActiveRpc concern
# 4. Dynamically generates gRPC Ruby files from a proto file in the fixtures directory
#
# It then verifies that:
# 1. Bulk loading works correctly with Google::Protobuf::RepeatedField
# 2. map(&:attribute) uses cached values instead of making individual RPC calls
# 3. Single user calls work correctly

require 'spec_helper'
require 'active_record'
require 'logger'
require 'grpc'
require 'fileutils'
require 'tmpdir'

# We're now using GrpcHelper for gRPC-related functionality

# Create a custom gRPC client for testing with a real gRPC server
# We use a separate module to avoid conflicts with other tests
module TestRpcClient
  # Use the GrpcHelper to create a test client
  def self.GrpcClient
    GrpcHelper.create_test_client('localhost', 50052)
  end

  # Legacy GrpcClient class for backward compatibility
  class GrpcClient
    def initialize(host, port)
      @client = GrpcHelper.create_test_client(host, port)
      @calls = []
    end

    def call(method, params = {})
      # Delegate to the client from GrpcHelper
      @calls << { method: method, params: params }
      @client.call(method, params)
    end

    def clear
      @calls = []
    end

    def calls
      @calls
    end
  end

  class GrpcClientFactory
    def self.instance
      @instance ||= new
    end

    def client_for(service, resource)
      # Return a real gRPC client using GrpcHelper
      GrpcHelper.create_test_client('localhost', 50052)
    end
  end

  # Define a GrpcConfig class for our test
  module GrpcConfig
    def self.config
      {
        'default' => {
          'core' => {
            'grpc_host' => 'localhost',
            'grpc_port' => 50052,
            'services' => ['Users']
          }
        },
        'development' => {
          'inherit' => 'default'
        },
        'test' => {
          'inherit' => 'default'
        }
      }
    end
  end
end

# Override AtharRpc classes for this test only
module AtharRpc
  # Save original classes
  OriginalGrpcClient = GrpcClient if defined?(GrpcClient)
  OriginalGrpcClientFactory = GrpcClientFactory if defined?(GrpcClientFactory)
  OriginalGrpcConfig = GrpcConfig if defined?(GrpcConfig)

  # Use our test implementations
  GrpcClient = TestRpcClient::GrpcClient
  GrpcClientFactory = TestRpcClient::GrpcClientFactory
  GrpcConfig = TestRpcClient::GrpcConfig
end

require 'concerns/active_rpc'

# The TestUsersService class will be defined after the Test module is loaded in before(:all)

# Add env method to the existing Rails module
module Rails
  def self.env
    'test'
  end

  def self.logger
    @logger ||= Logger.new(STDOUT)
  end
end

# The ActiveRecord environment is already set up in spec_helper.rb
# It uses separate file-based SQLite databases for each model

# Require the test models
require_relative '../fixtures/models/test_user'
require_relative '../fixtures/models/test_employee'

RSpec.describe 'ActiveRpc with real gRPC server' do
  # Set up temporary directory for generated gRPC files
  before(:all) do
    # Create a temporary directory for the generated gRPC files
    @tmp_dir = File.join(Dir.tmpdir, "grpc_test_#{Time.now.to_i}")
    FileUtils.mkdir_p(@tmp_dir)

    # Path to the proto fixture
    proto_fixture_path = File.join(File.dirname(__FILE__), '../fixtures/protos/test/users.proto')

    # Generate the gRPC Ruby files
    unless GrpcHelper.generate_grpc_files_from_proto(proto_fixture_path, @tmp_dir)
      raise "Failed to generate gRPC Ruby files from proto"
    end

    # Add the temporary directory to the load path
    $LOAD_PATH.unshift(@tmp_dir)

    # Get the paths to the generated files
    # The files are generated in protos/test/ based on the output from the command
    proto_dirname = 'protos/test'
    pb_file = File.join(@tmp_dir, proto_dirname, 'users_pb.rb')
    services_file = File.join(@tmp_dir, proto_dirname, 'users_services_pb.rb')

    # Check if the files exist
    unless File.exist?(pb_file) && File.exist?(services_file)
      raise "Generated gRPC files not found at #{pb_file} and #{services_file}"
    end

    # Require the generated files
    require pb_file
    require services_file

    # Load the TestUsersService implementation from the fixture
    require_relative '../fixtures/services/test_users_service'

    # Start a real gRPC server
    @server, @server_thread = GrpcHelper.start_grpc_server(TestServices::UsersService, 50052)
  end

  # Stop the server and clean up after all tests
  after(:all) do
    # Stop the gRPC server
    GrpcHelper.stop_grpc_server(@server, @server_thread)

    # Remove the temporary directory
    FileUtils.rm_rf(@tmp_dir)

    # Remove the temporary directory from the load path
    $LOAD_PATH.delete(@tmp_dir)
  end

  before(:each) do
    # Clear the database
    TestEmployee.delete_all

    # Create test data
    TestEmployee.create!(user_id: 1, department: 'IT', status: 'active')
    TestEmployee.create!(user_id: 2, department: 'HR', status: 'active')
  end

  it 'loads data in bulk when using with_user_data scope' do
    # This triggers a bulk gRPC call to our real gRPC server
    # The server returns a Google::Protobuf::RepeatedField of UserResponse objects
    # Our ActiveRpc concern should handle this format correctly
    employees = TestEmployee.with_user_data.all

    # This should use the cached values from the bulk loading
    # If the bulk loading didn't work, this would trigger individual gRPC calls
    names = employees.map(&:name)

    # Verify that we got the expected results
    expect(names).to eq(['John Doe', 'Jane Smith'])
  end

  it 'supports map(&:attribute) with bulk loading' do
    # This triggers a bulk gRPC call to our real gRPC server
    employees = TestEmployee.with_user_data.all

    # This should use the cached values from the bulk loading
    names = employees.map(&:name)

    # Verify that we got the expected results
    expect(names).to eq(['John Doe', 'Jane Smith'])

    # Call map(&:name) again to verify it uses cached values
    # If caching isn't working, this would trigger individual gRPC calls
    names_again = employees.map(&:name)
    expect(names_again).to eq(['John Doe', 'Jane Smith'])

    # Note: In a real application, we would verify that no additional gRPC calls were made
    # by checking the logs or using a mock client that counts calls
  end

  it 'loads data for a single user when accessing attributes directly' do
    # Get a single employee without using with_user_data scope
    # This should not trigger any gRPC calls yet
    employee = TestEmployee.find_by(user_id: 1)

    # Access an attribute that requires gRPC data
    # This should trigger a single gRPC call to get_user
    name = employee.name

    # Verify that we got the expected result
    expect(name).to eq('John Doe')

    # Access another attribute
    # This should use the cached data from the previous call
    email = employee.email
    expect(email).to eq('<EMAIL>')

    # Verify that roles and permissions are correctly handled
    # These are repeated fields in the proto definition
    expect(employee.roles).to eq(['admin'])
    expect(employee.permissions).to eq(['read', 'write'])

    # Verify that the status is correctly retrieved
    expect(employee.status).to eq('active')
  end

  it 'updates user attributes via the gRPC service' do
    # Get a single employee
    employee = TestEmployee.find_by(user_id: 1)

    # Set new values for attributes
    employee.name = 'John Updated'
    employee.email = '<EMAIL>'
    employee.roles = ['admin', 'manager']

    # Verify that the attributes were updated locally
    expect(employee.name).to eq('John Updated')
    expect(employee.email).to eq('<EMAIL>')
    expect(employee.roles).to eq(['admin', 'manager'])

    # Verify that there are pending updates
    expect(employee.has_user_updates?).to be true

    # Save the updates to the gRPC service
    result = employee.update_user

    # Verify that the update was successful
    expect(result).to be true

    # Verify that the attributes are still accessible
    expect(employee.name).to eq('John Updated')
    expect(employee.email).to eq('<EMAIL>')
    expect(employee.roles).to eq(['admin', 'manager'])

    # Verify that there are no more pending updates
    expect(employee.has_user_updates?).to be false
  end

  it 'creates a new user via the gRPC service' do
    # Create a new user
    user_id = TestEmployee.create_user(
      name: 'New Test User',
      email: '<EMAIL>',
      roles: ['user'],
      permissions: ['read']
    )

    # Verify that we got a valid user ID
    expect(user_id).not_to be_nil

    # Create a new employee with this user ID
    employee = TestEmployee.create!(user_id: user_id, department: 'Test', status: 'active')

    # Verify that we can access the user data
    expect(employee.name).to be_present
    expect(employee.email).to be_present
    expect(employee.roles).to be_present
    expect(employee.permissions).to be_present
  end

  it 'creates and associates a new user with an existing model' do
    # Create a new employee without a user ID
    employee = TestEmployee.create!(department: 'Test', status: 'active')

    # Create and associate a new user
    result = employee.create_and_associate_user(
      name: 'Associated User',
      email: '<EMAIL>',
      roles: ['user'],
      permissions: ['read']
    )

    # Verify that the operation was successful
    expect(result).to be true

    # Verify that the user ID was set
    expect(employee.user_id).not_to be_nil

    # Verify that we can access the user data
    expect(employee.name).to be_present
    expect(employee.email).to be_present
    expect(employee.roles).to be_present
    expect(employee.permissions).to be_present
  end

  it 'automatically saves delegated attributes when the model is saved' do
    # Get a single employee
    employee = TestEmployee.find_by(user_id: 1)

    # Set attributes on the model
    employee.name = 'Auto Updated'
    employee.email = '<EMAIL>'

    # Save the model (this should automatically save the delegated attributes)
    employee.save

    # Verify that an RPC call was made to update the user
    expect(employee.name).to eq('Auto Updated')
    expect(employee.email).to eq('<EMAIL>')
  end

  it 'creates a new user when saving a model with build_user' do
    # Create a new employee with build_user
    employee = TestEmployee.new(department: 'IT', status: 'active')
    employee.build_user(
      name: 'Built User',
      email: '<EMAIL>',
      roles: ['user'],
      permissions: ['read']
    )

    # Save the model (this should create the user and associate it)
    employee.save

    # Verify that the user ID was set
    expect(employee.user_id).to be_present

    # Verify that we can access the user data
    expect(employee.name).to eq('Built User')
    expect(employee.email).to eq('<EMAIL>')
  end

  it 'tracks changes to delegated attributes' do
    # Get a single employee
    employee = TestEmployee.find_by(user_id: 1)

    # Set attributes on the model
    employee.name = 'Changed Name'

    # Verify that the attribute was changed
    expect(employee.user_changed?).to be true
    expect(employee.user_changes).to include('name' => [anything, 'Changed Name'])
  end

  it 'validates delegated attributes on save' do
    # Get a single employee
    employee = TestEmployee.find_by(user_id: 1)

    # Set invalid attributes
    employee.email = 'invalid-email'

    # Try to save the model
    expect(employee.save).to be false
    expect(employee.errors.full_messages.join).to include('must be a valid email address')

    # Set valid attributes
    employee.email = '<EMAIL>'

    # Clear errors
    employee.errors.clear

    # Save the model again
    expect(employee.save).to be true
    expect(employee.errors.messages[:email]).to be_empty
  end

  it 'prevents saving the model if delegated attribute validation fails' do
    # Get a single employee
    employee = TestEmployee.find_by(user_id: 1)

    # Load the user data first to avoid GetUser call during the test
    employee.name # This will trigger a GetUser call with the real client

    # Mock the gRPC client to return validation errors
    client_double = instance_double(AtharRpc::GrpcClient)

    # We don't need to mock GetUser since we've already loaded the data

    # Then, make UpdateUser return validation errors
    allow(client_double).to receive(:call).with(:UpdateUser, anything).and_return(
      OpenStruct.new(
        message: nil,
        error: OpenStruct.new(
          code: 'INVALID_ARGUMENT',
          message: 'Validation failed',
          validation_errors: {
            'email' => ['must be a valid email address']
          },
          full_messages: ['Email must be a valid email address']
        )
      )
    )

    # Replace the real client with our double
    original_client = TestEmployee.grpc_client
    TestEmployee.grpc_client = client_double

    # Set invalid attributes
    employee.email = 'invalid-email'

    # Try to save the model
    result = employee.save

    # Restore the original client
    TestEmployee.grpc_client = original_client

    # Verify that the save failed
    expect(result).to be false

    # Verify that validation errors were added to the model
    expect(employee.errors.messages[:email]).to include('must be a valid email address')
  end

  it 'handles validation errors when creating a new record' do
    # Mock the gRPC client to return our new structured response format
    client_double = instance_double(AtharRpc::GrpcClient)

    # Create the structured response with validation errors
    structured_response = OpenStruct.new(
      id: "",
      name: "",
      email: "",
      roles: [],
      permissions: [],
      avatar_url: "",
      status: "",
      _metadata: OpenStruct.new(
        success: false,
        errors: {
          'name' => OpenStruct.new(messages: ['is too short (minimum is 2 characters)']),
          'email' => OpenStruct.new(messages: ['must be a valid email address'])
        }
      )
    )

    allow(client_double).to receive(:call).with(:CreateUser, anything).and_return(
      OpenStruct.new(
        message: structured_response,  # The actual response goes in message
        error: nil                     # No gRPC error since this is a successful call
      )
    )

    # Replace the real client with our double
    original_client = TestEmployee.grpc_client
    TestEmployee.grpc_client = client_double

    # Create a model to test validation errors
    employee = TestEmployee.new

    # Build an invalid user
    employee.build_user(
      name: 'A', # Too short
      email: 'invalid-email' # Invalid format
    )

    # Try to save the model
    result = employee.save

    # Restore the original client
    TestEmployee.grpc_client = original_client

    # Verify that the save failed
    expect(result).to be false

    # Verify that validation errors were added to the model
    expect(employee.errors.messages[:name]).to include('is too short (minimum is 2 characters)')
    expect(employee.errors.messages[:email]).to include('must be a valid email address')
  end

  it 'aborts the model save if the delegated update fails due to client error' do
    # Get a single employee
    employee = TestEmployee.find_by(user_id: 1)

    # Load the user data first to avoid GetUser call during the test
    employee.name

    # Add a special case to the test_users_service.rb to handle 'Will Fail' name
    # Set attributes on the model
    employee.name = 'Will Fail'
    employee.department = 'New Department'

    # Try to save the model
    result = employee.save

    # Verify that the save failed
    expect(result).to be false

    # Verify that an error was added to the model
    expect(employee.errors.full_messages.join).to match(/Failed to update user record/i)

    # Verify that the department was not updated (in a real app, we would check the database)
    # Since we're using a mock ActiveRecord, we can't actually test the rollback
    # but the test verifies that the save method returns false, which would trigger
    # a rollback in a real ActiveRecord model
  end

  it 'aborts the model save if the server responds with a failure' do
    # Get a single employee
    employee = TestEmployee.find_by(user_id: 1)

    # Set attributes on the model - this name will trigger a validation error in the service
    employee.name = 'Server Will Reject'
    employee.department = 'New Department'

    # Try to save the model
    result = employee.save

    # Verify that the save failed
    expect(result).to be false

    # Verify that error messages were added to the model
    expect(employee.errors.messages[:email]).to be_present
    expect(employee.errors.messages[:name]).to be_present

    # In a real application, we would verify that the database transaction was rolled back
    # and the department was not updated
  end
end
