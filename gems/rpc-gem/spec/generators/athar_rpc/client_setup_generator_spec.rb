# noinspection RubyResolve
require "spec_helper"
require "generator_spec"
require "generators/athar_rpc/client_setup/client_setup_generator"
require "fileutils"

RSpec.describe AtharRpc::Generators::ClientSetupGenerator, type: :generator do
  gem_root = Gem::Specification.find_by_name("athar_rpc").gem_dir
  tmp_path = File.join(gem_root, "tmp")
  self.file_fixture_path = File.join(gem_root, "spec/fixtures")
  destination File.join(gem_root, "tmp")

  let(:expected_config_fixture) { file_fixture("expected/config/grpc_clients.yml") }
  let(:expected_initializer_fixture) { file_fixture("expected/initializers/athar_rpc_client.rb") }

  before(:each) do
    allow(Gem::Specification).to receive(:find_by_name).with("athar_rpc").and_return(double(gem_dir: File.join(gem_root, "spec/fixtures")))
  end

  after(:each) do
    FileUtils.rm_rf(tmp_path)
  end

  it "generates a grpc.yml configuration file with detected services" do
    # Run the generator
    run_generator

    # Validate grpc_clients.yml file was created
    generated_config = File.join(tmp_path, "config/grpc_clients.yml")
    expect(File).to exist(generated_config)

    # Verify the content has the expected sections
    generated_content = File.read(generated_config)

    # Check for key sections rather than exact match
    expect(generated_content).to include('default: &default')
    expect(generated_content).to include('core:')
    expect(generated_content).to include('grpc_host: "core-app-grpc"')
    expect(generated_content).to include('services:')
    expect(generated_content).to include('- Users')
    expect(generated_content).to include('development:')
    expect(generated_content).to include('<<: *default')
  end

  it "generates an initializer for loading the gRPC config" do
    # Run the generator
    run_generator

    # Validate initializer file was created
    generated_initializer = File.join(tmp_path, "config/initializers/athar_rpc_client.rb")
    expect(File).to exist(generated_initializer)

    # Compare with the expected fixture
    generated_content = File.read(generated_initializer)
    expected_content = File.read(expected_initializer_fixture)
    expect(generated_content.strip).to eq(expected_content.strip)
  end
end
