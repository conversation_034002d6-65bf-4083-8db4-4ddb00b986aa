# noinspection RubyResolve
require "spec_helper"
require "generator_spec"
require "generators/athar_rpc/gruf_controller/gruf_controller_generator"
require "fileutils"

RSpec.describe AtharRpc::Generators::GrufControllerGenerator, type: :generator do
  gem_root = Gem::Specification.find_by_name("athar_rpc").gem_dir
  tmp_path = File.join(gem_root, "tmp")
  self.file_fixture_path = File.join(gem_root, "spec/fixtures")
  destination File.join(gem_root, "tmp")

  let(:proto_file) { "core/users.proto" }
  let(:expected_output_fixture) { file_fixture("expected/controllers/users_controller.rb") }

  before(:each) do
    allow(Gem::Specification).to receive(:find_by_name).with("athar_rpc").and_return(double(gem_dir: File.join(gem_root, "spec/fixtures")))
  end

  after(:each) do
    FileUtils.rm_rf(tmp_path)
  end

  it "generates a UsersController from the .proto file and matches the expected output" do
    # Run the generator
    run_generator [proto_file]

    # Validate file was created
    generated_file = File.join(tmp_path, "app/rpc/core/users_controller.rb")
    expect(File).to exist(generated_file)

    # Compare with the expected fixture
    generated_content = File.read(generated_file)
    expected_content = File.read(expected_output_fixture)
    expect(generated_content.strip).to eq(expected_content.strip)
  end
end