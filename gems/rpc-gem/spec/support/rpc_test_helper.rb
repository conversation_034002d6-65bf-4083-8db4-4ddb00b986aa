# frozen_string_literal: true

# This module provides shared test helpers for RPC-related tests
module RpcTestHelper
  # Mock GrpcClientFactory for testing
  module TestRpcClient
    class GrpcClient
      attr_reader :service, :resource, :calls

      def initialize(service, resource)
        @service = service
        @resource = resource
        @calls = []
      end

      def calls
        @calls
      end

      def clear
        @calls = []
      end

      def call(method_name, params = {})
        @calls << { method: method_name, params: params }

        case method_name
        when :CreateUser
          # Create a real TestUser record in the database if we're in a database test
          if defined?(TestUser) && TestUser.table_exists?
            user = TestUser.create!(
              name: params[:name],
              email: params[:email],
              roles: params[:roles] || [ 'user' ],
              permissions: params[:permissions] || [ 'read' ],
              status: params[:status] || 'active'
            )

            # Return a response with the ID
            OpenStruct.new(
              message: OpenStruct.new(
                id: user.id.to_s,
                name: user.name,
                email: user.email
              )
            )
          else
            # Return a mock response with an ID for non-database tests
            OpenStruct.new(
              message: OpenStruct.new(
                id: '123',
                name: params[:name] || 'Test User',
                email: params[:email] || '<EMAIL>'
              )
            )
          end
        when :UpdateUser
          if defined?(TestUser) && TestUser.table_exists?
            # Find the user in the database
            user = TestUser.find_by(id: params[:id])

            if user
              # Update the user with the provided attributes
              update_attrs = {}
              params.each do |key, value|
                # Skip the ID parameter
                next if key == :id
                # Only update attributes that exist on the model
                update_attrs[key] = value if user.respond_to?("#{key}=")
              end

              # Update the user
              user.update!(update_attrs)

              # Return a response with the updated user data
              OpenStruct.new(
                message: OpenStruct.new(
                  id: user.id.to_s,
                  name: user.name,
                  email: user.email,
                  roles: user.roles,
                  permissions: user.permissions,
                  status: user.status
                )
              )
            else
              nil
            end
          else
            # Return a mock response for non-database tests
            OpenStruct.new(
              message: OpenStruct.new(
                id: params[:id],
                name: params[:name] || 'Updated User',
                email: params[:email] || '<EMAIL>'
              )
            )
          end
        when :GetUser
          if defined?(TestUser) && TestUser.table_exists?
            # Find the user in the database
            user = TestUser.find_by(id: params[:id])

            if user
              # Return a response with the user data
              OpenStruct.new(
                message: OpenStruct.new(
                  id: user.id.to_s,
                  name: user.name,
                  email: user.email,
                  roles: user.roles,
                  permissions: user.permissions,
                  status: user.status
                )
              )
            else
              nil
            end
          else
            # Return a mock response with user data for non-database tests
            OpenStruct.new(
              message: OpenStruct.new(
                id: params[:id],
                name: params[:name] || 'Test User',
                email: params[:email] || '<EMAIL>'
              )
            )
          end
        when :ListUsers
          ids = params[:ids] || []
          items = ids.map do |id|
            OpenStruct.new(
              id: id,
              name: "User #{id}",
              email: "user#{id}@example.com",
              role: 'user'
            )
          end
          OpenStruct.new(message: OpenStruct.new(items: items))
        when :UpdateUser
          OpenStruct.new(
            message: OpenStruct.new(
              id: params[:id],
              name: params[:name] || 'Updated Name',
              email: params[:email] || '<EMAIL>',
              role: params[:role] || 'user'
            )
          )
        else
          nil
        end
      end
    end

    class GrpcClientFactory
      def self.instance
        @instance ||= new
      end

      def client_for(service, resource)
        @clients ||= {}
        @clients["#{service}_#{resource}"] ||= GrpcClient.new(service, resource)
      end
    end
  end

  # Method to set up the RPC test environment
  def self.setup
    # Store original classes if they exist
    AtharRpc.const_set(:OriginalGrpcClient, AtharRpc::GrpcClient) if AtharRpc.const_defined?(:GrpcClient) && !AtharRpc.const_defined?(:OriginalGrpcClient)
    AtharRpc.const_set(:OriginalGrpcClientFactory, AtharRpc::GrpcClientFactory) if AtharRpc.const_defined?(:GrpcClientFactory) && !AtharRpc.const_defined?(:OriginalGrpcClientFactory)

    # Remove existing constants if they exist to avoid warnings
    AtharRpc.send(:remove_const, :GrpcClientFactory) if AtharRpc.const_defined?(:GrpcClientFactory)
    AtharRpc.send(:remove_const, :GrpcClient) if AtharRpc.const_defined?(:GrpcClient)

    # Define the GrpcClient class
    AtharRpc.const_set(:GrpcClient, TestRpcClient::GrpcClient)

    # Define the GrpcClientFactory class
    AtharRpc.const_set(:GrpcClientFactory, Class.new do
      def self.instance
        @instance ||= new
      end

      def client_for(service, resource)
        @clients ||= {}
        @clients["#{service}_#{resource}"] ||= TestRpcClient::GrpcClient.new(service, resource)
      end
    end)
  end

  # Method to restore the original RPC environment
  def self.teardown
    # Restore original classes if they exist
    if AtharRpc.const_defined?(:OriginalGrpcClient)
      AtharRpc.send(:remove_const, :GrpcClient) if AtharRpc.const_defined?(:GrpcClient)
      AtharRpc.const_set(:GrpcClient, AtharRpc::OriginalGrpcClient)
      AtharRpc.send(:remove_const, :OriginalGrpcClient)
    end

    if AtharRpc.const_defined?(:OriginalGrpcClientFactory)
      AtharRpc.send(:remove_const, :GrpcClientFactory) if AtharRpc.const_defined?(:GrpcClientFactory)
      AtharRpc.const_set(:GrpcClientFactory, AtharRpc::OriginalGrpcClientFactory)
      AtharRpc.send(:remove_const, :OriginalGrpcClientFactory)
    end
  end
end
