# frozen_string_literal: true

# Helper module for gRPC-related functionality in tests
module GrpcHelper
  # Generate gRPC Ruby files from proto files in a temporary directory
  def self.generate_grpc_files_from_proto(proto_fixture_path, output_dir)
    # Create the output directory structure
    FileUtils.mkdir_p(output_dir)

    # Create a temporary directory for the proto file with the correct structure
    proto_tmp_dir = File.join(Dir.tmpdir, 'proto_tmp')
    FileUtils.rm_rf(proto_tmp_dir) if Dir.exist?(proto_tmp_dir)
    FileUtils.mkdir_p(proto_tmp_dir)

    # Get the proto file name and directory structure
    proto_filename = File.basename(proto_fixture_path)
    proto_dirname = File.dirname(proto_fixture_path).split('/').last(2).join('/')

    # Create the directory structure in the temporary directory
    FileUtils.mkdir_p(File.join(proto_tmp_dir, proto_dirname))

    # Copy the proto file to the temporary location with the correct structure
    FileUtils.cp(proto_fixture_path, File.join(proto_tmp_dir, proto_dirname, proto_filename))

    # Run the protoc compiler
    cmd = "bundle exec grpc_tools_ruby_protoc -I #{proto_tmp_dir} \
           --ruby_out=#{output_dir} \
           --grpc_out=#{output_dir} \
           #{File.join(proto_tmp_dir, proto_dirname, proto_filename)}"

    # Execute the command
    result = system(cmd)

    # Clean up the temporary directory
    FileUtils.rm_rf(proto_tmp_dir)

    # Return the result
    result
  end

  # Start a gRPC server for testing
  def self.start_grpc_server(service_class, port)
    server = GRPC::RpcServer.new
    server.add_http2_port("localhost:#{port}", :this_port_is_insecure)
    server.handle(service_class.new)

    # Start the server in a separate thread
    server_thread = Thread.new do
      server.run
    end

    # Wait for the server to start
    sleep 1

    # Return the server and thread so they can be stopped later
    [server, server_thread]
  end

  # Stop a gRPC server
  def self.stop_grpc_server(server, server_thread)
    server.stop
    server_thread.join
  end

  # Create a test gRPC client
  def self.create_test_client(host, port)
    # Create a custom gRPC client for testing
    Class.new do
      def initialize(host, port)
        @host = host
        @port = port
      end

      def call(method, params = {})
        # Create a real gRPC client
        stub = Test::Users::Stub.new("#{@host}:#{@port}", :this_channel_is_insecure)

        begin
          case method
          when :GetUser
            request = Test::UserRequest.new(id: params[:id])
            response = stub.get_user(request)
            # Wrap the response in an object with a message method
            OpenStruct.new(message: response)
          when :ListUsers
            request = Test::UserListRequest.new(ids: params[:ids])
            response = stub.list_users(request)
            # Wrap the response in an object with a message method
            OpenStruct.new(message: response)
          when :UpdateUser
            # Create an UpdateUserRequest with the provided parameters
            request = Test::UpdateUserRequest.new(
              id: params[:id],
              name: params[:name],
              email: params[:email],
              avatar_url: params[:avatar_url],
              roles: params[:roles],
              permissions: params[:permissions],
              status: params[:status]
            )
            response = stub.update_user(request)
            # Wrap the response in an object with a message method
            OpenStruct.new(message: response)
          when :CreateUser
            # Create a CreateUserRequest with the provided parameters
            request = Test::CreateUserRequest.new(
              name: params[:name],
              email: params[:email],
              avatar_url: params[:avatar_url],
              roles: params[:roles],
              permissions: params[:permissions],
              status: params[:status]
            )
            response = stub.create_user(request)
            # Wrap the response in an object with a message method
            OpenStruct.new(message: response)
          else
            raise "Unknown method: #{method}"
          end
        rescue GRPC::InvalidArgument => e
          # Handle validation errors from the server
          error_message = e.message

          # Check if this is a structured validation error
          if error_message.include?('VALIDATION_ERROR:')
            # Extract the JSON part
            json_str = error_message.gsub(/^\d+:VALIDATION_ERROR: /, '')

            begin
              # Parse the JSON
              error_data = JSON.parse(json_str)

              # Return a response with structured validation errors
              OpenStruct.new(
                message: nil,
                error: OpenStruct.new(
                  code: 'INVALID_ARGUMENT',
                  message: error_message,
                  validation_errors: error_data['errors'],
                  full_messages: error_data['full_messages']
                )
              )
            rescue JSON::ParserError
              # Fallback to generic error if JSON parsing fails
              OpenStruct.new(
                message: nil,
                error: OpenStruct.new(
                  code: 'INVALID_ARGUMENT',
                  message: error_message
                )
              )
            end
          # Legacy format for backward compatibility
          elsif error_message.include?('Validation failed:')
            # Parse the error message to extract validation errors
            validation_errors = {}
            error_message.gsub(/^\d+:/, '').gsub('Validation failed: ', '').split(', ').each do |error|
              if error =~ /^([A-Za-z_]+) (is .+)$/
                field = $1.downcase.to_sym
                message = $2
                validation_errors[field] = [message]
              end
            end

            # Return a response with validation errors
            OpenStruct.new(
              message: nil,
              error: OpenStruct.new(
                code: 'INVALID_ARGUMENT',
                message: error_message,
                validation_errors: validation_errors,
                full_messages: error_message.gsub(/^\d+:/, '').gsub('Validation failed: ', '').split(', ')
              )
            )
          else
            # Return a generic error response
            OpenStruct.new(
              message: nil,
              error: OpenStruct.new(
                code: 'INVALID_ARGUMENT',
                message: error_message
              )
            )
          end
        rescue GRPC::BadStatus => e
          # Handle other gRPC errors
          OpenStruct.new(
            message: nil,
            error: OpenStruct.new(
              code: e.class.name.split('::').last,
              message: e.message
            )
          )
        end
      end
    end.new(host, port)
  end
end
