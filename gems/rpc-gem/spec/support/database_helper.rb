# frozen_string_literal: true

# Helper module for database-related functionality in tests
module DatabaseHelper
  # Set up the database connections for testing
  def self.setup_database_connections
    # Use the gem's root path as the base for all paths
    gem_root = File.expand_path('../../', __dir__)
    db_dir = File.join(gem_root, 'tmp', 'test')

    # Make sure the directory exists
    FileUtils.mkdir_p(db_dir)

    # Remove any existing database files
    FileUtils.rm_f(File.join(db_dir, 'test_server.sqlite3'))
    FileUtils.rm_f(File.join(db_dir, 'test_client.sqlite3'))

    # Log the database directory for debugging
    puts "Using database directory: #{db_dir}"

    # Set up database connections
    ActiveRecord::Base.establish_connection(
      adapter: 'sqlite3',
      database: ':memory:'
    )

    # Define connection classes for each model
    # These classes should already be defined in the spec_helper.rb file
    # We just need to establish the connections

    # GrpcClientSideConnection is for the TestUser model
    GrpcClientSideConnection.establish_connection(
      adapter: 'sqlite3',
      database: File.join(db_dir, 'test_client.sqlite3')
    )

    # GrpcServerSideConnection is for the TestEmployee model
    GrpcServerSideConnection.establish_connection(
      adapter: 'sqlite3',
      database: File.join(db_dir, 'test_server.sqlite3')
    )
  end

  # Create and seed the database tables
  def self.create_and_seed_tables
    # Create the test_users table in its own database
    TestUser.connection.create_table :test_users, force: true do |t|
      t.string :name
      t.string :email
      t.string :avatar_url
      t.text :roles
      t.text :permissions
      t.string :status, default: 'active'
      t.timestamps
    end

    # Create the test_employees table in its own database
    TestEmployee.connection.create_table :test_employees, force: true do |t|
      t.integer :user_id
      t.string :department
      t.string :status
      t.timestamps
    end

    # Seed the test data
    TestUser.seed
    # Don't seed TestEmployee here to avoid issues with the gRPC client
  end

  # Reset the database tables
  def self.reset_tables
    # Reset the test tables
    TestUser.delete_all
    TestEmployee.delete_all

    # Seed the test data
    TestUser.seed
    # Don't seed TestEmployee here to avoid issues with the gRPC client
  end

end
