require "athar_rpc_services/core/users_services_pb"

module Core
  class UsersController < Gruf::Controllers::Base
    bind ::Core::Users::Service

    def get_user
      request_payload = request.message
      # Implement business logic here
      # Example response:
      ::Core::UserResponse.new(id: request_payload.id)
    rescue StandardError => e
      fail!(:internal, :unexpected_error, "Error: #{e.message}")
    end

    def list_users
      request_payload = request.message
      # Implement business logic here
      # Example response:
      ::Core::UserListResponse.new(items: [])
    rescue StandardError => e
      fail!(:internal, :unexpected_error, "Error: #{e.message}")
    end

    def update_user
      request_payload = request.message

      # Extract data from the request
      user_id = request_payload.id

      begin
        # Find the user (implementation depends on your application)
        user = User.find(user_id)

        # Update basic attributes if provided
        user.name = request_payload.name if request_payload.name.present?
        user.email = request_payload.email if request_payload.email.present?
        user.status = request_payload.status if request_payload.status.present?

        # Handle password update if provided
        if request_payload.password.present?
          # Your password update logic here
          user.password = request_payload.password
        end

        # Handle avatar image upload if provided
        if request_payload.avatar_image.present?
          # Create a tempfile with the image data
          filename = request_payload.avatar_filename.presence || 'avatar.jpg'
          content_type = request_payload.avatar_content_type.presence || detect_content_type(filename)

          # Directly attach the image data to Active Storage
          user.avatar.attach(
            io: StringIO.new(request_payload.avatar_image),
            filename: filename,
            content_type: content_type
          )
        end

        # Save the user
        if user.save
          # Use the existing to_rpc_response method from the User model if it exists
          if user.respond_to?(:to_rpc_response)
            user.to_rpc_response
          else
            # Fallback if to_rpc_response doesn't exist
            avatar_url = user.avatar.attached? ?
                         Rails.application.routes.url_helpers.rails_blob_url(user.avatar) :
                         nil

            ::Core::UserResponse.new(
              id: user.id.to_s,
              name: user.name,
              email: user.email,
              avatar_url: avatar_url,
              status: user.status,
              roles_list: user.respond_to?(:roles) ? user.roles.map(&:name) : [],
              permissions_list: user.respond_to?(:permissions) ? user.permissions.map(&:to_s) : []
            )
          end
        else
          # Return validation errors
          fail!(:invalid_argument, :validation_error, "Failed to save user: #{user.errors.full_messages.join(', ')}")
        end
      rescue ActiveRecord::RecordNotFound
        fail!(:not_found, :user_not_found, "User with ID #{user_id} not found")
      rescue StandardError => e
        fail!(:internal, :unexpected_error, "Error updating user: #{e.message}")
      ensure
        # No tempfiles to clean up with StringIO approach
      end
    end

    def create_user
      request_payload = request.message

      begin
        # Create a new user with the provided attributes
        user = User.new(
          name: request_payload.name,
          email: request_payload.email,
          status: request_payload.status.presence || 'active',
          password: request_payload.password
        )

        # Handle avatar image upload if provided
        if request_payload.avatar_image.present?
          # Create a tempfile with the image data
          filename = request_payload.avatar_filename.presence || 'avatar.jpg'
          content_type = request_payload.avatar_content_type.presence || detect_content_type(filename)

          # Directly attach the image data to Active Storage
          user.avatar.attach(
            io: StringIO.new(request_payload.avatar_image),
            filename: filename,
            content_type: content_type
          )
        # Handle legacy avatar URL if provided
        elsif request_payload.avatar.present?
          # Attach from URL if it's a URL
          begin
            user.avatar.attach(io: URI.open(request_payload.avatar),
                              filename: File.basename(URI.parse(request_payload.avatar).path))
          rescue
            # If it's not a valid URL, just store it as is
            # This assumes there's an avatar field in the database
            user.avatar_url = request_payload.avatar if user.respond_to?(:avatar_url=)
          end
        end

        # Save the user
        if user.save
          # Use the existing to_rpc_response method from the User model if it exists
          if user.respond_to?(:to_rpc_response)
            user.to_rpc_response
          else
            # Fallback if to_rpc_response doesn't exist
            avatar_url = user.avatar.attached? ?
                         Rails.application.routes.url_helpers.rails_blob_url(user.avatar) :
                         nil

            ::Core::UserResponse.new(
              id: user.id.to_s,
              name: user.name,
              email: user.email,
              avatar_url: avatar_url,
              status: user.status,
              roles_list: user.respond_to?(:roles) ? user.roles.map(&:name) : [],
              permissions_list: user.respond_to?(:permissions) ? user.permissions.map(&:to_s) : []
            )
          end
        else
          # Return validation errors
          fail!(:invalid_argument, :validation_error, "Failed to create user: #{user.errors.full_messages.join(', ')}")
        end
      rescue StandardError => e
        fail!(:internal, :unexpected_error, "Error creating user: #{e.message}")
      ensure
        # No tempfiles to clean up with StringIO approach
      end
    end

      private

    # Helper method to detect content type from filename
    def detect_content_type(filename)
      extension = File.extname(filename).downcase
      case extension
      when '.jpg', '.jpeg'
        'image/jpeg'
      when '.png'
        'image/png'
      when '.gif'
        'image/gif'
      when '.webp'
        'image/webp'
      else
        'application/octet-stream'
      end
    end
  end
end
