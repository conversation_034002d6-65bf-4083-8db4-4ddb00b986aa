# frozen_string_literal: true

module AtharRpc
  # Configuration class for AtharRpc
  class Configuration
    attr_accessor :local_mode, :local_mode_data

    def initialize
      @local_mode = nil
      @local_mode_data = nil
    end
  end

  class << self
    # Configuration accessor
    def configuration
      @configuration ||= Configuration.new
    end

    # Configuration block
    def configure
      yield(configuration)
    end

    # Local mode runtime control
    def enable_local_mode!
      @local_mode_override = true
    end

    def disable_local_mode!
      @local_mode_override = false
    end

    def local_mode?
      return @local_mode_override unless @local_mode_override.nil?

      configuration.local_mode || false
    end

    # Temporary local mode execution
    def with_local_mode
      original_override = @local_mode_override
      @local_mode_override = true
      begin
        yield
      ensure
        @local_mode_override = original_override
      end
    end
  end
end
