module AtharRpc
  module Grpc
    # Configuration class for gRPC concerns
    class Configuration
      attr_accessor :max_per_page, :default_per_page, :enable_full_text_search
      
      def initialize
        @max_per_page = 100
        @default_per_page = 25
        @enable_full_text_search = true
      end
    end
    
    # Global configuration
    def self.configuration
      @configuration ||= Configuration.new
    end
    
    def self.configure
      yield(configuration)
    end
  end
end
