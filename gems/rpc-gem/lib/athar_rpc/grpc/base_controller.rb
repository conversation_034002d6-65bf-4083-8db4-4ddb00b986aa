# frozen_string_literal: true

module AtharRpc
  module Grpc
    # Base controller class that includes all our concerns
    class BaseController < ::Gruf::Controllers::Base
      include AtharRpc::Grpc::Concerns::RequestProcessor
      include AtharRpc::Grpc::Concerns::QueryBuilder
      include AtharRpc::Grpc::Concerns::ResourceController
      include AtharRpc::Grpc::Concerns::Serializable

      # Add common methods for all gRPC controllers here

      private

      # Provide a params method that <PERSON><PERSON> can use
      # Converts the protobuf request into a Rails-style params hash
      def params
        @params ||= build_params_from_request
      end

      def build_params_from_request
        return {} unless request&.message

        request_message = request.message
        params_hash = {}

        # Extract all supported parameters
        extract_pagination_params(request_message, params_hash)
        extract_query_params(request_message, params_hash)
        extract_other_params(request_message, params_hash)

        params_hash
      end

      def extract_pagination_params(request_message, params_hash)
        params_hash[:page] = request_message.page if request_message.respond_to?(:page)
        params_hash[:per_page] = request_message.per_page if request_message.respond_to?(:per_page)
      end

      def extract_query_params(request_message, params_hash)
        if request_message.respond_to?(:q) && request_message.q
          params_hash[:q] = convert_protobuf_value(request_message.q)
        end

        return unless request_message.respond_to?(:scope) && request_message.scope

        params_hash[:scope] = convert_protobuf_value(request_message.scope)
      end

      def extract_other_params(request_message, params_hash)
        params_hash[:sort] = request_message.sort if request_message.respond_to?(:sort)
        params_hash[:include] = request_message.include if request_message.respond_to?(:include)
        params_hash[:ids] = request_message.ids if request_message.respond_to?(:ids) && request_message.ids
        params_hash[:where] = request_message.where.to_h if request_message.respond_to?(:where) && request_message.where
      end

      def convert_protobuf_value(value)
        value.respond_to?(:to_h) ? value.to_h : value
      end
    end
  end
end
