module AtharRpc
  module Grpc
    module Testing
      # Helper methods for testing gRPC controllers that use our concerns
      module ControllerHelpers
        # Create a mock request message with the given attributes
        def mock_request(attributes = {})
          OpenStruct.new(attributes)
        end
        
        # Create a mock collection that responds to ActiveRecord methods
        def mock_collection(items = [])
          collection = items.dup
          
          # Add methods to make it behave like an ActiveRecord::Relation
          def collection.model
            items.first.class
          end
          
          def collection.ransack(params)
            self
          end
          
          def collection.result
            self
          end
          
          def collection.page(page)
            self
          end
          
          def collection.per(per_page)
            self
          end
          
          def collection.except(*)
            self
          end
          
          def collection.count
            size
          end
          
          def collection.includes(*)
            self
          end
          
          def collection.order(*)
            self
          end
          
          def collection.where(*)
            self
          end
          
          collection
        end
      end
    end
  end
end
