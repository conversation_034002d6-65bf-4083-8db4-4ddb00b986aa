module AtharRpc
  module Grpc
    module Concerns
      # The Scopable concern provides methods for applying named scopes
      # to ActiveRecord queries in gRPC controllers.
      #
      # @example
      #   def list_users
      #     process_request do
      #       base_query = User.all
      #       query = apply_scopes(base_query, request.message)
      #       # ...
      #     end
      #   end
      module Scopable
        extend ActiveSupport::Concern

        # Apply scopes to a query
        def apply_scopes(query, params)
          return query unless params.respond_to?(:scope) && params.scope.present?

          # Handle both new JSON format and old individual parameters for backward compatibility
          scope_hash = params.scope.to_h
          scope_params = if scope_hash.key?('args')
                           # New format: JSON-serialized scope arguments
                           begin
                             JSON.parse(scope_hash['args'])
                           rescue JSON::ParserError => e
                             Rails.logger.error("[Scopable] Failed to parse scope args: #{e.message}")
                             {}
                           end
                         else
                           # Old format: individual string parameters (backward compatibility)
                           scope_hash
                         end

          # Get available scopes
          available_scopes = get_available_scopes(query.model)

          # Apply each scope
          scope_params.each do |scope_name, scope_value|
            # Skip if the scope is not available and we have defined scopes
            scope_available = available_scopes.empty? ||
                             available_scopes.key?(scope_name.to_sym) ||
                             available_scopes.key?(scope_name.to_s)

            unless scope_available
              Rails.logger.debug("[Scopable] Skipping scope #{scope_name} - not in available_scopes")
              next
            end

            unless query.respond_to?(scope_name)
              Rails.logger.debug("[Scopable] Skipping scope #{scope_name} - query doesn't respond to it")
              next
            end

            begin
              # Handle regular scopes
              method = query.method(scope_name)
              if method.arity.abs <= 1
                Rails.logger.debug("[Scopable] Applying scope: #{scope_name}(#{scope_value.class})")
                query = scope_value.present? ? query.public_send(scope_name, scope_value) : query.public_send(scope_name)
              else
                Rails.logger.debug("[Scopable] Skipping scope #{scope_name} - arity too high: #{method.arity}")
              end
            rescue => e
              Rails.logger.error("[Scopable] Error applying scope '#{scope_name}': #{e.message}")
              # Continue with other scopes even if one fails
            end
          end

          query
        end

        private

        # Get available scopes for a model
        def get_available_scopes(model_class)
          if model_class.respond_to?(:active_rpc_config) && model_class.active_rpc_config
            resource = model_class.active_rpc_config[:resource].to_s.underscore
            scopes_method = "#{resource}_scopes"

            return model_class.send(scopes_method) if model_class.respond_to?(scopes_method)
          end

          # Default empty scopes if none are defined
          {}
        end
      end
    end
  end
end
