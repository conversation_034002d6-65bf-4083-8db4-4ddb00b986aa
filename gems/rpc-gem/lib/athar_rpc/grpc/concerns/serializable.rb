module AtharRpc
  module Grpc
    module Concerns
      # The Serializable concern provides methods for transforming ActiveRecord models
      # into gRPC response objects.
      #
      # @example
      #   def get_user
      #     process_request do
      #       user = find_record(User, request.message.id)
      #       serialize_record(user, serializer: UserSerializer)
      #     end
      #   end
      module Serializable
        extend ActiveSupport::Concern
        
        # Transform a record using a serializer
        def serialize_record(record, options = {})
          serializer_class = options[:serializer] || "#{record.class.name}Serializer".constantize
          serializer_class.new(record).to_h
        end
        
        # Transform a collection using a serializer
        def serialize_collection(collection, options = {})
          collection.map { |record| serialize_record(record, options) }
        end
      end
    end
  end
end
