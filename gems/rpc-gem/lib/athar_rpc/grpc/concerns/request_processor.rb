module AtharRpc
  module Grpc
    module Concerns
      # The RequestProcessor concern provides methods for processing gRPC requests
      # with standardized error handling and parameter validation.
      #
      # @example
      #   def get_user
      #     process_request do
      #       validate_required_params!(request.message, :id)
      #       user = find_record(User, request.message.id)
      #       user.to_rpc_response
      #     end
      #   end
      module RequestProcessor
        extend ActiveSupport::Concern

        # Process a request with a block, handling common errors
        def process_request
          ActiveRecord::Base.transaction do
            yield
          end
        rescue ActiveRecord::RecordNotFound => e
          fail!(:not_found, :record_not_found, e.message)
        rescue ActiveRecord::RecordInvalid => e
          # For validation errors, we now return structured responses instead of failing
          # This should be handled by the individual controller methods
          raise e
        rescue ArgumentError => e
          fail!(:invalid_argument, :invalid_request, e.message)
        rescue UncaughtThrowError => e
          # Handle throw(:abort) from callbacks
          if e.tag == :abort
            fail!(:aborted, :operation_aborted, "Operation aborted")
          else
            # Re-raise if it's not an :abort throw
            raise e
          end
        rescue StandardError => e
          Rails.logger.error("Error processing request: #{e.message}\n#{e.backtrace.join("\n")}")
          fail!(:internal, :unexpected_error, "An unexpected error occurred")
        end

        # Extract parameters from a request payload
        def extract_params(payload, *param_names)
          # Step 1: Determine which fields to extract and get _fields metadata
          if payload.respond_to?(:_fields) && !payload._fields.empty?
            # Use _fields metadata (only top-level fields)
            fields_to_extract = payload._fields
              .reject { |path| path.include?('.') || path.include?('[') }
            # Pass the full _fields metadata for nested conversion
            fields_metadata = payload._fields
          elsif param_names.empty?
            # Extract all available fields from protobuf descriptor
            if payload.class.respond_to?(:descriptor)
              fields_to_extract = payload.class.descriptor.map(&:name)
            else
              fields_to_extract = []
            end
            fields_metadata = nil
          else
            # Extract only requested fields
            fields_to_extract = param_names.map(&:to_s)
            fields_metadata = nil
          end

          # Step 2: Extract the fields
          result = {}
          fields_to_extract.each do |field_name|
            field_sym = field_name.to_sym
            if payload.respond_to?(field_sym)
              field_value = payload.send(field_sym)
              # Pass the field path and metadata for nested conversion
              field_path = field_name
              result[field_sym] = convert_protobuf_to_ruby(field_value, field_path, fields_metadata)
            end
          end

          result
        end

        private

        # Convert protobuf objects to Ruby hashes/arrays recursively
        # WITHOUT using to_h to preserve empty arrays and explicit default values
        # Respects _fields metadata to only extract fields that were originally sent
        def convert_protobuf_to_ruby(value, current_path = '', fields_metadata = nil)
          case value
          when Google::Protobuf::MessageExts
            # Convert protobuf message to hash by extracting only tracked fields
            result = {}

            if fields_metadata
              # Only extract fields that are in the _fields metadata
              value.class.descriptor.each do |field|
                field_name = field.name
                field_path = current_path.empty? ? field_name : "#{current_path}.#{field_name}"

                # Check if this field path exists in _fields metadata
                # Match exact path or any child path (but not parent paths)
                if fields_metadata.any? { |path| path == field_path || path.start_with?("#{field_path}.") || path.start_with?("#{field_path}[") }
                  field_value = value.send(field_name)
                  result[field_name] = convert_protobuf_to_ruby(field_value, field_path, fields_metadata)
                end
              end
            else
              # Fallback: extract all fields when no metadata available
              value.class.descriptor.each do |field|
                field_name = field.name
                field_value = value.send(field_name)
                field_path = current_path.empty? ? field_name : "#{current_path}.#{field_name}"
                result[field_name] = convert_protobuf_to_ruby(field_value, field_path, fields_metadata)
              end
            end

            result
          when Google::Protobuf::RepeatedField
            # Convert repeated field to array and recursively convert elements
            value.map.with_index do |item, index|
              item_path = "#{current_path}[#{index}]"
              convert_protobuf_to_ruby(item, item_path, fields_metadata)
            end
          when Array
            # Convert array elements recursively
            value.map.with_index do |item, index|
              item_path = "#{current_path}[#{index}]"
              convert_protobuf_to_ruby(item, item_path, fields_metadata)
            end
          when Hash
            # Convert hash values recursively
            value.transform_values { |v| convert_protobuf_to_ruby(v, current_path, fields_metadata) }
          else
            # Return primitive values as-is
            value
          end
        end

        # Validate required parameters
        def validate_required_params!(payload, *param_names)
          missing = param_names.select { |name| !payload.respond_to?(name) || payload.send(name).nil? }
          if missing.any?
            raise ArgumentError, "Missing required parameters: #{missing.join(', ')}"
          end
        end

        # Find a record or fail with not found
        def find_record(model_class, id)
          record = model_class.find_by(id: id)
          unless record
            fail!(:not_found, :record_not_found, "#{model_class.name} with ID #{id} not found")
          end
          record
        end

        # Create structured response metadata for validation errors
        def create_validation_error_metadata(record)
          # Share the Rails errors object directly - no conversion needed!
          errors_map = {}
          record.errors.to_hash.each do |field, messages|
            errors_map[field.to_s] = Core::ErrorMessages.new(messages: messages)
          end

          Core::ResponseMetadata.new(
            success: false,
            errors: errors_map
          )
        end

        # Create successful response metadata
        def create_success_metadata
          Core::ResponseMetadata.new(success: true)
        end
      end
    end
  end
end
