module AtharRpc
  module Grpc
    module Concerns
      # The Paginatable concern provides methods for applying pagination
      # to ActiveRecord queries in gRPC controllers.
      #
      # It supports:
      # - Standard pagination with page and per_page parameters
      # - Integration with the Pagy gem if available
      # - Pagination metadata for responses
      #
      # @example
      #   def list_users
      #     process_request do
      #       base_query = User.all
      #       query = apply_pagination(base_query, request.message)
      #       # ...
      #     end
      #   end
      module Paginatable
        extend ActiveSupport::Concern

        included do
          # Include Pagy if available
          include Pagy::Backend if defined?(Pagy)
        end

        # Apply pagination to a query only if explicitly requested
        def apply_pagination(query, params)
          # Only apply pagination if both page and per_page are provided
          return query unless params.respond_to?(:page) && params.respond_to?(:per_page) &&
                              params.page.present? && params.per_page.present?

          begin
            # Sanitize pagination parameters
            page = [params.page.to_i, 1].max
            per_page = [[params.per_page.to_i, 1].max, AtharRpc::Grpc.configuration.max_per_page].min

            # Use Pagy if available, otherwise fall back to standard pagination
            if defined?(Pagy)
              pagy, records = pagy(query, page: page, items: per_page)
              @last_pagy = pagy # Store for metadata
              records
            else
              query.page(page).per(per_page)
            end
          rescue StandardError => e
            Rails.logger.error("Error applying pagination: #{e.message}")
            # Return original query if pagination fails
            query
          end
        end

        # Get pagination metadata
        def pagination_metadata(query, params, total_count = nil)
          # If Pagy was used, get metadata from it
          if defined?(@last_pagy) && @last_pagy
            # Try different ways to get items per page depending on Pagy version
            per_page = if @last_pagy.respond_to?(:items)
                         @last_pagy.items
                       elsif @last_pagy.respond_to?(:vars) && @last_pagy.vars[:items]
                         @last_pagy.vars[:items]
                       else
                         begin
                           @last_pagy.limit
                         rescue StandardError
                           20
                         end # fallback
                       end

            return {
              total_count: @last_pagy.count,
              page: @last_pagy.page,
              per_page: per_page,
              total_pages: @last_pagy.pages
            }
          end

          # If pagination is not requested, return metadata with all records
          unless params.respond_to?(:page) && params.respond_to?(:per_page) &&
                 params.page.present? && params.per_page.present?
            total_count = query.count
            return {
              total_count: total_count,
              page: 1,
              per_page: total_count,
              total_pages: 1
            }
          end

          # Calculate total count and pages
          total_count ||= query.except(:limit, :offset).count

          # Validate and sanitize per_page parameter
          per_page = params.per_page.to_i
          if per_page <= 0
            Rails.logger.warn("Invalid per_page value: #{params.per_page}. Using default value.")
            per_page = AtharRpc::Grpc.configuration.default_per_page || 20
          end

          # Ensure per_page doesn't exceed maximum allowed
          max_per_page = AtharRpc::Grpc.configuration.max_per_page || 100
          per_page = [per_page, max_per_page].min

          # Calculate total pages safely
          total_pages = total_count > 0 ? (total_count.to_f / per_page).ceil : 1

          {
            total_count: total_count,
            page: params.page,
            per_page: per_page,
            total_pages: total_pages
          }
        end
      end
    end
  end
end
