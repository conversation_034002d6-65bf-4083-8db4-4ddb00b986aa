module AtharRpc
  module Grpc
    module Concerns
      # The Includable concern provides methods for applying includes (eager loading)
      # to ActiveRecord queries in gRPC controllers.
      #
      # @example
      #   def list_users
      #     process_request do
      #       base_query = User.all
      #       query = apply_includes(base_query, request.message)
      #       # ...
      #     end
      #   end
      module Includable
        extend ActiveSupport::Concern

        # Apply includes to a query
        def apply_includes(query, params)
          return query unless params.respond_to?(:include) && params.include.present?

          begin
            # Convert the array to symbols
            includes = params.include.map(&:to_sym)

            # Get query configuration
            query_config = get_query_config(query.model)
            includable_assocs = query_config[:includable] || []

            # Filter out non-includable associations if we have defined includable associations
            filtered_includes = if includable_assocs.empty?
                                  includes
                                else
                                  includes.select do |assoc|
                                    includable_assocs.include?(assoc)
                                  end
                                end

            # Apply includes
            query.includes(filtered_includes)
          rescue StandardError => e
            Rails.logger.error("Error applying includes: #{e.message}")
            # Return original query if includes fails
            query
          end
        end

        # Get query configuration for a model
        def get_query_config(model_class)
          if model_class.respond_to?(:active_rpc_config) && model_class.active_rpc_config
            resource = model_class.active_rpc_config[:resource].to_s.underscore
            query_config_method = "#{resource}_query_config"

            return model_class.send(query_config_method) if model_class.respond_to?(query_config_method)
          end

          # Default configuration if none is defined
          {
            searchable: model_class.column_names,
            filterable: model_class.column_names,
            sortable: model_class.column_names,
            includable: model_class.reflect_on_all_associations.map(&:name)
          }
        end
      end
    end
  end
end
