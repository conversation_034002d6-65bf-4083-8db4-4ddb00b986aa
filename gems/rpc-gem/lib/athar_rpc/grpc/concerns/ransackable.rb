module AtharRpc
  module Grpc
    module Concerns
      # The Ransackable concern provides methods for applying Ransack-based filtering
      # to ActiveRecord queries in gRPC controllers.
      #
      # It supports:
      # - Standard Ransack predicates (e.g., q[name_cont]=<PERSON>)
      # - Full-text search via q[search]=query
      #
      # @example
      #   def list_users
      #     process_request do
      #       base_query = User.all
      #       query = apply_ransack(base_query, request.message)
      #       # ...
      #     end
      #   end
      module Ransackable
        extend ActiveSupport::Concern

        # Apply Ransack search parameters to a query
        def apply_ransack(query, params)
          return query unless params.respond_to?(:q) && params.q.present?
          
          begin
            # Convert the map to a hash
            search_params = params.q.to_h
            
            # Get query configuration
            query_config = get_query_config(query.model)
            searchable_attrs = query_config[:searchable] || []
            
            # Filter out non-searchable attributes
            filtered_params = {}
            search_params.each do |key, value|
              # Extract the attribute name from the Ransack predicate
              attr_name = key.to_s.split('_').first
              
              # Include the parameter if the attribute is searchable
              if searchable_attrs.include?(attr_name.to_sym) || searchable_attrs.include?(attr_name.to_s) || searchable_attrs.empty?
                filtered_params[key] = value
              end
            end
            
            # Check for special search parameter
            if AtharRpc::Grpc.configuration.enable_full_text_search &&
               filtered_params.key?('search') && filtered_params['search'].present?
              search_query = filtered_params.delete('search')
              query = apply_full_text_search(query, search_query)
            end
            
            # Apply standard Ransack filters
            query.ransack(filtered_params).result
          rescue => e
            Rails.logger.error("Error applying ransack: #{e.message}")
            # Return original query if ransack fails
            query
          end
        end
        
        private
        
        # Apply full-text search to a query
        def apply_full_text_search(query, search_query)
          resource_class = query.model
          
          # Try different search method patterns in order of preference
          resource_name = resource_class.name.underscore.pluralize
          
          # 1. Try model-specific search method
          specific_search_method = "search_#{resource_name}"
          if resource_class.respond_to?(specific_search_method)
            return resource_class.send(specific_search_method, search_query)
          end
          
          # 2. Try generic search method
          if resource_class.respond_to?(:search)
            return resource_class.search(search_query)
          end
          
          # 3. Try full_text_search method
          if resource_class.respond_to?(:full_text_search)
            return resource_class.full_text_search(search_query)
          end
          
          # If no search method is found, return the original query
          query
        end
        
        # Get query configuration for a model
        def get_query_config(model_class)
          if model_class.respond_to?(:active_rpc_config) && model_class.active_rpc_config
            resource = model_class.active_rpc_config[:resource].to_s.underscore
            query_config_method = "#{resource}_query_config"
            
            if model_class.respond_to?(query_config_method)
              return model_class.send(query_config_method)
            end
          end
          
          # Default configuration if none is defined
          {
            searchable: model_class.column_names,
            filterable: model_class.column_names,
            sortable: model_class.column_names,
            includable: model_class.reflect_on_all_associations.map(&:name)
          }
        end
      end
    end
  end
end
