module AtharRpc
  module Grpc
    module Concerns
      # The QueryBuilder concern combines all query-related concerns and provides
      # high-level methods for building and executing queries in gRPC controllers.
      #
      # @example
      #   def list_users
      #     process_request do
      #       execute_query(User, request.message,
      #         response_class: ::Core::UserListResponse,
      #         transformer: :to_rpc_response
      #       )
      #     end
      #   end
      module QueryBuilder
        extend ActiveSupport::Concern

        include AtharRpc::Grpc::Concerns::Ransackable
        include AtharRpc::Grpc::Concerns::Scopable
        include AtharRpc::Grpc::Concerns::Includable
        include AtharRpc::Grpc::Concerns::Paginatable
        include AtharRpc::Grpc::Concerns::Sortable

        # Build a query with all parameters
        def build_query(base_query, params)
          puts "🔥 [BUILD_QUERY] Called with params: #{params.class}"
          puts "🔥 [BUILD_QUERY] params.inspect: #{params.inspect}"
          puts "🔥 [BUILD_QUERY] params.respond_to?(:scope): #{params.respond_to?(:scope)}"
          if params.respond_to?(:scope)
            puts "🔥 [BUILD_QUERY] params.scope: #{params.scope.inspect}"
            puts "🔥 [BUILD_QUERY] params.scope.present?: #{params.scope.present?}"
          end

          query = base_query

          # Apply filters in a specific order
          puts "🔥 [BUILD_QUERY] Applying where filters..."
          query = apply_where_filters(query, params)
          puts "🔥 [BUILD_QUERY] Applying ransack..."
          query = apply_ransack(query, params)
          puts "🔥 [BUILD_QUERY] Applying scopes..."
          query = apply_scopes(query, params)
          puts "🔥 [BUILD_QUERY] Applying includes..."
          query = apply_includes(query, params)
          puts "🔥 [BUILD_QUERY] Applying sorting..."
          apply_sorting(query, params)

          # TEMPORARILY DISABLE PAGINATION TO TEST BULK LOADING
          # Apply pagination last
          # query = apply_pagination(query, params)
        end

        # Execute a query and return results with pagination metadata
        def execute_query(query_or_class, request_payload, options = {})
          # Determine the base query
          base_query = if query_or_class.is_a?(Class)
                         # If a class is provided, start with all records
                         options[:base_query] || query_or_class.all
                       else
                         # If a query is provided, use it directly
                         query_or_class
                       end

          # Filter by specific IDs if provided
          if request_payload.respond_to?(:ids) && request_payload.ids.present?
            Rails.logger.info("[DEBUG] IDs found: #{request_payload.ids.inspect} (#{request_payload.ids.class})")

            # Convert Google::Protobuf::RepeatedField to Ruby Array
            ids_array = if request_payload.ids.is_a?(Google::Protobuf::RepeatedField)
                          request_payload.ids.to_a
                        else
                          request_payload.ids
                        end

            Rails.logger.info("[DEBUG] Converted IDs: #{ids_array.inspect} (#{ids_array.class})")
            base_query = base_query.where(id: ids_array)
          else
            Rails.logger.info("[DEBUG] No IDs found. respond_to?(:ids): #{request_payload.respond_to?(:ids)}, ids.present?: #{request_payload.respond_to?(:ids) ? request_payload.ids.present? : 'N/A'}")
            Rails.logger.info("[DEBUG] Request payload: #{request_payload.inspect}")
          end

          # Apply additional filters if provided
          base_query = options[:additional_filters].call(base_query) if options[:additional_filters].is_a?(Proc)

          # Apply includes from options if provided
          # Note: includes are applied BEFORE scopes to avoid conflicts
          base_query = base_query.includes(options[:includes]) if options[:includes].present?

          # Build the query with all parameters
          query = build_query(base_query, request_payload)

          # Execute the query
          puts "🔥 [EXECUTE_QUERY] About to execute query.to_a"
          puts "🔥 [EXECUTE_QUERY] Query class: #{query.class}"
          puts "🔥 [EXECUTE_QUERY] Query SQL preview: #{query.to_sql rescue 'SQL generation failed'}"
          records = query.to_a
          puts "🔥 [EXECUTE_QUERY] Query executed, got #{records.size} records"

          # Transform records if a transformer is provided
          puts "🔥 [TRANSFORM] About to transform #{records.size} records"
          puts "🔥 [TRANSFORM] Transformer: #{options[:transformer].inspect}"
          if options[:transformer].is_a?(Proc)
            records = records.map(&options[:transformer])
          elsif options[:transformer] == :to_rpc_response
            records = records.map(&:to_rpc_response)
          end
          puts "🔥 [TRANSFORM] After transformation: #{records.size} records"

          # Return the response with pagination metadata
          response_class = options[:response_class]
          if response_class
            paginated_response(response_class, records, query, request_payload, records.size)
          else
            {
              records: records,
              metadata: pagination_metadata(query, request_payload, records.size)
            }
          end
        end

        # Get the response with pagination metadata
        def paginated_response(response_class, items, query, params, total_count = nil)
          # Get pagination metadata
          metadata = pagination_metadata(query, params, total_count)

          # Check if pagination was requested
          pagination_requested = params.respond_to?(:page) && params.respond_to?(:per_page) &&
                                 params.page.present? && params.per_page.present?

          # Create the response with items and pagination metadata
          response_class.new(
            items: items,
            total_count: metadata[:total_count],
            page: pagination_requested ? metadata[:page] : 1,
            per_page: pagination_requested ? metadata[:per_page] : metadata[:total_count],
            total_pages: pagination_requested ? metadata[:total_pages] : 1
          )
        end

        private

        # Apply ActiveRecord-style where conditions
        def apply_where_filters(query, params)
          return query unless params.respond_to?(:where) && params.where.present?

          begin
            # Convert protobuf map to hash if needed
            where_conditions = params.where.respond_to?(:to_h) ? params.where.to_h : params.where

            # Apply each condition
            where_conditions.each do |field, value|
              # Validate field is allowed (security)
              if allowed_where_field?(query.model, field)
                query = query.where(field => value)
              else
                Rails.logger.warn("Filtered out disallowed where field: #{field}")
              end
            end

            query
          rescue StandardError => e
            Rails.logger.error("Error applying where filters: #{e.message}")
            query
          end
        end

        # Check if a field is allowed for where filtering
        def allowed_where_field?(model_class, field)
          # Get allowed fields from query config or default to column names
          query_config = get_query_config(model_class)
          allowed_fields = query_config[:filterable] || model_class.column_names

          # Check if field is in allowed list
          allowed_fields.include?(field.to_s) || allowed_fields.include?(field.to_sym)
        end

        # Get query configuration for a model (from Ransackable concern)
        def get_query_config(model_class)
          if model_class.respond_to?(:active_rpc_config) && model_class.active_rpc_config
            resource = model_class.active_rpc_config[:resource].to_s.underscore
            query_config_method = "#{resource}_query_config"

            return model_class.send(query_config_method) if model_class.respond_to?(query_config_method)
          end

          # Default configuration if none is defined
          {
            searchable: model_class.column_names,
            filterable: model_class.column_names,
            sortable: model_class.column_names,
            includable: model_class.reflect_on_all_associations.map(&:name)
          }
        end
      end
    end
  end
end
