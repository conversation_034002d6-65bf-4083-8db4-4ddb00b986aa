module AtharRpc
  module Grpc
    module Concerns
      # The ResourceController concern provides high-level methods for common CRUD operations
      # in gRPC controllers.
      #
      # @example
      #   def get_user
      #     get_resource(User, request.message, transformer: :to_rpc_response)
      #   end
      #
      #   def list_users
      #     list_resources(User, request.message,
      #       response_class: ::Core::UserListResponse,
      #       transformer: :to_rpc_response
      #     )
      #   end
      module ResourceController
        extend ActiveSupport::Concern

        include AtharRpc::Grpc::Concerns::RequestProcessor
        include AtharRpc::Grpc::Concerns::QueryBuilder

        # Get a single resource
        def get_resource(model_class, request_payload, options = {})
          process_request do
            # Validate required parameters
            validate_required_params!(request_payload, :id)

            # Find the resource
            resource = find_record(model_class, request_payload.id)

            # Transform the resource
            if options[:transformer].is_a?(Proc)
              options[:transformer].call(resource)
            elsif options[:transformer] == :to_rpc_response
              resource.to_rpc_response
            else
              resource
            end
          end
        end

        # List resources
        def list_resources(model_class, request_payload, options = {})
          process_request do
            execute_query(model_class, request_payload, options)
          end
        end

        # Create a resource
        def create_resource(model_class, request_payload, options = {})
          process_request do
            # Validate required parameters
            if options[:required_params].is_a?(Array)
              validate_required_params!(request_payload, *options[:required_params])
            end

            # Extract parameters
            params = if options[:param_extractor].is_a?(Proc)
                       options[:param_extractor].call(request_payload)
                     else
                       extract_params(request_payload, *options[:param_names])
                     end

            # Create the resource
            resource = model_class.new(params.to_h.with_indifferent_access)

            # Apply before save hook if provided
            if options[:before_save].is_a?(Proc)
              hook_result = options[:before_save].call(resource, request_payload)
              # Check if the hook returned an error response
              return hook_result if hook_result.respond_to?(:success) && hook_result.success == false
            end

            # Save the resource
            if resource.save
              # Apply after save hook if provided
              if options[:after_save].is_a?(Proc)
                hook_result = options[:after_save].call(resource, request_payload)
                # Check if the hook returned an error response
                return hook_result if hook_result.respond_to?(:success) && hook_result.success == false
              end

              # Transform the resource with success metadata
              result = if options[:transformer].is_a?(Proc)
                         options[:transformer].call(resource)
                       elsif options[:transformer] == :to_rpc_response
                         resource.reload.to_rpc_response
                       else
                         resource.reload
                       end

              # Add success metadata to the response
              result._metadata = create_success_metadata if result.respond_to?(:_metadata=)

              result
            else
              # Create response with validation error metadata
              result = if options[:transformer].is_a?(Proc)
                         options[:transformer].call(resource)
                       elsif options[:transformer] == :to_rpc_response
                         resource.to_rpc_response
                       else
                         # Create a basic response object for the resource type
                         resource_class_name = resource.class.name
                         response_class = "Core::#{resource_class_name}Response".constantize
                         response_class.new
                       end

              # Add validation error metadata to the response
              result._metadata = create_validation_error_metadata(resource) if result.respond_to?(:_metadata=)

              result
            end
          end
        end

        # Update a resource
        def update_resource(model_class, request_payload, options = {})
          process_request do
            # Validate required parameters
            validate_required_params!(request_payload, :id)

            # Find the resource
            resource = find_record(model_class, request_payload.id)

            # Extract parameters
            params = if options[:param_extractor].is_a?(Proc)
                       options[:param_extractor].call(request_payload)
                     else
                       extract_params(request_payload, *options[:param_names])
                     end

            # Apply before save hook if provided
            if options[:before_save].is_a?(Proc)
              hook_result = options[:before_save].call(resource, request_payload)
              # Check if the hook returned an error response
              return hook_result if hook_result.respond_to?(:success) && hook_result.success == false
            end

            # Update the resource
            resource.assign_attributes(params.with_indifferent_access)
            if resource.save
              # Apply after save hook if provided
              if options[:after_save].is_a?(Proc)
                hook_result = options[:after_save].call(resource, request_payload)
                # Check if the hook returned an error response
                return hook_result if hook_result.respond_to?(:success) && hook_result.success == false
              end

              # Transform the resource with success metadata
              result = if options[:transformer].is_a?(Proc)
                         options[:transformer].call(resource)
                       elsif options[:transformer] == :to_rpc_response
                         resource.reload.to_rpc_response
                       else
                         resource.reload
                       end

              # Add success metadata to the response
              result._metadata = create_success_metadata if result.respond_to?(:_metadata=)

              result
            else
              # Create response with validation error metadata
              result = if options[:transformer].is_a?(Proc)
                         options[:transformer].call(resource)
                       elsif options[:transformer] == :to_rpc_response
                         resource.to_rpc_response
                       else
                         # Create a basic response object for the resource type
                         resource_class_name = resource.class.name
                         response_class = "Core::#{resource_class_name}Response".constantize
                         response_class.new
                       end

              # Add validation error metadata to the response
              result._metadata = create_validation_error_metadata(resource) if result.respond_to?(:_metadata=)

              result
            end
          end
        end

        # Delete a resource
        def delete_resource(model_class, request_payload, options = {})
          process_request do
            # Validate required parameters
            validate_required_params!(request_payload, :id)

            # Find the resource
            resource = find_record(model_class, request_payload.id)

            # Apply before destroy hook if provided
            options[:before_destroy].call(resource, request_payload) if options[:before_destroy].is_a?(Proc)

            # Destroy the resource
            resource.destroy!

            # Apply after destroy hook if provided
            options[:after_destroy].call(resource, request_payload) if options[:after_destroy].is_a?(Proc)

            # Return success response
            options[:success_response] || { success: true }
          end
        end
      end
    end
  end
end
