# frozen_string_literal: true

require 'rails/railtie'

module AtharRpc
  class Railtie < Rails::Railtie
    # Universal AtharRpc configuration namespace
    # Available to both client and server services
    config.athar_rpc = ActiveSupport::OrderedOptions.new

    # Set safe defaults that work for both client and server
    config.athar_rpc.local_mode = nil
    config.athar_rpc.local_mode_data = nil

    # Bridge Rails configuration to AtharRpc module configuration
    initializer 'athar_rpc.configure' do |app|
      AtharRpc.configure do |config|
        config.local_mode = app.config.athar_rpc.local_mode if config.local_mode.nil?
        config.local_mode_data = app.config.athar_rpc.local_mode_data if config.local_mode_data.nil?
      end
    end

    # Note: Specific configuration is handled by individual service initializers
    # - Client services use client_setup template for local mode configuration
    # - Server services use server_setup template for Gruf configuration
  end
end
