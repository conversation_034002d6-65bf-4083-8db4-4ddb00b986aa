# Generated by the protocol buffer compiler.  DO NOT EDIT!
# Source: core/approval_workflows.proto for package 'core'

require 'grpc'
require 'core/approval_workflows_pb'

module Core
  module ApprovalWorkflows
    class Service

      include ::GRPC::GenericService

      self.marshal_class_method = :encode
      self.unmarshal_class_method = :decode
      self.service_name = 'core.ApprovalWorkflows'

      rpc :GetWorkflow, ::Core::GetWorkflowRequest, ::Core::ApprovalWorkflowResponse
      rpc :GenerateSequence, ::Core::GenerateSequenceRequest, ::Core::ApprovalSequenceResponse
      rpc :ValidateApprover, ::Core::ValidateApproverRequest, ::Core::ValidateApproverResponse
      rpc :ListWorkflows, ::Core::ListWorkflowsRequest, ::Core::ApprovalWorkflowListResponse
    end

    Stub = Service.rpc_stub_class
  end
end
