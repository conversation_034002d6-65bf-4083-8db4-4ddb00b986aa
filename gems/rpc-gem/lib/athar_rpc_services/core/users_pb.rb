# frozen_string_literal: true
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: core/users.proto

require 'google/protobuf'


descriptor_data = "\n\x10\x63ore/users.proto\x12\x04\x63ore\"\x19\n\x0bUserRequest\x12\n\n\x02id\x18\x01 \x01(\x03\"c\n\x18\x41vatarAttributesResponse\x12\x0b\n\x03url\x18\x01 \x01(\t\x12\x10\n\x08\x66ilename\x18\x02 \x01(\t\x12\x14\n\x0c\x63ontent_type\x18\x03 \x01(\t\x12\x12\n\nbytes_size\x18\x04 \x01(\x03\"\xde\x01\n\x0cUserResponse\x12\n\n\x02id\x18\x01 \x01(\x03\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\r\n\x05\x65mail\x18\x03 \x01(\t\x12\x39\n\x11\x61vatar_attributes\x18\x04 \x01(\x0b\x32\x1e.core.AvatarAttributesResponse\x12/\n\x0fuser_roles_list\x18\x05 \x03(\x0b\x32\x16.core.UserRoleResponse\x12\x0e\n\x06status\x18\x06 \x01(\t\x12)\n\t_metadata\x18\x63 \x01(\x0b\x32\x16.core.ResponseMetadata\"|\n\x10UserRoleResponse\x12\n\n\x02id\x18\x01 \x01(\x03\x12 \n\x04role\x18\x02 \x01(\x0b\x32\x12.core.RoleResponse\x12&\n\x07project\x18\x03 \x01(\x0b\x32\x15.core.ProjectResponse\x12\x12\n\nis_default\x18\x04 \x01(\x08\"G\n\x0cRoleResponse\x12\n\n\x02id\x18\x01 \x01(\x03\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x0e\n\x06global\x18\x03 \x01(\x08\x12\r\n\x05level\x18\x04 \x01(\x05\"P\n\x0fProjectResponse\x12\n\n\x02id\x18\x01 \x01(\x03\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12\x0e\n\x06status\x18\x04 \x01(\t\"\xff\x02\n\x0fUserListRequest\x12\x0b\n\x03ids\x18\x01 \x03(\x03\x12\'\n\x01q\x18\x02 \x03(\x0b\x32\x1c.core.UserListRequest.QEntry\x12\x0f\n\x07include\x18\x03 \x03(\t\x12\x0c\n\x04page\x18\x04 \x01(\x05\x12\x10\n\x08per_page\x18\x05 \x01(\x05\x12\x0c\n\x04sort\x18\x06 \x01(\t\x12/\n\x05scope\x18\x07 \x03(\x0b\x32 .core.UserListRequest.ScopeEntry\x12/\n\x05where\x18\x08 \x03(\x0b\x32 .core.UserListRequest.WhereEntry\x12\x0f\n\x07_fields\x18\x63 \x03(\t\x1a(\n\x06QEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a,\n\nScopeEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a,\n\nWhereEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\x7f\n\x10UserListResponse\x12!\n\x05items\x18\x01 \x03(\x0b\x32\x12.core.UserResponse\x12\x13\n\x0btotal_count\x18\x02 \x01(\x05\x12\x0c\n\x04page\x18\x03 \x01(\x05\x12\x10\n\x08per_page\x18\x04 \x01(\x05\x12\x13\n\x0btotal_pages\x18\x05 \x01(\x05\"d\n\x17\x41vatarAttributesRequest\x12\r\n\x05image\x18\x01 \x01(\x0c\x12\x10\n\x08\x66ilename\x18\x02 \x01(\t\x12\x14\n\x0c\x63ontent_type\x18\x03 \x01(\t\x12\x12\n\nbytes_size\x18\x04 \x01(\x03\"\xaf\x01\n\x0fUserRoleRequest\x12\n\n\x02id\x18\x01 \x01(\x03\x12\x0f\n\x07role_id\x18\x02 \x01(\x03\x12\x12\n\nproject_id\x18\x03 \x01(\x03\x12\x12\n\nis_default\x18\x04 \x01(\x08\x12\r\n\x05level\x18\x05 \x01(\x05\x12 \n\x04role\x18\x06 \x01(\x0b\x32\x12.core.RoleResponse\x12&\n\x07project\x18\x07 \x01(\x0b\x32\x15.core.ProjectResponse\"\xb3\x02\n\x11UpdateUserRequest\x12\n\n\x02id\x18\x01 \x01(\x03\x12\x11\n\x04name\x18\x02 \x01(\tH\x00\x88\x01\x01\x12\x12\n\x05\x65mail\x18\x03 \x01(\tH\x01\x88\x01\x01\x12\x13\n\x06status\x18\x04 \x01(\tH\x02\x88\x01\x01\x12\x15\n\x08password\x18\x05 \x01(\tH\x03\x88\x01\x01\x12=\n\x11\x61vatar_attributes\x18\x06 \x01(\x0b\x32\x1d.core.AvatarAttributesRequestH\x04\x88\x01\x01\x12.\n\x0fuser_roles_list\x18\x07 \x03(\x0b\x32\x15.core.UserRoleRequest\x12\x0f\n\x07_fields\x18\x63 \x03(\tB\x07\n\x05_nameB\x08\n\x06_emailB\t\n\x07_statusB\x0b\n\t_passwordB\x14\n\x12_avatar_attributes\"\xcd\x01\n\x11\x43reateUserRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\r\n\x05\x65mail\x18\x02 \x01(\t\x12\x0e\n\x06status\x18\x03 \x01(\t\x12\x10\n\x08password\x18\x04 \x01(\t\x12\x38\n\x11\x61vatar_attributes\x18\x05 \x01(\x0b\x32\x1d.core.AvatarAttributesRequest\x12.\n\x0fuser_roles_list\x18\x06 \x03(\x0b\x32\x15.core.UserRoleRequest\x12\x0f\n\x07_fields\x18\x63 \x03(\t\"\x9b\x01\n\x10ResponseMetadata\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x32\n\x06\x65rrors\x18\x02 \x03(\x0b\x32\".core.ResponseMetadata.ErrorsEntry\x1a\x42\n\x0b\x45rrorsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\"\n\x05value\x18\x02 \x01(\x0b\x32\x13.core.ErrorMessages:\x02\x38\x01\"!\n\rErrorMessages\x12\x10\n\x08messages\x18\x01 \x03(\t2\xeb\x01\n\x05Users\x12\x30\n\x07GetUser\x12\x11.core.UserRequest\x1a\x12.core.UserResponse\x12:\n\tListUsers\x12\x15.core.UserListRequest\x1a\x16.core.UserListResponse\x12\x39\n\nUpdateUser\x12\x17.core.UpdateUserRequest\x1a\x12.core.UserResponse\x12\x39\n\nCreateUser\x12\x17.core.CreateUserRequest\x1a\x12.core.UserResponseB\x07\xea\x02\x04\x43oreb\x06proto3"

pool = Google::Protobuf::DescriptorPool.generated_pool
pool.add_serialized_file(descriptor_data)

module Core
  UserRequest = ::Google::Protobuf::DescriptorPool.generated_pool.lookup("core.UserRequest").msgclass
  AvatarAttributesResponse = ::Google::Protobuf::DescriptorPool.generated_pool.lookup("core.AvatarAttributesResponse").msgclass
  UserResponse = ::Google::Protobuf::DescriptorPool.generated_pool.lookup("core.UserResponse").msgclass
  UserRoleResponse = ::Google::Protobuf::DescriptorPool.generated_pool.lookup("core.UserRoleResponse").msgclass
  RoleResponse = ::Google::Protobuf::DescriptorPool.generated_pool.lookup("core.RoleResponse").msgclass
  ProjectResponse = ::Google::Protobuf::DescriptorPool.generated_pool.lookup("core.ProjectResponse").msgclass
  UserListRequest = ::Google::Protobuf::DescriptorPool.generated_pool.lookup("core.UserListRequest").msgclass
  UserListResponse = ::Google::Protobuf::DescriptorPool.generated_pool.lookup("core.UserListResponse").msgclass
  AvatarAttributesRequest = ::Google::Protobuf::DescriptorPool.generated_pool.lookup("core.AvatarAttributesRequest").msgclass
  UserRoleRequest = ::Google::Protobuf::DescriptorPool.generated_pool.lookup("core.UserRoleRequest").msgclass
  UpdateUserRequest = ::Google::Protobuf::DescriptorPool.generated_pool.lookup("core.UpdateUserRequest").msgclass
  CreateUserRequest = ::Google::Protobuf::DescriptorPool.generated_pool.lookup("core.CreateUserRequest").msgclass
  ResponseMetadata = ::Google::Protobuf::DescriptorPool.generated_pool.lookup("core.ResponseMetadata").msgclass
  ErrorMessages = ::Google::Protobuf::DescriptorPool.generated_pool.lookup("core.ErrorMessages").msgclass
end
