# Generated by the protocol buffer compiler.  DO NOT EDIT!
# Source: core/users.proto for package 'Core'

require 'grpc'
require 'core/users_pb'

module Core
  module Users
    class Service

      include ::GRPC::GenericService

      self.marshal_class_method = :encode
      self.unmarshal_class_method = :decode
      self.service_name = 'core.Users'

      rpc :GetUser, ::Core::UserRequest, ::Core::UserResponse
      rpc :ListUsers, ::Core::UserListRequest, ::Core::UserListResponse
      rpc :UpdateUser, ::Core::UpdateUserRequest, ::Core::UserResponse
      rpc :CreateUser, ::Core::CreateUserRequest, ::Core::UserResponse
    end

    Stub = Service.rpc_stub_class
  end
end
