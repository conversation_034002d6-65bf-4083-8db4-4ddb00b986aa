# frozen_string_literal: true
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: core/approval_workflows.proto

require 'google/protobuf'


descriptor_data = "\n\x1d\x63ore/approval_workflows.proto\x12\x04\x63ore\"a\n\x12GetWorkflowRequest\x12\x0e\n\x06\x61\x63tion\x18\x01 \x01(\t\x12\x15\n\rsubject_class\x18\x02 \x01(\t\x12\x13\n\x0bsystem_name\x18\x03 \x01(\t\x12\x0f\n\x07_fields\x18\x63 \x03(\t\"\xfd\x01\n\x17GenerateSequenceRequest\x12\x0e\n\x06\x61\x63tion\x18\x01 \x01(\t\x12\x15\n\rsubject_class\x18\x02 \x01(\t\x12\x13\n\x0bsystem_name\x18\x03 \x01(\t\x12\x14\n\x0crequestor_id\x18\x04 \x01(\t\x12\x12\n\nproject_id\x18\x05 \x01(\t\x12;\n\x07\x63ontext\x18\x06 \x03(\x0b\x32*.core.GenerateSequenceRequest.ContextEntry\x12\x0f\n\x07_fields\x18\x63 \x03(\t\x1a.\n\x0c\x43ontextEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"Q\n\x17ValidateApproverRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x14\n\x0c\x61pprover_ids\x18\x02 \x03(\t\x12\x0f\n\x07_fields\x18\x63 \x03(\t\"\xc4\x02\n\x14ListWorkflowsRequest\x12\x13\n\x0bsystem_name\x18\x01 \x01(\t\x12\x0b\n\x03ids\x18\x02 \x03(\t\x12,\n\x01q\x18\x03 \x03(\x0b\x32!.core.ListWorkflowsRequest.QEntry\x12\x0f\n\x07include\x18\x04 \x03(\t\x12\x0c\n\x04page\x18\x05 \x01(\x05\x12\x10\n\x08per_page\x18\x06 \x01(\x05\x12\x0c\n\x04sort\x18\x07 \x01(\t\x12\x34\n\x05scope\x18\x08 \x03(\x0b\x32%.core.ListWorkflowsRequest.ScopeEntry\x12\x0f\n\x07_fields\x18\x63 \x03(\t\x1a(\n\x06QEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a,\n\nScopeEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\xa4\x01\n\x14\x41pprovalStepResponse\x12\n\n\x02id\x18\x01 \x01(\t\x12\x13\n\x0bworkflow_id\x18\x02 \x01(\t\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x10\n\x08sequence\x18\x04 \x01(\x05\x12\x15\n\rapproval_type\x18\x05 \x01(\t\x12\x1f\n\x17\x64ynamic_approver_method\x18\x06 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x07 \x01(\t\"\xc0\x01\n\x18\x41pprovalWorkflowResponse\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x0e\n\x06\x61\x63tion\x18\x03 \x01(\t\x12\x15\n\rsubject_class\x18\x04 \x01(\t\x12\x13\n\x0bsystem_name\x18\x05 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x06 \x01(\t\x12\x0e\n\x06\x61\x63tive\x18\x07 \x01(\x08\x12)\n\x05steps\x18\x08 \x03(\x0b\x32\x1a.core.ApprovalStepResponse\"\x93\x01\n\x14\x41pprovalStepSequence\x12\x0f\n\x07step_id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x10\n\x08sequence\x18\x03 \x01(\x05\x12\x15\n\rapproval_type\x18\x04 \x01(\t\x12\x14\n\x0c\x61pprover_ids\x18\x05 \x03(\t\x12\x1d\n\x15selection_explanation\x18\x06 \x01(\t\"\xa6\x01\n\x18\x41pprovalSequenceResponse\x12\x13\n\x0bworkflow_id\x18\x01 \x01(\t\x12\x15\n\rworkflow_name\x18\x02 \x01(\t\x12)\n\x05steps\x18\x03 \x03(\x0b\x32\x1a.core.ApprovalStepSequence\x12\x15\n\rauto_approved\x18\x04 \x01(\x08\x12\x1c\n\x14\x61uto_approval_reason\x18\x05 \x01(\t\"/\n\x18ValidateApproverResponse\x12\x13\n\x0b\x63\x61n_approve\x18\x01 \x01(\x08\"\x97\x01\n\x1c\x41pprovalWorkflowListResponse\x12-\n\x05items\x18\x01 \x03(\x0b\x32\x1e.core.ApprovalWorkflowResponse\x12\x13\n\x0btotal_count\x18\x02 \x01(\x05\x12\x0c\n\x04page\x18\x03 \x01(\x05\x12\x10\n\x08per_page\x18\x04 \x01(\x05\x12\x13\n\x0btotal_pages\x18\x05 \x01(\x05\x32\xd3\x02\n\x11\x41pprovalWorkflows\x12G\n\x0bGetWorkflow\x12\x18.core.GetWorkflowRequest\x1a\x1e.core.ApprovalWorkflowResponse\x12Q\n\x10GenerateSequence\x12\x1d.core.GenerateSequenceRequest\x1a\x1e.core.ApprovalSequenceResponse\x12Q\n\x10ValidateApprover\x12\x1d.core.ValidateApproverRequest\x1a\x1e.core.ValidateApproverResponse\x12O\n\rListWorkflows\x12\x1a.core.ListWorkflowsRequest\x1a\".core.ApprovalWorkflowListResponseb\x06proto3"

pool = Google::Protobuf::DescriptorPool.generated_pool
pool.add_serialized_file(descriptor_data)

module Core
  GetWorkflowRequest = ::Google::Protobuf::DescriptorPool.generated_pool.lookup("core.GetWorkflowRequest").msgclass
  GenerateSequenceRequest = ::Google::Protobuf::DescriptorPool.generated_pool.lookup("core.GenerateSequenceRequest").msgclass
  ValidateApproverRequest = ::Google::Protobuf::DescriptorPool.generated_pool.lookup("core.ValidateApproverRequest").msgclass
  ListWorkflowsRequest = ::Google::Protobuf::DescriptorPool.generated_pool.lookup("core.ListWorkflowsRequest").msgclass
  ApprovalStepResponse = ::Google::Protobuf::DescriptorPool.generated_pool.lookup("core.ApprovalStepResponse").msgclass
  ApprovalWorkflowResponse = ::Google::Protobuf::DescriptorPool.generated_pool.lookup("core.ApprovalWorkflowResponse").msgclass
  ApprovalStepSequence = ::Google::Protobuf::DescriptorPool.generated_pool.lookup("core.ApprovalStepSequence").msgclass
  ApprovalSequenceResponse = ::Google::Protobuf::DescriptorPool.generated_pool.lookup("core.ApprovalSequenceResponse").msgclass
  ValidateApproverResponse = ::Google::Protobuf::DescriptorPool.generated_pool.lookup("core.ValidateApproverResponse").msgclass
  ApprovalWorkflowListResponse = ::Google::Protobuf::DescriptorPool.generated_pool.lookup("core.ApprovalWorkflowListResponse").msgclass
end
