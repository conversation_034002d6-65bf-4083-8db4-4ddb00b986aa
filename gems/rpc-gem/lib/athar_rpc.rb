require 'gruf'
require 'active_support'

# Ensure __dir__ is not nil
dir = __dir__ || File.dirname(__FILE__)

# Add the new generated directory to the load path
$LOAD_PATH.unshift(File.expand_path('athar_rpc_services', dir))

# Automatically require all gRPC-generated files
Dir.glob(File.expand_path('athar_rpc_services/**/*.rb', dir)).each do |file|
  require file
end

require 'athar_rpc/railtie'

module AtharRpc
  extend ActiveSupport::Autoload

  autoload :GrpcConfig, 'client/grpc_config'
  autoload :GrpcClientFactory, 'client/grpc_client_factory'
  autoload :ActiveRpc, 'concerns/active_rpc'

  # Require configuration
  require 'athar_rpc/configuration'

  # Require the gRPC module
  require 'athar_rpc/grpc'
end
