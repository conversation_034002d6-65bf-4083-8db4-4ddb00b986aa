require 'gruf'
require_relative 'grpc_config'
require 'singleton'
require 'ostruct'

module AtharRpc
  # Dummy client that logs calls but doesn't fail in production
  class DummyClient
    def initialize(subsystem, service_name)
      @subsystem = subsystem
      @service_name = service_name
    end

    def call(method, params = {})
      if defined?(Rails)
        Rails.logger.warn("[DummyClient] Called #{method} on #{@subsystem}/#{@service_name} with params: #{params.inspect}")
      end
      # Return an OpenStruct with a nil message to prevent nil errors
      OpenStruct.new(message: nil)
    end
  end

  class GrpcClientFactory
    include Singleton

    def initialize
      @clients = {}
    end

    def client_for(subsystem, service_name)
      key = "#{subsystem}_#{service_name}"
      @clients[key] ||= create_client(subsystem, service_name)
    end

    private

    def create_client(subsystem, service_name)
      # Ensure config is loaded
      if GrpcConfig.configs.empty? && defined?(Rails)
        Rails.logger.warn("GrpcConfig not loaded yet, attempting to load it now")
        GrpcConfig.load_config rescue nil
      end

      config = GrpcConfig.for(subsystem)
      if config.empty?
        if defined?(Rails) && Rails.env.production?
          Rails.logger.error("No gRPC configuration found for #{subsystem} in production environment")
          # Return a dummy client in production to prevent app crash
          return DummyClient.new(subsystem, service_name)
        else
          raise "No gRPC configuration found for #{subsystem}"
        end
      end

      service_class = "::#{subsystem.camelize}::#{service_name}".constantize

      Gruf::Client.new(
        service: service_class,
        options: {
          hostname: "#{config['grpc_host']}:#{config['grpc_port']}",
          use_ssl: config['use_ssl']
        }
      )
    end
  end
end