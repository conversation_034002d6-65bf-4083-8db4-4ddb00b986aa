require 'dry-configurable'
require 'yaml'

module AtharRpc
  module GrpcConfig
    extend Dry::Configurable

    setting :configs, default: {}, reader: true

    def self.load_config
      config_file = File.join(Rails.root, 'config', 'grpc_clients.yml')

      unless File.exist?(config_file)
        raise "gRPC client config file not found: #{config_file}"
      end

      configure do |config|
        config.configs = YAML.safe_load(File.read(config_file), aliases: true, permitted_classes: [Symbol])[Rails.env]
      end
    end

    def self.for(subsystem)
      configs.fetch(subsystem, {})
    end
  end
end
