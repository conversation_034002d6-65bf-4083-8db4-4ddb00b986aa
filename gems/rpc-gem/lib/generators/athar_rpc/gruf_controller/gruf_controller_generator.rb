require "rails/generators"
require "rails/generators/base"
require 'active_support/inflector'

module AtharRpc
  module Generators
    class GrufControllerGenerator < Rails::Generators::Base
      source_root File.expand_path("templates", __dir__ || File.dirname(__FILE__))
      argument :proto_file, type: :string, desc: "Path to the .proto file inside the gem"

      def generate_controller
        parsed_data = parse_proto_file(proto_file)
        return unless parsed_data

        package_name = parsed_data[:package_name]
        service_name = parsed_data[:service_name]
        rpc_methods = parsed_data[:rpc_methods]

        controller_name = "#{service_name}Controller"

        destination_path = "app/rpc/#{package_name.underscore}/#{controller_name.underscore}.rb"

        template(
          "gruf_controller.rb.erb",
          destination_path,
          package_name: package_name,
          service_name: service_name,
          controller_name: controller_name,
          rpc_methods: rpc_methods
        )
      end

      private

      def parse_proto_file(proto_relative_path)
        # 🔹 Locate `.proto` file inside the installed gem
        gem_path = Gem::Specification.find_by_name("athar_rpc").gem_dir
        proto_full_path = File.join(gem_path, "protos", proto_relative_path)

        unless File.exist?(proto_full_path)
          say_status("error", "Could not find #{proto_full_path}", :red)
          return nil
        end

        content = File.read(proto_full_path)

        package_name = content[/option ruby_package = "(.*?)";/, 1]
        service_name = content[/service (\w+) \{/, 1]

        return nil unless package_name && service_name

        rpc_methods = content.scan(/rpc (\w+) \((\w+)\) returns \((\w+)\);/).map do |match|
          { name: match[0], request: match[1], response: match[2] }
        end

        { package_name: package_name, service_name: service_name, rpc_methods: rpc_methods }
      end
    end
  end
end
