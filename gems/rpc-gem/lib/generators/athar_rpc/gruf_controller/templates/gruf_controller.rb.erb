module <%= config[:package_name] %>
  class <%= config[:service_name] %>Controller < Gruf::Controllers::Base
    bind ::<%= config[:package_name] %>::<%= config[:service_name] %>::Service
<% config[:rpc_methods].each do |method| %>
    def <%= method[:name].underscore %>
      request_payload = request.message
      # Implement business logic here
      # Example response:
      ::<%= config[:package_name] %>::<%= method[:response] %>.new(message: "Success")
    rescue StandardError => e
      fail!(:internal, :unexpected_error, "Error: #{e.message}")
    end
<% end %>
  end
end
