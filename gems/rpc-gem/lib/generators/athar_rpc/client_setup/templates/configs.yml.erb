default: &default
<% config[:services].each do |package, pkg_conf| %>
  <%= package.downcase %>:
    grpc_host: "<%= pkg_conf["grpc_host"] %>"
    grpc_port: <%= pkg_conf["grpc_port"] %>
    use_ssl: <%= pkg_conf["use_ssl"] %>
    services:
  <%- pkg_conf['services'].each do |service| -%>
      - <%= service %>
  <%- end -%>
<%- end -%>

development:
  <<: *default

# To customize the production environment, create a `production` section in this file
production:
  <<: *default
#  core:
#    grpc_host: "grpc.core.athar.com"
#    grpc_port: 6000
#    use_ssl: true
