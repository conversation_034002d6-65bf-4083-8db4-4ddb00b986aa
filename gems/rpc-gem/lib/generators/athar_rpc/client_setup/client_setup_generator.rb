require "rails/generators"
require "rails/generators/base"
require "active_support/inflector"
require "yaml"

module AtharRpc
  module Generators
    class ClientSetupGenerator < Rails::Generators::Base
      source_root File.expand_path("templates", __dir__ || File.dirname(__FILE__))

      def generate_client_config
        grpc_config_path = "config/grpc_clients.yml"
        services = extract_services_from_protos

        if services.empty?
          say_status("error", "No gRPC services found in the proto files.", :red)
          return
        end

        template("configs.yml.erb", grpc_config_path, services: services)
        say_status("success", "Generated gRPC client config at #{grpc_config_path}", :green)
      end

      def create_initializer
        initializer_path = "config/initializers/athar_rpc_client.rb"
        template "initializer.rb.erb", initializer_path
      end

      private

      def extract_services_from_protos
        gem_path = Gem::Specification.find_by_name("athar_rpc").gem_dir
        proto_files = Dir.glob(File.join(gem_path, "protos/**/*.proto"))

        services = {}
        proto_files.each do |proto_file|
          content = File.read(proto_file)
          package_name = content[/option ruby_package = "(.*?)";/, 1]
          service_names = content.scan(/service (\w+) \{/).flatten

          next unless package_name && service_names.any?

          services[package_name] ||= {
            "grpc_host" => "#{package_name.dasherize}-app-grpc".downcase,
            "grpc_port" => 10541,
            "use_ssl" => false,
            "services" => []
          }

          services[package_name]["services"].concat(service_names)
        end

        services
      end
    end
  end
end
