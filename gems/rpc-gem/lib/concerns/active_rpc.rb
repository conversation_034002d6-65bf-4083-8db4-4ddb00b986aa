# frozen_string_literal: true

require 'ostruct'
require_relative 'active_rpc/attribute_dsl'

# This module provides a DSL for integrating gRPC services with ActiveRecord models
# It automatically creates methods to access attributes from gRPC responses
# IMPORTANT: This concern is designed to be used ONLY with ActiveRecord models
module AtharRpc
  module ActiveRpc
    extend ActiveSupport::Concern

    included do
      # Include ActiveModel::Attributes for attribute management
      include ActiveModel::Attributes

      # Include ActiveModel::Dirty for change tracking
      include ActiveModel::Dirty

      # Class attribute to store the gRPC client for direct access
      class_attribute :grpc_client, instance_writer: false

      # Store remote attributes for proper initialization
      class_attribute :remote_attributes, instance_writer: false, default: {}

      # Override the reload method to also reload remote data
      def reload(*args)
        # Call the original reload method
        result = super

        # Reload remote data
        load_remote_data if respond_to?(:load_remote_data)

        # Return self for method chaining
        result
      end

      # Define a method to load all remote data
      def load_remote_data
        # Get all active_rpc configurations
        self.class.active_rpc_config&.dig(:options, :method_name).then do |method_name|
          # Load the remote data
          send("load_#{method_name}") if method_name && respond_to?("load_#{method_name}")
        end
      end
    end

    class_methods do
      # DSL method to configure gRPC integration
      # Example: active_rpc :core, :User, foreign_key: :user_id do
      #   define_rpc_attribute :name, :string, default: "Anonymous"
      #   define_rpc_attribute :email, :string
      #   define_rpc_attribute :roles, :json, default: []
      # end
      def active_rpc(service, resource, options = {}, &block)
        # Create a configuration object to store attributes
        config = OpenStruct.new(
          service: service,
          resource: resource,
          attributes: [],
          attribute_types: {},
          attribute_defaults: {},
          query_config: {},
          scopes: {},
          where_rpc_mappings: {},
          options: options
        )

        # Create a DSL context for defining attributes
        if block_given?
          dsl = AttributeDsl.new(config)
          dsl.instance_eval(&block)
        end

        # Get the final list of attributes
        attributes = config.attributes
        # Default options
        options = {
          foreign_key: :"#{resource.to_s.underscore}_id",
          method_name: :"#{resource.to_s.underscore}_data",
          reload_method: :"reload_#{resource.to_s.underscore}",
          cache_expires_in: 30.minutes,
          bulk_method: :"list_#{resource.to_s.pluralize.underscore}"
        }.merge(options)

        # Store configuration in class instance variable for better inheritance support
        @active_rpc_config = {
          service: service,
          resource: resource,
          attributes: config.attributes,
          attribute_types: config.attribute_types,
          attribute_defaults: config.attribute_defaults,
          query_config: config.query_config,
          scopes: config.scopes,
          where_rpc_mappings: config.where_rpc_mappings,
          options: options
        }

        # Store in remote_attributes for proper initialization
        self.remote_attributes = remote_attributes.merge(
          resource.to_s.underscore.to_sym => {
            attributes: config.attributes,
            attribute_types: config.attribute_types,
            attribute_defaults: config.attribute_defaults,
            query_config: config.query_config,
            scopes: config.scopes,
            where_rpc_mappings: config.where_rpc_mappings,
            service: service,
            options: options
          }
        )

        # Initialize class variables for validation errors
        @last_validation_errors = nil
        @last_validation_messages = nil

        # Initialize the gRPC client for this model
        begin
          self.grpc_client = AtharRpc::GrpcClientFactory.instance.client_for(service.to_s, "#{resource}s")
        rescue StandardError => e
          raise e unless defined?(Rails)

          Rails.logger.error("Failed to initialize gRPC client for #{service}:#{resource}: #{e.message}")
          # In production, we'll use a dummy client to prevent app crash
          if defined?(Rails.env) && Rails.env.production?
            self.grpc_client = AtharRpc::DummyClient.new(service.to_s, "#{resource}s")
          end

          # Re-raise in non-Rails environments
        end

        # Define method to access the configuration
        define_singleton_method(:active_rpc_config) do
          @active_rpc_config
        end

        # Define attributes using ActiveModel::Attributes
        config.attributes.each do |attr|
          attr_name = attr.to_s

          # Get the type for this attribute
          attr_type = config.attribute_types[attr] || :string

          # Get default value if specified
          default_value = config.attribute_defaults[attr]

          # Define the attribute with the appropriate type and default value
          if !default_value.nil?
            # Use the specified default value
            attribute attr_name, attr_type, default: default_value
          else
            # No default value
            attribute attr_name, attr_type
          end

          # Override the getter method to check for cached values first
          define_method(attr_name) do
            # Check if we have a cached value from bulk loading
            cached_var = "@#{attr_name}_cached"
            cached_loaded_var = "@#{attr_name}_cached_loaded"

            return instance_variable_get(cached_var) if instance_variable_get(cached_loaded_var)

            # Fall back to the original attribute access (which will trigger gRPC)
            super()
          end
        end

        # Add an after_find callback to load remote data when a model is loaded from the database
        # This ensures that remote attributes are available when using Model.find
        # BUT only if the data hasn't already been cached from bulk loading
        after_find :"load_#{options[:method_name]}_if_needed"

        # This callback is now handled by the combined before_save callback above

        # Add a before_save callback to handle both creation and updates
        before_save do
          # Skip RPC operations when in local mode (e.g., during seeding)
          if AtharRpc.local_mode?
            if defined?(Rails) && Rails.respond_to?(:logger)
              Rails.logger.debug("[ActiveRpc Local Mode] Skipping RPC operations for #{resource} during save")
            end
            next
          end

          # Handle creation - if we have pending attributes but no foreign key
          if send(options[:foreign_key]).blank? && send("has_#{resource.to_s.underscore}_updates?")
            data = send("pending_#{resource.to_s.underscore}_updates")

            # Only create if there are actual changes
            result = send("create_and_associate_#{resource.to_s.underscore}", data)

            # For debugging
            if defined?(Rails) && Rails.respond_to?(:logger)
              Rails.logger.debug("[ActiveRpc] create_and_associate result: #{result}, foreign_key: #{send(options[:foreign_key]).inspect}")
            end

            # If the creation failed, abort the save
            unless result
              if defined?(Rails) && Rails.respond_to?(:logger)
                Rails.logger.error("[ActiveRpc] Failed to create and associate #{resource} before creating #{self.class.name}")
              end
              throw :abort
            end
          end

          # Handle updates - if we have pending updates and a foreign key
          if send("has_#{resource.to_s.underscore}_updates?") && send(options[:foreign_key]).present?
            # Save the updates to the gRPC service
            result = send("update_#{resource.to_s.underscore}")

            # If the update failed, abort the save
            throw :abort unless result
          end
        end

        # Add a method to track changes to delegated attributes
        define_method("#{resource.to_s.underscore}_changed?") do
          # Check if any of the remote attributes have changed using ActiveModel::Dirty
          attributes.any? { |attr| respond_to?("#{attr}_changed?") && send("#{attr}_changed?") }
        end

        # Define method to get data from the gRPC service
        define_method(options[:method_name]) do
          # Create a mock message with the current attribute values
          message = OpenStruct.new(id: send(options[:foreign_key]))

          # Add all attributes to the message
          config.attributes.each do |attr|
            # Get the current value of the attribute
            message.send("#{attr}=", send(attr))
          end

          # Return a mock response with the message
          OpenStruct.new(message: message)
        end

        # Define method to force reload data from the gRPC service
        define_method(options[:reload_method]) do
          send("load_#{options[:method_name]}")
          self
        end

        # Define method to load data from the gRPC service
        define_method("load_#{options[:method_name]}") do
          foreign_key_value = send(options[:foreign_key])
          return unless foreign_key_value.present?

          # Fetch data from the gRPC service
          data = send("fetch_#{options[:method_name]}_from_#{service}")

          # Set attribute values directly
          return unless data&.message

          # Update attributes with the response data
          if data.message.respond_to?(:to_h)
            # Convert the response message to a hash
            response_data = data.message.to_h

            # Use intersection to get only the attributes that exist in both the response and our model
            attrs_to_assign = response_data.slice(*attributes.map(&:to_sym))

            # Assign the attributes in one go
            assign_attributes(attrs_to_assign)
          end

          # Clear changes since these are the initial values
          clear_changes_information if respond_to?(:clear_changes_information)
        end

        # Define conditional method to load data only if not already cached from bulk loading
        define_method("load_#{options[:method_name]}_if_needed") do
          # Check if data is already loaded (from bulk loading or previous individual load)
          return if instance_variable_get("@#{options[:method_name]}_loaded")

          # Check if we have bulk loading data available using the record's cache key
          # This happens when the record was loaded as part of a with_user_data scope
          cache_key = instance_variable_get(:@_bulk_loading_cache_key) || Thread.current[:active_rpc_current_cache_key]
          if cache_key
            # Set the cache key on this record for future use
            instance_variable_set(:@_bulk_loading_cache_key, cache_key)
            cache_hash = self.class.instance_variable_get(:@_bulk_loading_cache) || {}
            cache_data = cache_hash[cache_key]

            if cache_data && cache_data[:lookup]
              foreign_key_value = send(options[:foreign_key])
              remote_data = cache_data[:lookup][foreign_key_value.to_s] if foreign_key_value.present?

              if remote_data
                # Cache the gRPC response
                mock_response = OpenStruct.new(message: remote_data)
                instance_variable_set("@#{options[:method_name]}", mock_response)
                instance_variable_set("@#{options[:method_name]}_loaded", true)

                # Cache individual attributes
                bulk_attributes = cache_data[:attributes] || attributes
                bulk_attributes.each do |attr|
                  next unless remote_data.respond_to?(attr)

                  value = remote_data.send(attr)
                  instance_variable_set("@#{attr}_cached", value)
                  instance_variable_set("@#{attr}_cached_loaded", true)
                end
                return
              end
            end
          end

          # Check if this record was filtered out by where_rpc conditions
          if instance_variable_get(:@_rpc_data_filtered_out)
            # Set empty/default values to avoid repeated attempts
            instance_variable_set("@#{options[:method_name]}", nil)
            instance_variable_set("@#{options[:method_name]}_loaded", true)

            # Cache default values for attributes
            attributes.each do |attr|
              default_value = config.attribute_defaults[attr]
              instance_variable_set("@#{attr}_cached", default_value)
              instance_variable_set("@#{attr}_cached_loaded", true)
            end
            return
          end

          # Check if we're in a where_rpc context and should skip individual loading
          if Thread.current[:active_rpc_where_rpc_context]
            # Set empty/default values to avoid repeated attempts
            instance_variable_set("@#{options[:method_name]}", nil)
            instance_variable_set("@#{options[:method_name]}_loaded", true)

            # Cache default values for attributes
            attributes.each do |attr|
              default_value = config.attribute_defaults[attr]
              instance_variable_set("@#{attr}_cached", default_value)
              instance_variable_set("@#{attr}_cached_loaded", true)
            end
            return
          end

          send("load_#{options[:method_name]}")
        end

        # Define method to fetch data from the gRPC service
        define_method("fetch_#{options[:method_name]}_from_#{service}") do
          foreign_key_value = send(options[:foreign_key])

          # Check if local mode is enabled - skip gRPC calls entirely
          if AtharRpc.local_mode?
            Rails.logger.debug("[ActiveRpc Local Mode] Skipping gRPC call for #{resource} with #{options[:foreign_key]}=#{foreign_key_value}")

            # Create a mock response with default values
            message_data = { id: foreign_key_value }
            config.attributes.each do |attr|
              message_data[attr] = config.attribute_defaults[attr] if config.attribute_defaults.key?(attr)
            end

            return OpenStruct.new(message: OpenStruct.new(message_data))
          end

          # Add nil check before calling methods on the client
          if self.class.grpc_client.nil?
            Rails.logger.error("Failed to fetch #{resource} data for #{options[:foreign_key]}=#{send(options[:foreign_key])}: gRPC client is nil")
            return nil
          end

          formatted_id = AtharRpc::ActiveRpc.format_foreign_key_value(self.class, options[:foreign_key],
                                                                      foreign_key_value)
          response = self.class.grpc_client.call(:"Get#{resource}", id: formatted_id)

          # Add nil check for the response
          if response.nil?
            Rails.logger.error("Failed to fetch #{resource} data for #{options[:foreign_key]}=#{send(options[:foreign_key])}: gRPC response is nil")
            return nil
          end

          response
        rescue StandardError => e
          Rails.logger.error("Failed to fetch #{resource} data for #{options[:foreign_key]}=#{send(options[:foreign_key])}: #{e.message}")
          nil
        end

        # NOTE: Bulk method is defined later with query_options support (line ~1083)

        # Define a scope for eager loading gRPC data
        scope_method_name = "with_#{resource.to_s.underscore}_data".to_sym
        define_singleton_method(scope_method_name) do |query_options = {}|
          # Return a relation that can be chained, but mark it for bulk loading
          relation = all

          # Store the bulk loading configuration in the relation
          relation.instance_variable_set(:@_bulk_loading_enabled, true)
          relation.instance_variable_set(:@_bulk_loading_resource, resource)
          # Include resource in options for generic access
          bulk_options = options.merge(resource: resource)
          relation.instance_variable_set(:@_bulk_loading_options, bulk_options)
          relation.instance_variable_set(:@_bulk_loading_query_options, query_options)
          relation.instance_variable_set(:@_bulk_loading_attributes, attributes)

          # IDs will be extracted when bulk loading is first triggered

          # Create a unique cache key for this relation to avoid collisions between different relations
          @@bulk_loading_counter ||= 0
          @@bulk_loading_counter += 1
          cache_key = "bulk_loading_#{@@bulk_loading_counter}"
          relation.instance_variable_set(:@_bulk_loading_cache_key, cache_key)

          # Create a unique cache key for this relation to avoid collisions
          cache_key = "bulk_loading_#{relation.object_id}"
          relation.instance_variable_set(:@_bulk_loading_cache_key, cache_key)

          # Create a unique cache key for this specific relation
          cache_key = "bulk_loading_#{relation.object_id}_#{Time.now.to_f}"
          relation.instance_variable_set(:@_bulk_loading_cache_key, cache_key)

          # Method to add RPC filtering conditions using Ransack-style queries
          # This sends filters to the gRPC service's 'q' parameter
          relation.define_singleton_method(:filter_rpc) do |conditions = {}|
            Rails.logger.debug("[ActiveRpc] filter_rpc called with conditions: #{conditions.keys}") if defined?(Rails)

            # Store the conditions for later processing as Ransack queries
            @_filter_rpc_conditions = (@_filter_rpc_conditions || {}).merge(conditions)

            # Return self to allow chaining
            self
          end

          # Method to call remote scopes for complex queries
          # This sends scope calls to the gRPC service's 'scope' parameter
          relation.define_singleton_method(:rpc_scope) do |scope_name, value = nil, **args|
            if defined?(Rails)
              Rails.logger.debug("[ActiveRpc] rpc_scope called: #{scope_name} with value: #{value}, args: #{args}")
            end

            # Store the scope calls for later processing
            # If value is provided, use it directly; otherwise use the first arg value
            scope_value = value || (args.any? ? args.values.first : nil)
            @_rpc_scope_conditions = (@_rpc_scope_conditions || {}).merge(scope_name => scope_value)

            # Return self to allow chaining
            self
          end

          # Helper method to check if we have any RPC conditions
          relation.define_singleton_method(:has_rpc_conditions?) do
            @_filter_rpc_conditions.present? || @_rpc_scope_conditions.present?
          end

          # Helper method to apply RPC filters and call the original method
          relation.define_singleton_method(:apply_rpc_filters_then_call) do |method_name, *args, &block|
            Rails.logger.debug("[ActiveRpc] apply_rpc_filters_then_call called for #{method_name}") if defined?(Rails)

            # Check if we have any RPC filtering conditions to process
            has_conditions = has_rpc_conditions?

            if has_conditions
              Rails.logger.debug('[ActiveRpc] Making gRPC call with RPC conditions') if defined?(Rails)

              # Determine bulk method based on resource type
              resource = @_bulk_loading_options[:resource] || 'User'
              default_bulk_method = "list_#{resource.to_s.pluralize.underscore}".to_sym
              bulk_method = @_bulk_loading_options[:bulk_method] || default_bulk_method

              # Prepare query options for different types of RPC calls
              query_options = {}

              # 1. Handle filter_rpc conditions (Ransack queries)
              if @_filter_rpc_conditions.present?
                if defined?(Rails)
                  Rails.logger.debug("[ActiveRpc] Processing filter_rpc conditions: #{@_filter_rpc_conditions.inspect}")
                end
                query_options[:q] = @_filter_rpc_conditions
              end

              # 2. Handle rpc_scope conditions (remote scopes)
              if @_rpc_scope_conditions.present?
                if defined?(Rails)
                  Rails.logger.debug("[ActiveRpc] Processing rpc_scope conditions: #{@_rpc_scope_conditions.inspect}")
                end
                existing_scopes = query_options[:scope] || {}
                query_options[:scope] = existing_scopes.merge(@_rpc_scope_conditions)
              end

              Rails.logger.debug("[ActiveRpc] Final query_options: #{query_options.inspect}") if defined?(Rails)

              filtered_data = klass.send(bulk_method, [], query_options)
              filtered_ids = filtered_data.map(&:id)
              resource_name = @_bulk_loading_options[:resource] || 'records'
              if defined?(Rails)
                Rails.logger.debug("[ActiveRpc] Got #{filtered_data.size} #{resource_name.to_s.downcase} from gRPC call")
              end

              # 2. Handle empty results
              if filtered_ids.empty?
                Rails.logger.debug('[ActiveRpc] No filtered results, returning empty relation') if defined?(Rails)
                # For count method, return 0 directly to avoid infinite recursion
                return 0 if method_name == :count

                # For other methods, create empty relation from current relation to preserve ActiveRpc overrides
                empty_relation = none
                # Clear RPC conditions to prevent infinite recursion
                empty_relation.instance_variable_set(:@_filter_rpc_conditions, {})
                empty_relation.instance_variable_set(:@_rpc_scope_conditions, {})
                return empty_relation.send(method_name, *args, &block) if block_given?

                return empty_relation.send(method_name, *args)

              end

              # 3. Create new relation with filtered IDs
              foreign_key = @_bulk_loading_options[:foreign_key]
              if defined?(Rails)
                Rails.logger.debug("[ActiveRpc] Creating new relation with #{foreign_key} IN #{filtered_ids.size} IDs")
              end
              new_relation = where(foreign_key => filtered_ids)

              # 4. Clear all RPC conditions to prevent infinite recursion
              # Keep the bulk_loading_options so the relation retains its ActiveRpc configuration
              new_relation.instance_variable_set(:@_filter_rpc_conditions, {})
              new_relation.instance_variable_set(:@_rpc_scope_conditions, {})

              # 5. Call the original method on the new relation with proper argument handling
              result = if block_given?
                         new_relation.send(method_name, *args, &block)
                       else
                         new_relation.send(method_name, *args)
                       end
              Rails.logger.debug("[ActiveRpc] #{method_name} completed with result: #{result.class}") if defined?(Rails)
              result
            elsif block_given?
              # No RPC conditions, use normal behavior with proper argument handling
              super(*args, &block)
            else
              super(*args)
            end
          end

          # Override enumeration methods to use new RPC filter pattern
          relation.define_singleton_method(:each) do |&block|
            if has_rpc_conditions?
              apply_rpc_filters_then_call(:each, &block)
            else
              super(&block)
            end
          end

          relation.define_singleton_method(:map) do |&block|
            if has_rpc_conditions?
              apply_rpc_filters_then_call(:map, &block)
            else
              super(&block)
            end
          end

          relation.define_singleton_method(:to_a) do
            if has_rpc_conditions?
              apply_rpc_filters_then_call(:to_a)
            else
              super()
            end
          end

          # Override individual record access methods to use new RPC filter pattern
          relation.define_singleton_method(:first) do |limit = nil|
            if has_rpc_conditions?
              apply_rpc_filters_then_call(:first, limit)
            else
              super(limit)
            end
          end

          relation.define_singleton_method(:second) do
            if has_rpc_conditions?
              apply_rpc_filters_then_call(:second)
            else
              super()
            end
          end

          relation.define_singleton_method(:last) do |limit = nil|
            if has_rpc_conditions?
              apply_rpc_filters_then_call(:last, limit)
            else
              super(limit)
            end
          end

          relation.define_singleton_method(:[]) do |index|
            if has_rpc_conditions?
              apply_rpc_filters_then_call(:[], index)
            else
              super(index)
            end
          end

          # Override evaluation methods to use new RPC filter pattern
          relation.define_singleton_method(:count) do |column_name = nil|
            if has_rpc_conditions?
              Rails.logger.debug('[ActiveRpc] Using RPC filter pattern for count') if defined?(Rails)
              apply_rpc_filters_then_call(:count, column_name)
            else
              # Use the original ActiveRecord count method directly
              ActiveRecord::Relation.instance_method(:count).bind_call(self, column_name)
            end
          end

          relation.define_singleton_method(:size) do
            if has_rpc_conditions?
              apply_rpc_filters_then_call(:size)
            else
              super()
            end
          end

          relation.define_singleton_method(:inspect) do
            if has_rpc_conditions?
              apply_rpc_filters_then_call(:inspect)
            else
              super()
            end
          end

          relation.define_singleton_method(:length) do
            if has_rpc_conditions?
              apply_rpc_filters_then_call(:length)
            else
              super()
            end
          end

          relation.define_singleton_method(:empty?) do
            if has_rpc_conditions?
              apply_rpc_filters_then_call(:empty?)
            else
              super()
            end
          end

          # NOTE: Old post-filtering method removed - using new RPC filter pattern

          # Define the bulk loading method
          relation.define_singleton_method(:perform_bulk_loading_if_needed) do
            return if @_bulk_loading_performed

            # Extract IDs from the current relation state and store for reuse
            bulk_options = @_bulk_loading_options

            # Check if we have where_rpc conditions that should be applied remotely
            has_remote_conditions = @_bulk_loading_query_options&.dig(:where)&.present? ||
                                    @_bulk_loading_query_options&.dig(:scope)&.present?

            Rails.logger.debug("[ActiveRpc] has_remote_conditions: #{has_remote_conditions}") if defined?(Rails)
            Rails.logger.debug("[ActiveRpc] query_options: #{@_bulk_loading_query_options.keys}") if defined?(Rails)

            if has_remote_conditions
              # For remote filtering, don't extract IDs - let the remote service filter
              Rails.logger.debug('[ActiveRpc] Using remote filtering - not extracting IDs') if defined?(Rails)
              ids = []
            else
              # For regular bulk loading, extract IDs as usual
              if @_bulk_loading_extracted_ids
                ids = @_bulk_loading_extracted_ids
              else
                ids = pluck(bulk_options[:foreign_key]).compact.uniq
                @_bulk_loading_extracted_ids = ids
              end

              # Skip if no IDs and no remote conditions
              return if ids.empty?
            end

            # Call the bulk method
            bulk_method = bulk_options[:bulk_method]

            # Get the model class to call the bulk method
            model_class = klass
            bulk_data = model_class.send(bulk_method, ids, @_bulk_loading_query_options)

            # Create lookup hash from bulk data
            lookup = {}
            bulk_data.each do |item|
              lookup[item.id.to_s] = item if item.respond_to?(:id)
            end

            # Store the lookup using a hash-based cache to avoid collisions
            cache_key = @_bulk_loading_cache_key

            # Initialize the cache hash if it doesn't exist
            cache_hash = model_class.instance_variable_get(:@_bulk_loading_cache) || {}
            cache_hash[cache_key] = {
              lookup: lookup,
              attributes: @_bulk_loading_attributes,
              options: bulk_options
            }
            model_class.instance_variable_set(:@_bulk_loading_cache, cache_hash)

            # Store the current cache key in a thread-local variable so after_find can access it
            Thread.current[:active_rpc_current_cache_key] = cache_key

            # Mark as performed to avoid duplicate calls
            @_bulk_loading_performed = true
          end
          relation
        end

        # Define a method to directly access the gRPC client for custom calls
        client_method_name = "#{resource.to_s.underscore}_client".to_sym
        define_singleton_method(client_method_name) do
          grpc_client
        end

        # We don't need a separate method to check service availability
        # Instead, we'll handle connection errors directly in the create/update methods

        # Define a class method for creation (for backward compatibility with tests)
        define_singleton_method("create_#{resource.to_s.underscore}") do |attributes = {}|
          # Create a new instance
          instance = new

          # Build the resource
          instance.send("build_#{resource.to_s.underscore}", attributes)

          # Create and associate the resource
          return unless instance.send("create_and_associate_#{resource.to_s.underscore}", attributes)

          # Return the ID
          instance.send(options[:foreign_key])
        end

        # Define method to build a new resource without saving it
        define_method("build_#{resource.to_s.underscore}") do |attributes = {}|
          # Set any attributes that match our delegated attributes
          attributes.each do |attr, value|
            attr_sym = attr.to_sym

            # Check if this is a remote attribute
            next unless self.class.active_rpc_config[:attributes].include?(attr_sym)

            # Track the change using ActiveModel::Dirty
            send("#{attr}_will_change!") if respond_to?("#{attr}_will_change!")

            # Set the attribute value
            send("#{attr}=", value)
          end

          # Return self for method chaining
          self
        end

        # Define an instance method to make custom RPC calls
        custom_call_method = "call_#{resource.to_s.underscore}_rpc".to_sym
        define_method(custom_call_method) do |method_name, params = {}|
          # Add field tracking metadata for custom RPC calls with parameters
          if params.is_a?(Hash) && !params.empty?
            params_with_fields = params.to_h
            params_with_fields[:_fields] = AtharRpc::ActiveRpc._active_rpc_extract_field_paths(params)
            self.class.grpc_client.call(method_name, params_with_fields)
          else
            self.class.grpc_client.call(method_name, params)
          end
        rescue StandardError => e
          Rails.logger.error("Failed to call custom RPC method #{method_name} with params #{params}: #{e.message}")
          nil
        end

        # Define method to save pending updates to the gRPC service
        update_method = "update_#{resource.to_s.underscore}".to_sym
        define_method(update_method) do
          # Check if there are any changed attributes
          if send("has_#{resource.to_s.underscore}_updates?")
            foreign_key_value = send(options[:foreign_key])

            # Return false if foreign key is not present
            unless foreign_key_value.present?
              Rails.logger.error("[ActiveRpc] Cannot update #{resource} without a valid #{options[:foreign_key]}")
              errors.add(options[:foreign_key], "Cannot update #{resource.to_s.underscore} without a valid ID")
              return false
            end

            # Prepare parameters for the update call
            formatted_id = AtharRpc::ActiveRpc.format_foreign_key_value(self.class, options[:foreign_key],
                                                                        foreign_key_value)
            params = { id: formatted_id }

            # DEBUG: Log the formatted ID
            Rails.logger.debug("[ActiveRpc DEBUG] Foreign key #{options[:foreign_key]}: #{foreign_key_value.inspect} (#{foreign_key_value.class}) -> formatted: #{formatted_id.inspect} (#{formatted_id.class})")

            # Add all changed attributes to the params
            pending_updates = send("pending_#{resource.to_s.underscore}_updates")
            params.merge!(pending_updates)

            Rails.logger.info("[ActiveRpc] Updating #{resource} with ID #{foreign_key_value} with params: #{params.keys}")

            # Call the UpdateUser method on the gRPC service
            begin
              # Add field tracking metadata to help with protobuf field presence
              params_with_fields = params.to_h
              params_with_fields[:_fields] = AtharRpc::ActiveRpc._active_rpc_extract_field_paths(params)

              response = self.class.grpc_client.call("Update#{resource}".to_sym, params_with_fields)

              # If the update was successful, update the attributes
              if response&.message
                # Update attributes with the response data
                if response.message.respond_to?(:to_h)
                  # Convert the response message to a hash
                  response_data = response.message.to_h

                  # Use intersection to get only the attributes that exist in both the response and our model
                  attrs_to_assign = response_data.slice(*self.class.active_rpc_config[:attributes].map(&:to_sym))

                  # Assign the attributes in one go
                  assign_attributes(attrs_to_assign)
                end

                clear_changes_information if respond_to?(:clear_changes_information)

                Rails.logger.info("[ActiveRpc] Successfully updated #{resource} with ID #{foreign_key_value}")
                true
              else
                # Handle server error response
                error_message = 'No response message'

                # Extract error details if available
                if response&.error
                  error_code = response.error.respond_to?(:code) ? response.error.code : 'UNKNOWN'
                  error_details = response.error.respond_to?(:message) ? response.error.message : 'Unknown error'
                  error_message = "Server returned error: #{error_code} - #{error_details}"

                  # Add specific error messages to the model
                  # Clean up the error message - gRPC adds some debug info
                  clean_error = error_details.gsub(/\. debug_error_string:.+$/, '')

                  # Check if the error response has structured validation_errors
                  if response.error.respond_to?(:validation_errors) && response.error.validation_errors.present?
                    # Get the validation errors
                    validation_errors = response.error.validation_errors

                    # Add each validation error to the model
                    validation_errors.each do |field, messages|
                      field_sym = field.to_sym

                      # Convert messages to array if it's not already
                      messages_array = messages.is_a?(Array) ? messages : [messages]

                      # Add each message to the field
                      messages_array.each do |message|
                        errors.add(field_sym, message)
                      end
                    end
                    # Check if the error response has full_messages
                  elsif response.error.respond_to?(:full_messages) && response.error.full_messages.present?
                    # Add each full message to the model
                    response.error.full_messages.each do |message|
                      # Try to parse the message to extract field and error
                      if message =~ /^([A-Za-z_]+) (is .+)$/
                        field = ::Regexp.last_match(1).downcase.to_sym
                        error_msg = ::Regexp.last_match(2)
                        errors.add(field, error_msg)
                      else
                        errors.add(options[:foreign_key], message)
                      end
                    end
                    # Check for validation errors in the error message
                  elsif clean_error.include?('Validation failed:')
                    # Extract the validation errors part
                    validation_part = clean_error.gsub(/^\d+:/, '').strip
                    validation_errors = validation_part.gsub('Validation failed: ', '').split(', ')

                    validation_errors.each do |error|
                      # Parse errors in format "Field is invalid"
                      if error =~ /^([A-Za-z_]+) (is .+)$/
                        field = ::Regexp.last_match(1).downcase.to_sym
                        message = ::Regexp.last_match(2)
                        errors.add(field, message)
                      elsif error.include?(':')
                        field, message = error.split(':', 2)
                        errors.add(field.strip.to_sym, message.strip)
                      else
                        errors.add(options[:foreign_key], error.strip)
                      end
                    end
                  else
                    errors.add(options[:foreign_key],
                               "Failed to update #{resource.to_s.underscore} record: #{clean_error}")
                  end
                else
                  errors.add(options[:foreign_key], "Failed to update #{resource.to_s.underscore} record")
                end

                Rails.logger.error("[ActiveRpc] Failed to update #{resource} with ID #{foreign_key_value}: #{error_message}")
                false
              end
            rescue StandardError => e
              # Handle any exceptions that might occur during the RPC call
              error_message = e.message

              # Check if this is a connection error
              if error_message.include?('Connection refused') ||
                 error_message.include?('failed to connect') ||
                 error_message.include?('Deadline exceeded') ||
                 error_message.include?('UNAVAILABLE')
                # This is a connection error, so the service is likely down
                Rails.logger.error("[ActiveRpc] Failed to connect to #{resource} service: #{error_message}")
                errors.add(options[:foreign_key], 'Unable to connect to the required service. Please try again later.')
              else
                # This is some other error
                Rails.logger.error("[ActiveRpc] Error updating #{resource}: #{error_message}")
                errors.add(options[:foreign_key],
                           "Failed to update #{resource.to_s.underscore} record: #{error_message}")
              end

              false
            end
          else
            # No pending updates, return true
            Rails.logger.debug("[ActiveRpc] No pending updates for #{resource} with ID #{send(options[:foreign_key])}")
            true
          end
        end

        # Define save method for the resource
        define_method("save_#{resource.to_s.underscore}") do
          if send(options[:foreign_key]).present?
            # Update existing resource
            send(update_method)
          elsif attributes.any? { |attr| changed_attributes.key?(attr.to_s) }
            # Create new resource
            create_attrs = {}
            attributes.each do |attr|
              create_attrs[attr] = send(attr) if changed_attributes.key?(attr.to_s)
            end

            # Create and associate the resource
            send("create_and_associate_#{resource.to_s.underscore}", create_attrs)
          # Convert changed attributes to a hash for create
          else
            # No attributes to save
            true
          end
        end

        # Define save! method for the resource
        define_method("save_#{resource.to_s.underscore}!") do
          result = send("save_#{resource.to_s.underscore}")
          raise "Failed to save #{resource.to_s.underscore}: #{errors.full_messages.join(', ')}" unless result

          result
        end

        # Define method to check if there are pending updates
        define_method("has_#{resource.to_s.underscore}_updates?".to_sym) do
          # Check if any of the remote attributes have changed using ActiveModel::Dirty
          # Use changed_attributes to avoid infinite recursion
          attributes.any? { |attr| changed_attributes.key?(attr.to_s) }
        end

        # Define method to get changes to delegated attributes
        define_method("#{resource.to_s.underscore}_changes".to_sym) do
          changes.slice(*attributes.map(&:to_s))
        end

        # Define method to clear pending updates without saving them
        define_method("discard_#{resource.to_s.underscore}_updates".to_sym) do
          if send("has_#{resource.to_s.underscore}_updates?")
            # Get the original values from the server
            send(options[:reload_method])

            # Clear changes using ActiveModel::Dirty
            clear_changes_information if respond_to?(:clear_changes_information)

            Rails.logger.info("[ActiveRpc] Discarded pending updates for #{resource} with ID #{send(options[:foreign_key])}")
          end
          true
        end

        # Define method to get pending updates
        define_method("pending_#{resource.to_s.underscore}_updates".to_sym) do
          attributes_for_database.slice(*changed_attributes.keys.intersection(attributes.map(&:to_s)))
        end

        # Define method to create a new resource and associate it with this model
        define_method("create_and_associate_#{resource.to_s.underscore}".to_sym) do |attributes = {}|
          # Prepare parameters for the create call
          params = {}

          # Only include attributes that are defined in the proto
          self.class.active_rpc_config[:attributes].each do |attr|
            params[attr] = attributes[attr] if attributes.key?(attr)
          end

          # Include any additional attributes that might be needed for the RPC call
          # but are not part of the model's attributes (like password, avatar_image, etc.)
          attributes.each do |key, value|
            params[key.to_sym] = value unless params.key?(key.to_sym)
          end

          Rails.logger.info("[ActiveRpc] Creating new #{resource} with params: #{params.keys}")

          # Call the CreateUser method on the gRPC service
          # Add field tracking metadata to help with protobuf field presence
          params_with_fields = params.to_h
          params_with_fields[:_fields] = AtharRpc::ActiveRpc._active_rpc_extract_field_paths(params)

          response = self.class.grpc_client.call("Create#{resource}".to_sym, params_with_fields)

          # Check if the creation was successful by looking at _metadata first
          if response&.message&.respond_to?(:_metadata) && response.message._metadata&.success == false
            # This is a structured error response, handle validation errors
            Rails.logger.debug('[ActiveRpc] Received structured error response')

            # Process the structured validation errors
            validation_errors_added = false
            if response.message._metadata.errors && response.message._metadata.errors.length > 0
              response.message._metadata.errors.each do |field, error_messages|
                error_messages.messages.each { |msg| errors.add(field.to_sym, msg) }
              end
              validation_errors_added = true
              Rails.logger.debug('[ActiveRpc] Added structured validation errors')
            end

            unless validation_errors_added
              errors.add(options[:foreign_key],
                         "An error occurred while creating the #{resource.to_s.underscore} record. Please try again.")
            end

            Rails.logger.debug('[ActiveRpc] create_and_associate result: false, foreign_key: nil')
            false
          elsif response&.message&.respond_to?(:id)
            new_id = response.message.id
            Rails.logger.info("[ActiveRpc] Successfully created #{resource} with ID #{new_id}")

            # Set the foreign key
            send("#{options[:foreign_key]}=", new_id)

            # Update attributes with the response data
            if response.message.respond_to?(:to_h)
              # Convert the response message to a hash
              response_data = response.message.to_h

              # Use intersection to get only the attributes that exist in both the response and our model
              attrs_to_assign = response_data.slice(*self.class.active_rpc_config[:attributes].map(&:to_sym))

              # Assign the attributes in one go
              assign_attributes(attrs_to_assign)
            end

            # Clear changes since these are now the "original" values
            clear_changes_information

            Rails.logger.debug("[ActiveRpc] create_and_associate result: true, foreign_key: #{new_id}")
            true
          else
            # Check for validation errors
            if response&.error
              error_code = response.error.respond_to?(:code) ? response.error.code : 'UNKNOWN'
              error_details = response.error.respond_to?(:message) ? response.error.message : 'Unknown error'
              error_message = "Server returned error: #{error_code} - #{error_details}"

              # Handle validation errors - check for shared error object first
              validation_errors_added = false

              # Check for shared error object in response metadata (legacy fallback)
              if response.respond_to?(:_metadata) && response._metadata && !response._metadata.success && response._metadata.errors && response._metadata.errors.length > 0
                response._metadata.errors.each do |field, error_messages|
                  error_messages.messages.each { |msg| errors.add(field.to_sym, msg) }
                end
                validation_errors_added = true
              end

              # If no structured errors found, add generic error
              unless validation_errors_added
                errors.add(options[:foreign_key],
                           "An error occurred while creating the #{resource.to_s.underscore} record. Please try again.")
              end

              # If no validation errors were added, add a generic error
              unless validation_errors_added
                # Check if we have a meaningful error message to show
                if error_details.present? && error_details != 'Unknown error'
                  # Clean up the error message - remove gRPC debug info
                  clean_error = error_details.gsub(/\. debug_error_string:.+$/, '')
                  errors.add(options[:foreign_key], clean_error)
                else
                  errors.add(options[:foreign_key],
                             "An error occurred while creating the #{resource.to_s.underscore} record. Please try again.")
                end
              end

              Rails.logger.error("[ActiveRpc] Failed to create #{resource}: #{error_message}")
            else
              Rails.logger.error("[ActiveRpc] Failed to create #{resource}: No valid response")
              errors.add(options[:foreign_key],
                         "An error occurred while creating the #{resource.to_s.underscore} record. Please try again.")
            end

            Rails.logger.debug('[ActiveRpc] create_and_associate result: false, foreign_key: nil')
            false
          end
        rescue StandardError => e
          # Handle any exceptions that might occur during the RPC call
          error_message = e.message

          # Check if this is a connection error
          if error_message.include?('Connection refused') ||
             error_message.include?('failed to connect') ||
             error_message.include?('Deadline exceeded') ||
             error_message.include?('UNAVAILABLE')
            # This is a connection error, so the service is likely down
            Rails.logger.error("[ActiveRpc] Failed to connect to #{resource} service: #{error_message}")
            errors.add(options[:foreign_key], 'Unable to connect to the required service. Please try again later.')
          else
            # Add generic error for any other exceptions
            Rails.logger.error("[ActiveRpc] Error creating #{resource}: #{error_message}")
            errors.add(options[:foreign_key],
                       "An error occurred while creating the #{resource.to_s.underscore} record. Please try again.")
          end

          # Return false to indicate failure
          false
        end

        # Define method to get query configuration
        define_singleton_method("#{resource.to_s.underscore}_query_config") do
          active_rpc_config[:query_config] || {}
        end

        # Define method to get available scopes
        define_singleton_method("#{resource.to_s.underscore}_scopes") do
          active_rpc_config[:scopes] || {}
        end

        # Define class method for searching with query parameters
        define_singleton_method("search_#{resource.to_s.pluralize.underscore}") do |query_options = {}|
          # Prepare parameters for the search call
          params = {}

          # Add search parameters if provided
          params[:q] = query_options[:q] if query_options[:q].present?

          # Add scope parameters if provided
          params[:scope] = query_options[:scope] if query_options[:scope].present?

          # Add include parameters if provided
          params[:include] = query_options[:include] if query_options[:include].present?

          # Add pagination parameters ONLY if explicitly provided
          if query_options[:page].present? && query_options[:per_page].present?
            params[:page] = query_options[:page]
            params[:per_page] = query_options[:per_page]
          end

          # Add sort parameters if provided
          params[:sort] = query_options[:sort] if query_options[:sort].present?

          # Call the List method on the gRPC service
          # Add field tracking metadata to help with protobuf field presence
          params_with_fields = params.to_h
          params_with_fields[:_fields] = AtharRpc::ActiveRpc._active_rpc_extract_field_paths(params)

          response = grpc_client.call("List#{resource}s", params_with_fields)

          # Return the items from the response
          response&.message&.respond_to?(:items) ? response.message.items : []
        rescue StandardError => e
          Rails.logger.error("Failed to search #{resource} data: #{e.message}")
          []
        end

        # Update the bulk method to support query parameters
        define_singleton_method(options[:bulk_method]) do |ids = [], query_options = {}|
          if defined?(Rails)
            Rails.logger.debug("[ActiveRpc] #{options[:bulk_method]} called with IDs: #{ids.size} items, query_options: #{query_options.keys}")
          end

          # Don't return early if we have query options (scope, where, etc.)
          # This allows filtering without specific IDs
          has_query_conditions = query_options.present? && (
            query_options[:scope].present? ||
            query_options[:where].present? ||
            query_options[:q].present?
          )

          return [] if ids.empty? && !has_query_conditions

          begin
            # Prepare parameters for the bulk call
            # Format IDs based on the foreign key column type
            formatted_ids = ids.map do |id|
              AtharRpc::ActiveRpc.format_foreign_key_value(self, options[:foreign_key], id)
            end
            params = { ids: formatted_ids }
            Rails.logger.debug("[ActiveRpc] gRPC params prepared with #{formatted_ids.size} IDs") if defined?(Rails)

            # Add search parameters if provided
            params[:q] = query_options[:q] if query_options[:q].present?

            # Add ActiveRecord-style where filters if provided
            params[:where] = query_options[:where] if query_options[:where].present?

            # Add scope parameters if provided
            if query_options[:scope].present?
              # Serialize entire scope arguments as single JSON payload
              # This ensures consistent handling of all argument types (arrays, hashes, objects, etc.)
              params[:scope] = { 'args' => query_options[:scope].to_json }
              if defined?(Rails)
                Rails.logger.debug("[ActiveRpc] Serialized scope args as JSON: #{query_options[:scope].keys}")
              end
            end

            # Add include parameters if provided
            params[:include] = query_options[:include] if query_options[:include].present?

            # Add pagination parameters
            if query_options[:page].present? && query_options[:per_page].present?
              params[:page] = query_options[:page]
              params[:per_page] = query_options[:per_page]
              if defined?(Rails)
                Rails.logger.debug("[ActiveRpc] Using provided pagination: page=#{params[:page]}, per_page=#{params[:per_page]}")
              end
            elsif defined?(Rails)
              # For bulk operations, don't set per_page to get ALL results
              if defined?(Rails)
                Rails.logger.debug('[ActiveRpc] Bulk operation without pagination limit - fetching all results')
              end
            end

            # Add sort parameters if provided
            params[:sort] = query_options[:sort] if query_options[:sort].present?

            # Call the List method on the gRPC service
            # Add field tracking metadata to help with protobuf field presence
            params_with_fields = params.to_h
            params_with_fields[:_fields] = AtharRpc::ActiveRpc._active_rpc_extract_field_paths(params)

            Rails.logger.debug("[ActiveRpc] Making gRPC call: List#{resource}s") if defined?(Rails)

            begin
              response = grpc_client.call("List#{resource}s", params_with_fields)

              if response.nil?
                Rails.logger.error('[ActiveRpc] gRPC response is nil') if defined?(Rails)
              else
                item_count = if response&.message.respond_to?(:users)
                               response.message.users.size
                             elsif response&.message.respond_to?(:items)
                               response.message.items.size
                             else
                               0
                             end
                if defined?(Rails)
                  Rails.logger.debug("[ActiveRpc] gRPC call completed successfully, received #{item_count} items")
                end
              end
            rescue StandardError => e
              Rails.logger.error("[ActiveRpc] gRPC call failed: #{e.class}: #{e.message}") if defined?(Rails)
              raise e
            end

            # Return the items from the response
            result = if response&.message&.respond_to?(:users)
                       response.message.users
                     elsif response&.message&.respond_to?(:items)
                       response.message.items
                     else
                       Rails.logger.warn('[ActiveRpc] No users or items found in gRPC response') if defined?(Rails)
                       []
                     end

            Rails.logger.debug("[ActiveRpc] Returning #{result.size} items from gRPC call") if defined?(Rails)
            result
          rescue StandardError => e
            Rails.logger.error("Failed to fetch bulk #{resource} data for ids=#{ids}: #{e.message}")
            []
          end
        end
      end
    end

    # Module-level method to extract all field paths from a nested hash structure
    # Examples:
    #   {id: "22", user_roles_list: []} -> ["id", "user_roles_list"]
    #   {id: "22", avatar_attributes: {filename: "test.jpg"}} -> ["id", "avatar_attributes", "avatar_attributes.filename"]
    #   {user_roles_list: [{id: "1", role_id: "admin"}]} -> ["user_roles_list", "user_roles_list[0]", "user_roles_list[0].id", "user_roles_list[0].role_id"]
    def self._active_rpc_extract_field_paths(params, prefix = '')
      field_paths = []

      params.each do |key, value|
        # Skip internal fields (starting with _)
        next if key.to_s.start_with?('_')

        current_path = prefix.empty? ? key.to_s : "#{prefix}.#{key}"

        # Always include the current field path
        field_paths << current_path

        # Recursively handle nested hashes
        field_paths.concat(_active_rpc_extract_field_paths(value, current_path)) if value.is_a?(Hash) && !value.empty?

        # Handle arrays with hash elements (like user_roles_list)
        next unless value.is_a?(Array)

        # Include the array field itself even if empty
        value.each_with_index do |item, index|
          next unless item.is_a?(Hash)

          item_path = "#{current_path}[#{index}]"
          field_paths << item_path
          field_paths.concat(_active_rpc_extract_field_paths(item, item_path))
        end
      end

      field_paths
    end

    # Helper method to format foreign key values based on database column type
    # This ensures compatibility between ActiveRecord column types and protobuf field types
    def self.format_foreign_key_value(model_class, foreign_key, value)
      return nil if value.blank?

      # Get the column type from the model's database schema
      column = model_class.columns_hash[foreign_key.to_s]

      if %i[integer bigint].include?(column&.type)
        # Send as integer for integer/bigint columns to match protobuf int64 fields
        value.to_i
      else
        # Send as string for all other types (string, uuid, etc.)
        value.to_s
      end
    end
  end
end
