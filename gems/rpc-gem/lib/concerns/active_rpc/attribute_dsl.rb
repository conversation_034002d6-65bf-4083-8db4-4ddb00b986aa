# frozen_string_literal: true

module AtharRpc
  module ActiveRpc
    # DSL class for defining RPC attributes
    class AttributeDsl
      def initialize(config)
        @config = config
      end

      # Define an RPC attribute with type and options
      # @param name [Symbol, String] The attribute name
      # @param type [Symbol] The attribute type (:string, :integer, :float, :boolean, :json, etc.)
      # @param options [Hash] Options for the attribute
      # @option options [Object] :default The default value for the attribute
      def define_rpc_attribute(name, type, options = {})
        # Add the attribute to the list
        @config.attributes << name.to_sym

        # Store the type and default value
        @config.attribute_types[name.to_sym] = type
        @config.attribute_defaults[name.to_sym] = options[:default] if options.key?(:default)
      end

      # Alias for define_rpc_attribute for a more concise syntax
      alias_method :attribute, :define_rpc_attribute

      # Define query configuration for the resource
      # @param options [Hash] Query configuration options
      # @option options [Array<Symbol>] :searchable Attributes that can be searched
      # @option options [Array<Symbol>] :filterable Attributes that can be filtered
      # @option options [Array<Symbol>] :sortable Attributes that can be sorted
      # @option options [Array<Symbol>] :includable Associations that can be included
      def query_config(options = {})
        @config.query_config = options
      end

      # Define a scope that can be applied to the resource
      # @param name [Symbol] The scope name
      # @param description [String] Description of the scope
      def scope(name, description = nil)
        @config.scopes ||= {}
        @config.scopes[name] = { description: description }
      end

      # Define where_rpc condition mappings
      # @param mappings [Hash] Mapping from where conditions to scope names
      # @example
      #   where_rpc_mapping(
      #     'user_roles.project_id' => 'in_project',
      #     'user_roles.role_id' => 'with_role'
      #   )
      def where_rpc_mapping(mappings = {})
        @config.where_rpc_mappings ||= {}
        @config.where_rpc_mappings.merge!(mappings)
      end
    end
  end
end
