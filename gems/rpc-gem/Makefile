# Paths
PROTO_SRC = protos
RUBY_OUT = lib/athar_rpc_services

# gRPC Tools
PROTOC = bundle exec grpc_tools_ruby_protoc

# Find all .proto files
PROTO_FILES = $(shell find $(PROTO_SRC) -name "*.proto")

# Default: Compile everything
all: clean generate build

## 1️⃣ Compile gRPC Ruby Files
generate:
	@echo "🔧 Generating gRPC Ruby files..."
	$(PROTOC) -I $(PROTO_SRC) \
		--ruby_out=$(RUBY_OUT) \
		--grpc_out=$(RUBY_OUT) \
		$(PROTO_FILES)

## 2️⃣ Build Ruby Gem (Requires generate step)
build: generate
	@echo "📦 Building Ruby Gem..."
	gem build athar_rpc.gemspec

## 3️⃣ Install the Gem Locally (Requires build step)
install: build
	@echo "🚀 Installing athar_rpc locally..."
	gem install athar_rpc-0.1.0.gem

## 4️⃣ Push the Gem (Requires build step)
push: build
	@echo "🚀 Pushing gem to private repository..."
	gem push athar_rpc-0.1.0.gem --host https://gem.fury.io/athar/

## 5️⃣ Clean Generated Files
clean:
	@echo "🧹 Cleaning generated files..."
	rm -rf $(RUBY_OUT)
	rm -f *.gem

## 6️⃣ Help
help:
	@echo "Available Commands:"
	@echo "  make generate   - Compile .proto files to Ruby"
	@echo "  make build      - Build the Ruby gem"
	@echo "  make install    - Install gem locally"
	@echo "  make push       - Push the gem to private repo"
	@echo "  make clean      - Remove generated files"
