# Image Upload Guide for Athar RPC

This guide explains how to use the image upload functionality in the Athar RPC service.

## Overview

The Users service now supports uploading avatar images directly through the `CreateUser` and `UpdateUser` RPC methods. This allows clients to upload binary image data as part of user creation or updates, which will then be stored using Active Storage in the Rails application.

## Proto Definition

The proto definition has been updated to include image upload fields:

```protobuf
message UpdateUserRequest {
  string id = 1; // ID of the user to update
  string name = 2; // Updated name (optional)
  string email = 3; // Updated email (optional)
  string status = 4; // Updated status (optional)
  string password = 5; // Updated password (optional)
  bytes avatar_image = 6; // Binary image data for avatar (optional)
  string avatar_filename = 7; // Original filename with extension (optional)
  string avatar_content_type = 8; // MIME type of the image (optional)
  // roles and permissions are not editable
}

message CreateUserRequest {
  string name = 1; // User name (required)
  string email = 2; // User email (required)
  string status = 3; // Status (optional)
  string password = 4; // Password (required)
  string avatar = 5; // Avatar URL (optional, deprecated - use avatar_image instead)
  bytes avatar_image = 6; // Binary image data for avatar (optional)
  string avatar_filename = 7; // Original filename with extension (optional)
  string avatar_content_type = 8; // MIME type of the image (optional)
  // roles and permissions are not modifiable
}
```

## Server-Side Implementation

The server-side implementation handles the image upload process in both the `create_user` and `update_user` methods:

1. It extracts the image data, filename, and content type from the request
2. Directly attaches the image data to the user using Active Storage with StringIO
3. Returns the URL to the uploaded image in the response

### Active Storage Setup

The User model already has Active Storage configured with MinIO (S3-compatible storage):

```ruby
# In the User model
class User < ApplicationRecord
  has_one_attached :avatar

  # Custom method to get the avatar URL from MinIO
  def avatar_url
    avatar.cdn_url
  end

  # Custom method to set the avatar from a URL
  def avatar_url=(url)
    return unless url.present?
    self.avatar.attach(io: URI.open(url), filename: File.basename(URI.parse(url).path))
  end

  # Method to convert the user to a gRPC response
  def to_rpc_response
    ::Core::UserResponse.new(
      id: id.to_s,
      name: name,
      email: email,
      status: status,
      avatar_url: avatar.attached? ? avatar.cdn_url : nil,
      roles_list: roles.map(&:name),
      permissions_list: permissions.map(&:to_s)
    )
  end
end
```

The application is configured to use MinIO for storage in the development environment:

```ruby
# In config/environments/development.rb
config.active_storage.service = :minio
```

And the MinIO configuration is defined in `config/storage.yml`:

```yaml
minio:
  service: S3
  access_key_id: <%= ENV['MINIO_ACCESS_KEY'] %>
  secret_access_key: <%= ENV['MINIO_SECRET_KEY'] %>
  region: 'us-east-1'
  endpoint: <%= ENV['MINIO_ENDPOINT'] || 'http://minio:9000' %>
  bucket: <%= ENV['MINIO_BUCKET'] %>
  force_path_style: true
  public: true
  upload:
    acl: "public-read"
```

## Client-Side Implementation

### Creating a User with an Avatar

```ruby
require 'athar_rpc_services/core/users_services_pb'

# Get the client
client = AtharRpc::GrpcClientFactory.instance.client_for("core", "Users")

# Read the image file
image_path = '/path/to/avatar.jpg'
image_data = File.binread(image_path)
filename = File.basename(image_path)
content_type = 'image/jpeg'

# Create the request
request = Core::CreateUserRequest.new(
  name: 'John Doe',
  email: '<EMAIL>',
  password: 'secure_password',
  status: 'active',
  avatar_image: image_data,
  avatar_filename: filename,
  avatar_content_type: content_type
)

# Make the RPC call
response = client.call(:CreateUser, request)

# Handle the response
user = response.message
puts "User created with ID: #{user.id}"
puts "Avatar URL: #{user.avatar_url}"
```

### Updating a User's Avatar

```ruby
require 'athar_rpc_services/core/users_services_pb'

# Get the client
client = AtharRpc::GrpcClientFactory.instance.client_for("core", "Users")

# Read the image file
image_path = '/path/to/new_avatar.png'
image_data = File.binread(image_path)
filename = File.basename(image_path)
content_type = 'image/png'

# Create the request
request = Core::UpdateUserRequest.new(
  id: 'user123',
  avatar_image: image_data,
  avatar_filename: filename,
  avatar_content_type: content_type
)

# Make the RPC call
response = client.call(:UpdateUser, request)

# Handle the response
user = response.message
puts "User updated successfully"
puts "New avatar URL: #{user.avatar_url}"
```

### Using with ActiveRpc

If you're using the `ActiveRpc` concern, you can add methods to handle avatar uploads:

```ruby
class Employee < ApplicationRecord
  include AtharRpc::ActiveRpc

  active_rpc :core, :User, [:name, :email, :avatar_url], foreign_key: :user_id

  # Add a method to update avatar
  def update_avatar(image_path)
    # Read the image file
    image_data = File.binread(image_path)
    filename = File.basename(image_path)
    content_type = get_content_type(filename)

    # Make the RPC call
    response = call_user_rpc(:UpdateUser,
      id: user_id,
      avatar_image: image_data,
      avatar_filename: filename,
      avatar_content_type: content_type
    )

    # If successful, reload the user data to get the updated avatar_url
    reload_user if response.message.present?

    # Return the response
    response.message
  end

  private

  def get_content_type(filename)
    # Simple content type detection based on file extension
    extension = File.extname(filename).downcase
    case extension
    when '.jpg', '.jpeg'
      'image/jpeg'
    when '.png'
      'image/png'
    when '.gif'
      'image/gif'
    when '.webp'
      'image/webp'
    else
      'application/octet-stream'
    end
  end
end
```

### Using with a Rails Form

In a Rails application, you can use a form to upload avatars:

```ruby
# In your controller
def update_avatar
  if params[:avatar].present?
    # Get the uploaded file
    uploaded_file = params[:avatar]

    # Read the file data
    image_data = uploaded_file.read
    filename = uploaded_file.original_filename
    content_type = uploaded_file.content_type

    # Create the employee object
    employee = Employee.find(params[:id])

    # Make the RPC call
    response = employee.call_user_rpc(:UpdateUser,
      id: employee.user_id,
      avatar_image: image_data,
      avatar_filename: filename,
      avatar_content_type: content_type
    )

    # Handle the response
    if response.message.present?
      # Reload the employee to get the updated avatar_url
      employee.reload_user
      redirect_to employee_path(employee), notice: "Avatar uploaded successfully!"
    else
      redirect_to employee_path(employee), alert: "Failed to upload avatar"
    end
  else
    redirect_to employee_path(employee), alert: "No avatar file selected"
  end
end
```

## Limitations and Considerations

1. **File Size**: Be mindful of the file size when uploading images through gRPC. Large files may cause performance issues or exceed message size limits.

2. **Image Processing**: Consider implementing image processing on the server side to resize and optimize uploaded images.

3. **Error Handling**: Implement proper error handling to deal with various failure scenarios, such as invalid image formats or network issues.

4. **Security**: Validate uploaded files to ensure they are legitimate images and not malicious files.

5. **Performance**: For large files or high-volume uploads, consider implementing client-side image resizing before sending the data.

## Implementation Details

The controller implementation uses `StringIO` to directly attach the binary image data to Active Storage without creating temporary files:

```ruby
# Handle avatar image upload if provided
if request_payload.avatar_image.present?
  # Get filename and content type from the request
  filename = request_payload.avatar_filename.presence || 'avatar.jpg'
  content_type = request_payload.avatar_content_type.presence || detect_content_type(filename)

  # Directly attach the image data to Active Storage
  user.avatar.attach(
    io: StringIO.new(request_payload.avatar_image),
    filename: filename,
    content_type: content_type
  )
end
```

This approach is more efficient than creating temporary files because:

1. It avoids disk I/O operations
2. No need to clean up temporary files
3. Reduces the risk of leaving orphaned temporary files
4. Simplifies the code

## Conclusion

By integrating image upload functionality directly into the `CreateUser` and `UpdateUser` RPC methods, we've simplified the API and made it more intuitive to use. This approach allows clients to upload avatar images as part of the normal user creation and update process, without needing a separate API call.
