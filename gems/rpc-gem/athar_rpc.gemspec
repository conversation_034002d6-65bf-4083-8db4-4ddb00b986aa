Gem::Specification.new do |spec|
  spec.name = 'athar_rpc'
  spec.version = '0.4.2'
  spec.summary = "Athar's shared gRPC service definitions"
  spec.description = 'Contains Ruby gRPC service definitions and message structures for Athar microservices.'
  spec.authors = ['<PERSON><PERSON><PERSON>']
  spec.email = ['<EMAIL>']
  spec.files = Dir['lib/**/*.rb', 'lib/**/*.erb', 'protos/**/*']
  spec.require_paths = ['lib']
  spec.homepage = 'https://github.com/athar-association/rpc-protos'
  spec.license = 'MIT'

  spec.add_dependency 'activesupport', '>= 6.0'
  spec.add_dependency 'dry-configurable', '~> 1.3.0'
  spec.add_dependency 'gruf', '~> 2.21'
  spec.add_dependency 'singleton'

  spec.add_development_dependency 'activerecord', '~> 7.0'
  spec.add_development_dependency 'rails'
  spec.add_development_dependency 'rake'
  spec.add_development_dependency 'rspec'
  spec.add_development_dependency 'sqlite3'
end
