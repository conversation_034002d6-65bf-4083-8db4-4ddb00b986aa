require 'athar_rpc_services/core/users_services_pb'
require 'base64'

# Example of how to use the UploadAvatar RPC method
class AvatarUploader
  def initialize(client = nil)
    # Initialize the gRPC client
    @client = client || create_client
  end

  def upload_avatar(user_id, image_path)
    # Validate inputs
    raise ArgumentError, "User ID is required" if user_id.nil? || user_id.empty?
    raise ArgumentError, "Image path is required" if image_path.nil? || image_path.empty?
    raise ArgumentError, "Image file does not exist" unless File.exist?(image_path)

    # Read the image file
    image_data = File.binread(image_path)
    
    # Get filename and content type
    filename = File.basename(image_path)
    content_type = get_content_type(filename)

    # Create the request
    request = Core::UploadAvatarRequest.new(
      user_id: user_id,
      image_data: image_data,
      filename: filename,
      content_type: content_type
    )

    # Make the RPC call
    response = @client.call(:UploadAvatar, request)

    # Return the response
    response.message
  end

  private

  def create_client
    # Get the client from the factory
    AtharRpc::GrpcClientFactory.instance.client_for("core", "Users")
  end

  def get_content_type(filename)
    # Simple content type detection based on file extension
    extension = File.extname(filename).downcase
    case extension
    when '.jpg', '.jpeg'
      'image/jpeg'
    when '.png'
      'image/png'
    when '.gif'
      'image/gif'
    when '.webp'
      'image/webp'
    else
      'application/octet-stream'
    end
  end
end

# Example usage:
# 
# # Initialize the uploader
# uploader = AvatarUploader.new
# 
# # Upload an avatar for a user
# begin
#   response = uploader.upload_avatar('user123', '/path/to/avatar.jpg')
#   
#   if response.success
#     puts "Avatar uploaded successfully!"
#     puts "Avatar URL: #{response.avatar_url}"
#   else
#     puts "Failed to upload avatar: #{response.message}"
#   end
# rescue StandardError => e
#   puts "Error: #{e.message}"
# end
