require 'athar_rpc_services/core/users_services_pb'

# Example of how to use the image upload functionality with CreateUser and UpdateUser
class UserAvatarExample
  def initialize(client = nil)
    # Initialize the gRPC client
    @client = client || create_client
  end

  # Create a new user with an avatar
  def create_user_with_avatar(name, email, password, image_path)
    # Validate inputs
    raise ArgumentError, "Name is required" if name.nil? || name.empty?
    raise ArgumentError, "Email is required" if email.nil? || email.empty?
    raise ArgumentError, "Password is required" if password.nil? || password.empty?

    # Create the request
    request = Core::CreateUserRequest.new(
      name: name,
      email: email,
      password: password,
      status: 'active'
    )

    # Add avatar image if provided
    if image_path && File.exist?(image_path)
      # Read the image file
      image_data = File.binread(image_path)
      filename = File.basename(image_path)
      content_type = get_content_type(filename)

      # Add image data to the request
      request.avatar_image = image_data
      request.avatar_filename = filename
      request.avatar_content_type = content_type
    end

    # Make the RPC call
    response = @client.call(:CreateUser, request)

    # Return the response
    response.message
  end

  # Update an existing user's avatar
  def update_user_avatar(user_id, image_path)
    # Validate inputs
    raise ArgumentError, "User ID is required" if user_id.nil? || user_id.empty?
    raise ArgumentError, "Image path is required" if image_path.nil? || image_path.empty?
    raise ArgumentError, "Image file does not exist" unless File.exist?(image_path)

    # Read the image file
    image_data = File.binread(image_path)
    filename = File.basename(image_path)
    content_type = get_content_type(filename)

    # Create the request
    request = Core::UpdateUserRequest.new(
      id: user_id,
      avatar_image: image_data,
      avatar_filename: filename,
      avatar_content_type: content_type
    )

    # Make the RPC call
    response = @client.call(:UpdateUser, request)

    # Return the response
    response.message
  end

  private

  def create_client
    # Get the client from the factory
    AtharRpc::GrpcClientFactory.instance.client_for("core", "Users")
  end

  def get_content_type(filename)
    # Simple content type detection based on file extension
    extension = File.extname(filename).downcase
    case extension
    when '.jpg', '.jpeg'
      'image/jpeg'
    when '.png'
      'image/png'
    when '.gif'
      'image/gif'
    when '.webp'
      'image/webp'
    else
      'application/octet-stream'
    end
  end
end

# Example usage:
#
# # Initialize the client
# client = UserAvatarExample.new
#
# # Create a new user with an avatar
# begin
#   user = client.create_user_with_avatar(
#     'John Doe',
#     '<EMAIL>',
#     'secure_password',
#     '/path/to/avatar.jpg'
#   )
#
#   puts "User created successfully!"
#   puts "User ID: #{user.id}"
#   puts "Avatar URL: #{user.avatar_url}"
# rescue StandardError => e
#   puts "Error creating user: #{e.message}"
# end
#
# # Update an existing user's avatar
# begin
#   user = client.update_user_avatar('user123', '/path/to/new_avatar.png')
#
#   puts "User updated successfully!"
#   puts "New avatar URL: #{user.avatar_url}"
# rescue StandardError => e
#   puts "Error updating user avatar: #{e.message}"
# end
#
# # Note: In the actual implementation, the User model has a custom avatar_url method
# # that returns avatar.cdn_url for MinIO storage
