PATH
  remote: .
  specs:
    athar_rpc (0.4.2)
      activesupport (>= 6.0)
      dry-configurable (~> 1.3.0)
      gruf (~> 2.21)
      singleton

GEM
  remote: https://rubygems.org/
  specs:
    actioncable (7.2.2.1)
      actionpack (= 7.2.2.1)
      activesupport (= 7.2.2.1)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (7.2.2.1)
      actionpack (= 7.2.2.1)
      activejob (= 7.2.2.1)
      activerecord (= 7.2.2.1)
      activestorage (= 7.2.2.1)
      activesupport (= 7.2.2.1)
      mail (>= 2.8.0)
    actionmailer (7.2.2.1)
      actionpack (= 7.2.2.1)
      actionview (= 7.2.2.1)
      activejob (= 7.2.2.1)
      activesupport (= 7.2.2.1)
      mail (>= 2.8.0)
      rails-dom-testing (~> 2.2)
    actionpack (7.2.2.1)
      actionview (= 7.2.2.1)
      activesupport (= 7.2.2.1)
      nokogiri (>= 1.8.5)
      racc
      rack (>= 2.2.4, < 3.2)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
      useragent (~> 0.16)
    actiontext (7.2.2.1)
      actionpack (= 7.2.2.1)
      activerecord (= 7.2.2.1)
      activestorage (= 7.2.2.1)
      activesupport (= 7.2.2.1)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (7.2.2.1)
      activesupport (= 7.2.2.1)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    activejob (7.2.2.1)
      activesupport (= 7.2.2.1)
      globalid (>= 0.3.6)
    activemodel (7.2.2.1)
      activesupport (= 7.2.2.1)
    activerecord (7.2.2.1)
      activemodel (= 7.2.2.1)
      activesupport (= 7.2.2.1)
      timeout (>= 0.4.0)
    activestorage (7.2.2.1)
      actionpack (= 7.2.2.1)
      activejob (= 7.2.2.1)
      activerecord (= 7.2.2.1)
      activesupport (= 7.2.2.1)
      marcel (~> 1.0)
    activesupport (7.2.2.1)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
    ast (2.4.3)
    base64 (0.2.0)
    benchmark (0.4.0)
    bigdecimal (3.1.9)
    builder (3.3.0)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.3)
    crass (1.0.6)
    date (3.4.1)
    diff-lcs (1.6.2)
    drb (2.2.1)
    dry-configurable (1.3.0)
      dry-core (~> 1.1)
      zeitwerk (~> 2.6)
    dry-core (1.1.0)
      concurrent-ruby (~> 1.0)
      logger
      zeitwerk (~> 2.6)
    erubi (1.13.1)
    generator_spec (0.10.0)
      activesupport (>= 3.0.0)
      railties (>= 3.0.0)
    globalid (1.2.1)
      activesupport (>= 6.1)
    google-protobuf (4.30.2)
      bigdecimal
      rake (>= 13)
    google-protobuf (4.30.2-arm64-darwin)
      bigdecimal
      rake (>= 13)
    googleapis-common-protos-types (1.20.0)
      google-protobuf (>= 3.18, < 5.a)
    grpc (1.72.0)
      google-protobuf (>= 3.25, < 5.0)
      googleapis-common-protos-types (~> 1.0)
    grpc (1.72.0-arm64-darwin)
      google-protobuf (>= 3.25, < 5.0)
      googleapis-common-protos-types (~> 1.0)
    grpc-tools (1.72.0)
    gruf (2.21.0)
      activesupport (> 4)
      concurrent-ruby (> 1)
      grpc (~> 1.10)
      grpc-tools (~> 1.10)
      json (>= 2.3)
      slop (>= 4.6)
      zeitwerk (>= 2)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    io-console (0.8.0)
    irb (1.15.2)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    json (2.12.0)
    language_server-protocol (3.17.0.5)
    lint_roller (1.1.0)
    logger (1.7.0)
    loofah (2.24.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    mini_mime (1.1.5)
    mini_portile2 (2.8.9)
    minitest (5.25.5)
    net-imap (0.5.8)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.1)
      net-protocol
    nio4r (2.7.4)
    nokogiri (1.18.8)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    nokogiri (1.18.8-arm64-darwin)
      racc (~> 1.4)
    ostruct (0.6.1)
    parallel (1.27.0)
    parser (3.3.8.0)
      ast (~> 2.4.1)
      racc
    pp (0.6.2)
      prettyprint
    prettyprint (0.2.0)
    prism (1.4.0)
    psych (5.2.6)
      date
      stringio
    racc (1.8.1)
    rack (3.1.14)
    rack-session (2.1.1)
      base64 (>= 0.1.0)
      rack (>= 3.0.0)
    rack-test (2.2.0)
      rack (>= 1.3)
    rackup (2.2.1)
      rack (>= 3)
    rails (7.2.2.1)
      actioncable (= 7.2.2.1)
      actionmailbox (= 7.2.2.1)
      actionmailer (= 7.2.2.1)
      actionpack (= 7.2.2.1)
      actiontext (= 7.2.2.1)
      actionview (= 7.2.2.1)
      activejob (= 7.2.2.1)
      activemodel (= 7.2.2.1)
      activerecord (= 7.2.2.1)
      activestorage (= 7.2.2.1)
      activesupport (= 7.2.2.1)
      bundler (>= 1.15.0)
      railties (= 7.2.2.1)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    railties (7.2.2.1)
      actionpack (= 7.2.2.1)
      activesupport (= 7.2.2.1)
      irb (~> 1.13)
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (13.2.1)
    rdoc (6.13.1)
      psych (>= 4.0.0)
    regexp_parser (2.10.0)
    reline (0.6.1)
      io-console (~> 0.5)
    rspec (3.13.0)
      rspec-core (~> 3.13.0)
      rspec-expectations (~> 3.13.0)
      rspec-mocks (~> 3.13.0)
    rspec-core (3.13.3)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.4)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.4)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-support (3.13.3)
    rubocop (1.77.0)
      json (~> 2.3)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.45.1, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.45.1)
      parser (>= 3.3.7.2)
      prism (~> 1.4)
    ruby-progressbar (1.13.0)
    securerandom (0.4.1)
    singleton (0.3.0)
    slop (4.10.1)
    sqlite3 (2.6.0)
      mini_portile2 (~> 2.8.0)
    sqlite3 (2.6.0-arm64-darwin)
    stringio (3.1.7)
    thor (1.3.2)
    timeout (0.4.3)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unicode-display_width (3.1.4)
      unicode-emoji (~> 4.0, >= 4.0.4)
    unicode-emoji (4.0.4)
    useragent (0.16.11)
    websocket-driver (0.7.7)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    zeitwerk (2.7.2)

PLATFORMS
  arm64-darwin-24
  ruby

DEPENDENCIES
  activerecord (~> 7.0)
  athar_rpc!
  generator_spec
  grpc-tools (~> 1.58)
  ostruct
  rails
  rake
  rspec
  rubocop
  sqlite3 (~> 2.6)

BUNDLED WITH
   2.6.5
