# Distributed Testing Plan for Project-Based Permissions

## Overview

This document outlines a comprehensive testing strategy for the project-based permissions system, considering the distributed nature of the Athar EMS architecture.

## 🏗️ System Architecture Context

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│    Core     │    │   People    │    │Case Manager │    │   Procure   │
│  Service    │    │  Service    │    │  Service    │    │  Service    │
│             │    │             │    │             │    │             │
│ - Auth      │    │ - Employee  │    │ - Cases     │    │ - Purchase  │
│ - Users     │    │ - Roles     │    │ - Benefits  │    │ - Vendors   │
│ - Projects  │    │ - Export    │    │ - Reports   │    │ - Orders    │
│ - To<PERSON><PERSON>    │    │             │    │             │    │             │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │                   │
       └───────────────────┼───────────────────┼───────────────────┘
                           │                   │
                    ┌─────────────┐    ┌─────────────┐
                    │   Auth Gem  │    │  Frontend   │
                    │             │    │             │
                    │ - Models    │    │ - React     │
                    │ - Auth      │    │ - Perms     │
                    │ - Tokens    │    │ - UI        │
                    └─────────────┘    └─────────────┘
```

## 🧪 Testing Phases

### Phase 1: Foundation Testing (Unit & Integration)

#### 1.1 Auth Gem Testing

```bash
# Test the core authorization engine
cd gems/auth-gem
bundle exec rspec

# Test specific components
bundle exec rspec spec/models/
bundle exec rspec spec/concerns/
bundle exec rspec spec/token_compression_spec.rb
```

#### 1.2 Core Service Testing

```bash
# Test enhanced token generation
cd services/core
bundle exec rspec spec/models/user_token_generation_spec.rb

# Test session controller
bundle exec rspec spec/controllers/users/sessions_controller_spec.rb
```

#### 1.3 People Service Testing

```bash
# Test migrated models
cd services/people
bundle exec rspec spec/models/employee_spec.rb

# Test type registrations
ruby test_migration_validation.rb
```

#### 1.4 CM Service Testing

```bash
# Test enhanced controllers
cd services/case-manager
bundle exec rspec spec/controllers/api/cases_controller_spec.rb
bundle exec rspec spec/controllers/api/permissions_controller_spec.rb
```

### Phase 2: Service Integration Testing

#### 2.1 Token Flow Testing

Test the complete token generation → validation → usage flow:

```bash
# 1. Generate token in Core service
curl -X POST http://localhost:3001/users/sign_in \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password", "scope": "cm", "project_id": 2}'

# 2. Use token in CM service
curl -X GET http://localhost:3003/api/cases \
  -H "Authorization: Bearer <token_from_step_1>"

# 3. Check permissions endpoint
curl -X GET http://localhost:3003/api/permissions \
  -H "Authorization: Bearer <token_from_step_1>"
```

#### 2.2 Cross-Service Authorization Testing

Test project-based access across services:

```bash
# Test People service with CM token
curl -X GET http://localhost:3002/api/employees \
  -H "Authorization: Bearer <cm_token>"

# Test cross-project access
curl -X GET http://localhost:3003/api/cases \
  -H "Authorization: Bearer <multi_project_token>"
```

### Phase 3: End-to-End Distributed Testing

#### 3.1 Multi-Service Workflow Testing

Test complete user workflows across services:

1. **Case Manager Workflow**:

   - Login via Core service
   - Access employee data via People service
   - Create/manage cases via CM service
   - Generate reports

2. **HR Manager Workflow**:

   - Login with global access
   - Access all projects via People service
   - Supervise cases across projects via CM service

3. **Project Manager Workflow**:
   - Login with project-specific access
   - Manage team via People service
   - Oversee project cases via CM service

#### 3.2 Load & Performance Testing

Test system under realistic distributed load:

```bash
# Use Apache Bench or similar
ab -n 1000 -c 10 -H "Authorization: Bearer <token>" \
   http://localhost:3003/api/cases

# Test token compression benefits
# Compare compressed vs uncompressed token performance
```

## 🛠️ Testing Tools & Scripts

### Tool 1: Distributed Test Runner

```bash
#!/bin/bash
# distributed_test_runner.sh

echo "🧪 Starting Distributed Testing Suite"

# Phase 1: Unit Tests
echo "📋 Phase 1: Unit & Integration Tests"
cd gems/auth-gem && bundle exec rspec --format documentation
cd ../../services/core && bundle exec rspec spec/models/user_token_generation_spec.rb
cd ../people && ruby test_migration_validation.rb
cd ../case-manager && bundle exec rspec spec/controllers/api/

# Phase 2: Service Integration
echo "📋 Phase 2: Service Integration Tests"
./scripts/test_token_flow.sh
./scripts/test_cross_service_auth.sh

# Phase 3: E2E Testing
echo "📋 Phase 3: End-to-End Tests"
./scripts/test_user_workflows.sh

echo "✅ Distributed Testing Complete"
```

### Tool 2: Token Flow Validator

```bash
#!/bin/bash
# test_token_flow.sh

echo "🔐 Testing Token Generation → Validation → Usage Flow"

# 1. Generate token
echo "Step 1: Generating token..."
TOKEN_RESPONSE=$(curl -s -X POST http://localhost:3001/users/sign_in \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password", "scope": "cm", "project_id": 2}')

TOKEN=$(echo $TOKEN_RESPONSE | jq -r '.token')
echo "Token generated: ${TOKEN:0:50}..."

# 2. Validate token structure
echo "Step 2: Validating token structure..."
# Decode and check compressed structure
echo $TOKEN | base64 -d | jq '.'

# 3. Test CM service access
echo "Step 3: Testing CM service access..."
CASES_RESPONSE=$(curl -s -X GET http://localhost:3003/api/cases \
  -H "Authorization: Bearer $TOKEN")
echo "Cases response: $CASES_RESPONSE"

# 4. Test permissions endpoint
echo "Step 4: Testing permissions endpoint..."
PERMS_RESPONSE=$(curl -s -X GET http://localhost:3003/api/permissions \
  -H "Authorization: Bearer $TOKEN")
echo "Permissions response: $PERMS_RESPONSE"

echo "✅ Token flow test complete"
```

### Tool 3: Cross-Service Authorization Tester

```bash
#!/bin/bash
# test_cross_service_auth.sh

echo "🌐 Testing Cross-Service Authorization"

# Test different user types across services
declare -a USER_TYPES=("case_manager" "hr_manager" "project_manager")
declare -a SERVICES=("people:3002" "case-manager:3003")

for user_type in "${USER_TYPES[@]}"; do
  echo "Testing $user_type access..."

  # Generate token for user type
  TOKEN=$(generate_token_for_user_type $user_type)

  for service in "${SERVICES[@]}"; do
    IFS=':' read -r service_name port <<< "$service"
    echo "  Testing $service_name service..."

    response=$(curl -s -w "%{http_code}" -X GET \
      "http://localhost:$port/api/test_endpoint" \
      -H "Authorization: Bearer $TOKEN")

    echo "    Response: $response"
  done
done

echo "✅ Cross-service authorization test complete"
```

## 📊 Testing Scenarios

### Scenario 1: Project-Based Access Control

```yaml
Test: Case Manager Project Access
Given: User "<EMAIL>" with case_manager role in Project 2
When: Accessing cases via CM service
Then: Should only see cases from Project 2
And: Should not see cases from other projects
```

### Scenario 2: Cross-Project Access

```yaml
Test: HR Manager Cross-Project Access
Given: User "<EMAIL>" with hr_manager global role
When: Accessing any service
Then: Should see data from all projects
And: Should have elevated permissions
```

### Scenario 3: Token Compression Validation

```yaml
Test: Compressed Token Performance
Given: Standard vs compressed token
When: Making 1000 API requests
Then: Compressed tokens should be 31.2% smaller
And: Response times should be comparable or better
```

### Scenario 4: Service Failure Resilience

```yaml
Test: Core Service Unavailable
Given: Core service is down
When: CM service receives request with valid token
Then: Should continue working with cached token validation
And: Should gracefully handle token refresh failures
```

## 🔍 Monitoring & Validation

### Key Metrics to Track

1. **Token Size**: Verify 31.2% compression ratio
2. **Response Times**: Ensure no performance degradation
3. **Authorization Success Rate**: Track permission grants/denials
4. **Cross-Service Call Success**: Monitor inter-service communication
5. **Error Rates**: Track authentication/authorization failures

### Validation Checkpoints

- ✅ All unit tests pass
- ✅ Token generation works across all user types
- ✅ Project-based filtering works correctly
- ✅ Cross-service authorization works
- ✅ Permissions endpoint returns correct data
- ✅ No breaking changes in existing functionality
- ✅ Performance meets or exceeds baseline

## 🚀 Deployment Testing

### Staging Environment Testing

1. Deploy to staging with production-like data
2. Run full test suite against staging
3. Perform load testing
4. Validate monitoring and alerting

### Production Rollout Strategy

1. **Blue-Green Deployment**: Deploy to parallel environment
2. **Canary Testing**: Route 10% of traffic to new system
3. **Gradual Rollout**: Increase traffic percentage gradually
4. **Rollback Plan**: Ready to revert if issues detected

## 📋 Testing Checklist

- [ ] Auth gem unit tests pass
- [ ] Core service token generation tests pass
- [ ] People service migration validation passes
- [ ] CM service controller tests pass
- [ ] Token flow end-to-end test passes
- [ ] Cross-service authorization test passes
- [ ] Performance benchmarks meet targets
- [ ] Load testing completes successfully
- [ ] Security validation passes
- [ ] Backward compatibility confirmed
- [ ] Documentation updated
- [ ] Monitoring configured
- [ ] Rollback procedures tested

This comprehensive testing approach ensures the distributed system works correctly across all services while maintaining performance and security standards.
