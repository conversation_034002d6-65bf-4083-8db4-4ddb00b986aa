# Project-Based Permissions Implementation Plan

## Overview

This document outlines the implementation plan for project-based permissions across the Athar EMS system, enabling proper access control for project-based users while maintaining global user capabilities.

> **✅ UPDATE (July 2024)**: User class cleanup completed. All references now use `AtharAuth::Models::User` exclusively. The old `AtharAuth::User` class has been removed.

## Architectural Principle: Single Entry Point Token Expansion

**Core Principle**: All client-side token consumption should work with expanded/logical token data, not compressed token data directly.

### Token Flow Architecture

```
Compressed Token (Wire Format)     →     Single Entry Point     →     Logical Token (Client Format)
{ "s": 123, "u": { "n": "<PERSON>" } }  →   decoded_token method   →   { "user": { "id": 123, "name": "<PERSON>" } }
```

### Implementation Strategy

1. **Single Entry Point**: The `decoded_token` method in `AtharAuth::ControllerHelpers` serves as the architectural boundary
2. **Automatic Expansion**: Compressed tokens are expanded to logical format immediately upon decoding
3. **Client Isolation**: Models, controllers, and business logic never see compressed keys ("ss", "r", "u", "a")
4. **Performance**: Expansion happens once per request and is cached with `@decoded_token`

### Before/After Examples

**❌ Current Implementation (Mixed Approach):**

```ruby
# Some code uses JSONPath (good)
project_data = TokenCompression.dig_token(decoded_token, 'session.project')

# Other code directly accesses compressed keys (bad)
role_data = decoded_token&.dig('ss', 'r')
user_id = decoded_token["s"]
user_name = decoded_token.dig("u", "n")
```

**✅ Target Implementation (Single Entry Point):**

```ruby
# controller_helpers.rb - Single entry point
def decoded_token
  @decoded_token ||= begin
    compressed_token = AtharAuth.decode_token(bearer_token)
    TokenCompression.expand_token(compressed_token)  # Expand once
  end
end

# All client code uses logical paths
user_id = decoded_token["user"]["id"]
user_name = decoded_token["user"]["name"]
role_data = decoded_token["session"]["role"]
project_data = decoded_token["session"]["project"]
```

### Benefits

1. **Maintainability**: Compression changes don't ripple through the codebase
2. **Readability**: `token["user"]["name"]` is clearer than `token["u"]["n"]`
3. **Consistency**: All code follows the same pattern
4. **Separation of Concerns**: Only `TokenCompression` module knows about compressed format
5. **Performance**: Single expansion per request, cached automatically

## Current Problem

**Issue**: Project-based users (case managers, project managers, supervisors) cannot access CM system because the authorization system lacks project context support.

**Root Cause**: The athar_auth gem and subsystem authorization patterns don't support project-based access control.

## Updated Problem Analysis (December 2024)

**Current Issue**: Non-global users receive error "Project ID is required for non-global users" when attempting to login without explicitly providing a project_id parameter.

**Root Cause**: Token generation validation is too strict and doesn't properly handle default project logic for seamless user login experience.

**Additional Challenge**: Inconsistent project context handling between global users (no project) and project-based users (required project) creates complexity in authorization logic and frontend UX.

## Organization Project Solution

### Architectural Decision: Universal Project Context

**Solution**: Implement "Athar" project as default context for global users, ensuring all users operate within a project context for consistency.

**Benefits**:

- **Consistency**: All users have project context, eliminating special null-handling cases
- **Simplified Authorization**: Same authorization patterns work for global and project users
- **Better UX**: Consistent project display and switching interface for all users
- **Enhanced Security**: Every action has project context for auditing and compliance
- **Future-Proof**: Easy to add project-based features without breaking global user workflows

**Implementation Strategy**:

1. **Create "Athar" project** - Special project representing the organization itself
2. **Assign global users** to Athar project as their default project
3. **Update authorization logic** to handle Athar project appropriately
4. **Project switching** - Users switch projects by requesting new session tokens with different project_id

## Architecture Analysis

### Gap 0: Token Format Standardization

**Status**: ❌ **NEEDS STANDARDIZATION** - Inconsistent token formats across authentication flow

**Current Architecture**:

```ruby
# Main tokens use flat format
{
  sub: 9,
  token_type: "main",
  email: "<EMAIL>",
  name: "HR Manager User",
  exp: 1752725271
}

# Session tokens use compressed nested format
{
  s: 9,           # 'user.id' => 's'
  t: "session",   # 'token_type' => 't'
  u: {            # user context
    n: "HR Manager User",
    m: "<EMAIL>"
  },
  ss: {...},      # session context
  a: {...}        # access context
}
```

**Design Issue**: Mixed token formats create complexity in authentication logic and reduce maintainability.

**Required Standardization**:

1. **Unified Token Format**: All tokens should use compressed nested structure
2. **Consistent User Context**: Same user data structure across all token types
3. **Single Authentication Logic**: One expansion method for all tokens
4. **Maintainable Architecture**: Future token types automatically compatible

**Target Architecture**:

```ruby
# Standardized main tokens (compressed)
{
  s: 9,           # 'user.id' => 's'
  t: "main",      # 'token_type' => 't'
  u: {            # user context (consistent with session tokens)
    n: "HR Manager User",     # 'user.name' => 'u.n'
    m: "<EMAIL>"  # 'user.email' => 'u.m'
  },
  e: 1752725271   # 'expires.at' => 'e'
}

# Session tokens maintain existing compressed format
# Authentication logic works uniformly for all token types
```

### Gap 1: Core Service Token Generation Enhancement

**Status**: ❌ **NEEDS ENHANCEMENT** - Token generation requires standardization and enhanced validation

**Current Limitations**:

1. **Format Inconsistency**: Main and session tokens use different structures
2. **Limited Validation**: Insufficient system access validation during token generation
3. **Incomplete Context**: Session tokens need comprehensive project/role context

**Required Enhancements**:

1. **Standardized Main Token Generation**:

   ```ruby
   # Enhanced services/core/app/controllers/users/sessions_controller.rb
   def create
     main_token = AtharAuth.encode_token({
       s: user.id,           # Standardized user ID mapping
       t: "main",            # Consistent token type field
       u: {                  # Unified user context structure
         n: user.name,       # Compressed user name
         m: user.email       # Compressed user email
       },
       e: AtharAuth.main_token_expire_time.from_now.to_i
     })
   end
   ```

2. **Enhanced Session Token Generation**:

   - Comprehensive system access validation
   - Session+project specific token architecture
   - Optimized compressed token schema
   - Support for both global and project-based user patterns

3. **Unified Token Architecture**:
   - Consistent SHORTHAND usage across all token types
   - Standardized user context structure
   - Seamless authentication flow integration

### Gap 2: athar_auth Gem Lacks Project Support

**Status**: ❌ **NEEDS ENHANCEMENT** - athar_auth gem requires project-based authorization

**Current Limitation**:

```ruby
# Current athar_auth authorization (NO project support)
def authorize!(action, subject)
  return true if can?(action, subject)
  render_forbidden("You are not allowed to #{action} this #{subject}")
end

def can?(action, subject)
  current_permissions.include?("#{action}:#{subject}")
end
```

**What's Missing**:

```ruby
# What we NEED for project-based permissions
authorize!(:read, :case, project: current_project)  # ❌ NOT SUPPORTED!
authorize!(:update, @case)  # ❌ No instance-based project validation

# Project context methods missing
current_project_id    # ❌ NOT AVAILABLE
global_user?          # ❌ NOT AVAILABLE
user_can_access_project?(project)  # ❌ NOT AVAILABLE
```

**Required Enhancements**:

1. **Enhanced authorize! method** - Support both symbols and instances
2. **Project context support** - Handle project-based vs non-project resources
3. **Instance-based validation** - Automatic project_id checking for instances
4. **Session+project specific authorization** - Each token represents one system+project context
5. **Compressed token parsing** - Handle shorthand keys for distributed system efficiency
6. **Shared Project ActiveStruct model** in commons gem

## Enhanced athar_auth Gem Implementation

### 1. Enhanced Authorization Module

```ruby
# gems/auth-gem/lib/athar_auth/authorization.rb
module AtharAuth
  module Authorization
    # Enhanced authorize! with project context and instance support
    def authorize!(action, subject, options = {})
      return render_unauthorized("You must be signed in") unless user_signed_in?
      return true if can?(action, subject, options)

      subject_name = extract_subject_name(subject)
      render_forbidden("You are not allowed to #{action} this #{subject_name}")
      false
    end

    # Enhanced can? with automatic project validation
    def can?(action, subject, options = {})
      if subject.is_a?(Symbol) || subject.is_a?(String)
        # Symbol: explicit project context from caller
        can_general_action?(action, subject, options)
      else
        # Instance: automatic project validation via project_id
        can_instance_action?(action, subject, options)
      end
    end

    private

    def can_general_action?(action, subject, options = {})
      base = subject.to_s.underscore
      project = options[:project]

      # Check basic permission
      has_permission = current_permissions.include?("manage:#{base}") ||
                      current_permissions.include?("#{action}:#{base}")
      return false unless has_permission

      # Global users can do anything they have permissions for
      return true if global_user?

      # If project context provided, validate it
      if project.present?
        return user_can_access_project?(project)
      end

      # No project context = caller says it's not needed (AtharPeople)
      true
    end

    def can_instance_action?(action, instance, options = {})
      # Check general permission first
      subject_type = instance.class.name.underscore.to_sym
      return false unless can_general_action?(action, subject_type, options)

      # Automatic project validation using convention
      if instance.respond_to?(:project_id)
        return true if global_user?
        return user_can_access_project?(instance.project_id)
      end

      # No project_id = not project-based (AtharPeople), permission check was enough
      true
    end

    # Project access validation (compressed token support)
    def user_can_access_project?(project)
      return true if global_user?
      return false unless project

      project_id = project.respond_to?(:id) ? project.id : project.to_i
      accessible_project_ids.include?(project_id)
    end

    # Enhanced ActiveStruct instance methods - complete objects with all information
    # Uses expanded token data from single entry point
    def current_project
      @current_project ||= begin
        project_data = decoded_token.dig('session', 'project')
        AtharAuth::Models::Project.from_token_data(project_data)
      end
    end

    def current_role
      @current_role ||= begin
        role_data = decoded_token.dig('session', 'role')
        AtharAuth::Models::Role.from_token_data(role_data)
      end
    end

    def current_user
      @current_user ||= begin
        AtharAuth::Models::User.from_token_data(decoded_token)
      end
    end

    # Convenience methods for common checks (avoid fragmentation)
    def global_user?
      current_user&.global_user? || false
    end

    # Session+project specific access methods (using expanded token data)
    def accessible_project_ids
      @accessible_project_ids ||= begin
        if global_user?
          # Global user can access all projects in this system
          decoded_token.dig('access', 'projects')&.keys&.map(&:to_i) || []
        else
          # Project user: current project + other accessible projects
          current_id = current_project&.id
          other_projects = decoded_token.dig('access', 'other_projects') || []
          other_ids = other_projects.map { |p| p['id'] }

          ([current_id] + other_ids).compact.uniq
        end
      end
    end

    def accessible_projects_with_names
      @accessible_projects_with_names ||= begin
        if global_user?
          TokenCompression.dig_token(decoded_token, 'access.projects') || {}
        else
          result = {}

          # Add current project
          if current_project
            result[current_project.id] = current_project.name
          end

          # Add other projects
          other_projects = TokenCompression.dig_token(decoded_token, 'access.other_projects') || []
          other_projects.each do |project|
            result[project['id']] = project['n']  # 'n' from compressed format
          end

          result
        end
      end
    end

    def can_access_other_projects?
      if global_user?
        true
      else
        other_projects = TokenCompression.dig_token(decoded_token, 'access.other_projects') || []
        other_projects.any?
      end
    end

    def extract_subject_name(subject)
      if subject.is_a?(Symbol) || subject.is_a?(String)
        subject.to_s
      else
        subject.class.name.underscore
      end
    end

    # Automatic collection authorization and filtering
    def authorize_collection!(action, resource_class, options = {})
      # 1. Check if user has permission for this resource type
      return [] unless can?(action, resource_class.name.underscore.to_sym, options)

      # 2. Build filtered collection based on user type and resource
      build_authorized_collection(resource_class, options)
    end

    def build_authorized_collection(resource_class, options = {})
      if global_user?
        # Global users see everything
        resource_class.all
      else
        # Project-based users see filtered collections
        build_project_scoped_collection(resource_class, options)
      end
    end

    def build_project_scoped_collection(resource_class, options = {})
      if resource_class.respond_to?(:where) && resource_class.column_names.include?('project_id')
        # Resource has project_id - filter by accessible projects
        resource_class.where(project_id: accessible_project_ids)
      elsif resource_class.respond_to?(:accessible_to)
        # Resource has custom access scoping
        resource_class.accessible_to(current_user)
      else
        # No scoping available - return all (like AtharPeople)
        resource_class.all
      end
    end
  end
end
```

### 2. Method Call Style Resource Authorization (Following ActAsApprovable Pattern)

```ruby
# gems/auth-gem/lib/athar_auth/resource_authorization.rb
module AtharAuth
  module ResourceAuthorization
    extend ActiveSupport::Concern

    class_methods do
      # Method call style (like acts_as_approvable)
      def authorize_resources(options = {})
        # Store configuration
        class_attribute :authorization_config, default: options

        # Set up before_actions based on configuration
        setup_authorization_callbacks(options)
      end

      private

      def setup_authorization_callbacks(options)
        # Default actions that get auto-authorization
        auto_actions = options[:only] || [:index, :show, :create, :update, :destroy]
        collection_actions = options[:collection_actions] || [:index]
        skip_actions = options[:except] || []

        # Remove skipped actions
        auto_actions -= skip_actions
        collection_actions -= skip_actions

        # Set up callbacks
        before_action :authorize_and_load_collection!, only: collection_actions
        before_action :authorize_and_load_resource!, only: (auto_actions - collection_actions)
      end
    end

    private

    def authorize_and_load_collection!
      resource_class = controller_name.classify.constantize
      collection = authorize_collection!(:read, resource_class)

      # Set instance variable with resource name (like @cases, @employees)
      instance_variable_set("@#{controller_name}", collection)
    end

    def authorize_and_load_resource!
      resource_class = controller_name.classify.constantize
      resource = resource_class.find(params[:id])

      # Authorize the specific instance
      authorize!(action_name.to_sym, resource)

      # Set instance variable with singular resource name (like @case, @employee)
      instance_variable_set("@#{controller_name.singularize}", resource)
    end
  end
end
```

### 3. Unified ActiveStruct Models in Auth Gem (Migrated from AtharPeople)

```ruby
# gems/auth-gem/lib/athar_auth/models/project.rb
module AtharAuth
  module Models
    class Project < Athar::Commons::ActiveStruct::Base
      with_id(auto_generate: false)

      # Unified attributes from existing AtharPeople::Project + token enhancements
      attribute :id, :integer
      attribute :name, :string
      attribute :description, :string
      attribute :status, :string

      # Factory method to create from expanded token data
      def self.from_token_data(project_data)
        return nil unless project_data

        new(
          id: project_data['id'],
          name: project_data['name'],
          description: project_data['description'],
          status: project_data['status'] || 'active'
        )
      end

      # Factory method for backward compatibility with AtharPeople
      def self.from_grpc_data(grpc_data)
        new(
          id: grpc_data['id'],
          name: grpc_data['name'],
          description: grpc_data['description'],
          status: grpc_data['status'] || 'active'
        )
      end

      def active?
        status == 'active'
      end

      def to_s
        name
      end
    end
  end
end
```

```ruby
# gems/auth-gem/lib/athar_auth/models/role.rb
module AtharAuth
  module Models
    class Role < Athar::Commons::ActiveStruct::Base
      with_id(auto_generate: false)

      # Unified attributes from existing AtharPeople::Role + token enhancements
      attribute :name, :string
      attribute :level, :integer
      attribute :scope, :string  # 'global_role' or 'project_based'
      attribute :permissions, :array, default: []

      # Factory method to create from expanded token data
      def self.from_token_data(role_data)
        return nil unless role_data

        new(
          name: role_data['name'],
          level: role_data['level'],
          scope: role_data['scope'],
          permissions: [] # Permissions are stored separately in session context
        )
      end

      # Factory method for backward compatibility with AtharPeople
      def self.from_grpc_data(grpc_data)
        new(
          name: grpc_data['name'],
          global: grpc_data['global'] || false,
          level: grpc_data['level']
        )
      end

      def can?(action, resource)
        permissions.include?("#{action}:#{resource}") ||
        permissions.include?("manage:#{resource}")
      end

      def supervisor?
        name == 'supervisor'
      end

      def case_manager?
        name == 'case_manager'
      end

      def project_manager?
        name == 'project_manager'
      end

      def hr_manager?
        name == 'hr_manager'
      end

      def procurement_manager?
        name == 'procurement_manager'
      end

      def global_role?
        scope == 'global_role'
      end

      def project_based_role?
        scope == 'project_based'
      end

      def to_s
        name
      end
    end
  end
end
```

```ruby
# gems/auth-gem/lib/athar_auth/models/user.rb
module AtharAuth
  module Models
    class User < Athar::Commons::ActiveStruct::Base
      with_id(auto_generate: false)

      # Core user attributes (from token payload)
      attribute :id, :integer
      attribute :name, :string
      attribute :email, :string
      attribute :global, :boolean, default: false

      # Token context attributes (for service-specific usage)
      attribute :user_type, :string
      attribute :scope, :string
      attribute :permissions, :array, default: []

      # ✅ ActiveStruct associations - return rich objects
      belongs_to :role, class_name: 'AtharAuth::Models::Role', optional: true
      belongs_to :project, class_name: 'AtharAuth::Models::Project', optional: true

      # Factory method for token-based creation (receives expanded token data)
      def self.from_token_data(decoded_token)
        return nil unless decoded_token

        # Extract user data from expanded token
        user_data = decoded_token['user'] || {}
        session_data = decoded_token['session'] || {}

        # Create role instance from token data
        role_instance = if session_data['role']
          AtharAuth::Models::Role.from_token_data(session_data['role'])
        end

        # Create project instance from token data
        project_instance = if session_data['project']
          AtharAuth::Models::Project.from_token_data(session_data['project'])
        end

        new(
          id: user_data['id'],
          name: user_data['name'],
          email: user_data['email'],
          global: user_data['global'] || false,
          scope: decoded_token['scope'],
          permissions: session_data['permissions'] || [],
          role: role_instance,        # ✅ Rich Role object
          project: project_instance   # ✅ Rich Project object
        )
      end



      def global_user?
        global
      end

      def project_based_user?
        !global
      end

      def can?(permission)
        permissions.include?(permission)
      end

      # ✅ Domain methods using role association
      def case_manager?
        role&.name == 'case_manager'
      end

      def supervisor?
        role&.name == 'supervisor'
      end

      def project_manager?
        role&.name == 'project_manager'
      end

      def hr_manager?
        role&.name == 'hr_manager'
      end

      def procurement_manager?
        role&.name == 'procurement_manager'
      end

      # Role checking based on predefined role names from Core service
      def has_role?(role_name_to_check)
        role&.name == role_name_to_check
      end

      def to_s
        name
      end
    end
  end
end
```

```ruby
# gems/auth-gem/lib/athar_auth/models/user_role.rb
module AtharAuth
  module Models
    class UserRole < Athar::Commons::ActiveStruct::Base
      with_id(auto_generate: false)

      # Unified attributes from existing AtharPeople::UserRole
      attribute :is_default, :boolean

      # Associations to unified models (provide project_id and role.name automatically)
      belongs_to :project, class_name: 'AtharAuth::Models::Project'
      belongs_to :role, class_name: 'AtharAuth::Models::Role'

      # Factory method to create from compressed token data
      def self.from_token_data(user_role_data)
        return nil unless user_role_data

        user_role = new(
          is_default: user_role_data['def'] || false
        )

        # Set associations using the compressed data
        user_role.project = AtharAuth::Models::Project.from_token_data(user_role_data['project']) if user_role_data['project']
        user_role.role = AtharAuth::Models::Role.from_token_data(user_role_data['role']) if user_role_data['role']

        user_role
      end

      def global?
        role&.global_role?
      end

      def default?
        is_default == true
      end

      def project_based?
        !global?
      end
    end
  end
end
```

### 4. Shared Session Permissions Endpoint (Auth Gem)

```ruby
# gems/auth-gem/lib/athar_auth/controllers/session_controller.rb
module AtharAuth
  module Controllers
    class SessionController < ActionController::API
      include AtharAuth::ControllerHelpers
      include AtharAuth::Authorization

      before_action :authenticate_api_user!

      def permissions
        permissions_data = {
          # Current session context (from compressed token)
          session: {
            user: {
              id: current_user.id,
              name: current_user.name,
              email: current_user.email,
              global: current_user.global,
              type: current_user.user_type,
              status: current_user.status
            },
            role: current_role&.attributes || {},
            project: current_project&.attributes || {},
            permissions: current_permissions,
            scope: current_scope
          },

          # Cross-project access capabilities (from compressed token)
          access: {
            type: global_user? ? 'global' : 'project',
            projects: accessible_projects_with_names,
            can_access_other_projects: can_access_other_projects?
          },

          # System-specific capabilities (auto-detected from current scope)
          capabilities: extract_system_capabilities
        }

        render json: { permissions: permissions_data }
      end

      private

      def extract_system_capabilities
        case current_scope
        when 'cm'
          extract_cm_capabilities
        when 'people'
          extract_people_capabilities
        when 'procure'
          extract_procure_capabilities
        else
          {}
        end
      end

      def extract_cm_capabilities
        {
          can_manage_cases: current_permissions.any? { |p| p.include?('manage:case') },
          can_supervise: current_permissions.any? { |p| p.include?('supervise') },
          can_export_data: current_permissions.any? { |p| p.include?('export:case') }
        }
      end

      def extract_people_capabilities
        {
          can_manage_employees: current_permissions.any? { |p| p.include?('manage:employee') },
          can_manage_attendance: current_permissions.any? { |p| p.include?('manage:attendance') },
          can_manage_payroll: current_permissions.any? { |p| p.include?('manage:salary') }
        }
      end

      def extract_procure_capabilities
        {
          can_manage_procurement: current_permissions.any? { |p| p.include?('manage:procurement') },
          can_approve_requests: current_permissions.any? { |p| p.include?('approve:request') }
        }
      end
    end
  end
end
```

**Usage in Services (No Implementation Needed):**

```ruby
# services/case-manager/config/routes.rb
Rails.application.routes.draw do
  namespace :api do
    # Include shared session routes from auth gem
    mount AtharAuth::Engine, at: '/session'
  end
end

# services/people/config/routes.rb
Rails.application.routes.draw do
  namespace :api do
    # Include shared session routes from auth gem
    mount AtharAuth::Engine, at: '/session'
  end
end

# All services get: GET /api/session/permissions automatically
```

### 4. Direct Migration Strategy from AtharPeople Models

```ruby
# Clean migration plan - replace existing AtharPeople models directly

# Step 1: Remove existing AtharPeople ActiveStruct models
# DELETE: services/people/app/models/employees/project.rb
# DELETE: services/people/app/models/employees/role.rb
# DELETE: services/people/app/models/employees/user_role.rb
# DELETE: services/people/app/models/employees/types/project_type.rb
# DELETE: services/people/app/models/employees/types/role_type.rb
# DELETE: services/people/app/models/employees/types/user_role_collection_type.rb

# Step 2: Update AtharPeople Employee model based on actual gRPC structure
# services/people/app/models/employee.rb

# REMOVE these non-existent fields from ActiveRpc configuration:
# - attribute :roles, :json, default: []        # ❌ NOT in gRPC response
# - attribute :permissions, :json, default: []  # ❌ NOT in gRPC response

class Employee < ApplicationRecord
  include ActiveRpc

  # Update ActiveRpc configuration based on actual gRPC response structure
  active_rpc :core, :User, foreign_key: :user_id do
    attribute :name, :string, default: "Local User"
    attribute :email, :string, default: "<EMAIL>"
    attribute :phone, :string, default: "+962000000000"
    attribute :password, :string, default: DEFAULT_PASSWORD
    attribute :user_roles_list, :user_role_collection, default: []  # ✅ Only field in gRPC response
    attribute :avatar_attributes, :avatar_attributes_type, default: {}

    # Define query configuration based on actual gRPC fields
    query_config(
      searchable: [:name, :email, :status],
      filterable: [:name, :email, :status],
      sortable: [:name, :email, :status],
      includable: [:user_roles, :avatar_attributes]  # ✅ Removed :permissions from includable
    )
  end

  # ActiveStruct associations using the actual gRPC data structure
  has_many :projects, through: :user_roles_list
  has_many :roles, through: :user_roles_list
end

# Step 3: Update type registrations based on actual gRPC structure
# services/people/config/initializers/active_model_types.rb
Rails.application.config.to_prepare do
  require Rails.root.join('app/models/types/avatar_attributes_type')
  require Rails.root.join('app/models/employees/types/user_role_collection_type')

  # Register unified types from auth gem (only for fields that exist in gRPC)
  ActiveModel::Type.register(:avatar_attributes_type, Types::AvatarAttributesType)
  ActiveModel::Type.register(:user_role_collection, Employees::Types::UserRoleCollectionType)
  ActiveModel::Type.register(:project, AtharAuth::Models::ProjectType)
  ActiveModel::Type.register(:role, AtharAuth::Models::RoleType)
  ActiveModel::Type.register(:user_role, AtharAuth::Models::UserRoleType)

  ActiveRecord::Type.register(:avatar_attributes_type, Types::AvatarAttributesType)
  ActiveRecord::Type.register(:user_role_collection, Employees::Types::UserRoleCollectionType)
  ActiveRecord::Type.register(:project, AtharAuth::Models::ProjectType)
  ActiveRecord::Type.register(:role, AtharAuth::Models::RoleType)
  ActiveRecord::Type.register(:user_role, AtharAuth::Models::UserRoleType)
end

# Step 4: Update UserRoleCollection to use unified models
# services/people/app/models/employees/user_role_collection.rb
module Employees
  class UserRoleCollection
    include Athar::Commons::ActiveStruct::Collection

    # Use unified UserRole model from auth gem
    collection_item_class AtharAuth::Models::UserRole

    alias_method :user_role_ids, :ids
    alias_method :user_role_ids=, :ids=

    # Domain methods for filtering (only when actually needed)
    def global_roles
      select(&:global?)
    end

    def project_based_roles
      reject(&:global?)
    end

    def default_roles
      select(&:default?)
    end

    def roles_for_project(project_id)
      select { |ur| ur.project_id == project_id }
    end
  end
end
```

### 5. Enhanced Auth Gem Type Classes

```ruby
# gems/auth-gem/lib/athar_auth/models/types/project_type.rb
module AtharAuth
  module Models
    class ProjectType < Athar::Commons::ActiveStruct::Type
      def initialize(precision: nil, limit: nil, scale: nil)
        super(AtharAuth::Models::Project, precision: precision, limit: limit, scale: scale)
      end
    end
  end
end

# gems/auth-gem/lib/athar_auth/models/types/role_type.rb
module AtharAuth
  module Models
    class RoleType < Athar::Commons::ActiveStruct::Type
      def initialize(precision: nil, limit: nil, scale: nil)
        super(AtharAuth::Models::Role, precision: precision, limit: limit, scale: scale)
      end
    end
  end
end

# gems/auth-gem/lib/athar_auth/models/types/user_role_type.rb
module AtharAuth
  module Models
    class UserRoleType < Athar::Commons::ActiveStruct::Type
      def initialize(precision: nil, limit: nil, scale: nil)
        super(AtharAuth::Models::UserRole, precision: precision, limit: limit, scale: scale)
      end
    end
  end
end
```

### 5. Actual gRPC Response Structure Analysis

```ruby
# Based on Core service implementation analysis:

# Core User gRPC Response (from user.rb#to_rpc_response):
def to_rpc_response
  ::Core::UserResponse.new(
    id: id.to_s,
    name: name,
    email: email,
    status: status.to_s,
    avatar_attributes: avatar_attributes,
    user_roles_list: user_roles_list  # ← Key field containing all role/project data
  )
end

# Core UserRole gRPC Response (from user_role.rb#to_rpc_response):
def to_rpc_response
  ::Core::UserRoleResponse.new(
    id: id.to_s,
    role: role.to_rpc_response,      # ← Full Role object with all attributes
    project: project&.to_rpc_response, # ← Full Project object with all attributes
    is_default: is_default || false
  )
end

# Actual gRPC response structure received by AtharPeople:
{
  id: "123",
  name: "John Doe",
  email: "<EMAIL>",
  status: "active",
  avatar_attributes: {...},
  user_roles_list: [
    {
      id: "1",
      is_default: true,
      role: {
        id: "1",
        name: "manager",
        global: false,
        level: 2,
        # ... other role attributes
      },
      project: {
        id: "2",
        name: "Project Alpha",
        description: "...",
        status: "active"
        # ... other project attributes
      }
    },
    {
      id: "2",
      is_default: false,
      role: { id: "2", name: "supervisor", global: false },
      project: { id: "3", name: "Project Beta" }
    }
  ]
  # ❌ NO 'roles' field - roles come through user_roles_list.role
  # ❌ NO 'permissions' field - permissions would come through role.permissions if needed
}

# This means ActiveRpc attributes must match the actual gRPC response:
active_rpc :core, :User, foreign_key: :user_id do
  attribute :name, :string                                    # ✅ In gRPC response
  attribute :email, :string                                   # ✅ In gRPC response
  attribute :user_roles_list, :user_role_collection          # ✅ In gRPC response
  attribute :avatar_attributes, :avatar_attributes_type      # ✅ In gRPC response
  # attribute :roles, :json                                   # ❌ NOT in gRPC response
  # attribute :permissions, :json                             # ❌ NOT in gRPC response
end
```

## Enhanced Core Service Implementation

### 1. Pure JSONPath Token Compression (Dot Notation)

```ruby
# gems/auth-gem/lib/athar_auth/token_compression.rb
module AtharAuth
  module TokenCompression
    # Pure JSONPath approach with dot notation - single source of truth
    SHORTHAND = {
      # Standard JWT claims
      'user.id' => 's',                    # sub -> s
      'issued.at' => 'i',                  # iat -> i
      'expires.at' => 'e',                 # exp -> e
      'token.type' => 't',                 # token_type -> t
      'scope' => 'sc',                     # scope -> sc (no nesting needed)

      # User context
      'user.name' => 'u.n',               # user.name -> u.n
      'user.email' => 'u.m',              # user.email -> u.m (m for mail)
      'user.global' => 'u.g',             # user.global -> u.g

      # Session context
      'session.role' => 'ss.r',           # session.role -> ss.r (full object)
      'session.role.name' => 'ss.r.n',    # session.role.name -> ss.r.n
      'session.role.level' => 'ss.r.l',   # session.role.level -> ss.r.l
      'session.role.scope' => 'ss.r.s',   # session.role.scope -> ss.r.s
      'session.permissions' => 'ss.p',     # session.permissions -> ss.p
      'session.project' => 'ss.pr',       # session.project -> ss.pr (full object)
      'session.project.id' => 'ss.pr.id', # session.project.id -> ss.pr.id
      'session.project.name' => 'ss.pr.n', # session.project.name -> ss.pr.n

      # Access context
      'access.type' => 'a.ty',            # access.type -> a.ty
      'access.projects' => 'a.prs',       # access.projects -> a.prs
      'access.other_projects' => 'a.op'   # access.other_projects -> a.op
    }.freeze

    # Reverse mapping for decoding/debugging
    REVERSE_SHORTHAND = SHORTHAND.invert.freeze

    # Main method for token access using JSONPath
    def self.dig_token(token, path_key)
      compressed_path = SHORTHAND[path_key.to_s]
      return nil unless compressed_path

      if compressed_path.include?('.')
        # JSONPath - split and dig
        token&.dig(*compressed_path.split('.'))
      else
        # Simple key - direct access
        token&.dig(compressed_path)
      end
    end

    # Helper methods
    def self.valid_path?(path_key)
      SHORTHAND.key?(path_key.to_s)
    end

    def self.available_paths
      SHORTHAND.keys.sort
    end

    # Debug helper - show path mappings
    def self.show_mappings
      puts "=== TOKEN PATH MAPPINGS ==="
      SHORTHAND.each do |logical_path, compressed_path|
        puts "#{logical_path.ljust(25)} => #{compressed_path}"
      end
    end

    # ARCHITECTURAL PRINCIPLE: Single Entry Point Token Expansion
    # Primary method - expand compressed token to logical format for client consumption
    # Used by decoded_token method in ControllerHelpers as the single entry point
    def self.expand_token(compressed_token)
      expanded = {}
      compressed_token.each do |key, value|
        # Find logical path for this compressed key
        logical_path = REVERSE_SHORTHAND[key.to_s]

        if logical_path
          expanded[logical_path] = if value.is_a?(Hash)
            expand_token(value)  # Recursive for nested objects
          elsif value.is_a?(Array)
            value.map { |item| item.is_a?(Hash) ? expand_token(item) : item }
          else
            value
          end
        else
          # Keep unknown keys as-is
          expanded[key] = value
        end
      end
      expanded
    end

    # Helper to get compressed path for a logical path
    def self.compressed_path(logical_path)
      SHORTHAND[logical_path.to_s]
    end
  end
end
```

### 2. Session+Project Specific Token Generation (Pure JSONPath)

```ruby
# services/core/app/models/user.rb
include AtharAuth::TokenCompression

def generate_session_token(scope, role, project, exp)
  {
    # Standard JWT claims (maps to JSONPath)
    s: id,                    # 'user.id' => 's'
    i: Time.now.to_i,        # 'issued.at' => 'i'
    e: exp,                  # 'expires.at' => 'e'

    # Token metadata (maps to JSONPath)
    t: "session",            # 'token.type' => 't'
    sc: scope,               # 'scope' => 'sc'

    # User context (maps to 'user.*' => 'u.*')
    u: {
      id: id,
      n: name,               # 'user.name' => 'u.n'
      m: email,              # 'user.email' => 'u.m'
      g: global_user?        # 'user.global' => 'u.g'
    },

    # Session context (maps to 'session.*' => 'ss.*')
    ss: build_compressed_session_context(scope, role, project),

    # Access context (maps to 'access.*' => 'a.*')
    a: build_compressed_access_context(scope, project)
  }
end

private

def build_compressed_session_context(scope, role, project)
  {
    r: role ? { n: role.name, l: role.level, s: role.scope } : nil,  # 'session.role.*' => 'ss.r.*'
    p: role&.permissions&.by_system(scope)&.map(&:to_s) || [],      # 'session.permissions' => 'ss.p'
    pr: project ? { id: project.id, n: project.name } : nil         # 'session.project.*' => 'ss.pr.*'
  }
end

def build_compressed_access_context(scope, project)
  if global_user?
    {
      ty: "global",                                    # 'access.type' => 'a.ty'
      prs: Project.active.pluck(:id, :name).to_h     # 'access.projects' => 'a.prs'
    }
  else
    # Get other projects user has access to in this scope (excluding current)
    other_projects = user_roles.includes(:project, :role)
                               .joins(role: :permissions)
                               .where(permissions: { system_name: scope })
                               .where.not(project_id: project&.id)
                               .map do |user_role|
      {
        id: user_role.project_id,
        n: user_role.project.name,    # 'project.name' => 'n' (reused pattern)
        r: user_role.role.name        # 'role.name' => 'r' (reused pattern)
      }
    end

    {
      ty: "project",        # 'access.type' => 'a.ty'
      op: other_projects    # 'access.other_projects' => 'a.op'
    }
  end
end
```

### 3. Enhanced Session Controller with System Access Validation

```ruby
# services/core/app/controllers/users/sessions_controller.rb
def session_token
  user = User.find(current_user.id)

  # Validate system access before generating token
  unless user.can_access_system?(scope)
    return render_forbidden("You don't have access to #{scope.humanize} system")
  end

  # Generate session+project specific token for validated system
  role = determine_user_role(user, scope, project)
  token = user.generate_session_token(scope, role, project, token_expiry)

  render json: {
    token: encode_jwt(token),
    expires_at: token_expiry,
    scope: scope,
    project: project&.name
  }
end

private

def determine_user_role(user, scope, project)
  if user.global_user?
    # Global users use their primary role for the system
    user.user_roles.joins(:role, :permissions)
        .where(permissions: { system_name: scope })
        .first&.role
  else
    # Project-based users use their role for this specific project+system
    user.user_roles.joins(:role, :permissions)
        .where(permissions: { system_name: scope }, project: project)
        .first&.role
  end
end
```

### 4. System Access Validation in User Model

```ruby
# services/core/app/models/user.rb
def can_access_system?(system_name)
  if global_user?
    # Global users can access any system they have roles for
    user_roles.joins(:role, :permissions)
              .where(permissions: { system_name: system_name })
              .exists?
  else
    # Project-based users must have roles with permissions for this system
    user_roles.joins(:role, :permissions)
              .where(permissions: { system_name: system_name })
              .exists?
  end
end

def accessible_systems
  @accessible_systems ||= user_roles.joins(:role, :permissions)
                                    .pluck('permissions.system_name')
                                    .uniq
end

def can_access_system_in_project?(system_name, project)
  return true if global_user? && can_access_system?(system_name)

  user_roles.joins(:role, :permissions)
            .where(permissions: { system_name: system_name }, project: project)
            .exists?
end
```

## Athar Project Implementation Plan

### **Phase 1: Athar Project Infrastructure (Day 1)**

#### **1.1 Add Organization Project Configuration to AtharAuth**

**Benefits of Configuration Approach**:

- **Centralized Configuration**: All auth settings in one place
- **Environment-Specific**: Different project names for dev/staging/production
- **No Hard-coding**: Eliminates magic strings throughout codebase
- **Service Independence**: Each service uses same configuration
- **Easy Maintenance**: Single source of truth for organization project name

```ruby
# gems/auth-gem/lib/athar_auth.rb
module AtharAuth
  # Existing configurations...
  mattr_accessor :session_token_expire_time
  mattr_accessor :jwt_secret_key

  # Organization project configuration
  mattr_accessor :organization_project_name, default: "Athar"

  def self.configure
    yield(self)
  end
end
```

#### **1.2 Update Service Initializers**

```ruby
# services/core/config/initializers/athar_auth.rb
AtharAuth.configure do |config|
  config.organization_project_name = "Athar"
  config.session_token_expire_time = 24.hours
  config.jwt_secret_key = Rails.application.credentials.jwt_secret_key
end

# services/case-manager/config/initializers/athar_auth.rb
AtharAuth.configure do |config|
  config.organization_project_name = "Athar"
  # ... other service-specific configs
end

# Environment-specific configurations (optional)
# config/environments/development.rb
AtharAuth.organization_project_name = "Athar Dev"

# config/environments/staging.rb
AtharAuth.organization_project_name = "Athar Staging"

# config/environments/production.rb
AtharAuth.organization_project_name = "Athar"
```

#### **1.3 Update Project Seeds**

```ruby
# services/core/db/seeds/02_projects.rb
projects = [
  { name: AtharAuth.organization_project_name, description: "Default project for global users", status: true },
  { name: "TDH / SDC", description: "TDH / SDC", status: true },
  # ... existing projects
]

# Add project references
$athar_project = Project.find_by(name: AtharAuth.organization_project_name)
```

#### **1.4 Update User Seeds for Global Users**

```ruby
# services/core/db/seeds/04_users.rb
{
  name: "Super Admin User",
  email: "<EMAIL>",
  global: true,
  project_roles: [
    { project: nil, role: "super_admin" },                    # Global role
    { project: $athar_organization_project, role: "super_admin" }  # Organization context
  ]
}
```

#### **1.5 Enhanced Default Project Logic**

```ruby
# services/core/app/models/user.rb
def default_project
  # First try explicitly marked default
  default_user_role = user_roles.default.first
  return default_user_role.project if default_user_role&.project

  # Fallback: first project for this user
  first_project = user_roles.joins(:project).first&.project
  return first_project if first_project

  # Global users get organization project
  if global_user?
    Project.find_by(name: AtharAuth.organization_project_name)
  else
    nil
  end
end
```

#### **1.6 Fix Token Generation Validation**

```ruby
# services/core/app/models/user.rb
def generate_session_token(scope, role, project, exp)
  # ... existing validation ...

  # For non-global users, ensure they have a project context
  if !global_user? && (project.nil? || project.id.blank?)
    raise ArgumentError, "Project context is required for project-based users. User may not have a default project assigned."
  end

  # ... rest of method unchanged ...
end
```

#### **1.7 Enhanced Session Controller Error Handling**

```ruby
# services/core/app/controllers/users/sessions_controller.rb
def session_token
  user = User.find(current_user.id)

  # Determine project context
  if user.global_user?
    # Global users can specify project or use default (Athar Organization)
    if project_id.present?
      project = user.projects.find(project_id)
    else
      project = user.default_project
    end
  else
    # Project-based users must have project context
    if project_id.present?
      project = user.projects.find(project_id)
    else
      project = user.default_project

      unless project
        return render json: {
          error: "No default project assigned. Please contact administrator.",
          user_id: user.id,
          available_projects: user.projects.pluck(:id, :name)
        }, status: :unprocessable_entity
      end
    end
  end

  # ... rest of method unchanged ...
end
```

### **Phase 2: Validation & Business Logic (Day 2)**

#### **2.1 User Model Validations**

```ruby
# services/core/app/models/user.rb
validate :must_have_default_project
validate :global_users_must_have_organization_project

private

def must_have_default_project
  return if user_roles.any?(&:is_default?)
  errors.add(:base, "User must have a default project assigned")
end

def global_users_must_have_organization_project
  return unless global_user?

  org_project_name = AtharAuth.organization_project_name
  org_project = Project.find_by(name: org_project_name)
  return unless org_project

  unless user_roles.joins(:project).exists?(projects: { name: org_project_name })
    errors.add(:base, "Global users must be assigned to #{org_project_name} project")
  end
end
```

#### **2.2 Migration Script for Existing Users**

```ruby
# services/core/db/migrate/add_organization_project_to_users.rb
class AddOrganizationProjectToUsers < ActiveRecord::Migration[7.0]
  def up
    org_project_name = AtharAuth.organization_project_name
    org_project = Project.find_or_create_by!(
      name: org_project_name,
      description: "Default project for global users",
      status: true
    )

    # Assign global users to organization project
    User.where(global: true).find_each do |user|
      # Skip if already has organization project
      next if user.user_roles.joins(:project).exists?(projects: { name: "Athar Organization" })

      # Find appropriate role for this user
      global_role = user.user_roles.joins(:role).where(project: nil).first&.role
      next unless global_role

      # Create organization project assignment
      user_role = user.user_roles.create!(
        project: org_project,
        role: global_role,
        is_default: !user.user_roles.exists?(is_default: true)
      )
    end

    # Ensure all project-based users have a default project
    User.where(global: false).find_each do |user|
      next if user.user_roles.exists?(is_default: true)

      # Set first project as default
      first_user_role = user.user_roles.joins(:project).first
      first_user_role&.update!(is_default: true)
    end
  end

  def down
    # Remove organization project assignments
    org_project_name = AtharAuth.organization_project_name
    org_project = Project.find_by(name: org_project_name)
    return unless org_project

    UserRole.where(project: org_project).destroy_all
    org_project.destroy
  end
end
```

#### **2.3 Project Switching Logic**

**Project switching is handled by requesting a new session token with different project_id:**

```ruby
# Frontend: Switch to different project
POST /auth/session_token
{
  "scope": "cm",
  "project_id": 5  // Target project ID
}

# Response: New session token for the target project
{
  "session_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "project": { "id": 5, "name": "Tamkeen" },
  "role_name": "supervisor"
}
```

**No separate endpoint needed** - the existing session_token endpoint handles project switching by accepting different project_id values.

### **Phase 3: Authorization Logic Updates (Day 3)**

#### **3.1 Enhanced Authorization for Organization Project**

```ruby
# gems/auth-gem/lib/athar_auth/authorization.rb
def accessible_project_ids
  @accessible_project_ids ||= begin
    if global_user?
      # Global users can access all projects in the system
      all_projects = decoded_token&.dig('access', 'projects')&.keys&.map(&:to_i) || []

      # Always include organization project for global users
      org_project_id = Project.find_by(name: AtharAuth.organization_project_name)&.id
      all_projects << org_project_id if org_project_id

      all_projects.uniq
    else
      # Project user: current project + other accessible projects
      current_id = current_project&.id
      other_projects = decoded_token&.dig('access', 'other_projects') || []
      other_ids = other_projects.map { |p| p['id'] }

      ([current_id] + other_ids).compact.uniq
    end
  end
end

def organization_project?
  current_project&.name == AtharAuth.organization_project_name
end

def global_user_in_organization?
  global_user? && organization_project?
end
```

#### **3.2 Special Authorization Rules for Organization Project**

```ruby
# gems/auth-gem/lib/athar_auth/authorization.rb
def can_general_action?(action, subject, options = {})
  base = subject.to_s.underscore
  project = options[:project]

  # Check basic permission
  has_permission = current_permissions.include?("manage:#{base}") ||
                  current_permissions.include?("#{action}:#{base}")
  return false unless has_permission

  # Global users in organization project can access anything they have permissions for
  return true if global_user_in_organization?

  # Regular global users (shouldn't happen with new architecture)
  return true if global_user?

  # Project-based users need project validation
  if project.present?
    return user_can_access_project?(project)
  end

  # No project context = non-project resource (AtharPeople)
  true
end
```

### **Phase 4: Frontend Integration (Day 4)**

#### **4.1 Project Context Display Component**

```javascript
// Frontend: Project context always available
const ProjectContext = () => {
  const { permissions } = usePermissions();
  const currentProject = permissions?.session?.project;

  return (
    <div className="project-context">
      <span>Current Project: {currentProject?.name}</span>
      {permissions?.access?.can_access_other_projects && (
        <ProjectSwitcher projects={permissions.access.projects} />
      )}
    </div>
  );
};
```

#### **4.2 Enhanced Permissions Endpoint**

```ruby
# gems/auth-gem/app/controllers/athar_auth/session_controller.rb
def permissions
  permissions_data = {
    session: {
      user: current_user.attributes,
      role: current_role&.attributes || {},
      project: current_project&.attributes || {},  # Always present now
      permissions: current_permissions,
      scope: current_scope,
      is_organization_project: organization_project?
    },

    access: {
      type: global_user? ? 'global' : 'project',
      projects: accessible_projects_with_names,
      can_access_other_projects: can_access_other_projects?,
      organization_project_id: Project.find_by(name: AtharAuth.organization_project_name)&.id
    },

    capabilities: extract_system_capabilities
  }

  render json: { permissions: permissions_data }
end
```

## Implementation Roadmap

### 🔧 **PHASE 0**: Token Standardization (Day 1)

- [ ] **Standardize Main Token Format**: Implement compressed format for main tokens
- [ ] **Update Core Token Generation**: Modify main token creation to use SHORTHAND mappings
- [ ] **Validate Token Consistency**: Ensure uniform token structure across authentication flow
- [ ] **Test Authentication Integration**: Verify seamless token processing

### 🎯 **PHASE 1**: Single Entry Point Migration & Enhanced Authorization (Days 2-7)

- [ ] **Day 2**: Implement single entry point token expansion in `decoded_token` method
- [ ] **Day 3**: Update all AtharAuth models to consume expanded token data
- [ ] **Day 4**: Migrate authorization module to use logical token paths
- [ ] **Day 5**: Remove UserRoleCollection.from_token_data and clean up compressed key usage
- [ ] **Day 6**: Update Core service token generation and test integration
- [ ] **Day 7**: Complete testing and validation of single entry point architecture

### 🔄 **PHASE 2**: People System Integration (Week 2)

- [ ] **Day 8-10**: Integrate enhanced authorization in People service
- [ ] **Day 11-12**: Test cross-system access patterns
- [ ] **Day 13-14**: Performance optimization and caching

### 🚀 **PHASE 3**: Full System Rollout (Week 3-4)

- [ ] **Week 3**: Procurement and other subsystems
- [ ] **Week 4**: Production deployment and monitoring

## Legacy Success Criteria

### 🔧 **Phase 0 Success Criteria**

- [ ] **Token Format Unification**: All tokens use consistent compressed structure
- [ ] **SHORTHAND Integration**: Main tokens leverage existing compression mappings
- [ ] **Authentication Consistency**: Uniform token processing across all endpoints
- [ ] **User Context Standardization**: Consistent user data structure in all token types

### 🎯 **Phase 1 Success Criteria**

- [ ] **Enhanced Authorization**: `authorize!(:read, :case, project: current_project)` functionality
- [ ] **Automatic Instance Validation**: `authorize!(:update, @case)` with project_id checking
- [ ] **Shared Model Integration**: `AtharAuth::Models::Project` across all services
- [ ] **Context Helper Methods**: `current_project`, `global_user?`, `accessible_project_ids` with JSONPath
- [ ] **Multi-Project Access**: Users can access multiple projects via compressed tokens
- [ ] **Global User Support**: Unrestricted system access for global users
- [ ] **Project-Based Restrictions**: Automatic filtering for project-based users
- [ ] **Service Compatibility**: AtharPeople integration without project context requirements
- [ ] **Rich User Models**: ActiveStruct User with full association support
- [ ] **Cross-Project Capabilities**: Supervisor access across multiple projects

## Unified User Model Architecture

### **🎯 Standard Pattern: Service-Specific User Models**

All services should implement their own `User` model extending `AtharAuth::Models::User` for rich domain functionality and consistent architecture.

### **Core Service User Model (Exception)**

**Note**: The Core service is an exception to the composition-based User model pattern because it's the authentication service that manages the actual User database records.

```ruby
# services/core/app/models/user.rb
class User < ApplicationRecord
  # Core service uses ActiveRecord User model (not AtharAuth::Models::User)
  # This is the actual database model that stores user data

  include Devise::JWT::RevocationStrategies::JTIMatcher
  devise :database_authenticatable, :registerable,
         :recoverable, :rememberable, :validatable,
         :jwt_authenticatable, jwt_revocation_strategy: self

  has_many :user_roles, dependent: :destroy
  has_many :roles, through: :user_roles
  has_many :projects, through: :user_roles

  # Core service manages the actual user authentication and database records
  # Other services use composition-based User models that inherit from AtharAuth::Models::User
end
```

### **Case Manager User Model**

```ruby
# services/case-manager/app/models/user.rb
class User < AtharAuth::Models::User
  # CM-specific ActiveStruct associations
  has_many :managed_cases, class_name: 'Case', foreign_key: :case_manager_id, primary_key: :id
  belongs_to :case_manager, foreign_key: :id, primary_key: :user_id, optional: true

  # CM-specific domain methods
  def accessible_cases
    if global_user?
      Case.all
    elsif supervisor?
      Case.where(project_id: project_id)
    elsif case_manager?
      managed_cases.where(project_id: project_id)
    else
      Case.none
    end
  end

  def can_manage_case?(case_obj)
    return true if global_user?
    return false unless case_manager

    case_obj.case_manager_id == case_manager.id &&
      (project_id.blank? || case_obj.project_id == project_id)
  end

  def case_manager?
    role&.name&.include?('case_manager')
  end

  def supervisor?
    role&.name&.include?('supervisor')
  end
end
```

### **People Service User Model**

```ruby
# services/people/app/models/user.rb
class User < AtharAuth::Models::User
  # People-specific ActiveStruct associations
  belongs_to :employee, class_name: 'Employee', foreign_key: :id, primary_key: :user_id, optional: true
  has_many :managed_employees, class_name: 'Employee', foreign_key: :manager_id, primary_key: :id

  # People-specific domain methods
  def accessible_employees
    if global_user?
      Employee.all
    elsif hr_manager?
      Employee.for_project(project_id)
    elsif hr_officer?
      Employee.for_project(project_id)
    else
      Employee.none
    end
  end

  def can_manage_employee?(employee)
    return true if global_user?
    return true if hr_manager? && employee_in_accessible_projects?(employee)
    return true if hr_officer? && employee_in_accessible_projects?(employee)
    false
  end

  def hr_manager?
    role&.name == 'hr_manager'
  end

  def hr_officer?
    role&.name == 'hr_officer'
  end

  private

  def employee_in_accessible_projects?(employee)
    return true if global_user?
    employee.project_id == project_id
  end
end
```

### **Procurement Service User Model**

```ruby
# services/procure/app/models/user.rb
class User < AtharAuth::Models::User
  # Procure-specific ActiveStruct associations
  has_many :procurement_requests, class_name: 'ProcurementRequest', foreign_key: :requester_id, primary_key: :id
  has_many :approved_requests, class_name: 'ProcurementRequest', foreign_key: :approver_id, primary_key: :id

  # Procure-specific domain methods
  def accessible_requests
    if global_user?
      ProcurementRequest.all
    elsif procurement_manager?
      ProcurementRequest.where(project_id: project_id)
    else
      procurement_requests.where(project_id: project_id)
    end
  end

  def can_approve_request?(request)
    return true if global_user?
    return true if procurement_manager? && request.project_id == project_id
    false
  end

  def procurement_manager?
    role&.name&.include?('procurement_manager')
  end

  def procurement_officer?
    role&.name&.include?('procurement_officer')
  end
end
```

### **🔄 Migration Plan: Remove AuthUser Models**

#### **Step 1: Update AtharAuth Configurations**

```ruby
# services/core/config/initializers/athar_auth.rb
AtharAuth.configure do |config|
  config.user_class = "User"  # Core service uses ActiveRecord User model
  # ... other configs
end

# services/case-manager/config/initializers/athar_auth.rb
AtharAuth.configure do |config|
  config.user_class = "User"  # Change from "CoreUser"
  # ... other configs
end

# services/people/config/initializers/athar_auth.rb
AtharAuth.configure do |config|
  config.user_class = "User"  # Already correct
  # ... other configs
end

# services/procure/config/initializers/athar_auth.rb
AtharAuth.configure do |config|
  config.user_class = "User"  # Change from "AuthUser"
  # ... other configs
end
```

#### **Step 2: Update Legacy References (Completed)**

```bash
# ✅ COMPLETED: Updated auth gem to use AtharAuth::Models::User exclusively
# - Removed old AtharAuth::User class (gems/auth-gem/lib/athar_auth/user.rb)
# - Updated controller helpers fallback class
# - Updated generator templates
# - Updated specs to test AtharAuth::Models::User inheritance
```

#### **Step 3: Service-Specific User Models (Already Implemented)**

✅ All services already have rich User models inheriting from AtharAuth::Models::User:

### **Controller Integration (All Services)**

```ruby
# All services: app/controllers/application_controller.rb
class ApplicationController < Athar::Commons::Api::BaseController
  include ::AtharAuth::ControllerHelpers
  include ::AtharAuth::Authorization

  before_action :authenticate_api_user!

  private

  # Override current_user to return service-specific User with rich associations
  def current_user
    @current_user ||= User.from_token_data(decoded_token)
  end
end
```

### **✅ Benefits of Unified User Model Architecture**

#### **🎯 Consistency Across Services**

- **Same Pattern**: All client services use `User < AtharAuth::Models::User`
- **Core Exception**: Core service uses ActiveRecord User (manages actual database records)
- **Predictable API**: Consistent methods like `global_user?`, `can?()`, `accessible_*`
- **Unified Testing**: Same testing patterns across all client services

#### **🚀 Rich Domain Objects**

- **ActiveStruct Capabilities**: Full association support, validations, callbacks
- **Service-Specific Methods**: Each service gets domain-specific user methods
- **Token Integration**: Rich objects created directly from JWT tokens

#### **🔧 Simplified Architecture**

- **No AuthUser Layer**: Eliminates unnecessary intermediate models
- **Direct Token Mapping**: `Token → User.from_token_data → Rich User instance`
- **Reduced Complexity**: Fewer models to maintain and understand

#### **📈 Enhanced Functionality**

- **Role Associations**: Rich `user.role` objects with methods and attributes
- **Project Associations**: Rich `user.project` objects with full context
- **Permission Checks**: Inherited `can?()` method with role-based logic
- **Domain Methods**: Service-specific methods like `hr_manager?`, `case_manager?`

#### **🛡️ Better Authorization**

- **Project-Based Filtering**: Automatic filtering based on user's project access
- **Role-Based Logic**: Rich role objects enable sophisticated permission checks
- **Global User Support**: Consistent handling of global vs project users
- **Cross-Service Compatibility**: Same authorization patterns work everywhere

## Usage Examples with Enhanced Authorization

### AtharCM Controllers (Method Call Style)

```ruby
# services/case-manager/app/controllers/api/cases_controller.rb
class Api::CasesController < ApplicationController
  # Method call style authorization (like acts_as_approvable)
  authorize_resources

  def index
    # @cases automatically loaded and authorized
    serialize_response(@cases)
  end

  def show
    # @case automatically loaded and authorized
    # Rich object access for logging
    Rails.logger.info "User #{current_user.name} accessing case in #{current_project.name}"
    Rails.logger.info "User role: #{current_role.name} with permissions: #{current_role.permissions}"

    serialize_response(@case)
  end

  def create
    return unless current_user.case_manager?

    # Use rich ActiveStruct objects for building
    @case = current_user.managed_cases.build(case_params)
    @case.project = current_project unless global_user?
    @case.created_by = current_user

    if @case.save
      serialize_response(@case)
    else
      serialize_errors(@case.errors)
    end
  end

  def update
    # @case automatically loaded and authorized
    return unless current_user.can_manage_case?(@case)

    if @case.update(case_params)
      serialize_response(@case)
    else
      serialize_errors(@case.errors)
    end
  end

  private

  def case_params
    params.require(:case).permit(:title, :description, :status, :priority)
  end
end
```

### Custom Actions with Method Call Authorization

```ruby
# services/case-manager/app/controllers/api/cases_controller.rb
class Api::CasesController < ApplicationController
  # Custom configuration for method call authorization
  authorize_resources(
    only: [:index, :show, :update],           # Only these actions get auto-authorization
    except: [:cross_project_index],           # Skip these actions
    collection_actions: [:index, :archived]  # These actions load collections
  )

  def index
    # @cases automatically loaded and authorized
    serialize_response(@cases)
  end

  def archived
    # @cases automatically loaded, then we can filter further
    @cases = @cases.where(status: 'archived')
    serialize_response(@cases)
  end

  # Custom action with manual authorization
  def cross_project_index
    return unless authorize!(:read, :case) # No project restriction
    return unless current_user.supervisor?

    # Supervisors can see cases across their accessible projects
    @cases = Case.where(project_id: accessible_project_ids)
    serialize_response(@cases)
  end

  # Custom action with manual authorization
  def statistics
    return unless authorize!(:read, :case, project: current_project)

    stats = {
      total_cases: current_user.accessible_cases.count,
      pending_cases: current_user.accessible_cases.where(status: 'pending').count
    }
    render json: stats
  end
end
```

### AtharPeople Controllers (No Project Context)

```ruby
# services/people/app/controllers/api/employees_controller.rb
class Api::EmployeesController < ApplicationController
  # Method call style authorization works for non-project resources too
  authorize_resources

  def index
    # @employees automatically loaded and authorized
    # No project context needed for AtharPeople
    serialize_response(@employees)
  end

  def show
    # @employee automatically loaded and authorized
    # No project_id field on Employee, so no project validation
    serialize_response(@employee)
  end

  def update
    # @employee automatically loaded and authorized
    if @employee.update(employee_params)
      serialize_response(@employee)
    else
      serialize_errors(@employee.errors)
    end
  end

  private

  def employee_params
    params.require(:employee).permit(:name, :email, :position)
  end
end
```

### Frontend Integration with Permissions Endpoint

```javascript
// Frontend Permissions Context
const PermissionsContext = createContext();

export const PermissionsProvider = ({ children }) => {
  const [permissions, setPermissions] = useState(null);

  useEffect(() => {
    // Fetch permissions from session-specific endpoint
    fetch("/api/session/permissions")
      .then((res) => res.json())
      .then((data) => setPermissions(data.permissions));
  }, []);

  const canPerform = (action, resource) => {
    return (
      permissions?.resources?.[resource]?.actions?.includes(action) || false
    );
  };

  const canAccessProject = (projectId) => {
    return (
      permissions?.cross_project?.accessible_projects?.[projectId] || false
    );
  };

  return (
    <PermissionsContext.Provider
      value={{ permissions, canPerform, canAccessProject }}
    >
      {children}
    </PermissionsContext.Provider>
  );
};

// Usage in components
const CasesList = () => {
  const { canPerform, permissions } = useContext(PermissionsContext);

  return (
    <div>
      {canPerform("create", "cases") && <CreateCaseButton />}

      {permissions?.user?.role_flags?.supervisor && <CrossProjectView />}
    </div>
  );
};
```

### Token Examples (Pure JSONPath Compressed Schema)

#### **Global User - CM System Token:**

```json
{
  "s": 123, // 'user.id' => 's'
  "i": **********, // 'issued.at' => 'i'
  "e": **********, // 'expires.at' => 'e'
  "t": "session", // 'token.type' => 't'
  "sc": "cm", // 'scope' => 'sc'

  "u": {
    // user context
    "id": 123,
    "n": "Sarah Johnson", // 'user.name' => 'u.n'
    "m": "<EMAIL>", // 'user.email' => 'u.m'
    "g": true // 'user.global' => 'u.g'
  },

  "ss": {
    // session context
    "r": {
      // 'session.role' => 'ss.r'
      "n": "hr_manager", // 'session.role.name' => 'ss.r.n'
      "l": 30, // 'session.role.level' => 'ss.r.l'
      "s": "global_role" // 'session.role.scope' => 'ss.r.s'
    },
    "p": ["read:case", "read:beneficiary", "manage:user"], // 'session.permissions' => 'ss.p'
    "pr": null // 'session.project.*' => 'ss.pr.*'
  },

  "a": {
    // access context
    "ty": "global", // 'access.type' => 'a.ty'
    "prs": {
      // 'access.projects' => 'a.prs'
      "1": "Alpha Project",
      "2": "Beta Project",
      "3": "Gamma Project"
    }
  }
}
```

#### **Project User - CM System, Project 2 Token:**

```json
{
  "s": 456, // 'user.id' => 's'
  "i": **********, // 'issued.at' => 'i'
  "e": **********, // 'expires.at' => 'e'
  "t": "session", // 'token.type' => 't'
  "sc": "cm", // 'scope' => 'sc'

  "u": {
    // user context
    "id": 456,
    "n": "Ahmed Ali", // 'user.name' => 'u.n'
    "m": "<EMAIL>", // 'user.email' => 'u.m'
    "g": false // 'user.global' => 'u.g'
  },

  "ss": {
    // session context
    "r": {
      // 'session.role' => 'ss.r'
      "n": "case_manager", // 'session.role.name' => 'ss.r.n'
      "l": 10, // 'session.role.level' => 'ss.r.l'
      "s": "project_based" // 'session.role.scope' => 'ss.r.s'
    },
    "p": ["read:case", "create:case", "update:case"], // 'session.permissions' => 'ss.p'
    "pr": {
      // 'session.project.*' => 'ss.pr.*'
      "id": 2, // 'session.project.id' => 'ss.pr.id'
      "n": "Beta Project" // 'session.project.name' => 'ss.pr.n'
    }
  },

  "a": {
    // access context
    "ty": "project", // 'access.type' => 'a.ty'
    "op": [
      // 'access.other_projects' => 'a.op'
      {
        "id": 3,
        "n": "Gamma Project", // reused 'name' pattern
        "r": "supervisor" // reused 'role' pattern
      }
    ]
  }
}
```

### REVERSE_SHORTHAND Usage Examples (Debugging)

```ruby
# Debug helper to expand compressed token
def debug_token_structure
  compressed = decoded_token
  expanded = TokenCompression.expand_token(compressed)

  puts "=== COMPRESSED TOKEN ==="
  puts JSON.pretty_generate(compressed)

  puts "\n=== EXPANDED TOKEN (for debugging) ==="
  puts JSON.pretty_generate(expanded)

  puts "\n=== PATH MAPPINGS ==="
  TokenCompression.show_mappings

  puts "\n=== AVAILABLE PATHS ==="
  TokenCompression.available_paths.each do |path|
    value = TokenCompression.dig_token(compressed, path)
    puts "#{path.ljust(25)} => #{value.inspect}"
  end
end

# Example debug output:
# === PATH MAPPINGS ===
# access.other_projects     => a.op
# access.projects           => a.prs
# access.type               => a.ty
# session.project.id        => ss.pr.id
# session.project.name      => ss.pr.n
# session.role              => ss.r
# session.role.name         => ss.r.n
# session.role.level        => ss.r.l
# session.role.scope        => ss.r.s
# user.email                => u.m
# user.global               => u.g
# user.name                 => u.n
#
# === AVAILABLE PATHS ===
# user.name                 => "Ahmed Ali"
# user.email                => "<EMAIL>"
# user.global               => false
# session.role              => {"n"=>"case_manager", "l"=>10, "s"=>"project_based"}
# session.role.name         => "case_manager"
# session.role.level        => 10
# session.role.scope        => "project_based"
# session.project.id        => 2
# session.project.name      => "Beta Project"
# access.other_projects     => [{"id"=>3, "n"=>"Gamma Project", "r"=>"supervisor"}]

# Expanded token example (using REVERSE_SHORTHAND):
{
  "user.id": 456,
  "issued.at": **********,
  "expires.at": **********,
  "token.type": "session",
  "scope": "cm",
  "user.name": "Ahmed Ali",
  "user.email": "<EMAIL>",
  "user.global": false,
  "session.role": {"name": "case_manager", "level": 10, "scope": "project_based"},
  "session.role.name": "case_manager",
  "session.role.level": 10,
  "session.role.scope": "project_based",
  "session.permissions": ["read:case", "create:case", "update:case"],
  "session.project.id": 2,
  "session.project.name": "Beta Project",
  "access.type": "project",
  "access.other_projects": [
    {
      "id": 3,
      "name": "Gamma Project",
      "role": "supervisor"
    }
  ]
}
```

### Session Permissions Endpoint Response Example

```json
{
  "permissions": {
    "session": {
      "project_id": 2,
      "project_name": "Beta Project",
      "role": "case_manager",
      "permissions": ["read:case", "create:case", "update:case"]
    },
    "resources": {
      "cases": {
        "actions": ["read", "create", "update"],
        "full_permissions": ["read:case", "create:case", "update:case"]
      },
      "beneficiaries": {
        "actions": ["read"],
        "full_permissions": ["read:beneficiary"]
      }
    },
    "cross_project": {
      "can_access_other_projects": true,
      "accessible_projects": {
        "2": "Beta Project",
        "3": "Gamma Project"
      },
      "other_projects": [
        {
          "id": 3,
          "n": "Gamma Project",
          "r": "supervisor"
        }
      ]
    },
    "user": {
      "name": "Ahmed Ali",
      "email": "<EMAIL>",
      "global": false,
      "role_flags": {
        "case_manager": true,
        "supervisor": false,
        "project_manager": false
      }
    },
    "capabilities": {
      "can_access_cross_project": true,
      "can_manage_case_managers": false,
      "can_export_data": false
    }
  }
}
```

## Testing Strategy

### Enhanced Authorization Testing

```ruby
# Test enhanced authorization with project context
RSpec.describe "Enhanced Authorization" do
  let(:project_1) { create(:project) }
  let(:project_2) { create(:project) }
  let(:global_user) { create(:user, :global) }
  let(:project_user) { create(:user, :project_based, projects: [project_1]) }

  describe "Symbol-based authorization" do
    it "validates project context for project-based resources" do
      # Project user with correct project
      expect(project_user.can?(:read, :case, project: project_1)).to be_truthy

      # Project user with wrong project
      expect(project_user.can?(:read, :case, project: project_2)).to be_falsy

      # Global user with any project
      expect(global_user.can?(:read, :case, project: project_1)).to be_truthy
      expect(global_user.can?(:read, :case, project: project_2)).to be_truthy
    end

    it "allows non-project resources without project context" do
      # AtharPeople resources don't need project context
      expect(project_user.can?(:read, :employee)).to be_truthy
      expect(global_user.can?(:read, :employee)).to be_truthy
    end
  end

  describe "Instance-based authorization" do
    let(:case_in_project_1) { create(:case, project: project_1) }
    let(:case_in_project_2) { create(:case, project: project_2) }
    let(:employee) { create(:employee) } # No project_id

    it "automatically validates project_id for instances" do
      # Project user can access case in their project
      expect(project_user.can?(:read, case_in_project_1)).to be_truthy

      # Project user cannot access case in other project
      expect(project_user.can?(:read, case_in_project_2)).to be_falsy

      # Global user can access any case
      expect(global_user.can?(:read, case_in_project_1)).to be_truthy
      expect(global_user.can?(:read, case_in_project_2)).to be_truthy
    end

    it "allows instances without project_id" do
      # Employee has no project_id, so no project validation
      expect(project_user.can?(:read, employee)).to be_truthy
      expect(global_user.can?(:read, employee)).to be_truthy
    end
  end

  describe "Collection authorization" do
    let!(:case_in_project_1) { create(:case, project: project_1) }
    let!(:case_in_project_2) { create(:case, project: project_2) }

    it "filters collections based on accessible projects" do
      # Project user only sees cases in their project
      collection = project_user.authorize_collection!(:read, Case)
      expect(collection).to include(case_in_project_1)
      expect(collection).not_to include(case_in_project_2)

      # Global user sees all cases
      collection = global_user.authorize_collection!(:read, Case)
      expect(collection).to include(case_in_project_1)
      expect(collection).to include(case_in_project_2)
    end
  end
end
```

### Method Call Authorization Testing

```ruby
# Test method call style authorization
RSpec.describe "Method Call Authorization" do
  let(:controller_class) do
    Class.new(ApplicationController) do
      authorize_resources

      def controller_name
        'cases'
      end

      def action_name
        'index'
      end

      def params
        { id: 1 }
      end
    end
  end

  let(:controller) { controller_class.new }

  describe "authorize_resources configuration" do
    it "sets up before_action callbacks" do
      expect(controller_class).to have_received(:before_action).with(:authorize_and_load_collection!, only: [:index])
      expect(controller_class).to have_received(:before_action).with(:authorize_and_load_resource!, only: [:show, :create, :update, :destroy])
    end
  end

  describe "collection loading" do
    it "loads and authorizes collections" do
      allow(controller).to receive(:authorize_collection!).and_return([case_1, case_2])

      controller.send(:authorize_and_load_collection!)

      expect(controller.instance_variable_get(:@cases)).to eq([case_1, case_2])
    end
  end

  describe "resource loading" do
    it "loads and authorizes individual resources" do
      allow(Case).to receive(:find).with(1).and_return(case_1)
      allow(controller).to receive(:authorize!).and_return(true)

      controller.send(:authorize_and_load_resource!)

      expect(controller.instance_variable_get(:@case)).to eq(case_1)
    end
  end
end
```

### ActiveStruct User Testing

```ruby
# Test ActiveStruct User model
RSpec.describe "ActiveStruct User Model" do
  let(:project) { create(:project) }
  let(:case_manager_user) { create(:user, :case_manager, projects: [project]) }
  let(:case_manager_record) { create(:case_manager, user_id: case_manager_user.id) }
  let(:managed_case) { create(:case, case_manager: case_manager_record, project: project) }

  describe "Factory method" do
    let(:token_payload) do
      {
        'sub' => case_manager_user.id,
        'permissions' => ['read:case', 'create:case'],
        'project_id' => project.id,
        'user_type' => 'project_based'
      }
    end

    let(:auth_user) do
      AtharAuth::Models::User.new(
        id: case_manager_user.id,
        permissions: ['read:case', 'create:case']
      )
    end

    it "creates from AtharAuth::Models::User using factory method" do
      user = User.from_token_data(token_payload)

      expect(user.id).to eq(case_manager_user.id)
      expect(user.permissions).to eq(['read:case', 'create:case'])
      expect(user.project_id).to eq(project.id)
      expect(user.case_manager?).to be_truthy
    end

    it "supports ActiveStruct associations" do
      user = User.from_token_data(token_payload)

      expect(user.case_manager).to eq(case_manager_record)
      expect(user.managed_cases).to include(managed_case)
      expect(user.accessible_cases).to include(managed_case)
      expect(user.can_manage_case?(managed_case)).to be_truthy
    end
  end
end
```

## Updated Implementation Roadmap

### 🎯 **IMMEDIATE PRIORITY**: Unified User Model Architecture (Week 1)

- [x] **Day 1**: ✅ Implement organization project infrastructure and configuration
- [x] **Day 2**: ✅ Enhanced authorization logic and permissions endpoint updates
- [x] **Day 3**: ✅ People system User model with composition approach
- [x] **Day 4**: ✅ Remove AuthUser models and implement service-specific User models
- [x] **Day 5**: ✅ Update Case Manager and Procurement services to use composition-based User models
- [ ] **Day 6**: Update Procurement service and test unified User model architecture
- [ ] **Day 7**: Validate all services use consistent User model pattern and remove legacy code

### 🔄 **PHASE 2**: CM System Integration (Week 2)

- [ ] **Day 8-9**: Implement ActiveStruct User model in CM system using unified auth gem models
- [ ] **Day 10-11**: Update CM controllers to use authorize_resources method call
- [ ] **Day 12-13**: Fix AtharPeople breaking changes from model migration and remove non-existent gRPC fields
- [ ] **Day 14**: Integrate permissions endpoint with frontend and validate CM access

### 🚀 **PHASE 3**: System Rollout (Week 3-4)

- [ ] **Week 3**: Integrate enhanced authorization in People service
- [ ] **Week 4**: Production deployment and monitoring

## Updated Success Criteria

### 🎯 **ORGANIZATION PROJECT SUCCESS CRITERIA**

#### **Universal Project Context Implementation**

- [ ] **Athar Organization Project**: Created and assigned to all global users as default
- [ ] **Universal Project Context**: All users have project context, no null-handling needed
- [ ] **Default Project Logic**: Enhanced fallback chain ensures users always have project context
- [ ] **Migration Script**: Existing users properly assigned to organization or default projects
- [ ] **Validation Rules**: All users must have default project, global users must have organization project
- [ ] **Project Switching**: Users can switch between accessible projects via dedicated endpoint
- [ ] **Seamless Login**: Users can login without project_id parameter using default project
- [ ] **Consistent UX**: All users see project context and can switch projects when applicable
- [ ] **Error Handling**: Clear error messages when users lack proper project assignments
- [ ] **Backward Compatibility**: Existing project-based users continue working without changes

### 🎯 **ENHANCED SUCCESS CRITERIA**

- [ ] **SHORTHAND constants**: Pure JSONPath dot notation mapping for encoding/decoding consistency
- [ ] **Compressed token schema**: Session+project specific tokens with ~27% size reduction
- [ ] **Token generation**: Global users get one token per system, project users get one per system+project
- [ ] **JSONPath token parsing**: athar_auth gem handles dot notation paths using SHORTHAND constants
- [ ] **Semantic path access**: Consistent token access using `TokenCompression.dig_token(token, 'user.name')`
- [ ] **REVERSE_SHORTHAND debugging**: Expand compressed tokens for human-readable debugging
- [ ] **Method call authorization**: `authorize_resources` works like `acts_as_approvable`
- [ ] **Automatic collection loading**: `@cases` automatically loaded and filtered based on token context
- [ ] **Automatic resource loading**: `@case` automatically loaded and authorized
- [ ] **Custom action support**: Skip/configure authorization for custom actions
- [ ] **Session-specific permissions**: `/api/session/permissions` returns current session context + cross-project capabilities
- [ ] **Frontend integration**: Permissions context works with session-specific data
- [ ] **Project context helpers**: `current_project`, `global_user?` work with compressed tokens
- [ ] **Cross-project access**: Users can access other projects they have roles in within same system
- [ ] **Convention-based**: Automatic project validation for instances with project_id
- [ ] **Service-aware**: Different authorization patterns for project-based vs non-project resources
- [ ] **Distributed system ready**: Each token is self-contained for fault-tolerance

## Organization Project Benefits and Conclusion

### **Organization Project Architecture Benefits**

**Consistency & Simplification**:

- **Universal Project Context**: All users operate within a project context, eliminating special null-handling cases
- **Simplified Authorization**: Same authorization patterns work for global and project users
- **Consistent UX**: All users see project context and can switch projects when applicable
- **Reduced Complexity**: No conditional logic for "project vs no-project" scenarios

**Enhanced Security & Auditing**:

- **Complete Audit Trail**: Every action has project context for compliance and tracking
- **Clear Boundaries**: Project-based permissions apply consistently across all user types
- **Better Access Control**: Organization project provides clear scope for global operations

**Improved User Experience**:

- **Seamless Login**: Users login to default project without needing to specify project_id
- **Project Switching**: Users with multiple projects can easily switch between them
- **Consistent Interface**: Same project display and switching UI for all users

**Technical Advantages**:

- **Token Consistency**: All tokens have project context, no null handling needed
- **Database Queries**: Consistent project filtering across all operations
- **API Responses**: Always include project information in responses
- **Future-Proof**: Easy to add project-based features without breaking global user workflows

### **Implementation Impact**

**For Global Users**:

- Get assigned to "Athar Organization" project as default
- Maintain all existing permissions within organization context
- Can access all projects in system through cross-project capabilities
- Seamless experience with enhanced project switching

**For Project-Based Users**:

- No changes to existing workflow
- Enhanced default project logic ensures seamless login
- Better error handling when project assignments are missing

**For Developers**:

- Simplified authorization logic with consistent patterns
- No special cases for null project handling
- Enhanced debugging with always-present project context
- Easier testing with consistent data structures

## Benefits and Conclusion

This enhanced plan provides a robust, flexible authorization system that supports both project-based and global access patterns while maintaining clean separation between authentication and business logic.

**Immediate Benefits**:

- **Distributed System Optimization**: Compressed tokens reduce network overhead by ~27%
- **Session+Project Specificity**: Each token represents exactly one context, eliminating complexity
- **Developer Experience**: Method call style avoids inheritance issues, ultra-clean controllers
- **Automatic Authorization**: Collections and resources automatically loaded and authorized based on token context
- **Frontend Integration**: Session-specific permissions endpoint (`/api/session/permissions`) with cross-project capabilities
- **Operational**: CM system becomes accessible with full ActiveStruct association support
- **Architecture**: Enhanced authorization handles both project-based and non-project resources
- **Security**: Session-specific access control with cross-project visibility when appropriate
- **Maintainability**: Clean separation between current session and cross-project capabilities
- **Scalability**: Pattern optimized for distributed fault-tolerant systems

**Technical Achievements**:

- **SHORTHAND Constants**: Pure JSONPath dot notation mapping ensures encoding/decoding consistency
- **Rich ActiveStruct Objects**: Complete `current_user`, `current_project`, `current_role` instances with domain methods
- **Factory Methods**: `from_token_data` methods create objects directly from compressed token data
- **Single Token Parse**: Each object parsed once from token and cached with `@instance_variable`
- **REVERSE_SHORTHAND Debugging**: Expand compressed tokens to human-readable format for debugging
- **Compressed Token Schema**: Shorthand keys (s, u, ss, a) reduce payload size significantly (~27% reduction)
- **Session+Project Tokens**: Global users get one token per system, project users get one per system+project
- **Method Call Style**: `authorize_resources` follows proven ActAsApprovable pattern
- **Zero Boilerplate Controllers**: Most controllers become 3-line method calls
- **Enhanced athar_auth Gem**: JSONPath-based token parsing with project-based authorization
- **Cross-Project Capabilities**: Users can access other projects within same system when authorized
- **Convention-Based**: Automatic project validation for instances with project_id
- **Service-Aware**: Different authorization patterns for project-based vs non-project resources
- **ActiveStruct Integration**: Full association support using proven DeviceUser patterns
- **Fault-Tolerance Ready**: Self-contained tokens enable autonomous service decisions
- **Network Optimized**: Smaller tokens improve performance in distributed microservices architecture
- **Perfect Semantic Alignment**: Logical paths ('user.name') map directly to compressed paths ('u.n')
- **Object-Oriented Authorization**: Rich domain objects with behavior, not just data access
