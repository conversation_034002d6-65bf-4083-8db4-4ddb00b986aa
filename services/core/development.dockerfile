# syntax=docker/dockerfile:1
# Development Dockerfile for Athar Core (Rails)

# Use a specific Ruby version
ARG RUBY_VERSION=3.4.2
FROM docker.io/library/ruby:$RUBY_VERSION-slim AS base

# Define the Bundler version
ARG BUNDLER_VERSION=2.6.8

# Set working directory where your Rails app will live
WORKDIR /rails

# Install base and development packages
RUN apt-get update -qq && \
    apt-get install --no-install-recommends -y \
    build-essential \
    curl \
    git \
    libjemalloc2 \
    libpq-dev \
    libvips \
    pkg-config \
    postgresql-client \
    libyaml-dev \
    ruby-dev \
    libssl-dev \
    libreadline-dev \
    zlib1g-dev \
    libxml2-dev \
    libxslt1-dev \
    libcurl4-openssl-dev \
    libffi-dev \
    gdb \
    && rm -rf /var/lib/apt/lists/*

# Set up Bundler
ARG BUNDLE_GEM__FURY__IO
ENV BUNDLE_GEM__FURY__IO=${BUNDLE_GEM__FURY__IO}

RUN gem install bundler -v $BUNDLER_VERSION && \
    rm -rf /var/lib/apt/lists /var/cache/apt/archives

# Set environment variables
ENV RAILS_ENV="development" \
    BUNDLE_PATH="/usr/local/bundle" \
    PATH="/usr/local/bundle/bin:${PATH}"

# Install application gems
COPY Gemfile Gemfile.lock ./
RUN bundle install --jobs 20 --retry 3

# Create necessary directories
RUN mkdir -p db log storage tmp

# Entrypoint prepares the database.
ENTRYPOINT ["/rails/bin/docker-entrypoint"]

# Set default command to start the Rails server
CMD ["bin/rails", "server", "-b", "0.0.0.0"]
