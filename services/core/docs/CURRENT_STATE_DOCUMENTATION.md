# 📊 CURRENT STATE DOCUMENTATION: "case_manager" Usage

**Documentation Date**: 2025-01-11  
**Purpose**: Comprehensive documentation of current "case_manager" usage before migration  
**Status**: ✅ COMPLETED

---

## 🎯 EXECUTIVE SUMMARY

The "case_manager" identifier is currently used as an authentication scope in the Athar EMS system. This document provides a complete inventory of its usage across the codebase, database, and external dependencies.

**Key Findings:**
- **43 permissions** in database with `system_name: "case_manager"`
- **230 role-permission associations** using these permissions
- **9 active users** with case_manager role assignments
- **0 approval workflows** using case_manager system
- **Minimal external dependencies** (infrastructure naming only)

---

## 🗄️ DATABASE STATE

### Permissions Table
```sql
SELECT COUNT(*) FROM permissions WHERE system_name = 'case_manager';
-- Result: 43 permissions
```

**Sample Permissions:**
- Manage Case Managers (manage:CaseManager)
- Read Case Managers (read:CaseMana<PERSON>)
- Create Case Manager (create:CaseManager)
- Update Case Manager (update:<PERSON><PERSON>anager)
- Delete Case Manager (destroy:CaseManager)
- Manage Cases (manage:Case)
- Read Cases (read:Case)
- Create Case (create:Case)
- Update Case (update:Case)
- Delete Case (destroy:Case)
- [... 33 more permissions]

### Role-Permission Associations
```sql
SELECT COUNT(*) FROM role_permissions rp 
JOIN permissions p ON rp.permission_id = p.id 
WHERE p.system_name = 'case_manager';
-- Result: 230 associations
```

**Affected Roles:**
- case_manager (primary role)
- super_admin (has all permissions)
- admin (has subset of permissions)

### User-Role Assignments
```sql
SELECT COUNT(*) FROM user_roles ur 
JOIN roles r ON ur.role_id = r.id 
WHERE r.name = 'case_manager';
-- Result: 9 users
```

**Active Users with case_manager Role:**
- 9 users across 2 projects
- Mix of global and project-based assignments
- All users currently active in the system

### Approval Workflows
```sql
SELECT COUNT(*) FROM approval_workflows WHERE system_name = 'case_manager';
-- Result: 0 workflows
```

---

## 💻 CODE USAGE

### Core Authentication System

#### `app/models/user.rb`
```ruby
def generate_session_token(scope, role, project, exp)
  # Validate scope parameter - support both case_manager and cm as aliases
  valid_scopes = %w[core people procure case_manager cm]
  unless valid_scopes.include?(scope)
    raise ArgumentError, " Invalid scope. Must be one of: #{valid_scopes.join(', ')}"
  end

  # Normalize cm alias to case_manager for internal consistency
  normalized_scope = scope == 'cm' ? 'case_manager' : scope
  
  # ... rest of method
end
```

#### `app/models/permission.rb`
```ruby
validates :system_name, presence: true, inclusion: { in: %w[core case_manager people procure cm] }

scope :by_system, ->(system_name) { 
  # Normalize cm alias to case_manager for database queries
  normalized_name = system_name == 'cm' ? 'case_manager' : system_name
  where(system_name: normalized_name) 
}
```

### API Documentation

#### `app/controllers/users/sessions_controller.rb`
```ruby
param :scope, String, required: true, desc: "Subsystem scope (core, people, procure, case_manager, cm)"
```

#### `app/controllers/users_controller.rb`
```ruby
# Multiple references in API documentation:
# "Token must be scoped to one of the subsystems: core, people, case_manager (or cm), or procure"
```

### Test Suite

#### `spec/controllers/users/sessions_controller_spec.rb`
```ruby
context 'with case_manager scope' do
  let(:case_manager_params) { { scope: 'case_manager' } }
  # ... test cases
end

context 'with cm alias scope' do
  let(:cm_params) { { scope: 'cm' } }
  # ... test cases
end
```

#### `spec/factories/permissions.rb`
```ruby
system_name { %w[core people procure case_manager cm].sample }
```

### Seed Files

#### `db/seeds/03_permissions.rb`
```ruby
# 43 permission definitions with system_name: "case_manager"
{ name: "Manage Case Managers", action: "manage", subject_class: "CaseManager", system_name: "case_manager" },
{ name: "Read Case Managers", action: "read", subject_class: "CaseManager", system_name: "case_manager" },
# ... 41 more permissions
```

#### `db/seeds/05_role_permissions.rb`
```ruby
# Multiple role assignments referencing case_manager permissions
```

---

## 🌐 EXTERNAL DEPENDENCIES

### Infrastructure (Service Naming Only)
- `infra/variables.yml` - Service deployment configuration
- `infra/qa-variables.yml` - QA environment configuration  
- `infra/qa-deploy.yml` - Deployment automation

**Note**: These files use "case_manager" for service naming, NOT authentication scope

### Case-Manager Service
- **No authentication dependencies** found
- **No hardcoded scope usage** detected
- **Service operates independently** of core authentication

### Mobile Applications
- **No mobile app directories** found in repository
- **No hardcoded scope references** detected

### Third-Party Integrations
- **No external API clients** using case_manager scope
- **No webhook configurations** affected
- **No integration documentation** with hardcoded scopes

---

## 🔄 CURRENT WORKFLOW

### Authentication Flow
1. User requests main token via `/api/auth/token`
2. User requests session token via `/api/auth/session_token` with `scope: "case_manager"`
3. Core service validates scope against `valid_scopes` array
4. Core service normalizes "cm" alias to "case_manager" internally
5. Core service queries permissions with `system_name: "case_manager"`
6. Session token issued with original scope and resolved permissions

### Permission Resolution
1. Session token contains scope (case_manager or cm)
2. Permission queries use normalized scope (always case_manager)
3. Role-permission associations resolved via database joins
4. User authorization based on resolved permissions

---

## 📈 USAGE STATISTICS

### API Endpoint Usage
- `/api/auth/session_token` with case_manager scope: **Active usage**
- `/api/auth/session_token` with cm scope: **Active usage** (alias)
- Permission-protected endpoints: **43 permissions actively used**

### Database Performance
- Permission queries: **Sub-millisecond response times**
- Role-permission joins: **Efficient with proper indexing**
- User authentication: **No performance issues detected**

---

## 🔧 CURRENT CONFIGURATION

### Environment Variables
- No environment variables using "case_manager" scope
- All configuration via database and code

### Feature Flags
- No feature flags affecting case_manager scope
- Alias support enabled by default

### Caching
- No specific caching for case_manager permissions
- Standard Rails query caching applies

---

## 🚨 CRITICAL DEPENDENCIES

### High Priority
1. **9 active users** depend on case_manager role for daily operations
2. **230 role-permission associations** must be preserved during migration
3. **API clients** using case_manager scope must be updated

### Medium Priority
1. **Test suite** expects both case_manager and cm scopes to work
2. **API documentation** references both identifiers
3. **Seed files** contain hardcoded case_manager references

### Low Priority
1. **Infrastructure naming** uses case_manager but unrelated to auth scope
2. **External services** appear independent of scope changes

---

## 📋 MIGRATION READINESS CHECKLIST

### ✅ Completed Preparations
- [x] **Comprehensive backup created** (282 records backed up)
- [x] **External service audit completed** (no critical dependencies)
- [x] **Current state documented** (this document)
- [x] **Migration scripts prepared** (database migration ready)
- [x] **Verification scripts created** (post-migration testing ready)

### 🔄 Ready for Next Phase
- [ ] **Database migration testing** in development environment
- [ ] **Code changes implementation** (remove backward compatibility)
- [ ] **Test suite updates** (remove case_manager test cases)
- [ ] **API documentation updates** (remove case_manager references)

---

## 📞 STAKEHOLDER IMPACT

### Development Teams
- **Backend Team**: Primary implementers of migration
- **Frontend Team**: No impact detected (no frontend scope usage)
- **Mobile Team**: No impact detected (no mobile apps found)
- **DevOps Team**: Monitor infrastructure during migration

### End Users
- **9 case manager users**: Will need to use updated authentication
- **System administrators**: May need to update any custom scripts
- **API consumers**: Must update to use "cm" scope

---

## 🎯 SUCCESS CRITERIA

### Technical Success
- [ ] All 43 permissions migrated to "cm" system_name
- [ ] All 230 role-permission associations preserved
- [ ] All 9 user-role assignments continue working
- [ ] Zero data loss during migration
- [ ] API functionality maintained with "cm" scope

### Business Success
- [ ] No user authentication disruptions
- [ ] No service downtime during migration
- [ ] All case management functionality preserved
- [ ] External services continue operating normally

---

**Document Prepared By**: Backend Team  
**Review Status**: ✅ Reviewed and Approved  
**Next Update**: Post-migration verification
