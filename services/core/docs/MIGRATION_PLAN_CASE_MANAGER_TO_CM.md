# 🔥 MIGRATION PLAN: Remove "case_manager" Backward Compatibility

**Migration Goal**: Remove support for "case_manager" scope and make "cm" the only accepted identifier for the case management system.

**Status**: 🟡 Planning Phase  
**Created**: 2025-01-11  
**Estimated Duration**: 4 weeks  
**Risk Level**: 🔴 HIGH (Breaking Change)

---

## 📊 CURRENT STATE ANALYSIS

### Database Impact
- **43 permissions** with `system_name: "case_manager"`
- **230 role-permission associations** using case_manager permissions
- **9 users** with case_manager role assignments
- **0 approval workflows** (no workflow migration needed)

### Code Impact
- **43 locations** in seed files with hardcoded "case_manager"
- **Multiple API documentation** references
- **Test files** expecting "case_manager" scope
- **User metadata** showing case_manager as primary name

### External Dependencies
- AtharCM service potentially using "case_manager" scope
- Mobile applications requesting session tokens
- Third-party integrations using the API
- Frontend applications using "case_manager" scope

---

## ⚠️ CRITICAL RISKS

| Risk | Impact | Mitigation |
|------|--------|------------|
| 🔴 **API Breaking Change** | All clients using "case_manager" scope will fail | Comprehensive communication plan + staged rollout |
| 🔴 **Data Integrity** | 230 role-permission associations need migration | Tested migration script with rollback capability |
| 🔴 **User Impact** | 9 active users with case_manager roles affected | User notification + support during transition |
| 🔴 **External Services** | Dependent services may break | External service audit + coordination |
| 🔴 **Frontend Applications** | UI components may fail authentication | Frontend team coordination + testing |

---

## 📋 DETAILED MIGRATION PHASES

## Phase 1: Pre-Migration Assessment & Communication (Week 1)

### 1.1 Stakeholder Communication
- [ ] **Notify development teams** (Backend, Frontend, Mobile, DevOps)
- [ ] **Create announcement** in team channels (Slack/Teams)
- [ ] **Schedule migration meeting** with all stakeholders
- [ ] **Document breaking changes** in API changelog
- [ ] **Set migration deadline** (recommended: 2 weeks notice minimum)

### 1.2 External Service Audit
- [ ] **Audit AtharCM service** for "case_manager" references
  - Check authentication code
  - Check API client configurations
  - Check environment variables
- [ ] **Audit mobile applications**
  - iOS app session token requests
  - Android app session token requests
  - Check hardcoded scope values
- [ ] **Review third-party integrations**
  - External API consumers
  - Webhook configurations
  - Integration documentation
- [ ] **Check monitoring systems**
  - Log filters using "case_manager"
  - Alerting rules
  - Dashboard configurations

### 1.3 Backup & Rollback Strategy
- [ ] **Create full database backup** before migration
- [ ] **Test backup restoration** in staging environment
- [ ] **Document rollback procedure** step-by-step
- [ ] **Prepare rollback scripts** for quick execution
- [ ] **Test rollback in staging** environment

### 1.4 Impact Assessment Documentation
- [ ] **List all affected endpoints** and their usage
- [ ] **Identify peak usage times** for deployment planning
- [ ] **Document expected error patterns** post-migration
- [ ] **Create communication templates** for incident response

---

## Phase 2: Database Migration Preparation (Week 2)

### 2.1 Database Migration Script Development
- [ ] **Create migration file**: `db/migrate/20250111000001_migrate_case_manager_to_cm.rb`
- [ ] **Implement up migration**:
  - [ ] Update 43 permissions: `system_name: 'case_manager' → 'cm'`
  - [ ] Update approval workflows (if any): `system_name: 'case_manager' → 'cm'`
  - [ ] Verify foreign key constraints remain intact
  - [ ] Update any cached permission data
- [ ] **Implement down migration**: Complete rollback capability
  - [ ] Revert permissions: `system_name: 'cm' → 'case_manager'`
  - [ ] Revert approval workflows
  - [ ] Verify data integrity after rollback
- [ ] **Add comprehensive verification**:
  - [ ] Count verification: Ensure all 43 permissions migrated
  - [ ] Integrity checks: Verify 230 role-permission associations preserved
  - [ ] Constraint validation: Ensure no foreign key violations
- [ ] **Add detailed logging**: Track migration progress and timing

### 2.2 Database Backup & Safety
- [ ] **Create comprehensive backup script**: `scripts/backup_permissions.rb`
  - [ ] Backup all 43 permissions with case_manager system_name
  - [ ] Backup 230 role-permission associations
  - [ ] Backup 9 user-role assignments
  - [ ] Backup approval workflows (if any)
  - [ ] Create timestamped backup files
- [ ] **Test backup restoration**: Verify backup can be restored
- [ ] **Create verification script**: `scripts/verify_migration.rb`
  - [ ] Database state verification
  - [ ] API functionality testing
  - [ ] Permission system validation
  - [ ] User role verification

### 2.3 Seed File Updates
- [ ] **Update permissions seed**: `db/seeds/03_permissions.rb`
- [ ] **Update role permissions**: `db/seeds/05_role_permissions.rb`
- [ ] **Create seed update script**: Automated replacement tool
- [ ] **Verify seed consistency**: Ensure no missing permissions

### 2.4 Database Migration Testing
- [ ] **Test migration in development**:
  - [ ] Run migration: `rails db:migrate`
  - [ ] Verify 43 permissions updated to "cm"
  - [ ] Verify 230 role-permission associations preserved
  - [ ] Test API functionality with "cm" scope
  - [ ] Verify all 9 users with case_manager roles still work
- [ ] **Test rollback in development**:
  - [ ] Run rollback: `rails db:rollback`
  - [ ] Verify permissions reverted to "case_manager"
  - [ ] Test API functionality with "case_manager" scope
  - [ ] Verify no data loss during rollback
- [ ] **Performance testing**:
  - [ ] Measure migration execution time (target: < 30 seconds)
  - [ ] Test with production-size dataset
  - [ ] Monitor database locks during migration
- [ ] **Data integrity testing**:
  - [ ] Verify foreign key constraints
  - [ ] Check for orphaned records
  - [ ] Validate permission-role relationships
  - [ ] Confirm user authentication still works

---

## Phase 3: Code Changes (Week 2-3)

### 3.1 Core Authentication Changes
- [ ] **Update User model** (`app/models/user.rb`)
  - Remove "case_manager" from valid_scopes array
  - Remove normalization logic (no longer needed)
  - Update error messages
- [ ] **Update Permission model** (`app/models/permission.rb`)
  - Remove "case_manager" from validation inclusion
  - Update by_system scope (remove normalization)

### 3.2 API Documentation Updates
- [ ] **Update session controller** (`app/controllers/users/sessions_controller.rb`)
  - Remove "case_manager" from API documentation
  - Update parameter descriptions
- [ ] **Update users controller** (`app/controllers/users_controller.rb`)
  - Remove "case_manager" references in documentation
  - Update system metadata to use "cm" as primary

### 3.3 System Metadata Changes
- [ ] **Update allowed_systems metadata**
  ```ruby
  # FROM:
  { name: "case_manager", alias: "cm", display_name: "AtharCM", description: "Case management system" }
  
  # TO:
  { name: "cm", alias: "cm", display_name: "AtharCM", description: "Case management system" }
  ```

### 3.4 Test Suite Updates
- [ ] **Update controller tests** (`spec/controllers/users/sessions_controller_spec.rb`)
  - Remove "case_manager" test cases
  - Keep only "cm" test cases
- [ ] **Update model tests** (`spec/models/user_spec.rb`, `spec/models/permission_spec.rb`)
  - Remove "case_manager" test scenarios
  - Update factory definitions
- [ ] **Update factory definitions** (`spec/factories/permissions.rb`)
  - Remove "case_manager" from sample data

### 3.5 Documentation Updates
- [ ] **Update API documentation** (Apipie)
- [ ] **Update README files**
- [ ] **Update approval system documentation**
- [ ] **Update integration guides**

---

## Phase 4: Testing Strategy (Week 3)

### 4.1 Pre-Migration Testing Checklist
- [ ] **Functional testing**: Verify current "case_manager" scope works
- [ ] **Permission testing**: Verify all 43 permissions function correctly
- [ ] **Role testing**: Test all 9 users with case_manager roles
- [ ] **Integration testing**: Test with external services
- [ ] **Performance baseline**: Measure current response times

### 4.2 Migration Testing Checklist
- [ ] **Migration execution**: Run migration in staging
- [ ] **Data verification**: Confirm all 43 permissions migrated
- [ ] **Rollback testing**: Test rollback procedure
- [ ] **Performance testing**: Measure migration execution time
- [ ] **Integrity testing**: Verify no data corruption

### 4.3 Post-Migration Testing Checklist
- [ ] **Functional testing**: Verify "cm" scope works correctly
- [ ] **Error testing**: Confirm "case_manager" scope fails with proper error
- [ ] **Permission testing**: Verify all permissions still function
- [ ] **Role testing**: Test all user roles still work
- [ ] **Integration testing**: Verify external service compatibility
- [ ] **Performance testing**: Ensure no performance degradation

### 4.4 Test Scenarios
```bash
# Test cases to execute:
1. POST /api/auth/session_token with scope: "cm" → Should succeed
2. POST /api/auth/session_token with scope: "case_manager" → Should fail with proper error
3. GET /api/users/me with cm token → Should work
4. Permission queries with cm system_name → Should return correct permissions
5. Role-based access with migrated permissions → Should function correctly
```

---

## Phase 5: Deployment Strategy (Week 3-4)

### 5.1 Staging Deployment
- [ ] **Deploy code changes** to staging environment
- [ ] **Run database migration** in staging
- [ ] **Execute comprehensive test suite**
- [ ] **Verify external service compatibility**
- [ ] **Test rollback procedure** in staging
- [ ] **Performance testing** under load
- [ ] **Security testing** for authentication flows

### 5.2 Production Deployment Planning
- [ ] **Schedule maintenance window** (if required)
- [ ] **Choose deployment time** (low-traffic period)
- [ ] **Prepare deployment checklist**
- [ ] **Set up monitoring alerts**
- [ ] **Prepare incident response team**
- [ ] **Create deployment communication plan**

### 5.3 Production Deployment Execution
- [ ] **Announce maintenance** (if applicable)
- [ ] **Create database backup**
- [ ] **Deploy code changes**
- [ ] **Run database migration**
- [ ] **Verify migration success**
- [ ] **Execute smoke tests**
- [ ] **Monitor error rates**
- [ ] **Confirm external service functionality**

### 5.4 Monitoring & Alerting
- [ ] **Set up error rate monitoring** for authentication failures
- [ ] **Monitor "Invalid scope" error frequency**
- [ ] **Track API response times**
- [ ] **Monitor database performance**
- [ ] **Set up alerts** for unusual error patterns
- [ ] **Prepare escalation procedures**

---

## Phase 6: Post-Migration Activities (Week 4)

### 6.1 Immediate Post-Deployment (0-24 hours)
- [ ] **Monitor error rates** continuously
- [ ] **Verify all critical functionality**
- [ ] **Check external service status**
- [ ] **Respond to any incidents**
- [ ] **Communicate deployment success**

### 6.2 Short-term Follow-up (1-7 days)
- [ ] **Analyze error logs** for patterns
- [ ] **Gather feedback** from development teams
- [ ] **Monitor performance metrics**
- [ ] **Address any issues** that arise
- [ ] **Update documentation** based on learnings

### 6.3 Documentation & Cleanup
- [ ] **Update API documentation** (remove case_manager references)
- [ ] **Update integration guides** for external teams
- [ ] **Create migration retrospective** document
- [ ] **Clean up temporary scripts** and files
- [ ] **Archive migration artifacts**

### 6.4 Knowledge Transfer
- [ ] **Document lessons learned**
- [ ] **Update deployment procedures**
- [ ] **Train support team** on new error patterns
- [ ] **Update troubleshooting guides**

---

## 🚨 ROLLBACK PROCEDURES

### Immediate Rollback Triggers
- Authentication failure rate > 5%
- External service integration failures
- Database performance degradation
- Critical functionality broken

### Rollback Steps
1. **Stop deployment** immediately
2. **Run rollback migration**: `rails db:rollback`
3. **Deploy previous code version**
4. **Verify functionality restored**
5. **Communicate rollback status**
6. **Investigate root cause**

### Rollback Testing
- [ ] **Test rollback in staging** before production deployment
- [ ] **Verify data integrity** after rollback
- [ ] **Confirm all functionality** restored
- [ ] **Document rollback time** (target: < 15 minutes)

---

## 📈 SUCCESS METRICS

### Technical Metrics
- **Migration Success**: 100% of permissions migrated successfully
- **Zero Data Loss**: All role-permission associations preserved
- **Performance**: No degradation in API response times
- **Error Rate**: < 1% authentication failures post-migration

### Business Metrics
- **User Impact**: Zero user-reported authentication issues
- **Service Availability**: 99.9% uptime during migration
- **External Services**: All integrations functioning normally
- **Support Tickets**: No increase in authentication-related tickets

---

## 📞 COMMUNICATION PLAN

### Pre-Migration Communication
- **Week -2**: Initial announcement to all teams
- **Week -1**: Detailed technical briefing
- **Day -1**: Final reminder and readiness check

### During Migration Communication
- **Start**: Migration start announcement
- **Progress**: Regular updates every 30 minutes
- **Completion**: Migration completion confirmation

### Post-Migration Communication
- **Immediate**: Success/failure notification
- **24 hours**: Status update and metrics
- **1 week**: Final retrospective and lessons learned

---

## 🔧 TOOLS & SCRIPTS

### Migration Scripts
- `db/migrate/20250111000001_migrate_case_manager_to_cm.rb` - Database migration
- `update_seeds_to_cm.rb` - Seed file update script
- `verify_migration.rb` - Post-migration verification script
- `backup_permissions.rb` - Pre-migration backup script
- `test_migration.rb` - Migration testing script

### Testing Scripts
- `test_cm_scope.rb` - Comprehensive scope testing
- `test_permissions.rb` - Permission functionality testing
- `load_test.rb` - Performance testing script

### Monitoring Scripts
- `monitor_errors.rb` - Real-time error monitoring
- `check_external_services.rb` - External service health check
- `performance_monitor.rb` - Performance metrics collection

---

## ✅ FINAL CHECKLIST

### Pre-Migration
- [ ] All stakeholders notified
- [ ] External services audited
- [ ] Backup created and tested
- [ ] Migration tested in staging
- [ ] Rollback procedure tested
- [ ] Monitoring set up
- [ ] Team briefed and ready

### During Migration
- [ ] Migration executed successfully
- [ ] Verification checks passed
- [ ] Smoke tests completed
- [ ] Error rates within acceptable limits
- [ ] External services functioning
- [ ] Team monitoring actively

### Post-Migration
- [ ] All functionality verified
- [ ] Performance metrics normal
- [ ] External services confirmed working
- [ ] Documentation updated
- [ ] Team debriefed
- [ ] Lessons learned documented
- [ ] Migration marked complete

---

**Migration Owner**: Backend Team  
**Technical Lead**: [Name]  
**Project Manager**: [Name]  
**Last Updated**: 2025-01-11
