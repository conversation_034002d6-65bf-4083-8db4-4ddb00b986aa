# Add salary-related permissions
puts "Adding salary-related permissions..."

# Define new salary permissions
salary_permissions = [
  # Salary Package Permissions
  { name: "Manage Salary Packages", action: "manage", subject_class: "SalaryPackage", system_name: "people" },
  { name: "Read Salary Packages", action: "read", subject_class: "SalaryPackage", system_name: "people" },
  { name: "Read Own Salary Package", action: "read_own", subject_class: "SalaryPackage", system_name: "people" },
  { name: "Update Own Salary Package", action: "update_own", subject_class: "SalaryPackage", system_name: "people" },
  { name: "Manage Others Salary Packages", action: "manage_others", subject_class: "SalaryPackage", system_name: "people" },
  { name: "Create Salary Packages", action: "create", subject_class: "SalaryPackage", system_name: "people" },
  { name: "Create Others Salary Packages", action: "create_others", subject_class: "SalaryPackage", system_name: "people" },
  { name: "Approve Salary Packages", action: "approve", subject_class: "SalaryPackage", system_name: "people" },

  # Salary Calculation Permissions
  { name: "Read Salary Calculations", action: "read", subject_class: "SalaryCalculation", system_name: "people" },
  { name: "Read Own Salary Calculations", action: "read_own", subject_class: "SalaryCalculation", system_name: "people" },
  { name: "Calculate Salary", action: "calculate", subject_class: "SalaryCalculation", system_name: "people" },
  { name: "Update Salary Calculations", action: "update", subject_class: "SalaryCalculation", system_name: "people" },
  { name: "Submit Salary Calculations", action: "submit", subject_class: "SalaryCalculation", system_name: "people" },
  { name: "Approve Salary Calculations", action: "approve", subject_class: "SalaryCalculation", system_name: "people" },

  # Configuration Permissions
  { name: "Manage Tax Configurations", action: "manage", subject_class: "TaxConfig", system_name: "people" },
  { name: "Manage Social Security Configurations", action: "manage", subject_class: "SocialSecurityConfig", system_name: "people" }
]

# Create the permissions
salary_permissions.each do |perm|
  Permission.find_or_create_by!(
    name: perm[:name],
    action: perm[:action],
    subject_class: perm[:subject_class],
    system_name: perm[:system_name]
  )
end

# Update role permissions for salary-related features
role_salary_permissions = {
  "super_admin" => Permission.where(subject_class: %w[SalaryPackage SalaryCalculation TaxConfig SocialSecurityConfig]),
  "admin" => Permission.where(subject_class: %w[SalaryPackage SalaryCalculation TaxConfig SocialSecurityConfig]),

  # HR ROLES - FIXED: Remove dangerous 'manage', add proper permissions + Employee read access
  "hr_manager" => Permission.where(action: %w[manage_others create_others read], subject_class: "SalaryPackage")
                            .or(Permission.where(action: "read", subject_class: "SalaryCalculation"))
                            .or(Permission.where(action: "read", subject_class: "Employee", system_name: "people")),
  "hr_officer" => Permission.where(action: %w[manage_others create_others read], subject_class: "SalaryPackage")
                           .or(Permission.where(action: "read", subject_class: "SalaryCalculation"))
                           .or(Permission.where(action: "read", subject_class: "Employee", system_name: "people")),

  # FINANCIAL ROLES - FIXED: Add missing Employee read access + approve permission + create for backup
  "financial_manager" => Permission.where(action: %w[read approve create], subject_class: "SalaryPackage")
                                   .or(Permission.where(action: %w[read calculate update submit approve], subject_class: "SalaryCalculation"))
                                   .or(Permission.where(action: "manage", subject_class: %w[TaxConfig SocialSecurityConfig]))
                                   .or(Permission.where(action: "read", subject_class: "Employee", system_name: "people")),
  "accountant" => Permission.where(action: "read", subject_class: "SalaryPackage")
                            .or(Permission.where(action: %w[read calculate update submit], subject_class: "SalaryCalculation"))
                            .or(Permission.where(action: "read", subject_class: "Employee", system_name: "people")),

  # EMPLOYEES - READ-ONLY: employees cannot modify salary packages
  "employee" => Permission.where(action: "read_own", subject_class: "SalaryPackage")
                          .or(Permission.where(action: "read_own", subject_class: "SalaryCalculation"))
}

# Assign permissions to roles
role_salary_permissions.each do |role_name, perms|
  role = Role.find_by!(name: role_name) # Fail if role doesn't exist

  perms.each do |permission|
    unless role.permissions.exists?(permission.id)
      role.permissions << permission
    end
  end
end

# Ensure ALL roles (except super_admin and admin) have basic salary permissions
puts "🔄 Adding basic salary permissions to all roles..."

basic_salary_permissions = Permission.where(action: "read_own", subject_class: "SalaryPackage", system_name: "people")
                                    .or(Permission.where(action: "read_own", subject_class: "SalaryCalculation", system_name: "people"))

Role.where.not(name: %w[super_admin admin]).each do |role|
  basic_salary_permissions.each do |permission|
    unless role.permissions.exists?(permission.id)
      role.permissions << permission
    end
  end
end

puts "✅ Salary permissions added and assigned to roles successfully!"
puts "✅ All roles now have read access to their own salary information!"
puts "✅ Only HR and management can modify salary packages!"
