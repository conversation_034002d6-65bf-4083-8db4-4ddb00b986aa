# Define permissions per subsystem
permissions = [
  # 🔹 AtharCM (Case Manager Subsystem)
  { name: "Manage Case Managers", action: "manage", subject_class: "CaseManager", system_name: "cm" },
  { name: "Read Case Managers", action: "read", subject_class: "CaseManager", system_name: "cm" },
  { name: "Create Case Manager", action: "create", subject_class: "CaseManager", system_name: "cm" },
  { name: "Update Case Manager", action: "update", subject_class: "CaseManager", system_name: "cm" },
  { name: "Delete Case Manager", action: "destroy", subject_class: "CaseManager", system_name: "cm" },

  { name: "Manage Cases", action: "manage", subject_class: "Case", system_name: "cm" },
  { name: "Read Cases", action: "read", subject_class: "Case", system_name: "cm" },
  { name: "Create Case", action: "create", subject_class: "<PERSON>", system_name: "cm" },
  { name: "Update Case", action: "update", subject_class: "Case", system_name: "cm" },
  { name: "Delete Case", action: "destroy", subject_class: "Case", system_name: "cm" },
  { name: "Submit Case for Approval", action: "submit_for_approval", subject_class: "Case", system_name: "cm" },
  { name: "Approve Case", action: "approve", subject_class: "Case", system_name: "cm" },
  { name: "Reject Case", action: "reject", subject_class: "Case", system_name: "cm" },
  { name: "Assign Case", action: "assign", subject_class: "Case", system_name: "cm" },

  { name: "Manage Case Plans", action: "manage", subject_class: "CasePlan", system_name: "cm" },
  { name: "Read Case Plans", action: "read", subject_class: "CasePlan", system_name: "cm" },
  { name: "Create Case Plan", action: "create", subject_class: "CasePlan", system_name: "cm" },
  { name: "Update Case Plan", action: "update", subject_class: "CasePlan", system_name: "cm" },
  { name: "Delete Case Plan", action: "destroy", subject_class: "CasePlan", system_name: "cm" },

  { name: "Manage Beneficiaries", action: "manage", subject_class: "Beneficiary", system_name: "cm" },
  { name: "Read Beneficiaries", action: "read", subject_class: "Beneficiary", system_name: "cm" },
  { name: "Create Beneficiary", action: "create", subject_class: "Beneficiary", system_name: "cm" },
  { name: "Update Beneficiary", action: "update", subject_class: "Beneficiary", system_name: "cm" },
  { name: "Delete Beneficiary", action: "destroy", subject_class: "Beneficiary", system_name: "cm" },

  { name: "Manage Assessments", action: "manage", subject_class: "Assessment", system_name: "cm" },
  { name: "Read Assessments", action: "read", subject_class: "Assessment", system_name: "cm" },
  { name: "Create Assessment", action: "create", subject_class: "Assessment", system_name: "cm" },
  { name: "Update Assessment", action: "update", subject_class: "Assessment", system_name: "cm" },
  { name: "Delete Assessment", action: "destroy", subject_class: "Assessment", system_name: "cm" },

  { name: "Manage Sessions", action: "manage", subject_class: "Session", system_name: "cm" },
  { name: "Read Sessions", action: "read", subject_class: "Session", system_name: "cm" },
  { name: "Create Session", action: "create", subject_class: "Session", system_name: "cm" },
  { name: "Update Session", action: "update", subject_class: "Session", system_name: "cm" },
  { name: "Delete Session", action: "destroy", subject_class: "Session", system_name: "cm" },

  { name: "Manage Services", action: "manage", subject_class: "Service", system_name: "cm" },
  { name: "Read Services", action: "read", subject_class: "Service", system_name: "cm" },
  { name: "Create Service", action: "create", subject_class: "Service", system_name: "cm" },
  { name: "Update Service", action: "update", subject_class: "Service", system_name: "cm" },
  { name: "Delete Service", action: "destroy", subject_class: "Service", system_name: "cm" },

  # Form Management Permissions
  { name: "Read Form Templates", action: "read", subject_class: "FormTemplate", system_name: "cm" },
  { name: "Create Form Template", action: "create", subject_class: "FormTemplate", system_name: "cm" },
  { name: "Update Form Template", action: "update", subject_class: "FormTemplate", system_name: "cm" },
  { name: "Delete Form Template", action: "destroy", subject_class: "FormTemplate", system_name: "cm" },

  { name: "Read Form Submissions", action: "read", subject_class: "FormSubmission", system_name: "cm" },
  { name: "Create Form Submission", action: "create", subject_class: "FormSubmission", system_name: "cm" },
  { name: "Update Form Submission", action: "update", subject_class: "FormSubmission", system_name: "cm" },
  { name: "Delete Form Submission", action: "destroy", subject_class: "FormSubmission", system_name: "cm" },
  { name: "Submit Form Submission", action: "submit", subject_class: "FormSubmission", system_name: "cm" },
  { name: "Calculate Form Fields", action: "calculate_fields", subject_class: "FormSubmission", system_name: "cm" },

  # Comment Management Permissions
  { name: "Read Comments", action: "read", subject_class: "Comment", system_name: "cm" },
  { name: "Create Comment", action: "create", subject_class: "Comment", system_name: "cm" },
  { name: "Update Comment", action: "update", subject_class: "Comment", system_name: "cm" },
  { name: "Delete Comment", action: "destroy", subject_class: "Comment", system_name: "cm" },
  { name: "Resolve Comment", action: "resolve", subject_class: "Comment", system_name: "cm" },
  { name: "Reopen Comment", action: "reopen", subject_class: "Comment", system_name: "cm" },

  # Document Management Permissions
  { name: "Read Case Documents", action: "read", subject_class: "CaseDocument", system_name: "cm" },
  { name: "Create Case Document", action: "create", subject_class: "CaseDocument", system_name: "cm" },
  { name: "Update Case Document", action: "update", subject_class: "CaseDocument", system_name: "cm" },
  { name: "Delete Case Document", action: "destroy", subject_class: "CaseDocument", system_name: "cm" },
  { name: "Download Case Document", action: "download", subject_class: "CaseDocument", system_name: "cm" },
  { name: "Preview Case Document", action: "preview", subject_class: "CaseDocument", system_name: "cm" },

  # Reporting & Analytics Permissions
  { name: "Read Dashboard", action: "read", subject_class: "Dashboard", system_name: "cm" },
  { name: "Read Charts", action: "read", subject_class: "Charts", system_name: "cm" },
  { name: "Read Reports", action: "read", subject_class: "Reports", system_name: "cm" },
  { name: "Create Reports", action: "create", subject_class: "Reports", system_name: "cm" },
  { name: "Export Reports", action: "export", subject_class: "Reports", system_name: "cm" },
  { name: "Read Team Metrics", action: "read", subject_class: "TeamMetrics", system_name: "cm" },
  { name: "Read Analytics", action: "read", subject_class: "Analytics", system_name: "cm" },

  { name: "Generate Mental Health Reports", action: "generate", subject_class: "Report", system_name: "cm" },
  { name: "Export Mental Health Reports", action: "export", subject_class: "Report", system_name: "cm" },

  # 🔹 AtharPeople (HR Management Subsystem)
  { name: "Manage Employees", action: "manage", subject_class: "Employee", system_name: "people" },
  { name: "Read Employees", action: "read", subject_class: "Employee", system_name: "people" },
  { name: "Read Project Employees", action: "read_project", subject_class: "Employee", system_name: "people" },
  { name: "Create Employee", action: "create", subject_class: "Employee", system_name: "people" },
  { name: "Create Project Employee", action: "create_project", subject_class: "Employee", system_name: "people" },
  { name: "Update Employee", action: "update", subject_class: "Employee", system_name: "people" },
  { name: "Update Project Employee", action: "update_project", subject_class: "Employee", system_name: "people" },
  { name: "Delete Employee", action: "destroy", subject_class: "Employee", system_name: "people" },
  { name: "Delete Project Employee", action: "destroy_project", subject_class: "Employee", system_name: "people" },
  { name: "Manage Project Employees", action: "manage_project", subject_class: "Employee", system_name: "people" },
  { name: "Read Own Employee", action: "read_own", subject_class: "Employee", system_name: "people" },
  { name: "Update Own Employee", action: "update_own", subject_class: "Employee", system_name: "people" },

  { name: "Manage Payroll", action: "manage", subject_class: "Payroll", system_name: "people" },
  { name: "Read Payroll", action: "read", subject_class: "Payroll", system_name: "people" },
  { name: "Create Payroll", action: "create", subject_class: "Payroll", system_name: "people" },
  { name: "Update Payroll", action: "update", subject_class: "Payroll", system_name: "people" },
  { name: "Delete Payroll", action: "destroy", subject_class: "Payroll", system_name: "people" },

  # Leave Management Permissions
  { name: "Manage Leaves", action: "manage", subject_class: "Leave", system_name: "people" },
  { name: "Read Leaves", action: "read", subject_class: "Leave", system_name: "people" },
  { name: "Read Project Leaves", action: "read_project", subject_class: "Leave", system_name: "people" },
  { name: "Create Leave", action: "create", subject_class: "Leave", system_name: "people" },
  { name: "Update Leave", action: "update", subject_class: "Leave", system_name: "people" },
  { name: "Submit Leave", action: "submit", subject_class: "Leave", system_name: "people" },
  { name: "Withdraw Leave", action: "withdraw", subject_class: "Leave", system_name: "people" },
  { name: "Approve Leave", action: "approve", subject_class: "Leave", system_name: "people" },
  { name: "Approve Project Leaves", action: "approve_project", subject_class: "Leave", system_name: "people" },
  { name: "Reject Leave", action: "reject", subject_class: "Leave", system_name: "people" },
  { name: "Reject Project Leaves", action: "reject_project", subject_class: "Leave", system_name: "people" },
  { name: "Read Own Leaves", action: "read_own", subject_class: "Leave", system_name: "people" },
  { name: "Manage Own Leaves", action: "manage_own", subject_class: "Leave", system_name: "people" },

  # Attendance Management Permissions
  { name: "Manage Attendance Events", action: "manage", subject_class: "AttendanceEvent", system_name: "people" },
  { name: "Read Attendance Events", action: "read", subject_class: "AttendanceEvent", system_name: "people" },
  { name: "Read Project Attendance Events", action: "read_project", subject_class: "AttendanceEvent", system_name: "people" },
  { name: "Create Attendance Event", action: "create", subject_class: "AttendanceEvent", system_name: "people" },
  { name: "Update Attendance Event", action: "update", subject_class: "AttendanceEvent", system_name: "people" },
  { name: "Update Project Attendance Events", action: "update_project", subject_class: "AttendanceEvent", system_name: "people" },
  { name: "Delete Attendance Event", action: "destroy", subject_class: "AttendanceEvent", system_name: "people" },
  { name: "Record Own Attendance", action: "record", subject_class: "AttendanceEvent", system_name: "people" },
  { name: "Read Own Attendance Events", action: "read_own", subject_class: "AttendanceEvent", system_name: "people" },

  # Attendance Device Management Permissions
  { name: "Manage Attendance Devices", action: "manage", subject_class: "AttendanceDevice", system_name: "people" },
  { name: "Read Attendance Devices", action: "read", subject_class: "AttendanceDevice", system_name: "people" },
  { name: "Create Attendance Device", action: "create", subject_class: "AttendanceDevice", system_name: "people" },
  { name: "Update Attendance Device", action: "update", subject_class: "AttendanceDevice", system_name: "people" },
  { name: "Delete Attendance Device", action: "destroy", subject_class: "AttendanceDevice", system_name: "people" },
  { name: "Test Device Connection", action: "test_connection", subject_class: "AttendanceDevice", system_name: "people" },
  { name: "Sync Device Data", action: "sync", subject_class: "AttendanceDevice", system_name: "people" },
  { name: "Execute Device Command", action: "execute_command", subject_class: "AttendanceDevice", system_name: "people" },

  # Attendance Sync Log Permissions
  { name: "Manage Attendance Sync Logs", action: "manage", subject_class: "AttendanceSyncLog", system_name: "people" },
  { name: "Read Attendance Sync Logs", action: "read", subject_class: "AttendanceSyncLog", system_name: "people" },
  { name: "Cleanup Sync Logs", action: "cleanup", subject_class: "AttendanceSyncLog", system_name: "people" },

  # Settings Permissions
  { name: "Manage Settings", action: "manage", subject_class: "setting", system_name: "people" },
  { name: "Read Settings", action: "read", subject_class: "setting", system_name: "people" },
  { name: "Create Settings", action: "create", subject_class: "setting", system_name: "people" },
  { name: "Update Settings", action: "update", subject_class: "setting", system_name: "people" },

  # Holiday Management Permissions
  { name: "Manage Holidays", action: "manage", subject_class: "Holiday", system_name: "people" },

  # 🔹 AtharProcure (Procurement Management Subsystem)
  { name: "Manage Procurement Requests", action: "manage", subject_class: "ProcurementRequest", system_name: "procure" },
  { name: "Read Procurement Requests", action: "read", subject_class: "ProcurementRequest", system_name: "procure" },
  { name: "Create Procurement Request", action: "create", subject_class: "ProcurementRequest", system_name: "procure" },
  { name: "Update Procurement Request", action: "update", subject_class: "ProcurementRequest", system_name: "procure" },
  { name: "Delete Procurement Request", action: "destroy", subject_class: "ProcurementRequest", system_name: "procure" },

  { name: "Approve Procurement", action: "approve", subject_class: "ProcurementRequest", system_name: "procure" },
  { name: "Reject Procurement", action: "reject", subject_class: "ProcurementRequest", system_name: "procure" },

  { name: "Generate Procurement Reports", action: "generate", subject_class: "ProcurementReport", system_name: "procure" },
  { name: "Export Procurement Reports", action: "export", subject_class: "ProcurementReport", system_name: "procure" },

  # 🔹 System-Wide Permissions
  { name: "Manage Users", action: "manage", subject_class: "User", system_name: "core" },
  { name: "Read Users", action: "read", subject_class: "User", system_name: "core" },
  { name: "Create User", action: "create", subject_class: "User", system_name: "core" },
  { name: "Update User", action: "update", subject_class: "User", system_name: "core" },
  { name: "Delete User", action: "destroy", subject_class: "User", system_name: "core" },
  { name: "Read Own User", action: "read_own", subject_class: "User", system_name: "core" },
  { name: "Manage Own User", action: "manage_own", subject_class: "User", system_name: "core" },

  { name: "Manage Roles", action: "manage", subject_class: "Role", system_name: "core" },
  { name: "Read Roles", action: "read", subject_class: "Role", system_name: "core" },
  { name: "Create Role", action: "create", subject_class: "Role", system_name: "core" },
  { name: "Update Role", action: "update", subject_class: "Role", system_name: "core" },
  { name: "Destroy Role", action: "destroy", subject_class: "Role", system_name: "core" },

  { name: "Manage Projects", action: "manage", subject_class: "Project", system_name: "core" },
  { name: "Read Projects", action: "read", subject_class: "Project", system_name: "core" },
  { name: "Create Project", action: "create", subject_class: "Project", system_name: "core" },
  { name: "Update Project", action: "update", subject_class: "Project", system_name: "core" },
  { name: "Destroy Project", action: "destroy", subject_class: "Project", system_name: "core" },

  { name: "Assign Permissions", action: "assign", subject_class: "Permission", system_name: "core" },

  # Approval System Permissions
  { name: "Manage Approval Requests", action: "manage", subject_class: "ApprovalRequest", system_name: "core" },
  { name: "Read Approval Requests", action: "read", subject_class: "ApprovalRequest", system_name: "core" },
  { name: "Read Own Approval Requests", action: "read_own", subject_class: "ApprovalRequest", system_name: "core" },
  { name: "Approve Approval Request", action: "approve", subject_class: "ApprovalRequest", system_name: "core" },
  { name: "Reject Approval Request", action: "reject", subject_class: "ApprovalRequest", system_name: "core" },
  { name: "Cancel Approval Request", action: "cancel", subject_class: "ApprovalRequest", system_name: "core" },
  { name: "Cancel Own Approval Request", action: "cancel_own", subject_class: "ApprovalRequest", system_name: "core" },

  # People service approval permissions
  { name: "Read Approval Requests", action: "read", subject_class: "ApprovalRequest", system_name: "people" },
  { name: "Read Own Approval Requests", action: "read_own", subject_class: "ApprovalRequest", system_name: "people" },
  { name: "Approve Approval Request", action: "approve", subject_class: "ApprovalRequest", system_name: "people" },
  { name: "Reject Approval Request", action: "reject", subject_class: "ApprovalRequest", system_name: "people" },
  { name: "Cancel Approval Request", action: "cancel", subject_class: "ApprovalRequest", system_name: "people" },
  { name: "Cancel Own Approval Request", action: "cancel_own", subject_class: "ApprovalRequest", system_name: "people" },

  # Procure service approval permissions
  { name: "Read Approval Requests", action: "read", subject_class: "ApprovalRequest", system_name: "procure" },
  { name: "Read Own Approval Requests", action: "read_own", subject_class: "ApprovalRequest", system_name: "procure" },
  { name: "Approve Approval Request", action: "approve", subject_class: "ApprovalRequest", system_name: "procure" },
  { name: "Reject Approval Request", action: "reject", subject_class: "ApprovalRequest", system_name: "procure" },
  { name: "Cancel Approval Request", action: "cancel", subject_class: "ApprovalRequest", system_name: "procure" },
  { name: "Cancel Own Approval Request", action: "cancel_own", subject_class: "ApprovalRequest", system_name: "procure" },

  # Case Manager service approval permissions
  { name: "Read Approval Requests", action: "read", subject_class: "ApprovalRequest", system_name: "cm" },
  { name: "Read Own Approval Requests", action: "read_own", subject_class: "ApprovalRequest", system_name: "cm" },
  { name: "Approve Approval Request", action: "approve", subject_class: "ApprovalRequest", system_name: "cm" },
  { name: "Reject Approval Request", action: "reject", subject_class: "ApprovalRequest", system_name: "cm" },
  { name: "Cancel Approval Request", action: "cancel", subject_class: "ApprovalRequest", system_name: "cm" },
  { name: "Cancel Own Approval Request", action: "cancel_own", subject_class: "ApprovalRequest", system_name: "cm" },

  # 🔹 People Service - Attendance Exemptions
  { name: "Manage Attendance Exemptions", action: "manage", subject_class: "AttendanceExemption", system_name: "people" },
  { name: "Read Attendance Exemptions", action: "read", subject_class: "AttendanceExemption", system_name: "people" },
  { name: "Create Attendance Exemption", action: "create", subject_class: "AttendanceExemption", system_name: "people" },
  { name: "Update Attendance Exemption", action: "update", subject_class: "AttendanceExemption", system_name: "people" },
  { name: "Delete Attendance Exemption", action: "destroy", subject_class: "AttendanceExemption", system_name: "people" }
]

# Seed the permissions into the database
permissions.each do |perm|
  Permission.find_or_create_by!(
    name: perm[:name],
    action: perm[:action],
    subject_class: perm[:subject_class],
    system_name: perm[:system_name]
  )
end

puts "✅ Permissions seeded successfully!"
