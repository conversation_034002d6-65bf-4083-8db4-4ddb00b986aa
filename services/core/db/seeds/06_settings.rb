# Create default settings for attendance
default_settings = [
  { namespace: 'attendance', key: 'work_start_time', value: '09:00', description: 'Default work start time' },
  { namespace: 'attendance', key: 'work_end_time', value: '17:00', description: 'Default work end time' },
  { namespace: 'attendance', key: 'duplicate_threshold_seconds', value: '60', description: 'Threshold for identifying duplicate events in seconds' },
  { namespace: 'attendance', key: 'required_work_minutes', value: '480', description: 'Required work minutes per day (8 hours)' },
  { namespace: 'attendance', key: 'break_threshold_minutes', value: '120', description: 'Maximum break duration before flagging (2 hours)' },

  # Device Management Settings
  { namespace: 'attendance', key: 'device_sync_interval_minutes', value: '30', description: 'Default sync interval for attendance devices in minutes' },
  { namespace: 'attendance', key: 'device_connection_timeout_seconds', value: '10', description: 'Default connection timeout for devices in seconds' },
  { namespace: 'attendance', key: 'device_max_retry_attempts', value: '3', description: 'Maximum retry attempts for device operations' },
  { namespace: 'attendance', key: 'device_health_check_interval_hours', value: '24', description: 'Interval for device health checks in hours' },
  { namespace: 'attendance', key: 'sync_log_retention_days', value: '90', description: 'Number of days to retain sync logs' },
  { namespace: 'attendance', key: 'auto_sync_enabled', value: 'true', description: 'Enable automatic device synchronization' },
  { namespace: 'attendance', key: 'parallel_sync_enabled', value: 'true', description: 'Enable parallel device synchronization' },
  { namespace: 'attendance', key: 'max_concurrent_syncs', value: '5', description: 'Maximum number of concurrent device syncs' }
]

# Create settings if they don't exist
if defined?(Setting)
  default_settings.each do |setting_data|
    Setting.find_or_create_by!(namespace: setting_data[:namespace], key: setting_data[:key]) do |setting|
      setting.value = setting_data[:value]
      setting.description = setting_data[:description]
      setting.is_editable = true
    end
  end
  puts "✅ Default attendance settings created!"
else
  puts "⚠️ Setting model not defined, skipping settings creation"
end
