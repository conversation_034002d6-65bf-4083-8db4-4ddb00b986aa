class CreateApprovalSteps < ActiveRecord::Migration[8.0]
  def change
    create_table :approval_steps do |t|
      t.references :approval_workflow, null: false, foreign_key: true
      t.integer :sequence, null: false
      t.string :name, null: false
      t.string :approval_type, null: false, default: 'any'
      t.string :dynamic_approver_method, null: false
      t.text :description

      t.timestamps

      t.index [ :approval_workflow_id, :sequence ], unique: true
    end
  end
end
