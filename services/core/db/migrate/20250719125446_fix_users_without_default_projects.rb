class FixUsersWithoutDefaultProjects < ActiveRecord::Migration[8.0]
  def up
    puts "🔧 Fixing users without default projects..."

    # PART 1: Find users who have user_roles but no default project
    users_without_default_sql = <<-SQL
      SELECT DISTINCT u.id, u.email
      FROM users u
      INNER JOIN user_roles ur ON ur.user_id = u.id
      GROUP BY u.id, u.email
      HAVING COUNT(CASE WHEN ur.is_default = true THEN 1 END) = 0
    SQL

    users_without_default = connection.execute(users_without_default_sql)
    fixed_with_roles_count = 0

    puts "🔍 Found #{users_without_default.count} users with roles but no default project"

    users_without_default.each do |user_row|
      user_id = user_row['id'] || user_row[0]
      user_email = user_row['email'] || user_row[1]

      # Get the first user_role for this user
      first_role_sql = <<-SQL
        SELECT ur.id, p.name as project_name, r.name as role_name
        FROM user_roles ur
        LEFT JOIN projects p ON p.id = ur.project_id
        LEFT JOIN roles r ON r.id = ur.role_id
        WHERE ur.user_id = #{user_id}
        ORDER BY ur.id ASC
        LIMIT 1
      SQL

      first_role_result = connection.execute(first_role_sql).first

      if first_role_result
        user_role_id = first_role_result['id'] || first_role_result[0]
        project_name = first_role_result['project_name'] || first_role_result[1] || 'No Project'
        role_name = first_role_result['role_name'] || first_role_result[2] || 'Unknown Role'

        # Update the user_role to be default
        connection.execute(<<-SQL)
          UPDATE user_roles
          SET is_default = true, updated_at = NOW()
          WHERE id = #{user_role_id}
        SQL

        puts "✅ Set default project for #{user_email}: #{project_name} (#{role_name})"
        fixed_with_roles_count += 1
      end
    end

    # PART 2: Find users with NO user_roles at all
    users_no_roles_sql = <<-SQL
      SELECT u.id, u.email
      FROM users u
      LEFT JOIN user_roles ur ON ur.user_id = u.id
      WHERE ur.user_id IS NULL
    SQL

    users_no_roles = connection.execute(users_no_roles_sql)
    fixed_no_roles_count = 0

    puts "🔍 Found #{users_no_roles.count} users with NO roles at all"

    if users_no_roles.count > 0
      # Get default role and project for users without roles
      default_role_sql = <<-SQL
        SELECT id FROM roles WHERE name = 'employee' LIMIT 1
      SQL

      default_project_sql = <<-SQL
        SELECT id FROM projects ORDER BY id ASC LIMIT 1
      SQL

      default_role_result = connection.execute(default_role_sql).first
      default_project_result = connection.execute(default_project_sql).first

      if default_role_result && default_project_result
        default_role_id = default_role_result['id'] || default_role_result[0]
        default_project_id = default_project_result['id'] || default_project_result[0]

        users_no_roles.each do |user_row|
          user_id = user_row['id'] || user_row[0]
          user_email = user_row['email'] || user_row[1]

          # Create a default user_role for this user
          connection.execute(<<-SQL)
            INSERT INTO user_roles (user_id, role_id, project_id, is_default, created_at, updated_at)
            VALUES (#{user_id}, #{default_role_id}, #{default_project_id}, true, NOW(), NOW())
          SQL

          puts "✅ Created default role for #{user_email}: employee role with default project"
          fixed_no_roles_count += 1
        end
      else
        puts "⚠️  Could not find default role 'employee' or default project. Skipping users without roles."
        users_no_roles.each do |user_row|
          user_email = user_row['email'] || user_row[1]
          puts "⚠️  Skipped #{user_email} - no default role/project available"
        end
      end
    end

    puts "📊 Summary:"
    puts "   - Fixed #{fixed_with_roles_count} users who had roles but no default"
    puts "   - Fixed #{fixed_no_roles_count} users who had no roles at all"
    puts "   - Total fixed: #{fixed_with_roles_count + fixed_no_roles_count} users"
  end

  def down
    puts "⚠️  This migration cannot be safely reversed"
    puts "   Manual review would be required to determine which defaults to remove"
  end
end
