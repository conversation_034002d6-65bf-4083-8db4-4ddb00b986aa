class AddIsDefaultToUserRoles < ActiveRecord::Migration[8.0]
  def change
    add_column :user_roles, :is_default, :boolean, default: false

    # Create a partial index to ensure only one default per user
    add_index :user_roles, [ :user_id, :is_default ],
              unique: true,
              where: "is_default = true",
              name: 'index_user_roles_on_user_id_and_is_default'
  end
end
