class CreateApprovalWorkflows < ActiveRecord::Migration[8.0]
  def change
    create_table :approval_workflows do |t|
      t.string :name, null: false
      t.string :action, null: false
      t.string :subject_class, null: false
      t.string :system_name, null: false
      t.text :description
      t.boolean :active, default: true

      t.timestamps

      t.index [ :action, :subject_class, :system_name ], unique: true
      t.index :name, unique: true
    end
  end
end
