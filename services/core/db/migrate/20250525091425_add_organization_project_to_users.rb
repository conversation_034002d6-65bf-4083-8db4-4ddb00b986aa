class AddOrganizationProjectToUsers < ActiveRecord::Migration[7.0]
  def up
    org_project_name = "Athar"

    athar_project_result = connection.execute(
      "SELECT id FROM projects WHERE name = '#{org_project_name}' LIMIT 1"
    ).first

    if athar_project_result.nil?
      connection.execute(
        "INSERT INTO projects (name, description, status, created_at, updated_at)
         VALUES ('#{org_project_name}', 'Default project for global users', true, NOW(), NOW())"
      )
      athar_project_result = connection.execute(
        "SELECT id FROM projects WHERE name = '#{org_project_name}' LIMIT 1"
      ).first
    end

    athar_project_id = athar_project_result.is_a?(Hash) ? athar_project_result['id'] : athar_project_result[0]
    puts "✅ #{org_project_name} project ID: #{athar_project_id}"

    global_users = connection.execute("SELECT id, email FROM users WHERE global = true")
    global_users_count = 0

    global_users.each do |user|
      user_id = user.is_a?(Hash) ? user['id'] : user[0]
      user_email = user.is_a?(Hash) ? user['email'] : user[1]

      existing_assignment = connection.execute(
        "SELECT ur.id FROM user_roles ur
         JOIN projects p ON ur.project_id = p.id
         WHERE ur.user_id = #{user_id} AND p.name = '#{org_project_name}'
         LIMIT 1"
      ).first

      if existing_assignment
        puts "⏭️  Global user #{user_email} already has #{org_project_name} project"
        next
      end

      global_role = connection.execute(
        "SELECT id, role_id FROM user_roles
         WHERE user_id = #{user_id} AND project_id IS NULL
         LIMIT 1"
      ).first

      unless global_role
        puts "⚠️  Global user #{user_email} has no global roles - skipping"
        next
      end

      role_id = global_role.is_a?(Hash) ? global_role['role_id'] : global_role[1]

      has_default = connection.execute(
        "SELECT id FROM user_roles WHERE user_id = #{user_id} AND is_default = true LIMIT 1"
      ).first

      is_default = has_default.nil?

      # Remove ALL global roles (project_id = NULL) for this user
      connection.execute(
        "DELETE FROM user_roles WHERE user_id = #{user_id} AND project_id IS NULL"
      )

      # Create new organization project role
      connection.execute(
        "INSERT INTO user_roles (user_id, role_id, project_id, is_default, created_at, updated_at)
         VALUES (#{user_id}, #{role_id}, #{athar_project_id}, #{is_default}, NOW(), NOW())"
      )

      puts "✅ Migrated #{user_email} from global role to #{org_project_name} project (default: #{is_default})"
      global_users_count += 1
    end

    project_users = connection.execute("SELECT id, email FROM users WHERE global = false")
    project_users_count = 0

    project_users.each do |user|
      user_id = user.is_a?(Hash) ? user['id'] : user[0]
      user_email = user.is_a?(Hash) ? user['email'] : user[1]

      has_default = connection.execute(
        "SELECT id FROM user_roles WHERE user_id = #{user_id} AND is_default = true LIMIT 1"
      ).first

      next if has_default

      first_assignment = connection.execute(
        "SELECT ur.id, p.name FROM user_roles ur
         JOIN projects p ON ur.project_id = p.id
         WHERE ur.user_id = #{user_id}
         LIMIT 1"
      ).first

      if first_assignment
        assignment_id = first_assignment.is_a?(Hash) ? first_assignment['id'] : first_assignment[0]
        project_name = first_assignment.is_a?(Hash) ? first_assignment['name'] : first_assignment[1]

        connection.execute(
          "UPDATE user_roles SET is_default = true WHERE id = #{assignment_id}"
        )

        puts "✅ Set default project for #{user_email}: #{project_name}"
        project_users_count += 1
      else
        puts "⚠️  Project user #{user_email} has no project assignments"
      end
    end

    puts "\n📊 Migration Summary:"
    puts "   - Global users processed: #{global_users_count}"
    puts "   - Project users processed: #{project_users_count}"
    puts "   - #{org_project_name} project ID: #{athar_project_id}"
  end

  def down
    org_project_name = "Athar"

    athar_project_result = connection.execute(
      "SELECT id FROM projects WHERE name = '#{org_project_name}' LIMIT 1"
    ).first

    return unless athar_project_result

    athar_project_id = athar_project_result.is_a?(Hash) ? athar_project_result['id'] : athar_project_result[0]

    puts "🗑️  Reverting #{org_project_name} project migration..."

    # Get all user roles for the organization project before removing them
    org_user_roles = connection.execute(
      "SELECT user_id, role_id, is_default FROM user_roles WHERE project_id = #{athar_project_id}"
    )

    # Restore global roles (project_id = NULL) for global users
    org_user_roles.each do |user_role|
      user_id = user_role.is_a?(Hash) ? user_role['user_id'] : user_role[0]
      role_id = user_role.is_a?(Hash) ? user_role['role_id'] : user_role[1]
      is_default = user_role.is_a?(Hash) ? user_role['is_default'] : user_role[2]

      # Check if this is a global user
      global_user = connection.execute(
        "SELECT global FROM users WHERE id = #{user_id} LIMIT 1"
      ).first

      if global_user && (global_user.is_a?(Hash) ? global_user['global'] : global_user[0])
        # Check if user already has a default role to avoid constraint violation
        existing_default = connection.execute(
          "SELECT id FROM user_roles WHERE user_id = #{user_id} AND is_default = true LIMIT 1"
        ).first

        # If user already has a default, don't set this as default
        final_is_default = existing_default ? false : is_default

        # Restore global role (project_id = NULL)
        connection.execute(
          "INSERT INTO user_roles (user_id, role_id, project_id, is_default, created_at, updated_at)
           VALUES (#{user_id}, #{role_id}, NULL, #{final_is_default}, NOW(), NOW())"
        )
      end
    end

    # Remove organization project user roles
    user_roles_count = connection.execute(
      "SELECT COUNT(*) as count FROM user_roles WHERE project_id = #{athar_project_id}"
    ).first

    count = user_roles_count.is_a?(Hash) ? user_roles_count['count'] : user_roles_count[0]

    connection.execute("DELETE FROM user_roles WHERE project_id = #{athar_project_id}")
    connection.execute("DELETE FROM projects WHERE id = #{athar_project_id}")

    puts "✅ Reverted #{count} user role assignments and removed #{org_project_name} project"
    puts "✅ Restored global roles (project_id = NULL) for global users"
  end
end
