class MigrateCaseManagerToCm < ActiveRecord::Migration[8.0]
  def up
    # Update permissions from case_manager to cm
    execute(<<-SQL)
      UPDATE permissions
      SET system_name = 'cm'
      WHERE system_name = 'case_manager'
    SQL

    # Update approval workflows if table exists
    if table_exists?(:approval_workflows)
      execute(<<-SQL)
        UPDATE approval_workflows
        SET system_name = 'cm'
        WHERE system_name = 'case_manager'
      SQL
    end
  end

  def down
    # Rollback permissions from cm to case_manager
    execute(<<-SQL)
      UPDATE permissions
      SET system_name = 'case_manager'
      WHERE system_name = 'cm'
    SQL

    # Rollback approval workflows if table exists
    if table_exists?(:approval_workflows)
      execute(<<-SQL)
        UPDATE approval_workflows
        SET system_name = 'case_manager'
        WHERE system_name = 'cm'
      SQL
    end
  end
end
