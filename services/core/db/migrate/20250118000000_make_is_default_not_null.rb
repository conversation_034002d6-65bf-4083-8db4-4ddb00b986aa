class MakeIsDefaultNotNull < ActiveRecord::Migration[8.0]
  def up
    # First, update any existing NULL values to false
    execute "UPDATE user_roles SET is_default = false WHERE is_default IS NULL"

    # Then add the NOT NULL constraint
    change_column_null :user_roles, :is_default, false
  end

  def down
    # Remove the NOT NULL constraint
    change_column_null :user_roles, :is_default, true
  end
end
