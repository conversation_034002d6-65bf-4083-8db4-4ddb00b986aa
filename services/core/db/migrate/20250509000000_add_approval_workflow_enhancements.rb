class AddApprovalWorkflowEnhancements < ActiveRecord::Migration[8.0]
  def change
    # Add fields to approval_steps
    add_column :approval_steps, :condition, :string
    add_column :approval_steps, :skip_if_no_approvers, :boolean, default: false
    add_column :approval_steps, :static_approver_ids, :json

    # Add fields to approval_workflows
    add_column :approval_workflows, :condition, :string
    add_column :approval_workflows, :required_context_keys, :json
    add_column :approval_workflows, :empty_steps_behavior, :string, default: 'auto_approve'
  end
end
