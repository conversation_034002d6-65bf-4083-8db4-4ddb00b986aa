name: athar-core

services:
  app:
    hostname: core-app
    build:
      context: ./
      dockerfile: ./development.dockerfile
      args:
        BUNDLE_GEM__FURY__IO: ${BUNDLE_GEM__FURY__IO}
    #command: tail -f /dev/null
    #command: debug
    #command: dev
    env_file:
      - docker-compose.env
    depends_on:
      - core-db
    ports:
      - "1236:1234"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.core.rule=Host(`core.athar.test`)"
      - "traefik.http.routers.core.entrypoints=web,websecure"
      - "traefik.http.services.core.loadbalancer.server.port=3000"
      - "traefik.http.routers.core.tls=false"

      # Health Check Configuration
#      - "traefik.http.services.core.loadbalancer.healthcheck.path=/up"
#      - "traefik.http.services.core.loadbalancer.healthcheck.interval=10s"
#      - "traefik.http.services.core.loadbalancer.healthcheck.timeout=3s"

    networks:
      - athar-network
    volumes:
      - ./bin/docker-entrypoint:/rails/bin/docker-entrypoint
      - ./config/master.key:/rails/config/master.key
      - ./:/rails
      - /Users/<USER>/workspace/athar/athar-ems/gems/auth-gem:/Users/<USER>/workspace/athar/athar-ems/gems/auth-gem
      - /Users/<USER>/workspace/athar/athar-ems/gems/rpc-gem:/Users/<USER>/workspace/athar/athar-ems/gems/rpc-gem
      - /Users/<USER>/workspace/athar/athar-ems/gems/commons-gem:/Users/<USER>/workspace/athar/athar-ems/gems/commons-gem

  grpc:
    hostname: core-app-grpc
    build:
      context: ./
      dockerfile: ./development.dockerfile
      args:
        BUNDLE_GEM__FURY__IO: ${BUNDLE_GEM__FURY__IO}
    command: grpc
#    command: tail -f /dev/null
    env_file:
      - docker-compose.env
    depends_on:
      - core-db
    labels:
      - "traefik.enable=true"
      - "traefik.tcp.routers.grpc-core.rule=HostSNI(`grpc.core.athar.test`)"
      - "traefik.tcp.routers.grpc-core.entrypoints=grpc"
      - "traefik.tcp.routers.grpc-core.tls=true"
      - "traefik.tcp.routers.grpc-core.service=grpc-core"
      - "traefik.tcp.services.grpc-core.loadbalancer.server.port=10541"
      - "traefik.tcp.routers.grpc-core.tls.passthrough=true"
      - "traefik.tcp.routers.grpc-core.tls.options=default"
      - "traefik.tcp.routers.grpc-core.tls.certresolver=myresolver"

    networks:
      - athar-network
    volumes:
      - ./bin/docker-entrypoint:/rails/bin/docker-entrypoint
      - ./config/master.key:/rails/config/master.key
      - ./:/rails
      - /Users/<USER>/workspace/athar/athar-ems/gems/auth-gem:/Users/<USER>/workspace/athar/athar-ems/gems/auth-gem
      - /Users/<USER>/workspace/athar/athar-ems/gems/rpc-gem:/Users/<USER>/workspace/athar/athar-ems/gems/rpc-gem
      - /Users/<USER>/workspace/athar/athar-ems/gems/commons-gem:/Users/<USER>/workspace/athar/athar-ems/gems/commons-gem

  core-db:
    image: postgres:17
    container_name: core-db
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_DB: core_athar_db
    ports:
      - "5433:5432"
    networks:
      - athar-network
    labels:
      - "traefik.enable=false"

networks:
  athar-network:
    external: true
    name: athar-backend_athar-network
