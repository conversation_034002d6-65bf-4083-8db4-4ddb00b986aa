Rails.application.routes.draw do
  apipie

  scope :api do
    # devise_for :users,
    #            skip: [:sessions, :registrations],
    #            controllers: { passwords: "users/passwords" }

    # devise_scope :user do
    # post 'auth/session_token', to: 'users/sessions#session_token'
    post "auth/token", to: "users/sessions#create"
    post "auth/session_token", to: "users/sessions#session_token"
    post "auth/switch_project", to: "users/sessions#switch_project"
    # end

    # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html

    # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
    # Can be used by load balancers and uptime monitors to verify that the app is live.

    resources :users, only: [] do
      collection do
        get :me
        patch :me, to: "users#update"
        patch :'/me/password', to: "users#change_password"
        get :'/me/permissions', to: "users#permissions"
      end
    end
  end

  namespace :api do
    resources :projects
    resources :roles

    # Mount shared session endpoints from auth gem
    mount AtharAuth::Engine, at: '/session'
  end

  get "up" => "rails/health#show", as: :rails_health_check

  # Defines the root path route ("/")
  root "application#index"
end
