# frozen_string_literal: true

AtharAuth.configure do |config|
  # If you stored the key at the top level in credentials:
  config.secret_key = Rails.application.credentials.devise_jwt_secret_key!

  # Or if you have environment-specific credentials:
  # config.secret_key = Rails.application.credentials.dig!(Rails.env.to_sym, :devise_jwt_secret_key)

  # Optionally, you can use a different algorithm if needed
  config.algorithm = "HS256"

  # Custom class to decode the user from the token
  # Use AtharAuth::Models::User for token-based authentication instead of ActiveRecord User
  # config.user_class = "User"  # This was causing authentication failures

  # Main token expiration time
  # TODO: temporarily - return back to 30 days
  config.main_token_expire_time = 1.hour

  # Session token expiration time
  # TODO: temporarily - return back to 1 day
  config.session_token_expire_time = 15.minutes

  # Organization project configuration
  config.organization_project_name = "Athar"
end
