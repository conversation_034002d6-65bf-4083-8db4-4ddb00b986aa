# frozen_string_literal: true

require 'athar_rpc'

Gruf.configure do |config|
  config.server_binding_url = "#{ENV['GRPC_SERVER_HOST']}:#{ENV['GRPC_SERVER_PORT']}" if ENV['GRPC_SERVER_HOST'] && ENV['GRPC_SERVER_PORT']

  rpc_server_options = {}
  rpc_server_options[:pool_size] = ENV['GRPC_SERVER_POOL_SIZE'].to_i if ENV['GRPC_SERVER_POOL_SIZE']
  rpc_server_options[:pool_keep_alive] = ENV['GRPC_SERVER_POOL_KEEP_ALIVE'].to_i if ENV['GRPC_SERVER_POOL_KEEP_ALIVE']
  rpc_server_options[:poll_period] = ENV['GRPC_SERVER_POLL_PERIOD'].to_i if ENV['GRPC_SERVER_POLL_PERIOD']

  config.rpc_server_options = config.rpc_server_options.merge(rpc_server_options) unless rpc_server_options.empty?

  config.interceptors.use Gruf::Interceptors::Instrumentation::RequestLogging::Interceptor, logger: Rails.logger
  config.error_serializer = Gruf::Serializers::Errors::Json
  config.use_ssl = false
end
