require_all 'lib/core_extensions'

Rails.application.configure do
  # this is a work around to monkey patch after all of the gems are initialized
  config.after_initialize do
    return if ARGV.any? { |command| command.include? 'assets:' }

    if Object.const_defined?('ActiveStorage::Blob')
      ActiveStorage::Blob.include(CoreExtensions::ActiveStorage::Blob::CdnDecorator)
    end

    if Object.const_defined?('ActiveStorage::Attached::One')
      ActiveStorage::Attached::One.include(CoreExtensions::ActiveStorage::Blob::CdnDecorator)
    end
  rescue
    warn 'Failed to patch `ActiveStorage` extensions'
  end
end
