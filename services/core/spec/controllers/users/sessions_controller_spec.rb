require 'rails_helper'

RSpec.describe Users::SessionsController, type: :controller do
  describe 'POST #create' do
    let(:user) { create(:user, password: 'password123') }
    let(:valid_params) { { user: { email: user.email, password: 'password123' } } }
    let(:invalid_params) { { user: { email: user.email, password: 'wrong_password' } } }

    context 'with valid credentials' do
      before { post :create, params: valid_params }

      it 'returns a successful response' do
        expect(response).to have_http_status(:created)
      end

      it 'returns a main token' do
        json = JSON.parse(response.body)
        expect(json['main_token']).to be_present
      end

      it 'returns user information' do
        json = JSON.parse(response.body)
        expect(json['user']['id']).to eq(user.id)
        expect(json['user']['email']).to eq(user.email)
      end
    end

    context 'with valid credentials but different email case' do
      let(:uppercase_email_params) { { user: { email: user.email.upcase, password: 'password123' } } }
      let(:mixed_case_email_params) { { user: { email: user.email.swapcase, password: 'password123' } } }

      it 'returns successful response with uppercase email' do
        post :create, params: uppercase_email_params
        expect(response).to have_http_status(:created)

        json = JSON.parse(response.body)
        expect(json['main_token']).to be_present
        expect(json['user']['id']).to eq(user.id)
        expect(json['user']['email']).to eq(user.email) # Should return original email case
      end

      it 'returns successful response with mixed case email' do
        post :create, params: mixed_case_email_params
        expect(response).to have_http_status(:created)

        json = JSON.parse(response.body)
        expect(json['main_token']).to be_present
        expect(json['user']['id']).to eq(user.id)
        expect(json['user']['email']).to eq(user.email) # Should return original email case
      end

      it 'handles edge cases with whitespace and case variations' do
        edge_case_params = { user: { email: " #{user.email.upcase} ", password: 'password123' } }
        post :create, params: edge_case_params
        expect(response).to have_http_status(:created)

        json = JSON.parse(response.body)
        expect(json['main_token']).to be_present
        expect(json['user']['id']).to eq(user.id)
      end
    end

    context 'with invalid credentials' do
      before { post :create, params: invalid_params }

      it 'returns unauthorized status' do
        expect(response).to have_http_status(:unauthorized)
      end

      it 'returns an error message' do
        json = JSON.parse(response.body)
        expect(json['error']).to be_present
      end
    end
  end

  describe 'POST #session_token' do
    let(:user) { create(:user) }
    let(:role) { create(:role, name: 'test_session_role') }
    let(:project) { create(:project) }
    let(:core_permission) { create(:permission, name: 'core_session_perm', action: 'read', subject_class: 'User', system_name: 'core') }
    let(:cm_permission) { create(:permission, name: 'cm_session_perm', action: 'read', subject_class: 'Case', system_name: 'cm') }
    let(:main_token) { generate_main_token(user) }
    let(:valid_params) { { scope: 'core' } }
    let(:invalid_scope_params) { { scope: 'invalid_scope' } }

    before do
      role.permissions << [ core_permission, cm_permission ]
      create(:user_role, user: user, role: role, project: project, is_default: true)
    end

    context 'with valid main token and scope' do
      before do
        sign_in_with_main_token(user)
        post :session_token, params: valid_params
      end

      it 'returns a successful response' do
        expect(response).to have_http_status(:created)
      end

      it 'returns a session token' do
        json = JSON.parse(response.body)
        expect(json['session_token']).to be_present
      end

      it 'returns the correct scope' do
        json = JSON.parse(response.body)
        expect(json['scope']).to eq('core')
      end
    end

    context 'with invalid scope' do
      before do
        sign_in_with_main_token(user)
        post :session_token, params: invalid_scope_params
      end

      it 'returns unprocessable entity status' do
        expect(response).to have_http_status(:unprocessable_entity)
      end

      it 'returns an error message' do
        json = JSON.parse(response.body)
        expect(json['error']).to be_present
      end
    end

    context 'without main token' do
      before { post :session_token, params: valid_params }

      it 'returns unauthorized status' do
        expect(response).to have_http_status(:unauthorized)
      end
    end

    context 'with cm scope' do
      let(:cm_params) { { scope: 'cm' } }

      before do
        sign_in_with_main_token(user)
        post :session_token, params: cm_params
      end

      it 'returns a successful response' do
        expect(response).to have_http_status(:created)
      end

      it 'returns the correct scope' do
        json = JSON.parse(response.body)
        expect(json['scope']).to eq('cm')
      end
    end
  end
end
