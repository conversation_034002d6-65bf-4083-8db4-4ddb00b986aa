require 'rails_helper'

RSpec.describe UsersController, type: :controller do
  describe 'GET #me' do
    let(:role) { create(:role, name: 'employee') }
    let(:permission) { create(:permission, name: 'read_user_me', action: 'read', subject_class: 'User', system_name: 'core') }
    let(:user) { create(:user) }
    let(:project) { create(:project) }

    before do
      role.permissions << permission
      create(:user_role, user: user, role: role, project: project, is_default: true)
    end

    context 'when authenticated' do
      before do
        sign_in(user, 'core', project)
        get :me
      end

      it 'returns a successful response' do
        expect(response).to have_http_status(:success)
      end

      it 'returns the current user' do
        json = JSON.parse(response.body)
        expect(json['data']['id']).to eq(user.id.to_s)
        expect(json['data']['attributes']['email']).to eq(user.email)
      end
    end

    context 'when not authenticated' do
      before { get :me }

      it 'returns unauthorized status' do
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end
  describe 'GET #permissions' do
    let(:role) { create(:role, name: 'employee') }
    let(:permission1) { create(:permission, name: 'read_user_perms', action: 'read', subject_class: 'User', system_name: 'core') }
    let(:permission2) { create(:permission, name: 'update_user_perms', action: 'update', subject_class: 'User', system_name: 'core') }
    let(:user) { create(:user) }
    let(:project) { create(:project) }

    before do
      role.permissions << [ permission1, permission2 ]
      create(:user_role, user: user, role: role, project: project, is_default: true)
    end

    context 'when authenticated' do
      before do
        sign_in(user, 'core', project)
        get :permissions
      end

      it 'returns a successful response' do
        expect(response).to have_http_status(:success)
      end

      it 'returns the user permissions in JSON:API format' do
        json = JSON.parse(response.body)

        # Check that data is an array of permission objects
        expect(json['data']).to be_an(Array)
        expect(json['data'].length).to eq(2)

        # Check the structure of the first permission object
        permission = json['data'].first
        expect(permission['id']).to be_present
        expect(permission['type']).to eq('permission')
        expect(permission['attributes']).to be_present
        expect(permission['attributes']['action']).to be_present
        expect(permission['attributes']['subject']).to be_present

        # Check meta information
        expect(json['meta']['scope']).to eq('core')
        expect(json['meta']['count']).to eq(2)
      end
    end

    context 'when not authenticated' do
      before { get :permissions }

      it 'returns unauthorized status' do
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end
end
