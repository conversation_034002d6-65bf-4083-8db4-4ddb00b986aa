module AuthHelpers
  # Generate a valid main token for testing
  def generate_main_token(user)
    payload = {
      sub: user.id,
      token_type: "main",
      email: user.email,
      name: user.name,
      exp: AtharAuth.main_token_expire_time.from_now.to_i
    }
    AtharAuth.encode_token(payload)
  end

  # Generate a valid session token for testing
  def generate_session_token(user, scope = 'core', project = nil)
    # Ensure user has permissions for the requested system
    ensure_user_has_system_permissions(user, scope, project)

    role = user.user_roles.by_project(project).first&.role
    payload = user.generate_session_token(scope, role, project, AtharAuth.session_token_expire_time.from_now.to_i)
    AtharAuth.encode_token(payload)
  end

  private

  # Ensure user has the necessary permissions for the system
  def ensure_user_has_system_permissions(user, scope, project = nil)
    # Find or create a role with permissions for this system
    role = user.user_roles.by_project(project).first&.role

    unless role
      # Create a default role for the user if none exists
      role = FactoryBot.create(:role, name: "test_#{scope}_role", scope: project ? :project_based : :global_role)
      FactoryBot.create(:user_role, user: user, role: role, project: project, is_default: true)
    end

    # Ensure the role has permissions for the requested system
    unless role.permissions.where(system_name: scope).exists?
      # Create a basic permission for this system
      permission = FactoryBot.create(:permission,
        name: "test_#{scope}_permission_#{SecureRandom.hex(4)}",
        action: "read",
        subject_class: "User",
        system_name: scope
      )
      role.permissions << permission unless role.permissions.include?(permission)
    end
  end

  # Set the Authorization header with a valid session token
  def auth_headers(user, scope = 'core', project = nil)
    token = generate_session_token(user, scope, project)
    { 'Authorization' => "Bearer #{token}" }
  end

  # Helper to authenticate in controller specs with session token
  def sign_in(user, scope = 'core', project = nil)
    token = generate_session_token(user, scope, project)
    request.headers['Authorization'] = "Bearer #{token}"
  end

  # Helper to authenticate with main token in controller specs
  def sign_in_with_main_token(user)
    token = generate_main_token(user)
    request.headers['Authorization'] = "Bearer #{token}"
  end
end

RSpec.configure do |config|
  config.include AuthHelpers, type: :request
  config.include AuthHelpers, type: :controller
end
