FactoryBot.define do
  factory :user do
    name { Faker::Name.name }
    email { Faker::Internet.unique.email }
    password { 'password123' }
    status { :active }
    global { false }

    trait :with_global_role do
      global { true }

      after(:create) do |user, evaluator|
        role = create(:role, scope: :global_role)
        create(:user_role, user: user, role: role, is_default: true)
      end
    end

    trait :with_project_role do
      global { false }

      transient do
        project { create(:project) }
        role_name { 'project_manager' }
      end

      after(:create) do |user, evaluator|
        role = create(:role, name: evaluator.role_name, scope: :project_based)
        create(:user_role, user: user, role: role, project: evaluator.project, is_default: true)
      end
    end

    factory :global_user, traits: [ :with_global_role ]
    factory :project_user, traits: [ :with_project_role ]
  end
end
