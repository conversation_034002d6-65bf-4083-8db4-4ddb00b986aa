FactoryBot.define do
  factory :role do
    sequence(:name) { |n| "role_#{n}" }
    scope { :project_based }

    trait :global_role do
      scope { :global_role }
    end

    factory :super_admin_role do
      name { 'super_admin' }
      scope { :global_role }
    end

    factory :admin_role do
      name { 'admin' }
      scope { :global_role }
    end

    factory :employee_role do
      name { 'employee' }
      scope { :project_based }
    end
  end
end
