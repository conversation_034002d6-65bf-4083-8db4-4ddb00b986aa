require 'rails_helper'

RSpec.describe Api::ProjectsController, type: :request do
  let(:user) { create(:user, global: true) }
  let(:role) { create(:role, name: 'admin', scope: :global_role) }
  let(:permission) { create(:permission, name: 'manage_projects', action: 'manage', subject_class: 'Project', system_name: 'core') }

  before do
    role.permissions << permission
    create(:user_role, user: user, role: role, project: nil, is_default: true)
  end

  let(:token) { user.generate_session_token('core', role, nil, 1.day.from_now.to_i) }
  let(:headers) { { 'Authorization' => "Bearer #{AtharAuth.encode_token(token)}" } }

  before do
    # Create permissions for the user
    permission = create(:permission, name: 'Read Projects', action: 'read', subject_class: 'Project', system_name: 'core')
    create_permission = create(:permission, name: 'Create Project', action: 'create', subject_class: 'Project', system_name: 'core')
    update_permission = create(:permission, name: 'Update Project', action: 'update', subject_class: 'Project', system_name: 'core')
    destroy_permission = create(:permission, name: 'Destroy Project', action: 'destroy', subject_class: 'Project', system_name: 'core')

    # Add permissions to the role
    role.permissions << permission
    role.permissions << create_permission
    role.permissions << update_permission
    role.permissions << destroy_permission

    # Assign the role to the user
    user.user_roles.create(role: role, project: nil)
  end

  describe 'GET /api/projects' do
    before do
      create_list(:project, 3)
    end

    it 'returns a list of projects' do
      get '/api/projects', headers: headers

      expect(response).to have_http_status(:ok)
      json = JSON.parse(response.body)
      expect(json['data'].size).to eq(3)
      expect(json['data'][0]['type']).to eq('project')
      expect(json['meta']).to have_key('pagination')
    end

    it 'filters projects by name' do
      create(:project, name: 'Special Project')

      get '/api/projects', params: { filter: { name_cont: 'Special' } }, headers: headers

      expect(response).to have_http_status(:ok)
      json = JSON.parse(response.body)
      expect(json['data'].size).to eq(1)
      expect(json['data'][0]['attributes']['name']).to eq('Special Project')
    end

    it 'paginates results' do
      create_list(:project, 10)

      get '/api/projects', params: { page: { number: 2, size: 5 } }, headers: headers

      expect(response).to have_http_status(:ok)
      json = JSON.parse(response.body)
      expect(json['data'].size).to eq(5)
      expect(json['meta']['pagination']['page']).to eq(2)
    end
  end

  describe 'GET /api/projects/:id' do
    let(:project) { create(:project) }

    it 'returns a specific project' do
      get "/api/projects/#{project.id}", headers: headers

      expect(response).to have_http_status(:ok)
      json = JSON.parse(response.body)
      expect(json['data']['id']).to eq(project.id.to_s)
      expect(json['data']['attributes']['name']).to eq(project.name)
    end

    it 'returns 404 for non-existent project' do
      get '/api/projects/999999', headers: headers

      expect(response).to have_http_status(:not_found)
    end
  end

  describe 'POST /api/projects' do
    let(:valid_attributes) do
      {
        data: {
          type: 'project',
          attributes: {
            name: 'New Project',
            description: 'A new project description',
            status: 'active'
          }
        }
      }
    end

    it 'creates a new project' do
      expect {
        post '/api/projects', params: valid_attributes, headers: headers
      }.to change(Project, :count).by(1)

      expect(response).to have_http_status(:created)
      json = JSON.parse(response.body)
      expect(json['data']['attributes']['name']).to eq('New Project')
    end

    it 'returns validation errors for invalid data' do
      invalid_attributes = {
        data: {
          type: 'project',
          attributes: {
            name: '',
            description: 'Invalid project with no name'
          }
        }
      }

      post '/api/projects', params: invalid_attributes, headers: headers

      expect(response).to have_http_status(:unprocessable_entity)
      json = JSON.parse(response.body)
      expect(json['errors']).to be_present
    end
  end

  describe 'PATCH /api/projects/:id' do
    let(:project) { create(:project) }
    let(:update_attributes) do
      {
        data: {
          id: project.id.to_s,
          type: 'project',
          attributes: {
            name: 'Updated Project Name',
            description: 'Updated description'
          }
        }
      }
    end

    it 'updates the project' do
      patch "/api/projects/#{project.id}", params: update_attributes, headers: headers

      expect(response).to have_http_status(:ok)
      json = JSON.parse(response.body)
      expect(json['data']['attributes']['name']).to eq('Updated Project Name')
      expect(project.reload.name).to eq('Updated Project Name')
    end

    it 'returns validation errors for invalid data' do
      invalid_update = {
        data: {
          id: project.id.to_s,
          type: 'project',
          attributes: {
            name: ''
          }
        }
      }

      patch "/api/projects/#{project.id}", params: invalid_update, headers: headers

      expect(response).to have_http_status(:unprocessable_entity)
      json = JSON.parse(response.body)
      expect(json['errors']).to be_present
    end

    it 'updates the project status' do
      status_update = {
        data: {
          id: project.id.to_s,
          type: 'project',
          attributes: {
            status: 'inactive'
          }
        }
      }

      patch "/api/projects/#{project.id}", params: status_update, headers: headers

      expect(response).to have_http_status(:ok)
      json = JSON.parse(response.body)
      expect(json['data']['attributes']['status']).to eq('inactive')
      expect(project.reload.status).to eq('inactive')
    end
  end

  describe 'DELETE /api/projects/:id' do
    let!(:project) { create(:project) }

    it 'deletes the project' do
      expect {
        delete "/api/projects/#{project.id}", headers: headers
      }.to change(Project, :count).by(-1)

      expect(response).to have_http_status(:no_content)
    end
  end
end
