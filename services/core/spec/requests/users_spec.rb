require 'rails_helper'

RSpec.describe 'Users API', type: :request do
  describe 'GET /api/users/me' do
    let(:role) { create(:role, name: 'employee') }
    let(:permission) { create(:permission, name: 'read_user_api_spec', action: 'read', subject_class: 'User', system_name: 'core') }
    let(:user) { create(:user) }
    let(:project) { create(:project) }

    before do
      role.permissions << permission
      create(:user_role, user: user, role: role, project: project, is_default: true)
    end

    context 'when authenticated with valid token' do
      before do
        get '/api/users/me', headers: auth_headers(user, 'core', project)
      end

      it 'returns status code 200' do
        expect(response).to have_http_status(200)
      end

      it 'returns the user information' do
        expect(json_response['data']).to be_present
        expect(json_response['data']['id']).to eq(user.id.to_s)
        expect(json_response['data']['attributes']['email']).to eq(user.email)
      end
    end

    context 'when not authenticated' do
      before { get '/api/users/me' }

      it 'returns status code 401' do
        expect(response).to have_http_status(401)
      end

      it 'returns an error message' do
        expect(json_response['error']).to be_present
      end
    end
  end
end
