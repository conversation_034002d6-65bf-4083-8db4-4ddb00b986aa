require 'rails_helper'

RSpec.describe User, type: :model do
  describe 'associations' do
    it { should have_one_attached(:avatar) }
    it { should have_many(:user_roles).dependent(:destroy) }
    it { should have_many(:roles).through(:user_roles) }
    it { should have_many(:projects).through(:user_roles) }
    it { should have_many(:permissions).through(:roles) }
  end

  describe 'validations' do
    it { should validate_presence_of(:email) }
    it { should validate_uniqueness_of(:email).case_insensitive }
    it { should allow_value('<EMAIL>').for(:email) }
    it { should_not allow_value('invalid_email').for(:email) }

    context 'when creating a new user' do
      it { should validate_length_of(:password).is_at_least(6) }
    end
  end

  describe 'enums' do
    it 'defines status enum' do
      expect(User.statuses).to eq({ 'inactive' => false, 'active' => true })
    end

    it 'defines global enum' do
      expect(User.globals).to eq({ 'project_based' => false, 'global_user' => true })
    end
  end

  describe 'callbacks' do
    it 'sets default status to active' do
      user = User.new
      expect(user.status).to eq('active')
    end

    describe 'default project assignment' do
      let(:user) { create(:user, global: false) }
      let(:project1) { create(:project, name: 'Project 1') }
      let(:project2) { create(:project, name: 'Project 2') }
      let(:role) { create(:role, name: 'test_role') }

      context 'when user has no default project' do
        it 'auto-assigns first role as default when user is saved' do
          # Create user roles without default, but auto-assignment will kick in
          create(:user_role, user: user, project: project1, role: role, is_default: false)
          create(:user_role, user: user, project: project2, role: role, is_default: false)

          # Remove all defaults to test the auto-assignment
          user.user_roles.update_all(is_default: false)
          expect(user.user_roles.exists?(is_default: true)).to be false

          user.save!
          user.reload

          expect(user.user_roles.exists?(is_default: true)).to be true
          default_role = user.user_roles.find_by(is_default: true)
          expect(default_role.project).to eq(project1) # First project becomes default
        end
      end

      context 'when user already has a default project' do
        before do
          create(:user_role, user: user, project: project1, role: role, is_default: true)
          create(:user_role, user: user, project: project2, role: role, is_default: false)
        end

        it 'does not change the existing default' do
          expect(user.user_roles.where(is_default: true).count).to eq(1)

          user.save!
          user.reload

          expect(user.user_roles.where(is_default: true).count).to eq(1)
          default_role = user.user_roles.find_by(is_default: true)
          expect(default_role.project).to eq(project1)
        end
      end
    end
  end

  describe '#has_role?' do
    let(:user) { create(:user) }
    let(:project) { create(:project) }
    let(:role) { create(:role, name: 'test_employee_role') }

    context 'when user has the role in the project' do
      before do
        create(:user_role, user: user, role: role, project: project)
      end

      it 'returns true' do
        expect(user.has_role?('test_employee_role', project)).to be true
      end
    end

    context 'when user does not have the role in the project' do
      it 'returns false' do
        expect(user.has_role?('test_employee_role', project)).to be false
      end
    end

    context 'when user has a global role' do
      let(:global_role) { create(:role, name: 'test_admin_role', scope: :global_role) }
      let(:athar_project) { Project.find_by(name: 'Athar') || create(:project, name: 'Athar') }

      before do
        user.update(global: true)
        create(:user_role, user: user, role: global_role, project: athar_project)
      end

      it 'returns true when checking for global role' do
        expect(user.has_role?('test_admin_role')).to be true
      end
    end
  end

  describe '#add_role' do
    let(:user) { create(:user) }
    let(:project) { create(:project) }
    let!(:role) { create(:role, name: 'test_add_employee_role') }

    it 'adds a role to the user in the specified project' do
      expect {
        user.add_role('test_add_employee_role', project)
      }.to change { user.user_roles.count }.by(1)

      expect(user.has_role?('test_add_employee_role', project)).to be true
    end

    context 'with a global role' do
      let!(:global_role) { create(:role, name: 'test_add_admin_role', scope: :global_role) }
      let(:athar_project) { Project.find_by(name: 'Athar') || create(:project, name: 'Athar') }
      let(:global_user) { create(:user, global: true) }

      it 'rejects adding global role to project-based user' do
        expect {
          user.add_role('test_add_admin_role', nil)
        }.not_to change { user.user_roles.count }

        expect(user.has_role?('test_add_admin_role', athar_project)).to be false
      end

      it 'adds global role to global user with Athar project' do
        expect {
          global_user.add_role('test_add_admin_role', nil)
        }.to change { global_user.user_roles.count }.by(1)

        # Global roles are assigned to the Athar organization project
        user_role = global_user.user_roles.last
        expect(user_role.project).to eq(athar_project)
        expect(user_role.role).to eq(global_role)
        expect(global_user.has_role?('test_add_admin_role', athar_project)).to be true
      end
    end
  end

  describe '#generate_session_token' do
    let(:user) { create(:user) }
    let(:role) { create(:role) }
    let(:project) { create(:project) }
    let(:exp_time) { 1.day.from_now.to_i }

    before do
      # Ensure user has a user_role for system access validation
      create(:user_role, user: user, role: role, project: project)
    end

    context 'with valid scopes' do
      it 'accepts core scope' do
        # Create permission for core system
        permission = create(:permission, system_name: 'core')
        role.permissions << permission

        token = user.generate_session_token('core', role, project, exp_time)
        expect(token[:sc]).to eq('core')  # New compressed format
      end

      it 'accepts cm scope' do
        # Create permission for cm system
        permission = create(:permission, system_name: 'cm')
        role.permissions << permission

        token = user.generate_session_token('cm', role, project, exp_time)
        expect(token[:sc]).to eq('cm')  # New compressed format
      end

      it 'finds cm permissions correctly' do
        # Create a permission with cm system_name
        permission = create(:permission, system_name: 'cm')
        role.permissions << permission

        token = user.generate_session_token('cm', role, project, exp_time)

        # The token should include the cm permissions in compressed format
        expect(token[:sc]).to eq('cm')  # New compressed format
        expect(token[:ss][:p]).to include(permission.to_s)  # Permissions in session context
      end
    end

    context 'with invalid scope' do
      it 'raises ArgumentError for invalid scope' do
        expect {
          user.generate_session_token('invalid_scope', role, project, exp_time)
        }.to raise_error(ArgumentError, /Invalid scope/)
      end
    end
  end
end
