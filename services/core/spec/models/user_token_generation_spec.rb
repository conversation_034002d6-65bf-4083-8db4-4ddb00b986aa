# frozen_string_literal: true

require "rails_helper"

RSpec.describe User, type: :model do
  describe "#generate_session_token" do
    let(:project) { create(:project, name: "Beta Project", description: "A test project", status: "active") }
    let(:role) { create(:role, name: "test_case_manager_#{SecureRandom.hex(4)}", level: 10, scope: "project_based") }
    let(:permission) { create(:permission, name: "read:case", action: "read", subject_class: "Case", system_name: "cm") }

    before do
      role.permissions << permission
    end

    context "with project-based user" do
      let(:user) { create(:user, name: "<PERSON>", email: "<EMAIL>", global: "project_based") }
      let(:user_role) { create(:user_role, user: user, role: role, project: project) }

      before { user_role }

      it "generates compressed token with JSONPath structure" do
        exp_time = 1.hour.from_now.to_i
        token = user.generate_session_token("cm", role, project, exp_time)

        # Standard JWT claims
        expect(token[:s]).to eq(user.id)  # 'user.id' => 's'
        expect(token[:i]).to be_within(5).of(Time.now.to_i)  # 'issued.at' => 'i'
        expect(token[:e]).to eq(exp_time)  # 'expires.at' => 'e'
        expect(token[:t]).to eq("session")  # 'token.type' => 't'
        expect(token[:sc]).to eq("cm")  # 'scope' => 'sc'

        # User context
        user_context = token[:u]
        expect(user_context[:n]).to eq("Ahmed Ali")  # 'user.name' => 'u.n'
        expect(user_context[:m]).to eq("<EMAIL>")  # 'user.email' => 'u.m'
        expect(user_context[:g]).to be false  # 'user.global' => 'u.g'
        expect(user_context[:ty]).to eq("project_based")  # 'user.type' => 'u.ty'

        # Session context
        session_context = token[:ss]
        expect(session_context[:r][:n]).to eq("case_manager")  # 'session.role.name' => 'ss.r.n'
        expect(session_context[:r][:l]).to eq(10)  # 'session.role.level' => 'ss.r.l'
        expect(session_context[:r][:s]).to eq("project_based")  # 'session.role.scope' => 'ss.r.s'
        expect(session_context[:p]).to include("read:case")  # 'session.permissions' => 'ss.p'
        expect(session_context[:pr][:id]).to eq(project.id)  # 'session.project.id' => 'ss.pr.id'
        expect(session_context[:pr][:n]).to eq("Beta Project")  # 'session.project.name' => 'ss.pr.n'

        # Access context
        access_context = token[:a]
        expect(access_context[:ty]).to eq("project")  # 'access.type' => 'a.ty'
        expect(access_context[:op]).to be_an(Array)  # 'access.other_projects' => 'a.op'
      end

      it "includes other accessible projects in access context" do
        # Create another project and role for the user
        other_project = create(:project, name: "Gamma Project")
        supervisor_role = create(:role, name: "test_supervisor_#{SecureRandom.hex(4)}", level: 30, scope: "project_based")
        supervisor_permission = create(:permission, name: "supervise:case", action: "supervise", subject_class: "Case", system_name: "cm")
        supervisor_role.permissions << supervisor_permission
        create(:user_role, user: user, role: supervisor_role, project: other_project)

        token = user.generate_session_token("cm", role, project, 1.hour.from_now.to_i)

        other_projects = token[:a][:op]
        expect(other_projects).to be_an(Array)
        expect(other_projects.size).to eq(1)
        expect(other_projects.first[:id]).to eq(other_project.id)
        expect(other_projects.first[:n]).to eq("Gamma Project")
        expect(other_projects.first[:r]).to eq("supervisor")
      end
    end

    context "with global user" do
      let(:global_user) { create(:user, name: "Sarah Manager", email: "<EMAIL>", global: "global_user") }
      let(:global_role) { create(:role, name: "test_hr_manager_#{SecureRandom.hex(4)}", level: 30, scope: "global_role") }
      let(:global_permission) { create(:permission, name: "manage:user", action: "manage", subject_class: "User", system_name: "cm") }

      before do
        global_role.permissions << global_permission
        create(:user_role, user: global_user, role: global_role, project: nil)
      end

      it "generates compressed token for global user" do
        token = global_user.generate_session_token("cm", global_role, nil, 1.hour.from_now.to_i)

        # User context
        user_context = token[:u]
        expect(user_context[:g]).to be true  # 'user.global' => 'u.g'
        expect(user_context[:ty]).to eq("global")  # 'user.type' => 'u.ty'

        # Session context
        session_context = token[:ss]
        expect(session_context[:r][:s]).to eq("global_role")  # 'session.role.scope' => 'ss.r.s'
        expect(session_context[:pr]).to be_nil  # No project for global user

        # Access context
        access_context = token[:a]
        expect(access_context[:ty]).to eq("global")  # 'access.type' => 'a.ty'
        expect(access_context[:prs]).to be_a(Hash)  # 'access.projects' => 'a.prs'
      end
    end

    context "with system access validation" do
      let(:user) { create(:user, global: "project_based") }
      let(:cm_permission) { create(:permission, name: "test_read_case_#{SecureRandom.hex(4)}", action: "read", subject_class: "Case", system_name: "cm") }
      let(:people_permission) { create(:permission, name: "test_read_employee_#{SecureRandom.hex(4)}", action: "read", subject_class: "Employee", system_name: "people") }

      before do
        role.permissions << cm_permission
        create(:user_role, user: user, role: role, project: project)
      end

      it "allows token generation for accessible systems" do
        expect {
          user.generate_session_token("cm", role, project, 1.hour.from_now.to_i)
        }.not_to raise_error
      end

      it "raises error for inaccessible systems" do
        expect {
          user.generate_session_token("people", role, project, 1.hour.from_now.to_i)
        }.to raise_error(ArgumentError, "User doesn't have access to People system")
      end

      it "raises error for invalid scope" do
        expect {
          user.generate_session_token("invalid", role, project, 1.hour.from_now.to_i)
        }.to raise_error(ArgumentError, "Invalid scope. Must be one of: core, people, procure, cm")
      end
    end

    context "with project validation" do
      let(:user) { create(:user, global: "project_based") }

      before do
        role.permissions << permission
        create(:user_role, user: user, role: role, project: project)
      end

      it "raises error when project is missing for project-based user" do
        # Create a project-based user without global flag
        project_user = create(:user, global: false)

        expect {
          project_user.generate_session_token("cm", role, nil, 1.hour.from_now.to_i)
        }.to raise_error(ArgumentError, "Project ID is required for non-global users")
      end
    end
  end

  describe "#can_access_system?" do
    let(:user) { create(:user) }
    let(:role) { create(:role) }
    let(:project) { create(:project) }
    let(:cm_permission) { create(:permission, system_name: "cm") }
    let(:people_permission) { create(:permission, system_name: "people") }

    before do
      role.permissions << cm_permission
      create(:user_role, user: user, role: role, project: project)
    end

    it "returns true for accessible systems" do
      expect(user.can_access_system?("cm")).to be true
    end

    it "returns false for inaccessible systems" do
      expect(user.can_access_system?("people")).to be false
    end
  end

  describe "#accessible_systems" do
    let(:user) { create(:user) }
    let(:role) { create(:role) }
    let(:project) { create(:project) }
    let(:cm_permission) { create(:permission, system_name: "cm") }
    let(:people_permission) { create(:permission, system_name: "people") }

    before do
      role.permissions << [ cm_permission, people_permission ]
      create(:user_role, user: user, role: role, project: project)
    end

    it "returns list of accessible systems" do
      expect(user.accessible_systems).to contain_exactly("cm", "people")
    end
  end
end
