require 'rails_helper'

RSpec.describe UserRole, type: :model do
  describe 'associations' do
    it { should belong_to(:user) }
    it { should belong_to(:role) }
  end

  describe 'validations' do
    let(:user) { create(:user) }
    let(:role) { create(:role) }
    let(:project) { create(:project) }

    it 'validates presence of project' do
      user_role = build(:user_role, project: nil)
      expect(user_role).not_to be_valid
      expect(user_role.errors[:project]).to include('must be present for all roles')
    end

    context 'with project' do
      it 'validates uniqueness of user_id scoped to project_id' do
        create(:user_role, user: user, role: role, project: project)
        duplicate = build(:user_role, user: user, role: create(:role), project: project)
        expect(duplicate).not_to be_valid
        expect(duplicate.errors[:user_id]).to include('can have only one role per project')
      end
    end

    context 'with global role (assigned to Athar project)' do
      let(:global_role) { create(:role, scope: :global_role) }
      let(:global_user) { create(:user, global: :global_user) }
      let(:athar_project) { Project.find_by(name: '<PERSON><PERSON>') || create(:project, name: '<PERSON><PERSON>') }

      it 'validates uniqueness of user_id scoped to project_id' do
        create(:user_role, user: global_user, role: global_role, project: athar_project)
        another_role = create(:role, name: 'another_global_role', scope: :global_role)
        duplicate = build(:user_role, user: global_user, role: another_role, project: athar_project)
        expect(duplicate).not_to be_valid
        expect(duplicate.errors[:user_id]).to include('can have only one role per project')
      end
    end

    it 'validates role-user consistency for global roles' do
      global_role = create(:role, scope: :global_role)
      user.update(global: false)  # Make user project-based
      user_role = build(:user_role, user: user, role: global_role, project: project)
      expect(user_role).not_to be_valid
      expect(user_role.errors[:base]).to include(/Global roles can only be assigned to global users/)
    end

    it 'validates role-user consistency' do
      global_role = create(:role, scope: :global_role)
      user.update(global: false)
      user_role = build(:user_role, user: user, role: global_role, project: nil)
      expect(user_role).not_to be_valid
      expect(user_role.errors[:base]).to include(/Global roles can only be assigned to global users/)
    end

    it 'validates only one default per user' do
      create(:user_role, user: user, role: role, project: project, is_default: true)
      another_role = build(:user_role, user: user, role: create(:role), project: create(:project), is_default: true)
      expect(another_role).not_to be_valid
      expect(another_role.errors[:is_default]).to include('User can only have one default project role')
    end

    it 'has is_default default to false and cannot be null' do
      # Create a user that already has a default role to test the default behavior
      existing_user = create(:user)
      create(:user_role, user: existing_user, role: role, project: project, is_default: true)

      # Now create a second role for the same user - this should default to false
      second_role = create(:role, name: 'second_role')
      user_role = UserRole.create!(user: existing_user, role: second_role, project: create(:project))
      expect(user_role.is_default).to eq(false)
      expect(user_role.is_default).to be_a(FalseClass)

      # Test that NULL values are not allowed at database level
      expect {
        UserRole.connection.execute(
          "INSERT INTO user_roles (user_id, role_id, project_id, is_default, created_at, updated_at)
           VALUES (#{user.id}, #{role.id}, #{project.id}, NULL, NOW(), NOW())"
        )
      }.to raise_error(ActiveRecord::StatementInvalid, /null value in column "is_default"/)
    end
  end

  describe 'scopes' do
    let(:project) { create(:project) }
    let!(:user_role) { create(:user_role, project: project) }
    let!(:other_user_role) { create(:user_role, project: create(:project)) }

    describe '.by_project' do
      it 'returns user roles for the specified project' do
        expect(UserRole.by_project(project)).to include(user_role)
        expect(UserRole.by_project(project)).not_to include(other_user_role)
      end
    end
  end

  describe '#set_as_default!' do
    let(:user) { create(:user) }
    let(:role) { create(:role) }
    let(:project1) { create(:project) }
    let(:project2) { create(:project) }
    let!(:user_role1) { create(:user_role, user: user, role: role, project: project1, is_default: true) }
    let!(:user_role2) { create(:user_role, user: user, role: role, project: project2, is_default: false) }

    it 'sets the user role as default and unsets any existing default' do
      expect(user_role1.is_default).to be true
      expect(user_role2.is_default).to be false

      user_role2.set_as_default!

      user_role1.reload
      user_role2.reload

      expect(user_role1.is_default).to be false
      expect(user_role2.is_default).to be true
    end
  end

  describe 'auto-assignment of default role' do
    let(:user) { create(:user, global: false) }
    let(:project) { create(:project) }
    let(:role) { create(:role) }

    context 'when creating the first role for a user' do
      it 'automatically sets the role as default' do
        expect(user.user_roles.exists?(is_default: true)).to be false

        user_role = create(:user_role, user: user, project: project, role: role, is_default: false)

        expect(user_role.reload.is_default).to be true
      end
    end

    context 'when user already has a default role' do
      let!(:existing_role) { create(:user_role, user: user, project: project, role: role, is_default: true) }
      let(:another_project) { create(:project, name: 'Another Project') }
      let(:another_role) { create(:role, name: 'another_role') }

      it 'does not set new role as default' do
        expect(user.user_roles.where(is_default: true).count).to eq(1)

        new_user_role = create(:user_role, user: user, project: another_project, role: another_role, is_default: false)

        expect(new_user_role.reload.is_default).to be false
        expect(user.user_roles.where(is_default: true).count).to eq(1)
        expect(existing_role.reload.is_default).to be true
      end
    end

    context 'when using set_as_default! method' do
      let(:another_project) { create(:project, name: 'Another Project') }
      let(:another_role) { create(:role, name: 'another_role') }

      it 'properly handles changing default role' do
        # Create first role as default
        existing_role = create(:user_role, user: user, project: project, role: role, is_default: true)

        # Create second role (not default initially)
        new_user_role = create(:user_role, user: user, project: another_project, role: another_role, is_default: false)

        # Use set_as_default! to change default
        new_user_role.set_as_default!

        expect(new_user_role.reload.is_default).to be true
        expect(existing_role.reload.is_default).to be false
        expect(user.user_roles.where(is_default: true).count).to eq(1)
      end
    end
  end

  describe '#to_rpc_response' do
    let(:test_user) { create(:user) }
    let(:test_role) { create(:role) }
    let(:test_project) { create(:project) }
    let(:user_role) { create(:user_role, user: test_user, role: test_role, project: test_project, is_default: true) }

    it 'returns a properly formatted RPC response' do
      response = user_role.to_rpc_response

      expect(response).to be_a(Core::UserRoleResponse)
      expect(response.id).to eq(user_role.id)
      expect(response.role).to be_a(Core::RoleResponse)
      expect(response.project).to be_a(Core::ProjectResponse) if user_role.project
      expect(response.is_default).to be true
    end
  end
end
