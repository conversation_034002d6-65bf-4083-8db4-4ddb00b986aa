require 'rails_helper'

RSpec.describe Role, type: :model do
  describe 'associations' do
    it { should have_many(:user_roles).dependent(:destroy) }
    it { should have_many(:users).through(:user_roles) }
    it { should have_many(:projects).through(:user_roles) }
    it { should have_many(:role_permissions).dependent(:destroy) }
    it { should have_many(:permissions).through(:role_permissions) }
  end

  describe 'validations' do
    it { should validate_presence_of(:name) }

    it 'validates uniqueness of name' do
      create(:role, name: 'test_role')
      should validate_uniqueness_of(:name)
    end
  end

  describe 'scope attribute' do
    it 'has global_role? and project_based? methods' do
      global_role = create(:role, scope: :global_role)
      project_role = create(:role, scope: :project_based)

      expect(global_role.global_role?).to be true
      expect(global_role.project_based?).to be false
      expect(project_role.global_role?).to be false
      expect(project_role.project_based?).to be true
    end
  end

  describe 'callbacks' do
    it 'calls refresh_user_role_methods after save' do
      role = build(:role)
      expect(role).to receive(:refresh_user_role_methods)
      role.save
    end

    it 'calls refresh_user_role_methods after destroy' do
      role = create(:role)
      expect(role).to receive(:refresh_user_role_methods)
      role.destroy
    end
  end

  describe '#to_rpc_response' do
    let(:role) { create(:role, name: 'employee', scope: :project_based) }

    it 'returns a properly formatted RPC response' do
      response = role.to_rpc_response

      expect(response).to be_a(Core::RoleResponse)
      expect(response.id).to eq(role.id.to_s)
      expect(response.name).to eq('employee')
      expect(response.global).to be false
    end
  end

  describe '#refresh_user_role_methods' do
    let!(:role) { create(:role, name: 'test_role') }

    it 'defines dynamic role checking methods on User' do
      # This is a bit tricky to test since it's modifying the User class
      # We can check if the method exists after the role is created
      expect(User.new).to respond_to(:test_role?)
    end
  end
end
