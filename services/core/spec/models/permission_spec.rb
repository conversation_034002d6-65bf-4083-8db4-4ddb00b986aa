require 'rails_helper'

RSpec.describe Permission, type: :model do
  describe 'associations' do
    it { should have_many(:role_permissions) }
    it { should have_many(:roles).through(:role_permissions) }
  end

  describe 'validations' do
    it { should validate_presence_of(:name) }
    it { should validate_uniqueness_of(:name).scoped_to(:system_name) }
    it { should validate_presence_of(:action) }
    it { should validate_presence_of(:subject_class) }
    it { should validate_presence_of(:system_name) }
    it { should validate_inclusion_of(:system_name).in_array(%w[core cm people procure]) }
  end

  describe 'scopes' do
    let!(:core_permission) { create(:permission, system_name: 'core') }
    let!(:people_permission) { create(:permission, system_name: 'people') }
    let!(:cm_permission) { create(:permission, system_name: 'cm') }

    describe '.by_system' do
      it 'returns permissions for the specified system' do
        expect(Permission.by_system('core')).to include(core_permission)
        expect(Permission.by_system('core')).not_to include(people_permission)
      end

      it 'returns cm permissions when queried with cm' do
        expect(Permission.by_system('cm')).to include(cm_permission)
        expect(Permission.by_system('cm')).not_to include(core_permission)
      end
    end

    describe '.for_user' do
      let(:user) { create(:user) }
      let(:role) { create(:role) }
      let(:project) { create(:project) }

      before do
        role.permissions << core_permission
        create(:user_role, user: user, role: role, project: project)
      end

      it 'returns permissions for the specified user' do
        expect(Permission.for_user(user)).to include(core_permission)
        expect(Permission.for_user(user)).not_to include(people_permission)
      end
    end

    describe '.for_project' do
      let(:project) { create(:project) }
      let(:user) { create(:user) }
      let(:role) { create(:role) }

      before do
        role.permissions << core_permission
        create(:user_role, user: user, role: role, project: project)
      end

      it 'returns permissions for the specified project' do
        expect(Permission.for_project(project)).to include(core_permission)
        expect(Permission.for_project(project)).not_to include(people_permission)
      end
    end
  end

  describe '#to_s' do
    let(:permission) { build(:permission, action: 'read', subject_class: 'User') }

    it 'returns a string representation of the permission' do
      expect(permission.to_s).to eq('read:user')
    end
  end
end
