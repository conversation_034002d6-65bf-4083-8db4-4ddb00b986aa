require 'rails_helper'

RSpec.describe CoreExtensions::ActiveStorage::Blob::CdnDecorator do
  describe '#cdn_url' do
    let(:blob) { ActiveStorage::Blob.create_and_upload!(io: StringIO.new("test"), filename: "test.txt") }

    before do
      # Ensure the module is included in ActiveStorage::Blob
      ActiveStorage::Blob.include(described_class)
    end

    context 'when CDN_BASE_URL is set' do
      before do
        allow(ENV).to receive(:[]).with('CDN_BASE_URL').and_return('https://cdn.example.com')
        allow(blob).to receive(:url).and_return('https://example.com/path/to/file.txt')
      end

      it 'returns a CDN URL' do
        expect(blob.cdn_url).to be_a(String)
        expect(blob.cdn_url).to start_with('https://cdn.example.com')
      end
    end

    context 'when CDN_BASE_URL is not set' do
      before do
        allow(ENV).to receive(:[]).with('CDN_BASE_URL').and_return(nil)
      end

      it 'returns nil' do
        expect(blob.cdn_url).to be_nil
      end
    end
  end
end
