#!/bin/bash
set -e

echo "🖥️ Setting up Linux server as Docker host for DevContainers..."

# Update system
echo "📦 Updating system packages..."
sudo apt update && sudo apt upgrade -y

# Install required packages
echo "🔧 Installing required packages..."
sudo apt install -y \
    curl \
    wget \
    git \
    openssh-server \
    ca-certificates \
    gnupg \
    lsb-release

# Install Docker
echo "🐳 Installing Docker..."
if ! command -v docker &> /dev/null; then
    # Add Docker's official GPG key
    sudo mkdir -p /etc/apt/keyrings
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /etc/apt/keyrings/docker.gpg
    
    # Set up the repository
    echo \
      "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu \
      $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
    
    # Install Docker Engine
    sudo apt update
    sudo apt install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
    
    # Start and enable Docker
    sudo systemctl start docker
    sudo systemctl enable docker
else
    echo "✅ Docker already installed"
fi

# Create development user
echo "👤 Creating development user..."
if ! id "developer" &>/dev/null; then
    sudo useradd -m -s /bin/bash developer
    sudo usermod -aG docker developer
    sudo usermod -aG sudo developer
    
    # Set up SSH directory
    sudo mkdir -p /home/<USER>/.ssh
    sudo chmod 700 /home/<USER>/.ssh
    sudo chown developer:developer /home/<USER>/.ssh
    
    # Create authorized_keys file
    sudo touch /home/<USER>/.ssh/authorized_keys
    sudo chmod 600 /home/<USER>/.ssh/authorized_keys
    sudo chown developer:developer /home/<USER>/.ssh/authorized_keys
else
    echo "✅ Developer user already exists"
fi

# Configure SSH
echo "🔐 Configuring SSH..."
sudo sed -i 's/#PubkeyAuthentication yes/PubkeyAuthentication yes/' /etc/ssh/sshd_config
sudo sed -i 's/#PasswordAuthentication yes/PasswordAuthentication no/' /etc/ssh/sshd_config
sudo systemctl restart ssh

# Configure firewall (if ufw is installed)
if command -v ufw &> /dev/null; then
    echo "🔥 Configuring firewall..."
    sudo ufw allow ssh
    sudo ufw allow 3000/tcp  # Next.js dev server
    sudo ufw allow 3001/tcp  # Additional dev tools
    sudo ufw --force enable
fi

# Test Docker installation
echo "🧪 Testing Docker installation..."
sudo docker run --rm hello-world

echo ""
echo "✅ Server setup complete!"
echo ""
echo "📝 Next steps:"
echo "1. Add developer's SSH public key to /home/<USER>/.ssh/authorized_keys"
echo "2. Test SSH connection: ssh developer@$(hostname -I | awk '{print $1}')"
echo "3. Configure Windows developer machine"
echo ""
echo "🔑 To add SSH key, run on Windows:"
echo "   scp ~/.ssh/id_rsa.pub developer@$(hostname -I | awk '{print $1}'):~/.ssh/authorized_keys"
