# Athar EMS Approval System Documentation

## Overview

The Athar EMS Approval System is a distributed workflow engine that enables multi-step approval processes across different microservices. It provides a flexible framework for defining approval workflows, resolving approvers dynamically, and managing the approval lifecycle.

## Architecture

The approval system follows a distributed architecture with centralized workflow definitions:

1. **Core Service**: Defines approval workflows and dynamically resolves approvers
2. **Client Services**: Maintain their own approval state and handle approval actions
3. **Commons Gem**: Provides standardized models, concerns, and services for client services

## Key Components

### Core Service

- **ApprovalWorkflow**: Defines a sequence of approval steps for a specific action
- **ApprovalStep**: Defines a single step in an approval workflow with approver resolution
- **ApprovalEngine**: Service for dynamically resolving approvers based on roles and context

### Client Services

- **ApprovalRequest**: Represents a request for approval with its current state
- **ApprovalStep**: Represents a step in the approval process with its approvers
- **ApprovalAction**: Records actions (approve, reject, comment) taken on approval steps

### Commons Gem

- **Approvable**: Concern for making models approvable
- **ApprovalClient**: Client for communicating with Core service
- **ApprovalService**: Service for approval operations

## Setup Instructions

### 1. Core Service Setup

The Core service defines approval workflows and provides RPC endpoints for workflow retrieval and sequence generation.

#### Run Migrations

```bash
cd /path/to/core
bin/rails db:migrate
```

#### Seed Approval Workflows

```bash
cd /path/to/core
bin/rails db:seed
```

### 2. Client Service Setup

Each client service maintains its own approval state and handles approval actions.

#### Add Dependencies

Update your Gemfile:

```ruby
gem 'athar-commons', path: '/path/to/commons-gem'
```

Run bundle install:

```bash
bundle install
```

#### Generate Approval Components

```bash
# For a Project-Based Service with Direct DB Access
bin/rails g athar:commons:approval --user_table=users --project_based=true --project_table=projects --use_foreign_keys=true

# For a Non-Project-Based Service (like AtharPeople)
bin/rails g athar:commons:approval --user_table=users --project_based=false --use_foreign_keys=true

# For a Service without Direct DB Access to Users/Projects
bin/rails g athar:commons:approval --use_foreign_keys=false
```

#### Run Migrations

```bash
bin/rails db:migrate
```

## Usage Guide

### 1. Making a Model Approvable

To make a model approvable, use the `acts_as_approvable` method and implement the required methods:

```ruby
class ProcurementRequest < ApplicationRecord
  # Call acts_as_approvable to set up the association and methods
  acts_as_approvable
  
  # ... your model code ...
  
  # Required methods
  def approval_action
    "buy_new_item"  # Must match a workflow action in Core
  end
  
  def system_name
    "procure"  # Your service name (core, procure, people, case_manager)
  end
  
  # Optional: Handle approval status changes
  def on_approval_status_change(new_status, previous_status)
    case new_status.to_sym
    when :approved
      update_column(:status, :approved)
      notify_requestor("Your procurement request has been approved")
    when :rejected
      update_column(:status, :rejected)
      notify_requestor("Your procurement request has been rejected")
    end
  end
end
```

### 2. Submitting for Approval

To submit a record for approval:

```ruby
procurement_request = ProcurementRequest.create!(
  name: "New Laptop",
  description: "MacBook Pro for new developer",
  amount: 2000,
  user: current_user,
  project: project
)

# Submit for approval
procurement_request.submit_for_approval(current_user, project)
```

### 3. Checking Approval Status

```ruby
# Check if approved in the approval workflow
procurement_request.approval_approved?  # => true/false

# Check if rejected in the approval workflow
procurement_request.approval_rejected?  # => true/false

# Check if pending approval in the approval workflow
procurement_request.approval_pending?  # => true/false

# Get the approval workflow status
procurement_request.approval_workflow_status  # => "pending", "approved", "rejected", etc.

# Get current approval step
current_step = procurement_request.current_approval_step

# Get current approvers
approvers = procurement_request.current_approvers
```

### 4. Approving or Rejecting

You can approve or reject requests directly from the model instance:

```ruby
# Approve the request
result = procurement_request.approval_approve!(current_user.id, "Looks good!")

# Check if the approval was successful
if result.success?
  # Handle success
  puts "Request approved: #{result.message}"
else
  # Handle failure
  puts "Approval failed: #{result.message}"
  puts "Errors: #{result.errors}"
end

# Reject the request
result = procurement_request.approval_reject!(current_user.id, "Budget exceeded")

# Add a comment
result = procurement_request.approval_comment!(current_user.id, "Please provide more details")

# You can also use exception handling
begin
  result = procurement_request.approval_approve!(current_user.id, "Looks good!", raise_error: true)
  # Only reaches here if successful
  puts "Request approved: #{result.message}"
rescue StandardError => e
  # Handle the error
  puts "Approval failed: #{e.message}"
end
```

Alternatively, you can use the ApprovalService:

```ruby
# Create an approval service
service = Athar::Commons::Services::ApprovalService.new

# Approve the request
result = service.approve(procurement_request.approval_request, current_user.id, "Looks good!")

# Reject the request
result = service.reject(procurement_request.approval_request, current_user.id, "Budget exceeded")

# Add a comment
result = service.comment(procurement_request.approval_request, current_user.id, "Please provide more details")
```

#### The ApprovalResult Object

All approval operations return an `ApprovalResult` object with the following methods:

```ruby
result.success?     # => true/false
result.failure?     # => true/false
result.message      # => String message describing the result
result.errors       # => Hash of errors
result.data         # => Hash of additional data

# Convert to hash
result.to_h         # => { success: true, message: "...", errors: {}, data: {} }

# For backward compatibility
if result            # Can be used in boolean contexts
  # Success case
else
  # Failure case
end
```

### 5. Finding Requests to Approve

You can find approvable objects using the following scopes:

```ruby
# Find all objects pending approval for a specific user
ProcurementRequest.pending_approval_for(current_user.id)

# Find all objects approved in the approval workflow
ProcurementRequest.approval_workflow_approved

# Find all objects rejected in the approval workflow
ProcurementRequest.approval_workflow_rejected

# Find all objects pending approval in the approval workflow
ProcurementRequest.approval_workflow_pending
```

These scopes can be chained with other scopes and query methods:

```ruby
# Find all annual leaves pending approval for the current user, ordered by creation date
Leave.pending_approval_for(current_user.id).where(leave_type: :annual).order(created_at: :desc)
```

### 6. Handling Approval Status Changes

When the status of an approval request changes, the approvable model is notified through a callback. You can customize how your model responds to these changes:

#### Default Callback Method

By default, the system calls the `on_approval_status_change` method on your model:

```ruby
class ProcurementRequest < ApplicationRecord
  acts_as_approvable
  
  def on_approval_status_change(new_status, previous_status)
    case new_status.to_sym
    when :approved
      update_column(:status, :approved)
      notify_requestor("Your request has been approved")
    when :rejected
      update_column(:status, :rejected)
      notify_requestor("Your request has been rejected")
    end
  end
end
```

#### Custom Callback Method

You can specify a custom method name:

```ruby
class Leave < ApplicationRecord
  acts_as_approvable(on_status_change: :handle_approval_status)
  
  def handle_approval_status(new_status, previous_status)
    # Your custom logic here
  end
end
```

#### Using a Proc or Lambda

You can also use a proc or lambda for simple callbacks:

```ruby
class Expense < ApplicationRecord
  acts_as_approvable(on_status_change: ->(new_status, previous_status) {
    update_column(:status, new_status)
    
    if new_status.to_sym == :approved
      AccountingService.create_payment(self)
    end
  })
end
```

## API Endpoints

The following API endpoints are available for managing approval requests:

### List My Requests

```
GET /api/approval_requests
```

Returns all approval requests created by the current user.

### Show Request Details

```
GET /api/approval_requests/:id
```

Returns details of a specific approval request.

### Approve Request

```
POST /api/approval_requests/:id/approve
```

Parameters:
- `comment` (optional): Comment for the approval action

### Reject Request

```
POST /api/approval_requests/:id/reject
```

Parameters:
- `comment` (optional): Comment for the rejection action

### Cancel Request

```
POST /api/approval_requests/:id/cancel
```

Cancels a pending approval request.

### List Pending Approvals

```
GET /api/approval_requests/pending_approvals
```

Returns all approval requests that the current user can approve.

## Defining Approval Workflows

Approval workflows are defined in the Core service's seed file. Here's an example:

```ruby
# Procurement Request Workflow
procurement_workflow = ApprovalWorkflow.find_or_create_by(
  name: "Procurement Request Approval",
  action: "buy_new_item",
  subject_class: "ProcurementRequest",
  system_name: "procure"
) do |workflow|
  workflow.description = "Approval workflow for procurement requests"
  workflow.active = true
end

# Step 1: Project Manager Approval
pm_step = procurement_workflow.approval_steps.find_or_create_by(sequence: 1) do |step|
  step.name = "Project Manager Approval"
  step.approval_type = "any"  # "any" means any approver can approve
  step.dynamic_approver_method = "project_managers"
  step.description = "Approval from the project manager"
end

# Step 2: Procurement Officer/Manager Approval
procurement_step = procurement_workflow.approval_steps.find_or_create_by(sequence: 2) do |step|
  step.name = "Procurement Approval"
  step.approval_type = "any"
  step.dynamic_approver_method = "procurement_approvers"
  step.description = "Approval from procurement officer or manager"
end

# Step 3: HR Approval
hr_step = procurement_workflow.approval_steps.find_or_create_by(sequence: 3) do |step|
  step.name = "HR Approval"
  step.approval_type = "any"
  step.dynamic_approver_method = "hr_approvers"
  step.description = "Approval from HR officer or manager"
end

# Step 4: Financial Approval
finance_step = procurement_workflow.approval_steps.find_or_create_by(sequence: 4) do |step|
  step.name = "Financial Approval"
  step.approval_type = "any"
  step.dynamic_approver_method = "financial_approvers"
  step.description = "Approval from accountant or financial manager"
end
```

## Approval Types

The system supports two approval types:

1. **"any"**: Any approver can approve the step
2. **"all"**: All approvers must approve the step

## Dynamic Approver Resolution

Approvers are resolved dynamically based on the request context. The Core service provides several methods for resolving approvers:

- `project_managers`: Project managers for the project
- `procurement_approvers`: Procurement officers/managers for the project
- `hr_approvers`: HR officers/managers (global)
- `financial_approvers`: Accountants/financial managers (global)
- `direct_manager`: Direct manager of the requestor

## Approval Flow

1. User creates a record that requires approval
2. User submits the record for approval
3. Core service generates the approval sequence with steps and approvers
4. Client service creates an approval request with steps
5. Approvers approve or reject each step
6. When a step is complete, the system moves to the next step
7. If any step is rejected, the entire request is rejected
8. If all steps are approved, the request is approved
9. The approvable model is notified of status changes through the callback mechanism

## Models with Existing Status Enum

If your model already has a status enum with methods like `approved?`, `rejected?`, etc., the Approvable concern uses different method names to avoid conflicts:

```ruby
# Model's own status methods
model.approved?  # => From the model's status enum
model.rejected?  # => From the model's status enum
model.pending?   # => From the model's status enum

# Approval workflow status methods
model.approval_approved?  # => From the approval workflow
model.approval_rejected?  # => From the approval workflow
model.approval_pending?   # => From the approval workflow

# Model's own scopes
Model.approved   # => From the model's status enum
Model.rejected   # => From the model's status enum
Model.pending    # => From the model's status enum

# Approval workflow scopes
Model.approval_workflow_approved   # => From the approval workflow
Model.approval_workflow_rejected   # => From the approval workflow
Model.approval_workflow_pending    # => From the approval workflow
```

This allows models to maintain their own status while also participating in the approval workflow.

## Best Practices

1. **Define Clear Workflows**: Each approval workflow should have a clear purpose and well-defined steps
2. **Use Appropriate Approval Types**: Use "any" for steps where any approver can approve, and "all" for steps where all approvers must approve
3. **Implement Status Change Callbacks**: Add callbacks to your models to handle approval status changes
4. **Add Validations**: Ensure that records can only be modified when in the appropriate approval state
5. **Provide Clear Feedback**: Show users the current approval status and next steps
6. **Use Scopes**: Leverage the provided scopes to find approvable objects efficiently

## Troubleshooting

### Common Issues

1. **Workflow Not Found**: Ensure that the `approval_action` and `system_name` in your model match a workflow defined in the Core service
2. **No Approvers Found**: Check that the dynamic approver method is returning approvers for the given context
3. **Cannot Approve**: Ensure that the user is in the list of approvers for the current step
4. **Step Not Advancing**: Check that all required approvers have approved the step (for "all" approval type)
5. **Method Conflicts**: If you have method name conflicts, ensure you're using the prefixed methods (e.g., `approval_approved?` instead of `approved?`)
6. **Status Not Updating**: Ensure that your model's status change callback is properly implemented

### Debugging

1. **Check Approval Request Status**: `approval_request.status`
2. **Check Current Step**: `approval_request.current_step`
3. **Check Approvers**: `approval_request.current_step.approver_ids`
4. **Check Actions**: `approval_request.approval_actions`

## Advanced Usage

### Custom Approver Resolution

You can add custom approver resolution methods to the Core service's `ApprovalEngine`:

```ruby
# app/services/approval_engine.rb
class ApprovalEngine
  class << self
    # ... existing methods ...
    
    # Custom approver resolution method
    def department_heads(requestor, project)
      # Your custom logic to find department heads
      User.joins(:user_roles)
          .where(user_roles: { 
            role_id: Role.where(name: 'department_head').pluck(:id) 
          })
          .pluck(:id)
    end
  end
end
```

Then use this method in your workflow definition:

```ruby
custom_step = workflow.approval_steps.find_or_create_by(sequence: 1) do |step|
  step.name = "Department Head Approval"
  step.approval_type = "any"
  step.dynamic_approver_method = "department_heads"
  step.description = "Approval from department head"
end
```

### Custom Status Change Callbacks

You can customize how your model responds to approval status changes:

```ruby
class ProcurementRequest < ApplicationRecord
  # Use a custom method name for the status change callback
  acts_as_approvable(on_status_change: :handle_approval_status)
  
  # Custom status change handler
  def handle_approval_status(new_status, previous_status)
    case new_status.to_sym
    when :approved
      update_columns(status: 'approved', approved_at: Time.current)
      
      # Notify the requestor
      UserMailer.approval_notification(self, 'approved').deliver_later
      
      # Create a payment record
      PaymentRecord.create!(procurement_request: self, amount: amount)
    when :rejected
      update_columns(status: 'rejected', rejected_at: Time.current)
      
      # Notify the requestor
      UserMailer.approval_notification(self, 'rejected').deliver_later
    end
  end
end
```

### Using a Proc for Status Change Callbacks

For simple cases, you can use a proc or lambda:

```ruby
class SimpleRequest < ApplicationRecord
  acts_as_approvable(on_status_change: ->(new_status, previous_status) {
    # Just update the status
    update_column(:status, new_status)
  })
end
```

## Conclusion

The Athar EMS Approval System provides a flexible, distributed framework for implementing approval workflows across microservices. By centralizing workflow definitions while distributing approval processing, it achieves a balance of consistency and flexibility.

For more information, please refer to the source code and comments in the Core service and commons-gem.
