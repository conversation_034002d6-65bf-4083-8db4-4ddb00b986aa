# Athar Core Service

This is the core service for the Athar EMS (Employee Management System) project. It handles user management, roles, permissions, and authentication.

## Ruby Version

- Ruby 3.4.2

## System Dependencies

- PostgreSQL
- Redis (optional, for caching)
- direnv (for environment management)

## Configuration

1. Clone the repository
2. Install dependencies:
   ```bash
   bundle install
   ```
3. Copy the example environment file:
   ```bash
   cp .env.example .env
   ```
4. Configure your environment variables in `.env`
5. Allow direnv:
   ```bash
   direnv allow
   ```

## Database Setup

```bash
bin/rails db:create
bin/rails db:migrate
bin/rails db:seed
```

## Running the Application

```bash
bin/rails server
```

## Running Tests

This project uses RSpec for testing. To run all tests:

```bash
bin/test
```

To run specific tests:

```bash
bin/test spec/models/user_spec.rb
```

To run tests with specific tags:

```bash
bin/test --tag focus
```

## Code Coverage

SimpleCov is configured to generate code coverage reports. After running tests, you can view the coverage report by opening `coverage/index.html` in your browser.

## API Documentation

API documentation is available at `/apipie` when the server is running.

## GRPC Endpoint

The gRPC endpoint is available at: `grpc.core.athar.test:10541`

## Services

The Athar EMS consists of four microservices:
- **AtharCore** (this service): User management, roles, permissions
- **AtharPeople**: HR management, leave, attendance
- **AtharProcure**: Procurement processes
- **AtharCaseManager**: Case management