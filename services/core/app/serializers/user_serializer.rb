class UserSerializer
  include JSONAPI::Serializer
  attributes :email, :name, :user_type, :status

  attribute :avatar do |user|
    user.avatar.attached? ? user.avatar.cdn_url : nil
  end

  attribute :user_roles do |user|
    user.user_roles.includes(:role, :project).map do |user_role|
      {
        is_default: user_role.is_default || false,
        role: {
          id: user_role.role.id.to_s,
          name: user_role.role.name,
          global: user_role.role.global_role?
        },
        project: user_role.project ? {
          id: user_role.project.id.to_s,
          name: user_role.project.name
        } : nil
      }
    end
  end
end
