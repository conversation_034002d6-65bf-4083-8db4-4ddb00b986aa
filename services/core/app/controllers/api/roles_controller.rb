# frozen_string_literal: true

class Api::RolesController < ApplicationController
  before_action :set_role, only: [ :show, :update, :destroy ]

  api :GET, "/api/roles", "List all roles"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param_group :pagination_params
  param_group :filter_params
  param_group :sort_params
  description jsonapi_with_docs(<<-HTML
      Returns a paginated list of roles.

      <b>Permission required:</b> <code>:read, :role</code>
  HTML
              )
  returns code: 200, desc: "List of roles in JSON:API format" do
    property :data, Array, desc: "Array of role objects" do
      property :id, String, desc: "Role ID"
      property :type, String, desc: "Resource type (role)"
      property :attributes, Hash, desc: "Role attributes" do
        property :name, String, desc: "Role name"
        property :scope, String, desc: "Whether the role is global or project-based (project_based or global_role)"
      end
    end
    property :meta, Hash, desc: "Metadata" do
      property :pagination, Hash, desc: "Pagination information" do
        property :current_page, Integer, desc: "Current page number"
        property :total_pages, Integer, desc: "Total number of pages"
        property :total_count, Integer, desc: "Total number of records"
      end
    end
  end
  error code: 401, desc: "Unauthorized: missing/invalid/expired token"
  error code: 403, desc: "Forbidden: insufficient permissions"

  def index
    return unless authorize!(:read, :role)

    @collection = Role.all

    apply_filters(@collection) do |filtered_roles|
      records, meta = paginate(filtered_roles)
      serialize_response(records, meta: meta)
    end
  end

  api :GET, "/api/roles/:id", "Get a specific role"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :id, :number, desc: "Role ID", required: true
  description jsonapi_with_docs(<<-HTML
      Returns details for a specific role.

      <b>Permission required:</b> <code>:read, :role</code>
  HTML
              )
  returns code: 200, desc: "Role details in JSON:API format" do
    property :data, Hash, desc: "Role object" do
      property :id, String, desc: "Role ID"
      property :type, String, desc: "Resource type (role)"
      property :attributes, Hash, desc: "Role attributes" do
        property :name, String, desc: "Role name"
        property :scope, String, desc: "Whether the role is global or project-based (project_based or global_role)"
      end
    end
  end
  error code: 401, desc: "Unauthorized: missing/invalid/expired token"
  error code: 403, desc: "Forbidden: insufficient permissions"
  error code: 404, desc: "Role not found"

  def show
    return unless authorize!(:read, :role)

    serialize_response(@role)
  end

  api :POST, "/api/roles", "Create a new role"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :data, Hash, desc: "Role data", required: true do
    param :type, String, desc: "Resource type (must be 'role')", required: true
    param :attributes, Hash, desc: "Role attributes", required: true do
      param :name, String, desc: "Role name", required: true
      param :scope, String, desc: "Whether the role is global or project-based (default: project_based)"
    end
  end
  description jsonapi_with_docs(<<-HTML
      Creates a new role.

      <b>Permission required:</b> <code>:create, :role</code>
  HTML
              )
  returns code: 201, desc: "Role created successfully" do
    property :data, Hash, desc: "Created role" do
      property :id, String, desc: "Role ID"
      property :type, String, desc: "Resource type (role)"
      property :attributes, Hash, desc: "Role attributes" do
        property :name, String, desc: "Role name"
        property :scope, String, desc: "Whether the role is global or project-based (project_based or global_role)"
      end
    end
  end
  error code: 401, desc: "Unauthorized: missing/invalid/expired token"
  error code: 403, desc: "Forbidden: insufficient permissions"
  error code: 422, desc: "Validation failed"

  def create
    return unless authorize!(:create, :role)

    @role = Role.new(role_params)

    if @role.save
      serialize_response(@role, status: :created)
    else
      serialize_errors(@role.errors)
    end
  end

  api :PATCH, "/api/roles/:id", "Update a role"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :id, :number, desc: "Role ID", required: true
  param :data, Hash, desc: "Role data", required: true do
    param :type, String, desc: "Resource type (must be 'role')", required: true
    param :id, String, desc: "Role ID (must match URL ID)", required: true
    param :attributes, Hash, desc: "Role attributes", required: true do
      param :name, String, desc: "Role name"
      param :scope, String, desc: "Whether the role is global or project-based (project_based or global_role)"
    end
  end
  description jsonapi_with_docs(<<-HTML
      Updates an existing role.

      <b>Permission required:</b> <code>:update, :role</code>
  HTML
              )
  returns code: 200, desc: "Role updated successfully" do
    property :data, Hash, desc: "Updated role" do
      property :id, String, desc: "Role ID"
      property :type, String, desc: "Resource type (role)"
      property :attributes, Hash, desc: "Role attributes" do
        property :name, String, desc: "Role name"
        property :scope, String, desc: "Whether the role is global or project-based (project_based or global_role)"
      end
    end
  end
  error code: 401, desc: "Unauthorized: missing/invalid/expired token"
  error code: 403, desc: "Forbidden: insufficient permissions"
  error code: 404, desc: "Role not found"
  error code: 422, desc: "Validation failed"

  def update
    return unless authorize!(:update, :role)

    if @role.update(role_params)
      serialize_response(@role)
    else
      serialize_errors(@role.errors)
    end
  end

  api :DELETE, "/api/roles/:id", "Delete a role"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :id, :number, desc: "Role ID", required: true
  description jsonapi_with_docs(<<-HTML
      Deletes a role.

      <b>Permission required:</b> <code>:destroy, :role</code>
  HTML
              )
  returns code: 204, desc: "Role deleted successfully (no content)"
  error code: 401, desc: "Unauthorized: missing/invalid/expired token"
  error code: 403, desc: "Forbidden: insufficient permissions"
  error code: 404, desc: "Role not found"

  def destroy
    return unless authorize!(:destroy, :role)

    @role.destroy
    head :no_content
  end

  private

  def set_role
    @role = Role.find(params[:id])
  end

  def role_params
    params.require(:data,).require(:attributes).permit(:name, :scope)
  end
end
