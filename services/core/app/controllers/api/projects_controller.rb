# frozen_string_literal: true

class Api::ProjectsController < ApplicationController
  before_action :set_project, only: [ :show, :update, :destroy ]

  api :GET, "/api/projects", "List all projects"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param_group :pagination_params
  param_group :filter_params
  param_group :sort_params
  description jsonapi_with_docs(<<-HTML
      Returns a paginated list of projects.

      <b>Permission required:</b> <code>:read, :project</code>
  HTML
              )
  returns code: 200, desc: "List of projects in JSON:API format" do
    property :data, Array, desc: "Array of project objects" do
      property :id, String, desc: "Project ID"
      property :type, String, desc: "Resource type (project)"
      property :attributes, Hash, desc: "Project attributes" do
        property :name, String, desc: "Project name"
        property :description, String, desc: "Project description"
        property :status, String, desc: "Project status (active/inactive)"
      end
    end
    property :meta, Hash, desc: "Metadata" do
      property :pagination, Hash, desc: "Pagination information" do
        property :current_page, Integer, desc: "Current page number"
        property :total_pages, Integer, desc: "Total number of pages"
        property :total_count, Integer, desc: "Total number of records"
      end
    end
  end
  error code: 401, desc: "Unauthorized: missing/invalid/expired token"
  error code: 403, desc: "Forbidden: insufficient permissions"

  def index
    return unless authorize!(:read, :project)

    @collection = Project.all

    apply_filters(@collection) do |filtered_projects|
      records, meta = paginate(filtered_projects)
      serialize_response(records, meta: meta)
    end
  end

  api :GET, "/api/projects/:id", "Get a specific project"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :id, :number, desc: "Project ID", required: true
  description jsonapi_with_docs(<<-HTML
      Returns details for a specific project.

      <b>Permission required:</b> <code>:read, :project</code>
  HTML
              )
  returns code: 200, desc: "Project details in JSON:API format" do
    property :data, Hash, desc: "Project object" do
      property :id, String, desc: "Project ID"
      property :type, String, desc: "Resource type (project)"
      property :attributes, Hash, desc: "Project attributes" do
        property :name, String, desc: "Project name"
        property :description, String, desc: "Project description"
        property :status, String, desc: "Project status (active/inactive)"
      end
    end
  end
  error code: 401, desc: "Unauthorized: missing/invalid/expired token"
  error code: 403, desc: "Forbidden: insufficient permissions"
  error code: 404, desc: "Project not found"

  def show
    return unless authorize!(:read, :project)

    serialize_response(@project)
  end

  api :POST, "/api/projects", "Create a new project"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :data, Hash, desc: "Project data", required: true do
    param :type, String, desc: "Resource type (must be 'project')", required: true
    param :attributes, Hash, desc: "Project attributes", required: true do
      param :name, String, desc: "Project name", required: true
      param :description, String, desc: "Project description"
      param :status, String, desc: "Project status (default: active)", in: %w[active inactive]
    end
  end
  description jsonapi_with_docs(<<-HTML
      Creates a new project.

      <b>Permission required:</b> <code>:create, :project</code>
  HTML
              )
  returns code: 201, desc: "Project created successfully" do
    property :data, Hash, desc: "Created project" do
      property :id, String, desc: "Project ID"
      property :type, String, desc: "Resource type (project)"
      property :attributes, Hash, desc: "Project attributes" do
        property :name, String, desc: "Project name"
        property :description, String, desc: "Project description"
        property :status, String, desc: "Project status (active/inactive)"
      end
    end
  end
  error code: 401, desc: "Unauthorized: missing/invalid/expired token"
  error code: 403, desc: "Forbidden: insufficient permissions"
  error code: 422, desc: "Validation failed"

  def create
    return unless authorize!(:create, :project)

    @project = Project.new(project_params)

    if @project.save
      serialize_response(@project, status: :created)
    else
      serialize_errors(@project.errors)
    end
  end

  api :PATCH, "/api/projects/:id", "Update a project"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :id, :number, desc: "Project ID", required: true
  param :data, Hash, desc: "Project data", required: true do
    param :type, String, desc: "Resource type (must be 'project')", required: true
    param :id, String, desc: "Project ID (must match URL ID)", required: true
    param :attributes, Hash, desc: "Project attributes", required: true do
      param :name, String, desc: "Project name"
      param :description, String, desc: "Project description"
      param :status, String, desc: "Project status", in: %w[active inactive]
    end
  end
  description jsonapi_with_docs(<<-HTML
      Updates an existing project.

      <b>Permission required:</b> <code>:update, :project</code>
  HTML
              )
  returns code: 200, desc: "Project updated successfully" do
    property :data, Hash, desc: "Updated project" do
      property :id, String, desc: "Project ID"
      property :type, String, desc: "Resource type (project)"
      property :attributes, Hash, desc: "Project attributes" do
        property :name, String, desc: "Project name"
        property :description, String, desc: "Project description"
        property :status, String, desc: "Project status (active/inactive)"
      end
    end
  end
  error code: 401, desc: "Unauthorized: missing/invalid/expired token"
  error code: 403, desc: "Forbidden: insufficient permissions"
  error code: 404, desc: "Project not found"
  error code: 422, desc: "Validation failed"

  def update
    return unless authorize!(:update, :project)

    if @project.update(project_params)
      serialize_response(@project)
    else
      serialize_errors(@project.errors)
    end
  end

  api :DELETE, "/api/projects/:id", "Delete a project"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :id, :number, desc: "Project ID", required: true
  description jsonapi_with_docs(<<-HTML
      Deletes a project.

      <b>Permission required:</b> <code>:destroy, :project</code>
  HTML
              )
  returns code: 204, desc: "Project deleted successfully (no content)"
  error code: 401, desc: "Unauthorized: missing/invalid/expired token"
  error code: 403, desc: "Forbidden: insufficient permissions"
  error code: 404, desc: "Project not found"

  def destroy
    return unless authorize!(:destroy, :project)

    @project.destroy
    head :no_content
  end

  private

  def set_project
    @project = Project.find(params[:id])
  end

  def project_params
    # Extract attributes from JSON:API format
    attributes = params.dig(:data, :attributes)
    return {} unless attributes

    attributes.permit(:name, :description, :status)
  end
end
