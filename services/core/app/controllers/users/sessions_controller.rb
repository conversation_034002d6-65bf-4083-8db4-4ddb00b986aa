# frozen_string_literal: true

class Users::SessionsController < ApplicationController
  include Auth::TokenConcern
  skip_before_action :authenticate_api_user!, only: [ :create, :session_token ]

  respond_to :json

  api :POST, "/auth/token", "Issues a main token for the user to request scoped session tokens"
  formats [ "json" ]
  param :user, Hash, required: true, desc: "User credentials" do
    param :email, String, required: true, desc: "Registered email"
    param :password, String, required: true, desc: "User password"
  end
  returns code: 201, desc: "Main token issued successfully" do
    property :message, String, desc: "Success message"
    property :user, Hash, desc: "Authenticated user details" do
      property :id, Integer, desc: "User ID"
      property :email, String, desc: "User email address"
      property :name, String, desc: "User's full name"
    end
    property :main_token, String, desc: "Main JWT used to request scoped session tokens"
  end
  error code: 401, desc: "Invalid email or password"

  def create
    user = User.find_by("LOWER(email) = ?", params[:user][:email].strip.downcase)
    if user && user.valid_password?(params[:user][:password])
      main_token = AtharAuth.encode_token(
        {
          s: user.id,           # 'user.id' => 's'
          t: "main",            # 'token_type' => 't'
          u: {                  # user context (consistent with session tokens)
            n: user.name,       # 'user.name' => 'u.n'
            m: user.email       # 'user.email' => 'u.m'
          },
          e: AtharAuth.main_token_expire_time.from_now.to_i  # 'expires.at' => 'e'
        }
      )

      render json: {
        message: "Signed in successfully",
        user: { id: user.id, email: user.email, name: user.name },
        main_token: main_token
      }, status: :created
    else
      render json: { error: "Invalid email or password" }, status: :unauthorized
    end
  end

  api :POST, "/auth/session_token", "Issues a scoped session token for a specific subsystem"
  formats [ "json" ]
  header "Authorization", "Main token (JWT) as Bearer token", required: true
  param :scope, String, required: true, desc: "Subsystem scope (core, people, procure, cm)"
  param :project_id, Integer, required: false, desc: "Project ID (required for non-global users)"
  returns code: 201, desc: "Session token issued for the requested scope" do
    property :message, String, desc: "Token issuance confirmation"
    property :scope, String, desc: "Subsystem scope name"
    property :session_token, String, desc: "Scoped JWT to be used for subsystem access"
    property :user_type, String, desc: "Type of user (global or project_based)"
    property :project_id, Integer, desc: "Project ID (for project-based users)", required: false
    property :project_name, String, desc: "Project name (for project-based users)", required: false
    property :project_role, String, desc: "User's role in the project (for project-based users)", required: false
  end
  error code: 422, desc: "Scope parameter missing or project_id required for non-global users"

  def session_token
    scope = params[:scope]
    project_id = params[:project_id]

    user = User.find(current_user.id)

    # Determine project context - all users now have project context
    if project_id.present?
      # User explicitly specified a project
      begin
        project = user.projects.find(project_id)
      rescue ActiveRecord::RecordNotFound
        return render json: {
          error: "Project not accessible or does not exist.",
          user_id: user.id,
          requested_project_id: project_id,
          available_projects: user.projects.pluck(:id, :name)
        }, status: :unprocessable_entity
      end
    else
      # Use default project (works for both global and project-based users)
      project = user.default_project

      # If no default project, this is a configuration error
      unless project
        error_message = if user.global_user?
          "No default project assigned. Global users should have #{AtharAuth.organization_project_name} project as default."
        else
          "No default project assigned. Please contact administrator to assign a default project."
        end

        return render json: {
          error: error_message,
          user_id: user.id,
          user_type: user.global_user? ? "global" : "project_based",
          available_projects: user.projects.pluck(:id, :name)
        }, status: :unprocessable_entity
      end
    end

    role = user.user_roles.by_project(project).first&.role

    begin
      payload = user.generate_session_token(scope, role, project, AtharAuth.session_token_expire_time.from_now.to_i)
      session_token = AtharAuth.encode_token(payload)

      response = {
        message: "Session token issued successfully",
        scope: scope,
        user: UserSerializer.new(user).serializable_hash,
        role_name: role&.name,
        project: ProjectSerializer.new(project).serializable_hash,
        session_token: session_token
      }

      render json: response, status: :created
    rescue ArgumentError => e
      render json: { error: e.message }, status: :unprocessable_entity
    end
  end
end
