# frozen_string_literal: true

module Auth::TokenConcern
  include ActiveSupport::Concern

  def generate_main_token(user)
    payload = {
      user_id: user.id,
      exp: AtharAuth.main_token_expire_time.seconds.from_now.to_i
    }
    JWT.encode(payload, Devise.secret_key)
  end

  def decode_main_token(token)
    decoded = JWT.decode(token, AtharAuth.secret_key, true, { algorithm: 'HS256' })
    HashWithIndifferentAccess.new(decoded[0])
  end
end
