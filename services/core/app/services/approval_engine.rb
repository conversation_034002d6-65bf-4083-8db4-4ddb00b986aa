class ApprovalEngine
  class << self
    # Generate an approval sequence for a specific request
    def generate_sequence(action, subject_class, system_name, requestor_id, project_id = nil, context = {})
      # Find the workflow
      workflow = ApprovalWorkflow.find_by_key(action, subject_class, system_name)
      return nil unless workflow

      # Generate the sequence
      workflow.generate_sequence(requestor_id, project_id, context)
    end

    # Resolve direct manager of the requestor based on project and role hierarchy
    def direct_manager(requestor_id, project_id = nil, context = {})
      explanation = [ "Looking for direct manager of user #{requestor_id}" ]

      user = User.find_by(id: requestor_id)
      unless user
        explanation << "User not found with ID: #{requestor_id}"
        return { approvers: [], explanation: explanation.join("\n") }
      end

      unless project_id
        explanation << "No project_id provided"
        return { approvers: [], explanation: explanation.join("\n") }
      end

      project = Project.find_by(id: project_id)
      unless project
        explanation << "Project not found with ID: #{project_id}"
        return { approvers: [], explanation: explanation.join("\n") }
      end

      explanation << "Checking user's roles in project #{project.name} (ID: #{project.id})"

      # For employees, their direct manager is the project manager
      if user.has_role?('employee', project)
        explanation << "User has 'employee' role in this project"
        explanation << "Looking for project managers as direct managers"

        result = project_managers(requestor_id, project_id, context)
        managers = result.is_a?(Hash) ? result[:approvers] : result
        manager_explanation = result.is_a?(Hash) ? result[:explanation] : "Project managers found: #{managers.join(', ')}"

        explanation << manager_explanation
        return { approvers: managers, explanation: explanation.join("\n") }
      end

      # For project managers and other managers, their direct manager is the admin
      if user.has_role?('project_manager', project) ||
         user.has_role?('hr_manager', project) ||
         user.has_role?('financial_manager', project) ||
         user.has_role?('procurement_manager', project)

        explanation << "User has a manager role in this project"
        explanation << "Looking for admin users as direct managers"

        result = admin_users(requestor_id, project_id, context)
        admins = result.is_a?(Hash) ? result[:approvers] : result
        admin_explanation = result.is_a?(Hash) ? result[:explanation] : "Admin users found: #{admins.join(', ')}"

        explanation << admin_explanation
        return { approvers: admins, explanation: explanation.join("\n") }
      end

      explanation << "No matching role pattern found for direct manager resolution"
      explanation << "Using default fallback: no approvers"

      # Default fallback
      # Ensure approvers is an array of strings
      { approvers: [], explanation: explanation.join("\n") }
    end

    # Find admin users
    def admin_users(requestor_id = nil, project_id = nil, context = {})
      explanation = [ "Looking for users with 'admin' role" ]

      admin_role = Role.find_by(name: 'admin')
      unless admin_role
        explanation << "No 'admin' role found in the system"
        return { approvers: [], explanation: explanation.join("\n") }
      end

      explanation << "Found 'admin' role with ID: #{admin_role.id}"

      admins = User.joins(:user_roles)
                   .where(user_roles: { role_id: admin_role.id })
                   .pluck(:id)
                   .map(&:to_s)

      if admins.empty?
        explanation << "No users found with 'admin' role"
      else
        explanation << "Found #{admins.size} admin users: #{admins.join(', ')}"
      end

      # Ensure approvers is an array of strings
      admins = admins.map(&:to_s)

      { approvers: admins, explanation: explanation.join("\n") }
    end

    # Find project managers for a specific project
    def project_managers(requestor_id, project_id = nil, context = {})
      explanation = [ "Looking for project managers for project ID: #{project_id}" ]

      unless project_id
        explanation << "No project_id provided"
        return { approvers: [], explanation: explanation.join("\n") }
      end

      project = Project.find_by(id: project_id)
      unless project
        explanation << "Project not found with ID: #{project_id}"
        return { approvers: [], explanation: explanation.join("\n") }
      end

      explanation << "Found project: #{project.name} (ID: #{project.id})"

      # Find users with project_manager role in this project
      project_manager_role = Role.find_by(name: 'project_manager')
      unless project_manager_role
        explanation << "No 'project_manager' role found in the system"
        return { approvers: [], explanation: explanation.join("\n") }
      end

      explanation << "Found 'project_manager' role with ID: #{project_manager_role.id}"

      managers = User.joins(:user_roles)
                     .where(user_roles: { role_id: project_manager_role.id, project_id: project.id })
                     .pluck(:id)
                     .map(&:to_s)

      if managers.empty?
        explanation << "No project managers found for this project"
      else
        explanation << "Found #{managers.size} project managers: #{managers.join(', ')}"
      end

      { approvers: managers, explanation: explanation.join("\n") }
    end

    # Resolve HR managers/officers
    def hr_approvers(requestor_id, project_id = nil, context = {})
      explanation = [ "Looking for HR managers and officers" ]

      # Find users with HR roles
      hr_roles = Role.where(name: [ 'hr_manager', 'hr_officer' ])

      if hr_roles.empty?
        explanation << "No HR roles found in the system"
        return { approvers: [], explanation: explanation.join("\n") }
      end

      explanation << "Found HR roles: #{hr_roles.pluck(:name).join(', ')}"

      hr_users = User.joins(:user_roles)
                     .where(user_roles: { role_id: hr_roles.pluck(:id) })
                     .pluck(:id)
                     .map(&:to_s)

      if hr_users.empty?
        explanation << "No users found with HR roles"
      else
        explanation << "Found #{hr_users.size} HR users: #{hr_users.join(', ')}"
      end

      # Ensure approvers is an array of strings
      hr_users = hr_users.map(&:to_s)

      { approvers: hr_users, explanation: explanation.join("\n") }
    end

    # Resolve HR managers only (excluding officers)
    def hr_managers_only(requestor_id, project_id = nil, context = {})
      explanation = [ "Looking for HR managers only" ]

      # Find users with HR manager role only
      hr_manager_role = Role.find_by(name: 'hr_manager')

      unless hr_manager_role
        explanation << "HR manager role not found in the system"
        return { approvers: [], explanation: explanation.join("\n") }
      end

      explanation << "Found HR manager role: #{hr_manager_role.name}"

      hr_managers = User.joins(:user_roles)
                        .where(user_roles: { role_id: hr_manager_role.id })
                        .pluck(:id)
                        .map(&:to_s)

      if hr_managers.empty?
        explanation << "No users found with HR manager role"
      else
        explanation << "Found #{hr_managers.size} HR managers: #{hr_managers.join(', ')}"
      end

      { approvers: hr_managers, explanation: explanation.join("\n") }
    end

    # Resolve financial managers/accountants
    def financial_approvers(requestor_id, project_id = nil, context = {})
      explanation = [ "Looking for financial managers and accountants" ]

      # Find users with financial roles
      financial_roles = Role.where(name: [ 'financial_manager', 'accountant' ])

      if financial_roles.empty?
        explanation << "No financial roles found in the system"
        return { approvers: [], explanation: explanation.join("\n") }
      end

      explanation << "Found financial roles: #{financial_roles.pluck(:name).join(', ')}"

      financial_users = User.joins(:user_roles)
                            .where(user_roles: { role_id: financial_roles.pluck(:id) })
                            .pluck(:id)
                            .map(&:to_s)

      if financial_users.empty?
        explanation << "No users found with financial roles"
      else
        explanation << "Found #{financial_users.size} financial users: #{financial_users.join(', ')}"
      end

      # Ensure approvers is an array of strings
      financial_users = financial_users.map(&:to_s)

      { approvers: financial_users, explanation: explanation.join("\n") }
    end

    # Resolve procurement managers/officers
    def procurement_approvers(requestor_id, project_id = nil, context = {})
      explanation = [ "Looking for procurement managers and officers" ]

      # Find users with procurement roles
      procurement_roles = Role.where(name: [ 'procurement_manager', 'procurement_officer' ])

      if procurement_roles.empty?
        explanation << "No procurement roles found in the system"
        return { approvers: [], explanation: explanation.join("\n") }
      end

      explanation << "Found procurement roles: #{procurement_roles.pluck(:name).join(', ')}"

      procurement_users = User.joins(:user_roles)
                              .where(user_roles: { role_id: procurement_roles.pluck(:id) })
                              .pluck(:id)
                              .map(&:to_s)

      if procurement_users.empty?
        explanation << "No users found with procurement roles"
      else
        explanation << "Found #{procurement_users.size} procurement users: #{procurement_users.join(', ')}"
      end

      # Ensure approvers is an array of strings
      procurement_users = procurement_users.map(&:to_s)

      { approvers: procurement_users, explanation: explanation.join("\n") }
    end

    # Resolve supervisors for case managers
    def supervisors(requestor_id, project_id = nil, context = {})
      explanation = [ "Looking for supervisors for user #{requestor_id} in project #{project_id}" ]

      user = User.find_by(id: requestor_id)
      unless user
        explanation << "User not found with ID: #{requestor_id}"
        return { approvers: [], explanation: explanation.join("\n") }
      end

      unless project_id
        explanation << "No project_id provided"
        return { approvers: [], explanation: explanation.join("\n") }
      end

      project = Project.find_by(id: project_id)
      unless project
        explanation << "Project not found with ID: #{project_id}"
        return { approvers: [], explanation: explanation.join("\n") }
      end

      explanation << "Found project: #{project.name} (ID: #{project.id})"

      # Find users with supervisor role in this project
      supervisor_role = Role.find_by(name: 'supervisor')
      unless supervisor_role
        explanation << "No 'supervisor' role found in the system"
        explanation << "Falling back to project managers"

        result = project_managers(requestor_id, project_id, context)
        managers = result.is_a?(Hash) ? result[:approvers] : result
        manager_explanation = result.is_a?(Hash) ? result[:explanation] : "Project managers found: #{managers.join(', ')}"

        explanation << manager_explanation
        return { approvers: managers, explanation: explanation.join("\n") }
      end

      explanation << "Found 'supervisor' role with ID: #{supervisor_role.id}"

      supervisors = User.joins(:user_roles)
                        .where(user_roles: { role_id: supervisor_role.id, project_id: project.id })
                        .pluck(:id)
                        .map(&:to_s)

      if supervisors.empty?
        explanation << "No supervisors found for this project"
        explanation << "Falling back to project managers"

        result = project_managers(requestor_id, project_id, context)
        managers = result.is_a?(Hash) ? result[:approvers] : result
        manager_explanation = result.is_a?(Hash) ? result[:explanation] : "Project managers found: #{managers.join(', ')}"

        explanation << manager_explanation
        return { approvers: managers, explanation: explanation.join("\n") }
      else
        explanation << "Found #{supervisors.size} supervisors: #{supervisors.join(', ')}"
      end

      { approvers: supervisors, explanation: explanation.join("\n") }
    end

    # Context-aware approver resolution methods

    # Level-based approver resolution methods

    # Find approvers at a specific level
    def approvers_at_level(requestor_id = nil, project_id = nil, context = {})
      # Extract level from context
      level = context[:level].to_i
      explanation = [ "Looking for approvers at level #{level}" ]

      if level <= 0
        explanation << "Invalid level: #{level} (must be > 0)"
        return { approvers: [], explanation: explanation.join("\n") }
      end

      # Find roles at the specified level
      role_ids = Role.where(level: level).pluck(:id)
      if role_ids.empty?
        explanation << "No roles found at level #{level}"
        return { approvers: [], explanation: explanation.join("\n") }
      end

      explanation << "Found #{role_ids.size} roles at level #{level}: #{Role.where(id: role_ids).pluck(:name).join(', ')}"

      # Find users with these roles
      query = User.joins(:user_roles).where(user_roles: { role_id: role_ids })
      if project_id.present?
        query = query.where(user_roles: { project_id: project_id })
        explanation << "Filtering by project_id: #{project_id}"
      end

      approvers = query.distinct.pluck(:id).map(&:to_s)

      if approvers.empty?
        explanation << "No users found with roles at level #{level}"
      else
        explanation << "Found #{approvers.size} approvers: #{approvers.join(', ')}"
      end

      { approvers: approvers, explanation: explanation.join("\n") }
    end

    # Find approvers at or above a specific level
    def approvers_at_or_above_level(requestor_id = nil, project_id = nil, context = {})
      # Extract level from context
      level = context[:level].to_i
      explanation = [ "Looking for approvers at or above level #{level}" ]

      if level <= 0
        explanation << "Invalid level: #{level} (must be > 0)"
        return { approvers: [], explanation: explanation.join("\n") }
      end

      # Find roles at or above the specified level
      role_ids = Role.where("level >= ?", level).pluck(:id)
      if role_ids.empty?
        explanation << "No roles found at or above level #{level}"
        return { approvers: [], explanation: explanation.join("\n") }
      end

      explanation << "Found #{role_ids.size} roles at or above level #{level}: #{Role.where(id: role_ids).pluck(:name).join(', ')}"

      # Find users with these roles
      query = User.joins(:user_roles).where(user_roles: { role_id: role_ids })
      if project_id.present?
        query = query.where(user_roles: { project_id: project_id })
        explanation << "Filtering by project_id: #{project_id}"
      end

      approvers = query.distinct.pluck(:id).map(&:to_s)

      if approvers.empty?
        explanation << "No users found with roles at or above level #{level}"
      else
        explanation << "Found #{approvers.size} approvers: #{approvers.join(', ')}"
      end

      { approvers: approvers, explanation: explanation.join("\n") }
    end

    # Find the next level up approvers from a user
    def next_level_approvers(requestor_id, project_id = nil, context = {})
      explanation = [ "Looking for approvers at the next level up from user #{requestor_id}" ]

      user = User.find_by(id: requestor_id)
      unless user
        explanation << "User not found with ID: #{requestor_id}"
        return { approvers: [], explanation: explanation.join("\n") }
      end

      explanation << "Found user: #{user.name} (ID: #{user.id})"

      # Get all user roles, filtered by project if specified
      user_roles = user.user_roles
      if project_id.present?
        user_roles = user_roles.where(project_id: project_id)
        explanation << "Filtering by project_id: #{project_id}"
      end

      # Find the highest level among the user's roles
      role_ids = user_roles.pluck(:role_id)
      if role_ids.empty?
        explanation << "User has no roles"
        return { approvers: [], explanation: explanation.join("\n") }
      end

      explanation << "User has roles: #{Role.where(id: role_ids).pluck(:name).join(', ')}"

      highest_level = Role.where(id: role_ids).maximum(:level) || 0
      explanation << "User's highest role level is: #{highest_level}"

      # Find users with roles at the next level up
      next_level = ((highest_level / 10) + 1) * 10 # Round up to next level
      explanation << "Next level up is: #{next_level}"

      # Find roles at the next level
      next_level_role_ids = Role.where(level: next_level).pluck(:id)
      if next_level_role_ids.empty?
        explanation << "No roles found at level #{next_level}"
        return { approvers: [], explanation: explanation.join("\n") }
      end

      explanation << "Found #{next_level_role_ids.size} roles at level #{next_level}: #{Role.where(id: next_level_role_ids).pluck(:name).join(', ')}"

      # Find users with these roles
      query = User.joins(:user_roles).where(user_roles: { role_id: next_level_role_ids })
      if project_id.present?
        query = query.where(user_roles: { project_id: project_id })
        explanation << "Filtering by project_id: #{project_id}"
      end

      approvers = query.distinct.pluck(:id).map(&:to_s)

      if approvers.empty?
        explanation << "No users found with roles at level #{next_level}"
      else
        explanation << "Found #{approvers.size} approvers: #{approvers.join(', ')}"
      end

      # Ensure approvers is an array of strings
      approvers = approvers.map(&:to_s)

      { approvers: approvers, explanation: explanation.join("\n") }
    end

    # Find approvers based on threshold levels from context
    def threshold_based_approvers(requestor_id = nil, project_id = nil, context = {})
      explanation = [ "Looking for approvers based on amount threshold" ]

      amount = context[:amount].to_f
      explanation << "Amount: #{amount}"

      # Determine required approval level based on amount thresholds
      required_level = case
      when amount > 10000 then 40 # Admin level
      when amount > 5000 then 30 # Department head level
      when amount > 1000 then 20 # Mid-management level
      else 10 # Base level
      end

      explanation << "Required approval level based on amount: #{required_level}"

      # Find roles at or above the required level
      role_ids = Role.where("level >= ?", required_level).pluck(:id)
      if role_ids.empty?
        explanation << "No roles found at or above level #{required_level}"
        return { approvers: [], explanation: explanation.join("\n") }
      end

      explanation << "Found #{role_ids.size} roles at or above level #{required_level}: #{Role.where(id: role_ids).pluck(:name).join(', ')}"

      # Find users with these roles
      query = User.joins(:user_roles).where(user_roles: { role_id: role_ids })
      if project_id.present?
        query = query.where(user_roles: { project_id: project_id })
        explanation << "Filtering by project_id: #{project_id}"
      end

      approvers = query.distinct.pluck(:id).map(&:to_s)

      if approvers.empty?
        explanation << "No users found with roles at or above level #{required_level}"
      else
        explanation << "Found #{approvers.size} approvers: #{approvers.join(', ')}"
      end

      # Ensure approvers is an array of strings
      approvers = approvers.map(&:to_s)

      { approvers: approvers, explanation: explanation.join("\n") }
    end

    # Context-aware approver resolution methods

    # Resolve approvers based on priority
    def priority_based_approvers(requestor_id, project_id = nil, context = {})
      priority = context[:priority].to_s
      explanation = [ "Looking for approvers based on priority: #{priority}" ]

      result = if priority == 'high'
                 # For high priority requests, require admin approval
                 admin_users(requestor_id, project_id, context)
      else
                 # For normal priority, use direct manager
                 direct_manager(requestor_id, project_id, context)
      end

      # Ensure result is properly formatted
      if result.is_a?(Hash) && result[:approvers] && result[:explanation]
        # Already in the correct format
        result
      else
        # Convert to the correct format
        approvers = Array(result).map(&:to_s)
        explanation = "Approvers selected based on priority: #{priority}"
        { approvers: approvers, explanation: explanation }
      end
    end

    # Resolve approvers based on leave type
    def leave_type_based_hr_approvers(requestor_id, project_id = nil, context = {})
      leave_type = context[:leave_type].to_s
      explanation = [ "Looking for approvers based on leave type: #{leave_type}" ]

      if leave_type == 'sick'
        # For sick leave, only HR managers can approve
        hr_role = Role.find_by(name: 'hr_manager')
        unless hr_role
          explanation << "HR manager role not found"
          return { approvers: [], explanation: explanation.join("\n") }
        end

        approvers = User.joins(:user_roles)
                        .where(user_roles: { role_id: hr_role.id })
                        .pluck(:id)
                        .map(&:to_s)

        explanation << "Selected HR managers for sick leave"
      else
        # For other leave types, any HR approver can approve
        result = hr_approvers(requestor_id, project_id, context)

        if result.is_a?(Hash) && result[:approvers] && result[:explanation]
          return result # Already has explanation
        end

        approvers = Array(result).map(&:to_s)
        explanation << "Selected HR approvers for #{leave_type} leave"
      end

      { approvers: approvers, explanation: explanation.join("\n") }
    end

    # Resolve approvers based on leave type - HR managers only (no officers)
    def leave_type_based_hr_managers_only(requestor_id, project_id = nil, context = {})
      leave_type = context[:leave_type].to_s
      explanation = [ "Looking for HR managers only based on leave type: #{leave_type}" ]

      # For all leave types, only HR managers can approve (not officers)
      hr_manager_role = Role.find_by(name: 'hr_manager')
      unless hr_manager_role
        explanation << "HR manager role not found"
        return { approvers: [], explanation: explanation.join("\n") }
      end

      approvers = User.joins(:user_roles)
                      .where(user_roles: { role_id: hr_manager_role.id })
                      .pluck(:id)
                      .map(&:to_s)

      explanation << "Selected HR managers only for #{leave_type} leave"

      if approvers.empty?
        explanation << "No HR managers found"
      else
        explanation << "Found #{approvers.size} HR managers: #{approvers.join(', ')}"
      end

      { approvers: approvers, explanation: explanation.join("\n") }
    end

    # Resolve approvers based on request amount
    def amount_based_financial_approvers(requestor_id, project_id = nil, context = {})
      amount = context[:amount].to_f
      explanation = [ "Looking for approvers based on amount: #{amount}" ]

      if amount > 5000
        # For large amounts, only financial managers can approve
        financial_role = Role.find_by(name: 'financial_manager')
        unless financial_role
          explanation << "Financial manager role not found"
          return { approvers: [], explanation: explanation.join("\n") }
        end

        approvers = User.joins(:user_roles)
                        .where(user_roles: { role_id: financial_role.id })
                        .pluck(:id)
                        .map(&:to_s)

        explanation << "Selected financial managers for amount > 5000"
      else
        # For smaller amounts, any financial approver can approve
        result = financial_approvers(requestor_id, project_id, context)

        if result.is_a?(Hash) && result[:approvers] && result[:explanation]
          return result # Already has explanation
        end

        approvers = Array(result).map(&:to_s)
        explanation << "Selected financial approvers for amount <= 5000"
      end

      { approvers: approvers, explanation: explanation.join("\n") }
    end

    # Level and priority based approvers
    def level_and_priority_approvers(requestor_id, project_id = nil, context = {})
      explanation = [ "Looking for approvers based on priority level" ]

      priority = context[:priority].to_s
      explanation << "Priority: #{priority}"

      # Determine required level based on priority
      required_level = case priority
      when 'high' then 40 # Admin level
      when 'medium' then 30 # Department head level
      else 20 # Project manager level
      end

      explanation << "Required approval level based on priority: #{required_level}"

      # Find roles at the required level
      role_ids = Role.where(level: required_level).pluck(:id)
      if role_ids.empty?
        explanation << "No roles found at level #{required_level}"
        return { approvers: [], explanation: explanation.join("\n") }
      end

      explanation << "Found #{role_ids.size} roles at level #{required_level}: #{Role.where(id: role_ids).pluck(:name).join(', ')}"

      # Find users with these roles
      query = User.joins(:user_roles).where(user_roles: { role_id: role_ids })
      if project_id.present?
        query = query.where(user_roles: { project_id: project_id })
        explanation << "Filtering by project_id: #{project_id}"
      end

      approvers = query.distinct.pluck(:id).map(&:to_s)

      if approvers.empty?
        explanation << "No users found with roles at level #{required_level}"
      else
        explanation << "Found #{approvers.size} approvers: #{approvers.join(', ')}"
      end

      # Ensure approvers is an array of strings
      approvers = approvers.map(&:to_s)

      { approvers: approvers, explanation: explanation.join("\n") }
    end

    # Find only financial managers (not accountants)
    def financial_managers_only(requestor_id, project_id = nil, context = {})
      explanation = [ "Looking for financial managers only" ]

      financial_role = Role.find_by(name: 'financial_manager')
      unless financial_role
        explanation << "Financial manager role not found"
        return { approvers: [], explanation: explanation.join("\n") }
      end

      approvers = User.joins(:user_roles)
                      .where(user_roles: { role_id: financial_role.id })
                      .pluck(:id)
                      .map(&:to_s)

      if approvers.empty?
        explanation << "No users found with financial manager role"
      else
        explanation << "Found #{approvers.size} financial managers: #{approvers.join(', ')}"
      end

      { approvers: approvers, explanation: explanation.join("\n") }
    end

    # Case Management Approver Resolution Methods

    # Find case manager for a specific case
    def case_manager_for_case(requestor_id = nil, project_id = nil, context = {})
      explanation = [ "Looking for case manager for case approval" ]

      case_manager_id = context[:case_manager_id]
      unless case_manager_id
        explanation << "No case_manager_id provided in context"
        return { approvers: [], explanation: explanation.join("\n") }
      end

      explanation << "Case manager ID from context: #{case_manager_id}"

      # Verify the case manager exists and is active
      case_manager = User.find_by(id: case_manager_id)
      unless case_manager
        explanation << "Case manager not found with ID: #{case_manager_id}"
        return { approvers: [], explanation: explanation.join("\n") }
      end

      # Check if case manager has the appropriate role
      unless case_manager.has_role?('case_manager', project_id)
        explanation << "User #{case_manager_id} does not have case_manager role in project #{project_id}"
        return { approvers: [], explanation: explanation.join("\n") }
      end

      explanation << "Found case manager: #{case_manager.name} (#{case_manager_id})"

      { approvers: [ case_manager_id.to_s ], explanation: explanation.join("\n") }
    end

    # Find supervisors for case management in a specific project
    def supervisors_for_case(requestor_id = nil, project_id = nil, context = {})
      explanation = [ "Looking for supervisors for case management" ]

      unless project_id
        explanation << "No project_id provided"
        return { approvers: [], explanation: explanation.join("\n") }
      end

      explanation << "Project ID: #{project_id}"

      # Check if requestor is a supervisor (smart bypass logic)
      if requestor_id
        requestor = User.find_by(id: requestor_id)
        if requestor&.has_role?('supervisor', project_id)
          explanation << "Requestor is a supervisor - bypassing supervisor approval"
          return { approvers: [], explanation: explanation.join("\n") }
        end
      end

      # Find supervisors in the project
      supervisor_role = Role.find_by(name: 'supervisor')
      unless supervisor_role
        explanation << "Supervisor role not found"
        return { approvers: [], explanation: explanation.join("\n") }
      end

      supervisors = User.joins(:user_roles)
                       .where(user_roles: { role_id: supervisor_role.id, project_id: project_id })
                       .pluck(:id)
                       .map(&:to_s)

      if supervisors.empty?
        explanation << "No supervisors found for project #{project_id}"
        explanation << "Falling back to project managers"

        result = project_managers_for_case(requestor_id, project_id, context)
        return result
      else
        explanation << "Found #{supervisors.size} supervisors: #{supervisors.join(', ')}"
      end

      { approvers: supervisors, explanation: explanation.join("\n") }
    end

    # Find project managers for case management
    def project_managers_for_case(requestor_id = nil, project_id = nil, context = {})
      explanation = [ "Looking for project managers for case management" ]

      unless project_id
        explanation << "No project_id provided"
        return { approvers: [], explanation: explanation.join("\n") }
      end

      explanation << "Project ID: #{project_id}"

      # Check if requestor is a project manager (smart bypass logic)
      if requestor_id
        requestor = User.find_by(id: requestor_id)
        if requestor&.has_role?('project_manager', project_id)
          explanation << "Requestor is a project manager - bypassing project manager approval"
          return { approvers: [], explanation: explanation.join("\n") }
        end
      end

      # Find project managers
      pm_role = Role.find_by(name: 'project_manager')
      unless pm_role
        explanation << "Project manager role not found"
        return { approvers: [], explanation: explanation.join("\n") }
      end

      project_managers = User.joins(:user_roles)
                            .where(user_roles: { role_id: pm_role.id, project_id: project_id })
                            .pluck(:id)
                            .map(&:to_s)

      if project_managers.empty?
        explanation << "No project managers found for project #{project_id}"
        explanation << "Falling back to admin users"

        result = admin_users(requestor_id, project_id, context)
        return result
      else
        explanation << "Found #{project_managers.size} project managers: #{project_managers.join(', ')}"
      end

      { approvers: project_managers, explanation: explanation.join("\n") }
    end

    # Find receiving supervisors for case transfers
    def receiving_supervisors_for_transfer(requestor_id = nil, project_id = nil, context = {})
      explanation = [ "Looking for receiving supervisors for case transfer" ]

      new_project_id = context[:new_project_id]
      unless new_project_id
        explanation << "No new_project_id provided in context"
        return { approvers: [], explanation: explanation.join("\n") }
      end

      explanation << "New project ID: #{new_project_id}"

      # Find supervisors in the receiving project
      supervisor_role = Role.find_by(name: 'supervisor')
      unless supervisor_role
        explanation << "Supervisor role not found"
        return { approvers: [], explanation: explanation.join("\n") }
      end

      supervisors = User.joins(:user_roles)
                       .where(user_roles: { role_id: supervisor_role.id, project_id: new_project_id })
                       .pluck(:id)
                       .map(&:to_s)

      if supervisors.empty?
        explanation << "No supervisors found for receiving project #{new_project_id}"
        explanation << "Falling back to project managers in receiving project"

        # Use project managers from receiving project
        pm_role = Role.find_by(name: 'project_manager')
        if pm_role
          project_managers = User.joins(:user_roles)
                                .where(user_roles: { role_id: pm_role.id, project_id: new_project_id })
                                .pluck(:id)
                                .map(&:to_s)

          if project_managers.any?
            explanation << "Found #{project_managers.size} project managers in receiving project: #{project_managers.join(', ')}"
            return { approvers: project_managers, explanation: explanation.join("\n") }
          end
        end

        explanation << "No project managers found in receiving project either"
        return { approvers: [], explanation: explanation.join("\n") }
      else
        explanation << "Found #{supervisors.size} supervisors in receiving project: #{supervisors.join(', ')}"
      end

      { approvers: supervisors, explanation: explanation.join("\n") }
    end
  end
end
