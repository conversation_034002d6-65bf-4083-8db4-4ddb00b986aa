class Project < ApplicationRecord
  include Athar::Commons::Models::Concerns::Ransackable

  has_many :user_roles, dependent: :destroy
  has_many :users, through: :user_roles
  has_many :roles, through: :user_roles

  # Scopes
  scope :active, -> { where(status: :active) }
  scope :inactive, -> { where(status: :inactive) }
  scope :with_user, ->(user) { joins(:user_roles).where(user_roles: { user_id: user.id }).distinct }
  scope :with_role, ->(role_name) { joins(user_roles: :role).where(roles: { name: role_name }).distinct }
  scope :with_permission, ->(permission_name) {
    joins(user_roles: { role: :permissions })
      .where(permissions: { name: permission_name })
      .distinct
  }
  scope :with_user_having_role, ->(user, role_name) {
    joins(user_roles: :role)
      .where(user_roles: { user_id: user.id }, roles: { name: role_name })
      .distinct
  }
  scope :with_user_having_permission, ->(user, permission_name) {
    joins(user_roles: { role: :permissions })
      .where(user_roles: { user_id: user.id }, permissions: { name: permission_name })
      .distinct
  }

  enum :status, { inactive: false, active: true }

  validates :name, presence: true
  validates :name, uniqueness: true

  after_initialize :set_default_status, if: :new_record?

  # Get all users with a specific role in this project
  def users_with_role(role_name)
    role = Role.find_by(name: role_name)
    return User.none unless role

    users.joins(:user_roles).where(user_roles: { role_id: role.id, project_id: id })
  end

  # Check if a user has a specific role in this project
  def user_has_role?(user, role_name)
    return false unless user
    user.has_role?(role_name, self)
  end

  # Add a user to this project with a specific role
  def add_user(user, role_name)
    return unless user
    user.add_role(role_name, self)
  end

  # Remove a user from this project
  def remove_user(user)
    return unless user
    # Remove all roles the user has in this project
    user_roles.where(user: user).destroy_all
  end

  # Assign a role to a user in this project
  def assign_role_to_user(user, role_name)
    return unless user
    user.add_role(role_name, self)
  end

  # Get all users in this project
  def members
    users
  end

  # Get count of users in this project
  def member_count
    users.count
  end

  def to_rpc_response
    ::Core::ProjectResponse.new(
      id: id,
      name: name,
      description: description,
      status: status.to_s
    )
  end

  private

  def set_default_status
    self.status ||= :active
  end
end
