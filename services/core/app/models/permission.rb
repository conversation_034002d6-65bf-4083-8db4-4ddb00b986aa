class Permission < ApplicationRecord
  has_many :role_permissions
  has_many :roles, through: :role_permissions

  validates :name, presence: true, uniqueness: { scope: :system_name }
  validates :action, presence: true
  validates :subject_class, presence: true
  validates :system_name, presence: true, inclusion: { in: %w[core cm people procure] }

  scope :by_system, ->(system_name) { where(system_name: system_name) }

  # Find permissions for a specific user
  scope :for_user, ->(user) {
    includes(roles: :user_roles)
    .where(user_roles: { user_id: user.is_a?(User) ? user.id : user })
    .distinct
  }

  # Find permissions for a specific project
  scope :for_project, ->(project) {
    includes(roles: :user_roles)
    .where(user_roles: { project_id: project.is_a?(Project) ? project.id : project })
    .distinct
  }

  def to_s
    "#{action}:#{subject_class&.underscore}"
  end
end
