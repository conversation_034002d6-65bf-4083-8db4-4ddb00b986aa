class User < ApplicationRecord
  # Include default devise modules. Others available are:
  # :confirmable, :lockable, :timeoutable, :trackable and :omniauthable
  devise :database_authenticatable, :registerable, :recoverable, :validatable

  include Athar::Commons::Models::Concerns::Ransackable

  has_one_attached :avatar
  has_many :user_roles, dependent: :destroy
  has_many :roles, through: :user_roles
  has_many :projects, through: :user_roles
  has_many :permissions, through: :roles

  accepts_nested_attributes_for :user_roles, allow_destroy: true, reject_if: :all_blank

  # Scopes
  scope :in_project, ->(project) {
    project_id = project.respond_to?(:id) ? project.id : project
    includes(:user_roles).where(user_roles: { project_id: project_id }).distinct
  }
  scope :in_projects, ->(project_ids) {
    project_ids = Array(project_ids).map { |p| p.respond_to?(:id) ? p.id : p }
    includes(:user_roles).where(user_roles: { project_id: project_ids }).distinct
  }
  scope :with_role, ->(role_name) { joins(user_roles: :role).where(roles: { name: role_name }).distinct }
  scope :with_role_in_project, ->(role_name, project) {
    includes(user_roles: :role)
      .where(roles: { name: role_name }, user_roles: { project_id: project.id })
      .distinct
  }
  scope :with_permission, ->(permission_name) {
    includes(roles: :permissions).where(permissions: { name: permission_name }).distinct
  }
  scope :with_permission_in_project, ->(permission_name, project) {
    includes(user_roles: { role: :permissions })
      .where(permissions: { name: permission_name }, user_roles: { project_id: project.id })
      .distinct
  }

  enum :status, { inactive: false, active: true }
  enum :global, { project_based: false, global_user: true }

  # Validations for user type
  validate :validate_role_consistency, if: :persisted?

  # Athar Project validations
  validate :global_users_must_have_athar_project, if: :persisted?

  # Avatar handling
  attr_accessor :pending_avatar_data
  after_save :attach_pending_avatar

  after_initialize :set_default_status, if: :new_record?
  before_validation :ensure_default_project, if: :persisted?

  validates :email, presence: true, uniqueness: true, format: { with: URI::MailTo::EMAIL_REGEXP }
  validates :password, length: { minimum: 6 }, if: -> { new_record? || !password.nil? }

  def generate_session_token(scope, role, project, exp)
    # Validate scope parameter
    valid_scopes = %w[core people procure cm]
    unless valid_scopes.include?(scope)
      raise ArgumentError, "Invalid scope. Must be one of: #{valid_scopes.join(', ')}"
    end

    # Validate system access before generating token
    unless can_access_system?(scope)
      raise ArgumentError, "User doesn't have access to #{scope.humanize} system"
    end

    # For non-global users, ensure they have a project context
    # This should not happen if default_project logic is working correctly
    if !global_user? && (project.nil? || project.id.blank?)
      raise ArgumentError, "Project context is required for project-based users. User may not have a default project assigned. Please contact administrator."
    end

    # For global users, ensure they have Athar project context
    # This maintains consistency - all users should have project context
    if global_user? && (project.nil? || project.id.blank?)
      raise ArgumentError, "Athar project context is required for global users. Please contact administrator."
    end

    # Generate compressed token using JSONPath structure
    {
      # Standard JWT claims (maps to JSONPath)
      s: id, # 'user.id' => 's'
      i: Time.now.to_i, # 'issued.at' => 'i'
      e: exp, # 'expires.at' => 'e'

      # Token metadata (maps to JSONPath)
      t: "session", # 'token.type' => 't'
      sc: scope, # 'scope' => 'sc'

      # User context (maps to 'user.*' => 'u.*')
      u: build_compressed_user_context,

      # Session context (maps to 'session.*' => 'ss.*')
      ss: build_compressed_session_context(scope, role, project),

      # Access context (maps to 'access.*' => 'a.*')
      a: build_compressed_access_context(scope, project)
    }
  end

  def user_type
    global_user? ? "global" : "project_based"
  end

  # System access validation
  def can_access_system?(system_name)
    if global_user?
      # Global users can access any system they have roles for
      user_roles.joins(:role, role: :permissions)
                .where(permissions: { system_name: system_name })
                .exists?
    else
      # Project-based users must have roles with permissions for this system
      user_roles.joins(:role, role: :permissions)
                .where(permissions: { system_name: system_name })
                .exists?
    end
  end

  def accessible_systems
    @accessible_systems ||= user_roles.joins(:role, role: :permissions)
                                      .pluck('permissions.system_name')
                                      .uniq
  end

  def can_access_system_in_project?(system_name, project)
    return true if global_user? && can_access_system?(system_name)

    user_roles.joins(:role, role: :permissions)
              .where(permissions: { system_name: system_name }, project: project)
              .exists?
  end

  def default_role
    user_roles.find_by(is_default: true)&.role
  end

  def has_role?(role_name, project = nil)
    role = Role.find_by(name: role_name)
    return false unless role

    if role.global_role?
      # Global roles are stored with the organization project
      org_project_name = AtharAuth.organization_project_name
      org_project = Project.find_by(name: org_project_name)
      return false unless org_project

      user_roles.joins(:role).exists?(roles: { name: role_name }, project: org_project)
    else
      # Project roles require a specific project
      return false unless project

      user_roles.joins(:role).exists?(roles: { name: role_name }, project: project)
    end
  end

  def add_role(role_name, project)
    role = Role.find_by(name: role_name)
    return false unless role

    # Determine target project based on role type
    if role.global_role?
      # Global roles cannot be added to project-based users
      if project_based?
        Rails.logger.error "Cannot add global role '#{role_name}' to project-based user #{email}"
        return false
      end

      # Global roles are assigned to the organization project
      org_project_name = AtharAuth.organization_project_name
      target_project = Project.find_by(name: org_project_name)
      unless target_project
        Rails.logger.error "Organization project '#{org_project_name}' not found"
        return false
      end
    else
      # Project roles require a specific project
      unless project
        Rails.logger.error "Project is required for project-based role '#{role_name}'"
        return false
      end
      target_project = project
    end

    # First remove any existing role for this project
    existing_role = user_roles.where(project: target_project).first
    user_roles.destroy(existing_role) if existing_role

    # Create the new role assignment
    user_role = user_roles.create(role: role, project: target_project)
    if user_role.persisted?
      true
    else
      Rails.logger.error "Failed to create user role: #{user_role.errors.full_messages}"
      false
    end
  end

  def default_project
    # First try to get the explicitly marked default project (respects "first role is default")
    default_user_role = user_roles.default.first
    return default_user_role.project if default_user_role&.project

    # Fallback: get the first project for this user (maintains "first role" concept)
    first_project = user_roles.joins(:project).first&.project
    return first_project if first_project

    # Global users get organization project as ultimate fallback
    if global_user?
      Project.find_by(name: AtharAuth.organization_project_name)
    else
      nil
    end
  end

  # Avatar methods
  def avatar_attributes
    return nil unless avatar.attached?

    {
      url: avatar.cdn_url
    }
  end

  # Setter method for avatar attributes from RPC requests
  def avatar_attributes=(attrs)
    return unless attrs.present?

    # Extract attributes
    image_data = attrs.is_a?(Hash) ? attrs[:image] : attrs.image
    filename = attrs.is_a?(Hash) ? (attrs[:filename] || 'avatar.jpg') : (attrs.filename.presence || 'avatar.jpg')
    content_type = attrs.is_a?(Hash) ? attrs[:content_type] : attrs.content_type

    return unless image_data.present?


    # Always store data for attachment after save (both new and existing users)
    self.pending_avatar_data = {
      image_data: image_data,
      filename: filename,
      content_type: content_type
    }
  end

  # Getter method for user_roles as a list of hashes
  def user_roles_list
    user_roles.map(&:to_rpc_response)
  end

  # Setter method for user_roles from RPC requests
  def user_roles_list=(roles_data)
    new_roles = []
    user_roles.to_a.map(&:attributes).map do |role_data|
      new_data = roles_data.find { |data| data["id"].to_i == role_data["id"].to_i }
      unless new_data
        new_data = role_data
        new_data["_destroy"] = true
      end
      roles_data.delete(new_data)
      role_data.merge!(new_data.slice(*UserRole.column_names.intersection(new_data.keys))) if new_data
      new_roles << role_data
    end
    new_roles += roles_data

    self.user_roles_attributes = new_roles
  end

  def to_rpc_response
    ::Core::UserResponse.new(
      id: id,
      name: name,
      email: email,
      status: status.to_s,
      avatar_attributes: avatar_attributes,
      user_roles_list: user_roles_list
    )
  end

  # Dynamically define role checking methods (e.g., user.super_admin?(project))
  Role.pluck(:name).each do |role_name|
    define_method("#{role_name}?") do |project|
      has_role?(role_name, project)
    end
  end

  private

  # Compressed token building methods
  def build_compressed_user_context
    {
      n: name, # 'user.name' => 'u.n'
      m: email, # 'user.email' => 'u.m'
      g: global_user?, # 'user.global' => 'u.g'
      ty: user_type, # 'user.type' => 'u.ty'
      st: status # 'user.status' => 'u.st'
    }
  end

  def build_compressed_session_context(scope, role, project)
    {
      r: role ? build_compressed_role_data(role) : nil, # 'session.role.*' => 'ss.r.*'
      p: role&.permissions&.by_system(scope)&.map(&:to_s) || [], # 'session.permissions' => 'ss.p'
      pr: project ? build_compressed_project_data(project) : nil # 'session.project.*' => 'ss.pr.*'
    }
  end

  def build_compressed_role_data(role)
    {
      n: role.name, # 'session.role.name' => 'ss.r.n'
      l: role.level, # 'session.role.level' => 'ss.r.l'
      s: role.scope # 'session.role.scope' => 'ss.r.s'
    }
  end

  def build_compressed_project_data(project)
    {
      id: project.id, # 'session.project.id' => 'ss.pr.id' (ensure string for gRPC)
      n: project.name, # 'session.project.name' => 'ss.pr.n'
      d: project.description, # 'session.project.description' => 'ss.pr.d'
      st: project.status # 'session.project.status' => 'ss.pr.st'
    }
  end

  def build_compressed_access_context(scope, project)
    if global_user?
      {
        ty: "global", # 'access.type' => 'a.ty'
        prs: Project.active.pluck(:id, :name).to_h # 'access.projects' => 'a.prs' (ensure string keys)
      }
    else
      # Get other projects user has access to in this scope (excluding current)
      other_projects = user_roles.includes(:project, :role)
                                 .joins(role: :permissions)
                                 .where(permissions: { system_name: scope })
                                 .where.not(project_id: project&.id)
                                 .map do |user_role|
        {
          id: user_role.project_id,
          n: user_role.project.name, # 'project.name' => 'n' (reused pattern)
          r: user_role.role.name # 'role.name' => 'r' (reused pattern)
        }
      end

      {
        ty: "project", # 'access.type' => 'a.ty'
        op: other_projects # 'access.other_projects' => 'a.op'
      }
    end
  end

  def set_default_status
    self.status ||= :active
  end

  def validate_role_consistency
    # Get user_roles that are not marked for destruction
    active_user_roles = user_roles.reject(&:marked_for_destruction?)

    # Check if any active user_roles have roles with the wrong global setting
    has_project_roles = active_user_roles.any? { |ur| ur.role&.project_based? }
    has_global_roles = active_user_roles.any? { |ur| ur.role&.global_role? }

    # For new users, automatically set the user type based on their roles
    if new_record?
      if has_global_roles && !has_project_roles
        self.global = :global_user
        Rails.logger.info "Auto-setting new user #{email} as global_user due to global roles"
      elsif has_project_roles && !has_global_roles
        self.global = :project_based
        Rails.logger.info "Auto-setting new user #{email} as project_based due to project roles"
      elsif has_global_roles && has_project_roles
        errors.add(:base, "Users cannot have both global and project-specific roles")
      end
      return
    end

    # For existing users, validate role consistency
    if global_user? && has_project_roles
      errors.add(:base, "Global users cannot have project-specific roles")
    elsif project_based? && has_global_roles
      errors.add(:base, "Project users cannot have global roles")
    end
  end

  def global_users_must_have_athar_project
    return unless global_user?

    org_project_name = AtharAuth.organization_project_name
    athar_project = Project.find_by(name: org_project_name)
    return unless athar_project

    unless user_roles.joins(:project).exists?(projects: { name: org_project_name })
      errors.add(:base, "Global users must be assigned to #{org_project_name} project")
    end
  end

  def ensure_default_project
    # Skip if user already has a default project
    return if user_roles.exists?(is_default: true)

    # Skip if user has no roles yet
    return if user_roles.empty?

    # Auto-assign first role as default (follows "first role is default" principle)
    first_role = user_roles.joins(:project).first
    if first_role
      first_role.update_column(:is_default, true)
      Rails.logger.info "Auto-assigned default project for user #{email}: #{first_role.project.name}"
    end
  end

  def attach_pending_avatar
    return unless pending_avatar_data && pending_avatar_data[:image_data].present?

    # Prevent infinite recursion by clearing data first
    avatar_data = pending_avatar_data
    self.pending_avatar_data = nil

    # Store reference to old avatar for cleanup after successful upload
    old_avatar = avatar.attached? ? avatar.blob : nil

    # Force immediate upload by creating blob first
    blob = ActiveStorage::Blob.create_and_upload!(
      io: StringIO.new(avatar_data[:image_data]),
      filename: avatar_data[:filename],
      content_type: avatar_data[:content_type]
    )

    # Then attach the uploaded blob
    avatar.attach(blob)

    # Only purge old avatar after new one is successfully attached
    old_avatar&.purge_later if old_avatar
  end
end
