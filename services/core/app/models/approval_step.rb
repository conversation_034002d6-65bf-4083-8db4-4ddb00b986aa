class ApprovalStep < ApplicationRecord
  belongs_to :approval_workflow

  # Include the LevelBasedApproval concern
  include Athar::Commons::Models::Concerns::LevelBasedApproval

  # Approval types
  APPROVAL_TYPES = %w[any all].freeze

  # Validations
  validates :name, presence: true
  validates :sequence, presence: true, numericality: { only_integer: true, greater_than: 0 }
  validates :approval_type, presence: true, inclusion: { in: APPROVAL_TYPES }
  validates :dynamic_approver_method, presence: true, unless: -> { static_approver_ids.present? }

  # Evaluate if this step should be included based on the condition
  def should_include?(context)
    return true if condition.blank?

    # Validate context values
    begin
      # Check for numeric values where needed
      if condition.include?(".to_f") || condition.include?(".to_i")
        # Extract keys used in numeric operations
        numeric_keys = condition.scan(/([\w_]+)\.to_(f|i)/).map(&:first).uniq
        numeric_keys.each do |key|
          value = context[key.to_sym] || context[key]
          next unless value.present?

          # Try to convert to number
          begin
            Float(value)
          rescue ArgumentError
            Rails.logger.warn "Invalid numeric value for context key '#{key}' in step '#{name}': #{value}"
            return false
          end
        end
      end

      # Create a safe evaluation context
      evaluation_context = SafeEvaluationContext.new(context)

      # Evaluate the condition in the context
      evaluation_context.evaluate(condition)
    rescue => e
      Rails.logger.error "Error evaluating condition '#{condition}' in step '#{name}': #{e.message}"
      Rails.logger.error e.backtrace.join("\n") if e.backtrace
      true # Include the step by default if there's an error
    end
  end

  # Helper class for safely evaluating conditions
  class SafeEvaluationContext
    def initialize(context)
      @context = context

      # Define methods for each context key
      context.each do |key, value|
        if key.to_s == 'previous_steps'
          # Special handling for previous_steps hash
          @previous_steps = value
          singleton_class.define_method('previous_steps') { value }
        else
          singleton_class.define_method(key.to_s) { value }
        end
      end
    end

    def evaluate(condition)
      # Special case for 'true' condition
      return true if condition.strip == 'true'
      return false if condition.strip == 'false'

      # Define allowed operators and functions
      allowed_operators = %w[> < >= <= == != && || + - * / ( ) [ ] {}]
      allowed_functions = %w[present? blank? nil? is_a? kind_of? instance_of? respond_to? to_i to_f to_s empty? include? key? has_key?]

      # Check if condition contains only allowed operators and functions
      condition_sanitized = condition.dup
      allowed_operators.each { |op| condition_sanitized.gsub!(op, '') }
      allowed_functions.each { |func| condition_sanitized.gsub!(/\.#{func}\b/, '') }

      # Check if any suspicious code remains
      if condition_sanitized =~ /[;`&|]|\beval\b|\bsystem\b|\bexec\b|\b__send__\b|\bsend\b/
        raise "Potentially unsafe condition: #{condition}"
      end

      # Evaluate the condition directly in this context
      instance_eval(condition)
    rescue NoMethodError => e
      Rails.logger.error "Error evaluating condition '#{condition}': #{e.message}"
      # If the error is related to previous_steps, provide a more helpful message
      if e.message.include?('previous_steps') && !@context.key?(:previous_steps)
        Rails.logger.error "The 'previous_steps' hash is not available in this context. Make sure this step is not the first step."
      end
      false
    end
  end
end
