class UserRole < ApplicationRecord
  belongs_to :user
  belongs_to :role
  belongs_to :project, optional: true

  validates :user_id, uniqueness: { scope: :project_id, message: "can have only one role per project" }, if: -> { project_id.present? }
  validates :user_id, uniqueness: { scope: :role_id, message: "already has this global role" }, if: -> { project_id.nil? }

  before_validation :auto_assign_organization_project_for_global_roles

  validate :validate_role_project_consistency
  validate :validate_role_user_consistency
  validate :only_one_default_per_user, if: :is_default?
  validate :only_one_global_role_per_user

  scope :by_project, ->(project) { where(project: project) }
  scope :default, -> { where(is_default: true) }

  # Set this user role as the default for the user
  def set_as_default!
    # First, unset any existing default for this user
    UserRole.where(user_id: user_id, is_default: true).where.not(id: id).update_all(is_default: false)
    # Then set this one as default
    update(is_default: true)
  end

  def to_rpc_response
    ::Core::UserRoleResponse.new(
      id: id,
      role: role.to_rpc_response,
      project: project&.to_rpc_response,
      is_default: is_default
    )
  end

  private

  def only_one_default_per_user
    # The database constraint will enforce this, but we add a validation for better error messages
    if new_record? && UserRole.where(user_id: user_id, is_default: true).exists?
      errors.add(:is_default, "User can only have one default project role")
    end
  end

  def validate_role_project_consistency
    # After automatic assignment in before_validation, validate that project is present
    if project.blank?
      if role&.global_role?
        errors.add(:project, "organization project could not be assigned for global role")
      else
        errors.add(:project, "must be specified for project-based roles")
      end
    end
  end

  private

  def auto_assign_organization_project_for_global_roles
    # Automatically assign organization project for global roles if no project is specified
    if project.blank? && role&.global_role?
      assign_organization_project
    end
  end

  def assign_organization_project
    org_project = Project.find_by(name: AtharAuth.organization_project_name)
    if org_project
      self.project = org_project
      Rails.logger.info "Auto-assigned organization project '#{org_project.name}' (ID: #{org_project.id}) to global role '#{role.name}'"
    else
      Rails.logger.error "Organization project '#{AtharAuth.organization_project_name}' not found for auto-assignment"
    end
  end

  def validate_role_user_consistency
    # Skip validation for new users - let the roles determine the user type
    return if user&.new_record?

    if role&.global_role? && !user&.global_user?
      errors.add(:base, "Global roles can only be assigned to global users")
    elsif role&.project_based? && user&.global_user?
      errors.add(:base, "Project roles cannot be assigned to global users")
    end
  end

  def only_one_global_role_per_user
    return unless role&.global_role?
    return if user&.new_record? # Allow during creation - will be validated after user is saved

    existing_global_roles = user.user_roles.joins(:role)
                               .where(roles: { scope: true }) # global roles have scope: true
                               .where.not(id: id)

    if existing_global_roles.exists?
      existing_role_names = existing_global_roles.joins(:role).pluck('roles.name')
      errors.add(:base, "User can only have one global role. Already has: #{existing_role_names.join(', ')}")
    end
  end
end
