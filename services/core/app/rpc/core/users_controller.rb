module Core
  class UsersController < AtharRpc::Grpc::BaseController
    bind ::Core::Users::Service

    # GET USER
    def get_user
      get_resource(User, request.message,
                   transformer: :to_rpc_response,
                   includes: [ :avatar_attachment, user_roles: [ :role, :project ] ]
      )
    end

    # LIST USERS
    def list_users
      puts "🚀🚀🚀 [CORE DEBUG] list_users method called - LATEST VERSION!"

      # Simple debug that should definitely appear
      STDOUT.flush
      $stdout.puts "🔥🔥🔥 [CORE] SIMPLE DEBUG - METHOD CALLED"
      $stdout.flush

      puts "🔥 [CORE DEBUG] Full request JSON: #{request.message.to_json}"
      Rails.logger.info("[gRPC DEBUG] Full request JSON: #{request.message.to_json}")

      puts "🔥 [CORE SERVICE] list_users called"
      puts "🔥 [CORE SERVICE] Request IDs: #{request.message.ids.to_a.inspect}"
      puts "🔥 [CORE SERVICE] Request scope: #{request.message.scope.to_h.inspect}"
      puts "🔥 [CORE SERVICE] Request where: #{request.message.where.to_h.inspect}"

      result = list_resources(User, request.message,
                     response_class: ::Core::UserListResponse,
                     transformer: :to_rpc_response,
                     includes: [ :avatar_attachment, user_roles: [ :role, :project ] ]
      )

      puts "🔥 [CORE SERVICE] list_resources returned result: #{result.class}"
      if result.respond_to?(:users)
        puts "🔥 [CORE SERVICE] result.users.size: #{result.users.size}"
        puts "🔥 [CORE SERVICE] First user: #{result.users.first&.id}"
      else
        puts "🔥 [CORE SERVICE] result structure: #{result.inspect}"
      end
      Rails.logger.info("[gRPC DEBUG] list_users returning result: #{result.class}")
      result
    end

    # UPDATE USER
    def update_user
      update_resource(User, request.message,
                      param_names: [ :name, :email, :status, :password, :avatar_attributes, :user_roles_list ],
                      param_extractor: method(:extract_user_params),
                      transformer: :to_rpc_response
      )
    end

    # CREATE USER
    def create_user
      create_resource(User, request.message,
                      param_names: [ :name, :email, :status, :password, :avatar_attributes, :user_roles_list ],
                      required_params: [ :name, :email, :password ],
                      param_extractor: method(:extract_user_params),
                      transformer: :to_rpc_response
      )
    end

    private

    # Extract user parameters from request
    def extract_user_params(request_payload)
      params = extract_params(request_payload,
                              :name, :email, :status, :password, :avatar_attributes, :user_roles_list
      )

      params
    end
  end
end
