#!/bin/bash -e

# Enable jemalloc for reduced memory usage and latency
if [ -z "${LD_PRELOAD+x}" ]; then
    LD_PRELOAD=$(find /usr/lib -name libjemalloc.so.2 -print -quit)
    export LD_PRELOAD
fi

# Run database migrations before starting Rails on development env
if [[ "$RAILS_ENV" == "development" ]]; then
    bin/bundle install
    echo "Running database migrations..."
    rm -f /rails/tmp/pids/server.pid
    bin/rails db:prepare
fi

# Check if the first argument is "debug"
if [[ "$1" == "debug" ]]; then
    echo "Starting Rails in debug mode..."
    exec bundle exec rdebug-ide --host 0.0.0.0 --port 1234 --dispatcher-port 26162 -- bin/rails server -b 0.0.0.0
fi

if [[ "$1" == "grpc" ]]; then
    echo "Starting Gruf server..."
    exec bundle exec gruf
fi

echo "Running the command... $*"
exec "${@}"
