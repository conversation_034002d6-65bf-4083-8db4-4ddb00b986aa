source "https://rubygems.org"

# Bundle edge Rails instead: gem "rails", github: "rails/rails", branch: "main"
gem "rails", "~> 8.0.2"
# Use postgresql as the database for Active Record
gem "pg", "~> 1.1"
# Use the Puma web server [https://github.com/puma/puma]
gem "puma", ">= 5.0"
# Build JSON APIs with ease [https://github.com/rails/jbuilder]
# gem "jbuilder"

# Use Active Model has_secure_password [https://guides.rubyonrails.org/active_model_basics.html#securepassword]
# gem "bcrypt", "~> 3.1.7"

# Windows does not include zoneinfo files, so bundle the tzinfo-data gem
gem "tzinfo-data", platforms: %i[ mingw mswin x64_mingw jruby ]

# Use the database-backed adapters for Rails.cache, Active Job, and Action Cable
gem "solid_cache"
gem "solid_queue"
gem "solid_cable"

# Reduces boot times through caching; required in config/boot.rb
gem "bootsnap", require: false

# Add HTTP asset caching/compression and X-Sendfile acceleration to Puma [https://github.com/basecamp/thruster/]
gem "thruster", require: false

# Use Active Storage variants [https://guides.rubyonrails.org/active_storage_overview.html#transforming-images]
# gem "image_processing", "~> 1.2"

# Use Rack CORS for handling Cross-Origin Resource Sharing (CORS), making cross-origin Ajax possible
gem "rack-cors"

# Authentication
gem "devise", "~> 4.9"
# gem "devise-jwt", "~> 0.12.1"
gem "jwt"

# APIs
gem "jsonapi-serializer", "~> 2.2"
gem "oj" # for faster serialization
gem "apipie-rails", github: "shqear93/apipie-rails"
gem "commonmarker"


group :development, :test do
  # See https://guides.rubyonrails.org/debugging_rails_applications.html#debugging-with-the-debug-gem
  gem "debug", platforms: %i[ mri mingw mswin x64_mingw ], require: "debug/prelude"

  # Static analysis for security vulnerabilities [https://brakemanscanner.org/]
  gem "brakeman", require: false

  # Omakase Ruby styling [https://github.com/rails/rubocop-rails-omakase/]
  gem "rubocop-rails-omakase", require: false

  # RSpec testing framework
  gem "rspec-rails", "~> 6.1.0"
  gem "factory_bot_rails", "~> 6.4.0"
  gem "faker", "~> 3.2.2"
  gem "shoulda-matchers", "~> 6.0.0"
  gem "database_cleaner-active_record", "~> 2.1.0"
  gem "simplecov", "~> 0.22.0", require: false
end

group :test do
  gem "webmock", "~> 3.19.1"
  gem "rails-controller-testing", "~> 1.0.5"
end

group :development do
  gem "ruby-lsp-rspec"
end

# Authorisation
# gem "cancancan", "~> 3.6"

source "https://gem.fury.io/athar11/" do
  gem "athar_auth", path: "/Users/<USER>/workspace/athar/athar-ems/gems/auth-gem"
  # gem "athar_auth", "2.0.2"

  gem "athar_rpc", path: "/Users/<USER>/workspace/athar/athar-ems/gems/rpc-gem"
  # gem "athar_rpc", "0.4.2"

  gem "athar_commons", path: "/Users/<USER>/workspace/athar/athar-ems/gems/commons-gem"
  # gem "athar_commons", "0.3.5"
end

# Core Extensions
gem "require_all"

# ActiveStorage
gem "aws-sdk-s3", require: false
gem "image_processing", "~> 1.14"
