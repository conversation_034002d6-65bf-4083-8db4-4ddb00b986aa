# frozen_string_literal: true

puts "🌱 Creating Sample Cases for Development..."

# Sample beneficiary data for cases
sample_beneficiaries = [
  {
    name: "<PERSON>",
    age: 12,
    gender: "male",
    nationality: "Syrian",
    phone: "+962790123456",
    id_number: "SYR123456789",
    date_of_birth: Date.new(2012, 3, 15),
    city: "Amman"
  },
  {
    name: "<PERSON><PERSON> No<PERSON>Din",
    age: 8,
    gender: "female",
    nationality: "Palestinian",
    phone: "+962791234567",
    id_number: "PAL987654321",
    date_of_birth: Date.new(2016, 7, 22),
    city: "Zarqa"
  },
  {
    name: "<PERSON>",
    age: 15,
    gender: "male",
    nationality: "Iraqi",
    phone: "+962792345678",
    id_number: "IRQ456789123",
    date_of_birth: Date.new(2009, 11, 8),
    city: "Irbid"
  },
  {
    name: "<PERSON>",
    age: 6,
    gender: "female",
    nationality: "Yemeni",
    phone: "+962793456789",
    id_number: "YEM789123456",
    date_of_birth: Date.new(2018, 1, 30),
    city: "Amman"
  },
  {
    name: "<PERSON> <PERSON> <PERSON>-<PERSON>",
    age: 17,
    gender: "male",
    nationality: "Sudanese",
    phone: "+962794567890",
    id_number: "SUD321654987",
    date_of_birth: Date.new(2007, 9, 12),
    city: "Mafraq"
  }
]

# Create sample cases with different statuses and types
sample_beneficiaries.each_with_index do |beneficiary_data, index|
  case_data = {
    case_number: "CM-2025-#{(index + 1).to_s.rjust(4, '0')}",
    case_type: [ :protection, :education, :health, :livelihood ].sample,
    status: [ :case_draft, :case_active, :case_pending_approval, :case_approved, :case_closed ].sample,
    priority_level: [ :low, :medium, :high, :urgent, :critical ].sample,
    confidentiality_level: [ :public_access, :restricted, :confidential ].sample,
    assigned_user_id: (index + 1).to_s,
    project_id: 24, # Default project
    started_at: rand(30..180).days.ago,
    created_by_id: "550e8400-e29b-41d4-a716-44665544000#{index + 1}",
    notes: "Case for #{beneficiary_data[:name]} - #{beneficiary_data[:age]} year old #{beneficiary_data[:gender]} from #{beneficiary_data[:nationality]}",

    # Beneficiary cache data
    beneficiary_name: beneficiary_data[:name],
    beneficiary_age: beneficiary_data[:age],
    beneficiary_gender: beneficiary_data[:gender],
    beneficiary_nationality: beneficiary_data[:nationality],
    beneficiary_phone: beneficiary_data[:phone],
    beneficiary_id_number: beneficiary_data[:id_number],
    beneficiary_date_of_birth: beneficiary_data[:date_of_birth],
    beneficiary_city: beneficiary_data[:city],
    beneficiary_cache: {
      "name" => beneficiary_data[:name],
      "age" => beneficiary_data[:age],
      "gender" => beneficiary_data[:gender],
      "nationality" => beneficiary_data[:nationality],
      "phone" => beneficiary_data[:phone],
      "id_number" => beneficiary_data[:id_number],
      "date_of_birth" => beneficiary_data[:date_of_birth].to_s,
      "city" => beneficiary_data[:city]
    }
  }

  # Set closed_at for closed cases
  if case_data[:status] == :case_closed
    case_data[:closed_at] = rand(1..30).days.ago
  end

  # Check if case already exists
  case_record = Case.find_by(case_number: case_data[:case_number])

  unless case_record
    # Create case record directly without associations and callbacks
    case_record = Case.new(case_data)

    # Skip problematic callbacks during seeding
    case_record.define_singleton_method(:update_beneficiary_cache) { true }
    case_record.define_singleton_method(:create_initial_form_submissions) { true }
    case_record.define_singleton_method(:generate_case_number) { self.case_number ||= case_data[:case_number] }

    case_record.save!(validate: false) # Skip validations that require User associations
  end

  puts "✅ Created case: #{case_record.case_number} for #{beneficiary_data[:name]}"
end

puts "🌱 Sample Cases creation completed!"
