# frozen_string_literal: true

puts "🌱 Creating Sample Form Submissions..."

# Get form templates
consent_template = FormTemplate.find_by(name: 'consent_assent')
registration_template = FormTemplate.find_by(name: 'registration_rapid_assessment')
assessment_template = FormTemplate.find_by(name: 'comprehensive_assessment')

return unless consent_template && registration_template && assessment_template

# Get sample cases
sample_cases = Case.limit(5)

sample_cases.each do |case_record|
  puts "Creating form submissions for case: #{case_record.case_number}"
  
  # Create consent form submission
  consent_data = {
    "consent_given" => true,
    "consent_date" => case_record.started_at&.to_date&.to_s || Date.current.to_s,
    "consent_given_by" => ["beneficiary", "parent_guardian", "legal_representative"].sample,
    "assent_applicable" => (case_record.beneficiary_age || 18) < 18,
    "assent_given" => (case_record.beneficiary_age || 18) < 18 ? [true, false].sample : false,
    "interpreter_used" => [true, false].sample,
    "interpreter_language" => ["Arabic", "English", "Kurdish", "French"].sample
  }
  
  consent_submission = FormSubmission.find_by(case_id: case_record.id, form_template: consent_template)
  unless consent_submission
    consent_submission = FormSubmission.new(
      case_id: case_record.id,
      form_template: consent_template,
      form_data: consent_data,
      created_by_id: case_record.assigned_user_id,
      submitted_at: case_record.started_at || Time.current,
      status: :form_submitted
    )
    consent_submission.save!(validate: false)
  end
  
  # Create registration form submission
  registration_data = {
    "child_personal_details" => {
      "first_name" => case_record.beneficiary_name&.split(' ')&.first || "Unknown",
      "last_name" => case_record.beneficiary_name&.split(' ')&.last || "Unknown",
      "age" => case_record.beneficiary_age || 18,
      "sex" => case_record.beneficiary_gender,
      "nationality_status" => case_record.beneficiary_nationality,
      "identification_number" => case_record.beneficiary_id_number,
      "date_of_birth" => case_record.beneficiary_date_of_birth&.to_s
    },
    "care_arrangements" => {
      "contact_phone" => case_record.beneficiary_phone,
      "current_address" => case_record.beneficiary_city,
      "caregiver_name" => "#{case_record.beneficiary_name} Guardian",
      "relationship_to_child" => ["parent", "guardian", "relative", "foster_parent"].sample
    },
    "immediate_needs" => {
      "shelter" => [true, false].sample,
      "food" => [true, false].sample,
      "medical_care" => [true, false].sample,
      "education" => (case_record.beneficiary_age || 18) >= 6 && (case_record.beneficiary_age || 18) <= 17,
      "protection" => [true, false].sample
    }
  }
  
  registration_submission = FormSubmission.find_by(case_id: case_record.id, form_template: registration_template)
  unless registration_submission
    registration_submission = FormSubmission.new(
      case_id: case_record.id,
      form_template: registration_template,
      form_data: registration_data,
      created_by_id: case_record.assigned_user_id,
      submitted_at: (case_record.started_at || Time.current) + 1.day,
      status: :form_submitted
    )
    registration_submission.save!(validate: false)
  end
  
  # Create assessment form submission for some cases
  if ['case_active', 'case_approved', 'case_closed'].include?(case_record.status)
    assessment_data = {
      "risk_level" => ["low", "medium", "high", "critical"].sample,
      "specific_vulnerabilities" => [
        "unaccompanied_minor",
        "separated_child", 
        "child_with_disability",
        "survivor_of_trafficking"
      ].sample(rand(1..2)),
      "protection_incidents" => [true, false].sample,
      "accommodation_type" => ["family_home", "shelter", "camp", "informal_settlement"].sample,
      "accommodation_adequate" => [true, false].sample,
      "food_security" => ["secure", "moderately_insecure", "severely_insecure"].sample,
      "healthcare_access" => ["full_access", "limited_access", "emergency_only", "no_access"].sample,
      "education_access" => (case_record.beneficiary_age || 18) >= 6 ?
        ["enrolled_in_school", "not_enrolled_barriers", "not_enrolled_choice"].sample :
        "not_applicable",
      "income_source" => ["family_support", "ngo_support", "government_assistance", "no_income"].sample
    }
    
    assessment_submission = FormSubmission.find_by(case_id: case_record.id, form_template: assessment_template)
    unless assessment_submission
      assessment_submission = FormSubmission.new(
        case_id: case_record.id,
        form_template: assessment_template,
        form_data: assessment_data,
        created_by_id: case_record.assigned_user_id,
        submitted_at: (case_record.started_at || Time.current) + 7.days,
        status: :form_submitted
      )
      assessment_submission.save!(validate: false)
    end
  end
  
  puts "✅ Created form submissions for case: #{case_record.case_number}"
end

puts "🌱 Sample Form Submissions creation completed!"
