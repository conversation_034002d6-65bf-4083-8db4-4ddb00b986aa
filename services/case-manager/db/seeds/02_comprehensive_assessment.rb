# Comprehensive Assessment Form Template
# Detailed needs assessment for case planning

puts "🌱 Creating Comprehensive Assessment Form Template..."

# Project 1 (assuming it exists from existing seeds)
project_id = 24

# 3. Comprehensive Assessment Form Template
assessment_template = FormTemplate.find_or_create_by!(
  name: 'comprehensive_assessment',
  project_id: project_id
) do |template|
  template.title = 'Comprehensive Assessment'
  template.description = 'Detailed assessment of needs, risks, and protection concerns'

  template.active = true
  template.sequence_order = 3
  template.prerequisite_forms = ['consent_assent', 'registration_rapid_assessment']
  template.target_role = 'both'
end

# Protection Concerns Section
protection_section = FormSection.find_or_create_by!(
  form_template: assessment_template,
  name: 'protection_concerns'
) do |section|
  section.title = 'Protection Concerns'
  section.description = 'Assessment of protection risks and safety concerns'
  section.display_order = 1
  section.is_required = true
end

# Protection Fields
protection_fields = [
  {
    name: 'immediate_safety_concerns',
    label: 'Immediate Safety Concerns',
    field_type: 'textarea',
    is_required: true,
    help_text: 'Describe any immediate safety or protection concerns'
  },
  {
    name: 'risk_level',
    label: 'Overall Risk Level',
    field_type: 'select',
    is_required: true,
    help_text: 'Assessment of overall risk level for the beneficiary'
  },
  {
    name: 'specific_vulnerabilities',
    label: 'Specific Vulnerabilities',
    field_type: 'checkbox',
    is_required: false,
    help_text: 'Select all applicable vulnerabilities'
  },
  {
    name: 'protection_incidents',
    label: 'Previous Protection Incidents',
    field_type: 'textarea',
    is_required: false,
    help_text: 'Description of any previous protection incidents'
  }
]

protection_fields.each_with_index do |field_data, index|
  field = FormField.find_or_create_by!(
    form_section: protection_section,
    field_name: field_data[:name]
  ) do |field|
    field.label = field_data[:label]
    field.field_type = field_data[:field_type]
    field.data_type = field_data[:field_type] == 'checkbox' ? 'boolean' :
                      field_data[:field_type] == 'date' ? 'date' :
                      field_data[:field_type] == 'datetime' ? 'datetime' :
                      field_data[:field_type] == 'number' ? 'integer' : 'string'
    field.required = field_data[:is_required]
    field.display_order = index + 1
    field.help_text = field_data[:help_text]
  end

  # Add options for risk level
  if field_data[:name] == 'risk_level'
    options = ['Low', 'Medium', 'High', 'Critical']
    options.each_with_index do |option, opt_index|
      FormFieldOption.find_or_create_by!(
        form_field: field,
        option_key: option.downcase,
        option_value: option,
        display_order: opt_index + 1
      )
    end
  end

  # Add options for vulnerabilities
  if field_data[:name] == 'specific_vulnerabilities'
    vulnerabilities = [
      'Unaccompanied Minor',
      'Separated Child',
      'Single Parent/Caregiver',
      'Elderly Person',
      'Person with Disability',
      'Serious Medical Condition',
      'Survivor of Violence',
      'Survivor of Trafficking',
      'LGBTI Individual',
      'Other'
    ]
    vulnerabilities.each_with_index do |vuln, opt_index|
      FormFieldOption.find_or_create_by!(
        form_field: field,
        option_key: vuln.downcase.gsub(/[^a-z0-9]/, '_'),
        option_value: vuln,
        display_order: opt_index + 1
      )
    end
  end
end

# Basic Needs Section
needs_section = FormSection.find_or_create_by!(
  form_template: assessment_template,
  name: 'basic_needs'
) do |section|
  section.title = 'Basic Needs Assessment'
  section.description = 'Assessment of basic needs and living conditions'
  section.display_order = 2
  section.is_required = true
end

# Basic Needs Fields
needs_fields = [
  {
    name: 'accommodation_type',
    label: 'Current Accommodation',
    field_type: 'select',
    is_required: true,
    help_text: 'Type of current accommodation'
  },
  {
    name: 'accommodation_adequate',
    label: 'Is Accommodation Adequate?',
    field_type: 'checkbox',
    is_required: true,
    help_text: 'Is the current accommodation safe and adequate?'
  },
  {
    name: 'food_security',
    label: 'Food Security Level',
    field_type: 'select',
    is_required: true,
    help_text: 'Level of food security'
  },
  {
    name: 'healthcare_access',
    label: 'Access to Healthcare',
    field_type: 'select',
    is_required: true,
    help_text: 'Level of access to healthcare services'
  },
  {
    name: 'education_access',
    label: 'Access to Education',
    field_type: 'select',
    is_required: false,
    help_text: 'Access to education (for children and youth)'
  },
  {
    name: 'income_source',
    label: 'Source of Income',
    field_type: 'checkbox',
    is_required: false,
    help_text: 'Current sources of income or support'
  }
]

needs_fields.each_with_index do |field_data, index|
  field = FormField.find_or_create_by!(
    form_section: needs_section,
    field_name: field_data[:name]
  ) do |field|
    field.label = field_data[:label]
    field.field_type = field_data[:field_type]
    field.data_type = field_data[:field_type] == 'checkbox' ? 'boolean' :
                      field_data[:field_type] == 'date' ? 'date' :
                      field_data[:field_type] == 'datetime' ? 'datetime' :
                      field_data[:field_type] == 'number' ? 'integer' : 'string'
    field.required = field_data[:is_required]
    field.display_order = index + 1
    field.help_text = field_data[:help_text]
  end

  # Add options for various select fields
  case field_data[:name]
  when 'accommodation_type'
    options = ['Refugee Camp', 'Urban Housing', 'Host Family', 'Temporary Shelter', 'Street/Homeless', 'Other']
  when 'food_security'
    options = ['Secure', 'Moderately Secure', 'Insecure', 'Severely Insecure']
  when 'healthcare_access'
    options = ['Full Access', 'Limited Access', 'Emergency Only', 'No Access']
  when 'education_access'
    options = ['Enrolled in School', 'Not Enrolled - Barriers', 'Not Enrolled - Choice', 'Not Applicable']
  when 'income_source'
    options = ['Employment', 'Government Assistance', 'NGO Support', 'Family Support', 'Informal Work', 'No Income']
  end

  if options
    options.each_with_index do |option, opt_index|
      FormFieldOption.find_or_create_by!(
        form_field: field,
        option_key: option.downcase.gsub(/[^a-z0-9]/, '_'),
        option_value: option,
        display_order: opt_index + 1
      )
    end
  end
end

puts "✅ Created Comprehensive Assessment Form Template"

puts "🌱 Comprehensive Assessment creation completed!"
