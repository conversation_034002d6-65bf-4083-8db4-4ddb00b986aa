# Service Planning Form Template
# Action planning and service coordination

puts "🌱 Creating Service Planning Form Template..."

# Project 1 (assuming it exists from existing seeds)
project_id = 24

# 4. Service Planning Form Template
service_planning_template = FormTemplate.find_or_create_by!(
  name: 'service_planning',
  project_id: project_id
) do |template|
  template.title = 'Service Planning'
  template.description = 'Development and tracking of service goals and interventions'

  template.active = true
  template.sequence_order = 4
  template.prerequisite_forms = ['consent_assent', 'registration_rapid_assessment', 'comprehensive_assessment']
  template.target_role = 'both'
end

# Goals and Objectives Section
goals_section = FormSection.find_or_create_by!(
  form_template: service_planning_template,
  name: 'goals_objectives'
) do |section|
  section.title = 'Goals and Objectives'
  section.description = 'Primary goals and specific objectives for case management'
  section.display_order = 1
  section.is_required = true
end

# Goals Fields
goals_fields = [
  {
    name: 'primary_goal',
    label: 'Primary Case Goal',
    field_type: 'select',
    is_required: true,
    help_text: 'Main goal for case management intervention'
  },
  {
    name: 'goal_description',
    label: 'Goal Description',
    field_type: 'textarea',
    is_required: true,
    help_text: 'Detailed description of the primary goal'
  },
  {
    name: 'target_completion_date',
    label: 'Target Completion Date',
    field_type: 'date',
    is_required: true,
    help_text: 'Expected date for goal completion'
  },
  {
    name: 'success_indicators',
    label: 'Success Indicators',
    field_type: 'textarea',
    is_required: true,
    help_text: 'How will success be measured?'
  }
]

goals_fields.each_with_index do |field_data, index|
  field = FormField.find_or_create_by!(
    form_section: goals_section,
    field_name: field_data[:name]
  ) do |field|
    field.label = field_data[:label]
    field.field_type = field_data[:field_type]
    field.data_type = field_data[:field_type] == 'checkbox' ? 'boolean' :
                      field_data[:field_type] == 'date' ? 'date' :
                      field_data[:field_type] == 'datetime' ? 'datetime' :
                      field_data[:field_type] == 'number' ? 'integer' : 'string'
    field.required = field_data[:is_required]
    field.display_order = index + 1
    field.help_text = field_data[:help_text]
  end

  # Add options for primary goal
  if field_data[:name] == 'primary_goal'
    goals = [
      'Family Reunification',
      'Durable Solution',
      'Protection from Harm',
      'Access to Services',
      'Legal Documentation',
      'Education Access',
      'Healthcare Access',
      'Livelihood Support',
      'Psychosocial Support',
      'Other'
    ]
    goals.each_with_index do |goal, opt_index|
      FormFieldOption.find_or_create_by!(
        form_field: field,
        option_key: goal.downcase.gsub(/[^a-z0-9]/, '_'),
        option_value: goal,
        display_order: opt_index + 1
      )
    end
  end
end

# Services and Interventions Section
services_section = FormSection.find_or_create_by!(
  form_template: service_planning_template,
  name: 'services_interventions'
) do |section|
  section.title = 'Services and Interventions'
  section.description = 'Planned services and interventions to achieve goals'
  section.display_order = 2
  section.is_required = true
end

# Services Fields
services_fields = [
  {
    name: 'planned_services',
    label: 'Planned Services',
    field_type: 'checkbox',
    is_required: true,
    help_text: 'Select all services planned for this case',
    lookup_source_type: 'ServiceCategory'
  },
  {
    name: 'service_providers',
    label: 'Service Providers',
    field_type: 'checkbox',
    is_required: true,
    help_text: 'Select organizations providing services',
    lookup_source_type: 'PartnerAgency'
  },
  {
    name: 'referrals_made',
    label: 'Referrals Made',
    field_type: 'textarea',
    is_required: false,
    help_text: 'Details of referrals made to other services'
  },
  {
    name: 'follow_up_schedule',
    label: 'Follow-up Schedule',
    field_type: 'select',
    is_required: true,
    help_text: 'Frequency of case follow-up'
  }
]

services_fields.each_with_index do |field_data, index|
  field = FormField.find_or_create_by!(
    form_section: services_section,
    field_name: field_data[:name]
  ) do |field|
    field.label = field_data[:label]
    field.field_type = field_data[:field_type]
    field.data_type = field_data[:field_type] == 'checkbox' ? 'boolean' :
                      field_data[:field_type] == 'date' ? 'date' :
                      field_data[:field_type] == 'datetime' ? 'datetime' :
                      field_data[:field_type] == 'number' ? 'integer' : 'string'
    field.required = field_data[:is_required]
    field.display_order = index + 1
    field.help_text = field_data[:help_text]
  end

  # Lookup-based fields don't need hardcoded options
  # planned_services and service_providers will use lookup data from ServiceCategory and PartnerAgency models

  # Add options for follow-up schedule
  if field_data[:name] == 'follow_up_schedule'
    schedules = ['Weekly', 'Bi-weekly', 'Monthly', 'Quarterly', 'As Needed']
    schedules.each_with_index do |schedule, opt_index|
      FormFieldOption.find_or_create_by!(
        form_field: field,
        option_key: schedule.downcase.gsub(/[^a-z0-9]/, '_'),
        option_value: schedule,
        display_order: opt_index + 1
      )
    end
  end
end

# Case Closure Section
closure_section = FormSection.find_or_create_by!(
  form_template: service_planning_template,
  name: 'case_closure'
) do |section|
  section.title = 'Case Closure Planning'
  section.description = 'Planning for case closure and transition'
  section.display_order = 3
  section.is_required = false
end

# Closure Fields
closure_fields = [
  {
    name: 'closure_criteria',
    label: 'Closure Criteria',
    field_type: 'textarea',
    is_required: false,
    help_text: 'Criteria that must be met for case closure'
  },
  {
    name: 'transition_plan',
    label: 'Transition Plan',
    field_type: 'textarea',
    is_required: false,
    help_text: 'Plan for transitioning services or support'
  },
  {
    name: 'follow_up_after_closure',
    label: 'Follow-up After Closure',
    field_type: 'checkbox',
    is_required: false,
    help_text: 'Will follow-up be conducted after case closure?'
  }
]

closure_fields.each_with_index do |field_data, index|
  FormField.find_or_create_by!(
    form_section: closure_section,
    field_name: field_data[:name]
  ) do |field|
    field.label = field_data[:label]
    field.field_type = field_data[:field_type]
    field.data_type = field_data[:field_type] == 'checkbox' ? 'boolean' :
                      field_data[:field_type] == 'date' ? 'date' :
                      field_data[:field_type] == 'datetime' ? 'datetime' :
                      field_data[:field_type] == 'number' ? 'integer' : 'string'
    field.required = field_data[:is_required]
    field.display_order = index + 1
    field.help_text = field_data[:help_text]
  end
end

puts "✅ Created Service Planning Form Template"

puts "🌱 Service Planning creation completed!"
