# Field Type Definitions Seeds
# Comprehensive field types for the form builder system

puts "🌱 Creating Field Type Definitions..."

class FieldTypeSeeder
  FIELD_TYPES = [
    {
      name: 'text',
      input_type: 'text',
      data_type: 'string',
      description: 'Single line text input',
      validation_schema: {
        supported_validations: ['required', 'min_length', 'max_length', 'pattern'],
        default_rules: { max_length: 255 }
      }.to_json,
      default_config: {
        placeholder: 'Enter text',
        max_length: 255,
        autocomplete: false
      }.to_json
    },
    {
      name: 'textarea',
      input_type: 'textarea',
      data_type: 'string',
      description: 'Multi-line text input',
      validation_schema: {
        supported_validations: ['required', 'min_length', 'max_length'],
        default_rules: { max_length: 2000 }
      }.to_json,
      default_config: {
        placeholder: 'Enter detailed text',
        rows: 4,
        max_length: 2000
      }.to_json
    },
    {
      name: 'number',
      input_type: 'number',
      data_type: 'integer',
      description: 'Numeric input for integers',
      validation_schema: {
        supported_validations: ['required', 'min_value', 'max_value'],
        default_rules: { min: 0, max: 999999 }
      }.to_json,
      default_config: {
        min: 0,
        max: 999999,
        step: 1
      }.to_json
    },
    {
      name: 'decimal',
      input_type: 'number',
      data_type: 'decimal',
      description: 'Numeric input for decimal numbers',
      validation_schema: {
        supported_validations: ['required', 'min_value', 'max_value'],
        default_rules: { min: 0, max: 999999.99 }
      }.to_json,
      default_config: {
        min: 0,
        max: 999999.99,
        step: 0.01,
        decimal_places: 2
      }.to_json
    },
    {
      name: 'date',
      input_type: 'date',
      data_type: 'date',
      description: 'Date picker input',
      validation_schema: {
        supported_validations: ['required', 'date_range'],
        default_rules: { min_date: '1900-01-01', max_date: 'today' }
      }.to_json,
      default_config: {
        format: 'YYYY-MM-DD',
        min_date: '1900-01-01',
        max_date: 'today'
      }.to_json
    },
    {
      name: 'datetime',
      input_type: 'datetime',
      data_type: 'datetime',
      description: 'Date and time picker',
      validation_schema: {
        supported_validations: ['required', 'date_range'],
        default_rules: {}
      }.to_json,
      default_config: {
        format: 'YYYY-MM-DD HH:mm',
        include_seconds: false
      }.to_json
    },
    {
      name: 'select_box',
      input_type: 'select',
      data_type: 'string',
      description: 'Dropdown selection',
      validation_schema: {
        supported_validations: ['required'],
        default_rules: {}
      }.to_json,
      default_config: {
        searchable: true,
        clearable: true,
        multiple: false
      }.to_json
    },
    {
      name: 'multi_select',
      input_type: 'select',
      data_type: 'array',
      description: 'Multiple selection dropdown',
      validation_schema: {
        supported_validations: ['required', 'min_selections', 'max_selections'],
        default_rules: { max_selections: 10 }
      }.to_json,
      default_config: {
        searchable: true,
        clearable: true,
        multiple: true,
        max_selections: 10
      }.to_json
    },
    {
      name: 'radio_button',
      input_type: 'radio',
      data_type: 'string',
      description: 'Radio button selection',
      validation_schema: {
        supported_validations: ['required'],
        default_rules: {}
      }.to_json,
      default_config: {
        layout: 'vertical'
      }.to_json
    },
    {
      name: 'checkbox',
      input_type: 'checkbox',
      data_type: 'boolean',
      description: 'Single checkbox',
      validation_schema: {
        supported_validations: ['required'],
        default_rules: {}
      }.to_json,
      default_config: {
        default_value: false
      }.to_json
    },
    {
      name: 'yes_no',
      input_type: 'radio',
      data_type: 'boolean',
      description: 'Yes/No radio buttons',
      validation_schema: {
        supported_validations: ['required'],
        default_rules: {}
      }.to_json,
      default_config: {
        options: [
          { key: 'yes', value: true, label: 'Yes' },
          { key: 'no', value: false, label: 'No' }
        ]
      }.to_json
    },
    {
      name: 'phone',
      input_type: 'text',
      data_type: 'string',
      description: 'Phone number input with validation',
      validation_schema: {
        supported_validations: ['required', 'pattern'],
        default_rules: { pattern: '^\\+?[1-9]\\d{1,14}$' }
      }.to_json,
      default_config: {
        pattern: '^\\+?[1-9]\\d{1,14}$',
        placeholder: '+962 7X XXX XXXX',
        format_mask: true
      }.to_json
    },
    {
      name: 'email',
      input_type: 'email',
      data_type: 'string',
      description: 'Email address input',
      validation_schema: {
        supported_validations: ['required', 'email'],
        default_rules: { pattern: '^[^@]+@[^@]+\\.[^@]+$' }
      }.to_json,
      default_config: {
        pattern: '^[^@]+@[^@]+\\.[^@]+$',
        placeholder: '<EMAIL>'
      }.to_json
    },
    {
      name: 'file_upload',
      input_type: 'file',
      data_type: 'string',
      description: 'File upload input',
      validation_schema: {
        supported_validations: ['required', 'file_size', 'file_type'],
        default_rules: { max_file_size: '10MB', allowed_types: ['pdf', 'doc', 'docx', 'jpg', 'png'] }
      }.to_json,
      default_config: {
        max_file_size: '10MB',
        allowed_types: ['pdf', 'doc', 'docx', 'jpg', 'png'],
        multiple: false
      }.to_json
    },
    {
      name: 'lookup_single',
      input_type: 'lookup_single',
      data_type: 'string',
      description: 'Single selection from lookup table',
      validation_schema: {
        supported_validations: ['required'],
        default_rules: {}
      }.to_json,
      default_config: {
        searchable: true,
        clearable: true,
        project_scoped: true
      }.to_json
    },
    {
      name: 'lookup_multiple',
      input_type: 'lookup_multiple',
      data_type: 'array',
      description: 'Multiple selection from lookup table',
      validation_schema: {
        supported_validations: ['required', 'min_selections', 'max_selections'],
        default_rules: { max_selections: 10 }
      }.to_json,
      default_config: {
        searchable: true,
        clearable: true,
        project_scoped: true,
        max_selections: 10
      }.to_json
    },
    {
      name: 'calculated',
      input_type: 'calculated',
      data_type: 'string',
      description: 'Auto-calculated field based on other fields',
      validation_schema: {
        supported_validations: [],
        default_rules: {}
      }.to_json,
      default_config: {
        readonly: true,
        auto_calculate: true
      }.to_json
    },
    {
      name: 'currency',
      input_type: 'number',
      data_type: 'decimal',
      description: 'Currency input with formatting',
      validation_schema: {
        supported_validations: ['required', 'min_value', 'max_value'],
        default_rules: { min: 0 }
      }.to_json,
      default_config: {
        currency: 'JOD',
        decimal_places: 3,
        min: 0,
        format_display: true
      }.to_json
    },
    {
      name: 'rating',
      input_type: 'rating',
      data_type: 'integer',
      description: 'Star rating input',
      validation_schema: {
        supported_validations: ['required', 'min_value', 'max_value'],
        default_rules: { min: 1, max: 5 }
      }.to_json,
      default_config: {
        max_rating: 5,
        icon: 'star',
        allow_half: false
      }.to_json
    },
    {
      name: 'location',
      input_type: 'location',
      data_type: 'json',
      description: 'Geographic location picker',
      validation_schema: {
        supported_validations: ['required'],
        default_rules: {}
      }.to_json,
      default_config: {
        enable_gps: true,
        map_provider: 'google',
        zoom_level: 15,
        address_lookup: true
      }.to_json
    },
    {
      name: 'signature',
      input_type: 'signature',
      data_type: 'string',
      description: 'Digital signature capture',
      validation_schema: {
        supported_validations: ['required'],
        default_rules: {}
      }.to_json,
      default_config: {
        width: 400,
        height: 200,
        background_color: '#ffffff',
        pen_color: '#000000'
      }.to_json
    }
  ].freeze

  def self.seed!
    FIELD_TYPES.each do |field_type_data|
      FieldTypeDefinition.find_or_create_by(name: field_type_data[:name]) do |field_type|
        field_type.assign_attributes(field_type_data.except(:name))
        field_type.active = true
      end
    end
    
    puts "✅ Created #{FIELD_TYPES.count} field type definitions"
  end
end

# Execute the seeding
FieldTypeSeeder.seed!

puts "🌱 Field Type Definitions creation completed!"
