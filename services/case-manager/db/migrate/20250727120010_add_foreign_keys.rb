class AddForeignKeys < ActiveRecord::Migration[8.0]
  def change
    # Add foreign key from form_sections to form_fields
    add_foreign_key :form_sections, :form_fields, column: :display_condition_field_id, on_delete: :nullify

    # Add foreign key from form_fields to form_fields (parent_field_id)
    # Note: form_fields -> form_sections foreign key is already created in create_form_fields migration
    add_foreign_key :form_fields, :form_fields, column: :parent_field_id, on_delete: :nullify
  end
end
