class EnhanceCasesTable < ActiveRecord::Migration[8.0]
  def change
    # Add new columns to cases table
    add_column :cases, :case_number, :string
    add_column :cases, :case_type, :string, default: 'general'
    add_column :cases, :priority_level, :integer, default: 1
    add_column :cases, :confidentiality_level, :integer, default: 0
    add_column :cases, :beneficiary_cache, :json, default: {}
    add_column :cases, :created_by_id, :uuid
    add_column :cases, :notes, :text
    
    # Add indexes for new columns
    add_index :cases, :case_number, unique: true
    add_index :cases, :case_type
    add_index :cases, :priority_level
    add_index :cases, :confidentiality_level
    add_index :cases, :created_by_id
    
    # Update existing status column to support new enum values
    # Note: This will be handled in the model enum definition
    
    # Add foreign key constraints where appropriate
    # Note: created_by_id references users in core service, so no FK constraint here
  end
end
