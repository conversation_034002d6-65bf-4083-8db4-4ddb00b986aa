class CreateFieldTypeDefinitions < ActiveRecord::Migration[7.0]
  def change
    create_table :field_type_definitions do |t|
      t.string :name, null: false
      t.string :input_type, null: false
      t.integer :data_type, null: false, default: 0
      t.text :description
      t.json :validation_schema
      t.json :default_config
      t.boolean :active, default: true
      t.integer :version, default: 1
      
      t.timestamps
    end

    # Indexes for performance
    add_index :field_type_definitions, :name, unique: true
    add_index :field_type_definitions, :input_type
    add_index :field_type_definitions, :data_type
    add_index :field_type_definitions, :active
    add_index :field_type_definitions, [:active, :name]
  end
end
