class CreateFormSections < ActiveRecord::Migration[8.0]
  def change
    create_table :form_sections do |t|
      t.references :form_template, null: false, foreign_key: true
      t.string :name, null: false
      t.string :title, null: false
      t.text :description
      t.integer :display_order, null: false
      t.boolean :is_required, default: false
      t.boolean :visible, default: true
      
      # Conditional display fields
      t.bigint :display_condition_field_id
      t.string :display_condition_value
      t.string :display_condition_user_role
      t.string :display_condition_project_type
      
      t.timestamps
    end

    add_index :form_sections, [:form_template_id, :display_order], unique: true
    add_index :form_sections, :name
    add_index :form_sections, :display_condition_field_id
    # Foreign key will be added after form_fields table is created
  end
end
