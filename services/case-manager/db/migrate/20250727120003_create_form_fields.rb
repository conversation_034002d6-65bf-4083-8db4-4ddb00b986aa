class CreateFormFields < ActiveRecord::Migration[8.0]
  def change
    create_table :form_fields do |t|
      t.references :form_section, null: false, foreign_key: true
      t.string :field_name, null: false
      t.string :label, null: false
      t.string :field_type, null: false
      t.string :data_type, default: 'string'
      t.integer :display_order, null: false
      t.boolean :required, default: false
      t.boolean :visible, default: true
      t.text :help_text
      t.text :placeholder
      t.json :validation_rules, default: {}
      
      # Field configuration for calculations and dependencies
      t.json :field_config, default: {}
      
      # Calculation fields
      t.string :calculation_formula
      t.string :lookup_source_type
      t.text :lookup_source_config
      
      # Field dependencies
      t.bigint :parent_field_id
      
      t.timestamps
    end

    add_index :form_fields, [:form_section_id, :display_order], unique: true
    add_index :form_fields, :field_name
    add_index :form_fields, :field_type
    add_index :form_fields, :required
    add_index :form_fields, :parent_field_id
    # Self-referencing foreign key will be added later
  end
end
