class CreateCaseDocuments < ActiveRecord::Migration[8.0]
  def change
    create_table :case_documents do |t|
      t.uuid :case_id, null: false
      t.uuid :uploaded_by_id, null: false
      t.string :file_name, null: false
      t.string :file_type, null: false
      t.bigint :file_size, null: false
      t.string :document_type, null: false
      t.text :description
      t.json :metadata, default: {}

      t.timestamps
    end

    add_index :case_documents, :case_id
    add_index :case_documents, :uploaded_by_id
    add_index :case_documents, :document_type
    add_index :case_documents, :file_type
    add_index :case_documents, :created_at
  end
end
