class CreateFormFieldOptions < ActiveRecord::Migration[8.0]
  def change
    create_table :form_field_options do |t|
      t.references :form_field, null: false, foreign_key: true
      t.string :option_key, null: false
      t.string :option_value, null: false
      t.integer :display_order, null: false
      t.boolean :active, default: true
      t.json :metadata, default: {}

      t.timestamps
    end

    add_index :form_field_options, [:form_field_id, :option_key], unique: true
    add_index :form_field_options, :display_order
    add_index :form_field_options, :active
  end
end
