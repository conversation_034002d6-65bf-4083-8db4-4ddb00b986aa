class AddNotNullConstraintsToServices < ActiveRecord::Migration[8.0]
  def up
    # First, update any NULL values with defaults
    execute "UPDATE services SET category = 'social' WHERE category IS NULL"
    execute "UPDATE services SET provider_type = 'internal' WHERE provider_type IS NULL"
    execute "UPDATE services SET name = 'Unnamed Service' WHERE name IS NULL"
    execute "UPDATE services SET description = 'No description provided' WHERE description IS NULL"

    # Add NOT NULL constraints to category and provider_type
    change_column_null :services, :category, false
    change_column_null :services, :provider_type, false
    change_column_null :services, :name, false
    change_column_null :services, :description, false
  end

  def down
    # Remove NOT NULL constraints
    change_column_null :services, :category, true
    change_column_null :services, :provider_type, true
    change_column_null :services, :name, true
    change_column_null :services, :description, true
  end
end
