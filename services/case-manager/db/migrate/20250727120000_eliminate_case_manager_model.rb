class EliminateCaseManagerModel < ActiveRecord::Migration[7.0]
  def change
    # Rename case_manager_id to assigned_user_id for semantic clarity
    # This makes it clear that cases are assigned to users, not to a separate "case manager" entity
    rename_column :cases, :case_manager_id, :assigned_user_id
    
    # Drop case_managers table completely since we're using User model directly
    drop_table :case_managers, if_exists: true do |t|
      t.integer :user_id, null: false
      t.integer :supervisor_id
      t.integer :project_id, null: false
      t.timestamps
    end
  end
end
