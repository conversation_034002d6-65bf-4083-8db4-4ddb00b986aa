class CreateLookupTables < ActiveRecord::Migration[8.0]
  def change
    # Staff Positions
    create_table :staff_positions do |t|
      t.string :name, null: false
      t.string :name_ar, null: false
      t.string :code, null: false
      t.string :department, null: false
      t.boolean :active, default: true, null: false
      t.integer :display_order, default: 0
      t.integer :project_id, null: true  # nil = global
      t.timestamps
    end

    add_index :staff_positions, [:project_id, :code], unique: true
    add_index :staff_positions, [:active, :display_order, :name]
    add_index :staff_positions, :department

    # Partner Agencies
    create_table :partner_agencies do |t|
      t.string :name, null: false
      t.string :name_ar, null: false
      t.integer :agency_type, null: false
      t.string :contact_email
      t.boolean :active, default: true, null: false
      t.integer :display_order, default: 0
      t.integer :project_id, null: true  # nil = global
      t.timestamps
    end

    add_index :partner_agencies, [:project_id, :name], unique: true
    add_index :partner_agencies, [:active, :display_order, :name]
    add_index :partner_agencies, :agency_type

    # Geographic Locations (Hierarchical)
    create_table :geographic_locations do |t|
      t.string :name, null: false
      t.string :name_ar, null: false
      t.integer :location_type, null: false
      t.string :iso_code
      t.references :parent_location, null: true, foreign_key: { to_table: :geographic_locations }
      t.boolean :active, default: true, null: false
      t.integer :display_order, default: 0
      t.integer :project_id, null: true  # nil = global
      t.timestamps
    end

    add_index :geographic_locations, [:project_id, :name], unique: true
    add_index :geographic_locations, [:active, :display_order, :name]
    add_index :geographic_locations, :location_type
    # parent_location_id index is automatically created by references

    # Case Types
    create_table :case_types do |t|
      t.string :name, null: false
      t.string :name_ar, null: false
      t.integer :category, null: false
      t.boolean :active, default: true, null: false
      t.integer :display_order, default: 0
      t.integer :project_id, null: true  # nil = global
      t.timestamps
    end

    add_index :case_types, [:project_id, :name], unique: true
    add_index :case_types, [:active, :display_order, :name]
    add_index :case_types, :category

    # Service Categories
    create_table :service_categories do |t|
      t.string :name, null: false
      t.string :name_ar, null: false
      t.integer :sector, null: false
      t.integer :target_group, null: false
      t.boolean :active, default: true, null: false
      t.integer :display_order, default: 0
      t.integer :project_id, null: true  # nil = global
      t.timestamps
    end

    add_index :service_categories, [:project_id, :name], unique: true
    add_index :service_categories, [:active, :display_order, :name]
    add_index :service_categories, :sector
    add_index :service_categories, :target_group

    # Referral Sources
    create_table :referral_sources do |t|
      t.string :name, null: false
      t.string :name_ar, null: false
      t.integer :source_type, null: false
      t.boolean :active, default: true, null: false
      t.integer :display_order, default: 0
      t.integer :project_id, null: true  # nil = global
      t.timestamps
    end

    add_index :referral_sources, [:project_id, :name], unique: true
    add_index :referral_sources, [:active, :display_order, :name]
    add_index :referral_sources, :source_type
  end
end
