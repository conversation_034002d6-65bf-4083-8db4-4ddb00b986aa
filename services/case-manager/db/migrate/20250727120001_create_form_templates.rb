class CreateFormTemplates < ActiveRecord::Migration[8.0]
  def change
    create_table :form_templates do |t|
      t.string :name, null: false
      t.string :title, null: false
      t.text :description
      t.string :form_type, null: false
      t.string :target_role, default: 'both'
      t.integer :sequence_order, default: 0
      t.boolean :active, default: true
      t.json :prerequisite_forms, default: []
      t.integer :project_id

      t.timestamps
    end

    add_index :form_templates, :name, unique: true
    add_index :form_templates, :form_type
    add_index :form_templates, :target_role
    add_index :form_templates, :active
    add_index :form_templates, :project_id
  end
end
