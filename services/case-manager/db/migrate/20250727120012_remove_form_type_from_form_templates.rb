class RemoveFormTypeFromFormTemplates < ActiveRecord::Migration[8.0]
  def up
    # Remove the form_type index first
    remove_index :form_templates, :form_type if index_exists?(:form_templates, :form_type)

    # Remove the form_type column
    remove_column :form_templates, :form_type if column_exists?(:form_templates, :form_type)
  end

  def down
    # Add the form_type column back
    add_column :form_templates, :form_type, :string, null: false, default: 'consent_assent'

    # Add the index back
    add_index :form_templates, :form_type

    # Update existing records with appropriate form_type based on name
    execute <<-SQL
      UPDATE form_templates#{' '}
      SET form_type = CASE#{' '}
        WHEN name = 'consent_assent' THEN 'consent_assent'
        WHEN name = 'registration_rapid_assessment' THEN 'registration_rapid_assessment'
        WHEN name = 'comprehensive_assessment' THEN 'comprehensive_assessment'
        WHEN name = 'case_plan_implementation' THEN 'case_plan_implementation'
        ELSE 'consent_assent'
      END
    SQL
  end
end
