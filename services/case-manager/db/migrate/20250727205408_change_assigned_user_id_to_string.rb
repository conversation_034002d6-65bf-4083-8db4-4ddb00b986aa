class ChangeAssignedUserIdToString < ActiveRecord::Migration[8.0]
  def up
    # Change assigned_user_id from uuid to integer (extract the numeric part from UUID)
    change_column :cases, :assigned_user_id, :integer, using: "SUBSTRING(assigned_user_id::text FROM '550e8400-e29b-41d4-a716-44665544000(.+)')::integer"
  end

  def down
    # Change back to uuid using the original seed pattern
    change_column :cases, :assigned_user_id, :uuid, using: "('550e8400-e29b-41d4-a716-44665544000' || assigned_user_id::text)::uuid"
  end
end
