class RemoveCasePlansAndUpdateServices < ActiveRecord::Migration[8.0]
  def up
    # Remove the old case_plan_id column
    remove_column :services, :case_plan_id

    # Drop the case_plans table
    drop_table :case_plans

    # Note: Services are now a generic catalog, not tied to specific cases
  end

  def down
    # Recreate case_plans table
    create_table :case_plans do |t|
      t.uuid :case_id
      t.string :status
      t.text :summary
      t.boolean :approved
      t.datetime :approved_at
      t.timestamps
    end

    # Add case_plan_id back to services
    add_column :services, :case_plan_id, :uuid

    # Create default case plans for existing services
    execute <<-SQL
      INSERT INTO case_plans (case_id, status, summary, approved, created_at, updated_at)
      SELECT DISTINCT case_id, 'active', 'Migrated case plan', true, NOW(), NOW()
      FROM services
    SQL

    # Update services to reference case plans
    execute <<-SQL
      UPDATE services
      SET case_plan_id = case_plans.id
      FROM case_plans
      WHERE services.case_id = case_plans.case_id
    SQL

    # Remove case_id from services
    remove_column :services, :case_id

    # Add constraints
    change_column_null :services, :case_plan_id, false
    add_index :services, :case_plan_id
    add_index :case_plans, :case_id
  end
end
