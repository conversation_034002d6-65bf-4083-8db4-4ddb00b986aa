class CreateComments < ActiveRecord::Migration[8.0]
  def change
    create_table :comments do |t|
      t.uuid :case_id, null: false
      t.uuid :user_id, null: false
      t.references :commentable, polymorphic: true, null: false
      t.bigint :parent_comment_id
      t.text :content, null: false
      t.integer :comment_type, default: 0
      t.integer :status, default: 0
      t.datetime :resolved_at
      t.uuid :resolved_by_id

      t.timestamps
    end

    add_index :comments, :case_id
    add_index :comments, :user_id
    add_index :comments, [:commentable_type, :commentable_id]
    add_index :comments, :parent_comment_id
    add_index :comments, :comment_type
    add_index :comments, :status
    add_index :comments, :resolved_at
    add_index :comments, :resolved_by_id
    add_foreign_key :comments, :comments, column: :parent_comment_id, on_delete: :cascade
  end
end
