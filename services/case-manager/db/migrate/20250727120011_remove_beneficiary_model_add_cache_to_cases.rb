class RemoveBeneficiaryModelAddCacheToCases < ActiveRecord::Migration[8.0]
  def up
    # Add individual accessor columns for performance (beneficiary_cache already exists)
    add_column :cases, :beneficiary_name, :string unless column_exists?(:cases, :beneficiary_name)
    add_column :cases, :beneficiary_age, :integer unless column_exists?(:cases, :beneficiary_age)
    add_column :cases, :beneficiary_gender, :string unless column_exists?(:cases, :beneficiary_gender)
    add_column :cases, :beneficiary_nationality, :string unless column_exists?(:cases, :beneficiary_nationality)
    add_column :cases, :beneficiary_phone, :string unless column_exists?(:cases, :beneficiary_phone)
    add_column :cases, :beneficiary_id_number, :string unless column_exists?(:cases, :beneficiary_id_number)
    add_column :cases, :beneficiary_date_of_birth, :date unless column_exists?(:cases, :beneficiary_date_of_birth)
    add_column :cases, :beneficiary_city, :string unless column_exists?(:cases, :beneficiary_city)
    
    # Add indexes for commonly queried fields
    add_index :cases, :beneficiary_name unless index_exists?(:cases, :beneficiary_name)
    add_index :cases, :beneficiary_age unless index_exists?(:cases, :beneficiary_age)
    add_index :cases, :beneficiary_gender unless index_exists?(:cases, :beneficiary_gender)
    add_index :cases, :beneficiary_nationality unless index_exists?(:cases, :beneficiary_nationality)
    
    # Migrate existing beneficiary data to cases
    if table_exists?(:beneficiaries)
      migrate_beneficiary_data_to_cases
    end
    
    # Remove beneficiary_id from cases table
    if column_exists?(:cases, :beneficiary_id)
      remove_column :cases, :beneficiary_id
    end
    
    # Drop beneficiaries table
    drop_table :beneficiaries if table_exists?(:beneficiaries)
  end
  
  def down
    # Recreate beneficiaries table
    create_table :beneficiaries do |t|
      t.string :name, null: false
      t.string :gender, null: false
      t.string :nationality, null: false
      t.string :city, null: false
      t.date :date_of_birth, null: false
      t.string :phone
      t.string :id_number
      t.integer :project_id, null: false
      t.timestamps
    end
    
    # Add beneficiary_id back to cases
    add_reference :cases, :beneficiary, null: true, foreign_key: true
    
    # Migrate data back from cache to beneficiaries table
    migrate_cache_data_back_to_beneficiaries
    
    # Remove cache fields from cases
    remove_column :cases, :beneficiary_cache if column_exists?(:cases, :beneficiary_cache)
    remove_column :cases, :beneficiary_name if column_exists?(:cases, :beneficiary_name)
    remove_column :cases, :beneficiary_age if column_exists?(:cases, :beneficiary_age)
    remove_column :cases, :beneficiary_gender if column_exists?(:cases, :beneficiary_gender)
    remove_column :cases, :beneficiary_nationality if column_exists?(:cases, :beneficiary_nationality)
    remove_column :cases, :beneficiary_phone if column_exists?(:cases, :beneficiary_phone)
    remove_column :cases, :beneficiary_id_number if column_exists?(:cases, :beneficiary_id_number)
    remove_column :cases, :beneficiary_date_of_birth if column_exists?(:cases, :beneficiary_date_of_birth)
    remove_column :cases, :beneficiary_city if column_exists?(:cases, :beneficiary_city)
  end
  
  private
  
  def migrate_beneficiary_data_to_cases
    say "Migrating beneficiary data to cases cache..."
    
    execute <<-SQL
      UPDATE cases
      SET
        beneficiary_name = beneficiaries.name,
        beneficiary_age = EXTRACT(YEAR FROM AGE(beneficiaries.date_of_birth)),
        beneficiary_gender = beneficiaries.gender,
        beneficiary_nationality = beneficiaries.nationality,
        beneficiary_phone = NULL,
        beneficiary_id_number = NULL,
        beneficiary_date_of_birth = beneficiaries.date_of_birth,
        beneficiary_city = beneficiaries.city,
        beneficiary_cache = json_build_object(
          'name', beneficiaries.name,
          'age', EXTRACT(YEAR FROM AGE(beneficiaries.date_of_birth)),
          'gender', beneficiaries.gender,
          'nationality', beneficiaries.nationality,
          'phone', NULL,
          'id_number', NULL,
          'date_of_birth', beneficiaries.date_of_birth,
          'city', beneficiaries.city
        )
      FROM beneficiaries
      WHERE cases.beneficiary_id::text = beneficiaries.id::text
    SQL
    
    say "Migrated beneficiary data for cases"
  end
  
  def migrate_cache_data_back_to_beneficiaries
    say "Migrating cache data back to beneficiaries table..."
    
    # This would need to be implemented if rollback is needed
    # For now, just log that rollback is not fully supported
    say "Rollback migration not fully implemented - manual data recovery required"
  end
end
