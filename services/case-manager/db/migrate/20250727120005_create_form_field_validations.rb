class CreateFormFieldValidations < ActiveRecord::Migration[8.0]
  def change
    create_table :form_field_validations do |t|
      t.references :form_field, null: false, foreign_key: true
      t.string :validation_type, null: false
      t.string :validation_value, null: false
      t.string :error_message, null: false
      t.boolean :active, default: true
      t.integer :priority, default: 0

      t.timestamps
    end

    add_index :form_field_validations, :validation_type
    add_index :form_field_validations, :active
    add_index :form_field_validations, :priority
  end
end
