module AuthenticationHelpers
  # Create a test user with compressed token data (using OpenStruct for simplicity)
  def create_test_user(role_name: 'case_manager', project_id: 1, global: false)
    OpenStruct.new(
      id: 1,
      name: "Test User",
      email: "<EMAIL>",
      global: global,
      type: global ? "global" : "project_based",
      status: "active"
    )
  end

  # Create a test project
  def create_test_project(id: 1, name: "Test Project")
    OpenStruct.new(
      id: id,
      name: name
    )
  end

  # Create a test role
  def create_test_role(name: 'case_manager', level: 1, scope: 'project_role')
    OpenStruct.new(
      name: name,
      level: level,
      scope: scope
    )
  end

  # Mock authentication and authorization for the new system
  def mock_authentication_and_authorization(user: nil, project: nil, role: nil)
    user ||= create_test_user
    project ||= create_test_project
    role ||= create_test_role

    # Mock the authentication methods
    allow_any_instance_of(ApplicationController).to receive(:authenticate_api_user!).and_return(true)
    allow_any_instance_of(ApplicationController).to receive(:authenticate_session!).and_return(true)

    # Mock the current_user, current_project, current_role helpers
    allow_any_instance_of(ApplicationController).to receive(:current_user).and_return(user)
    allow_any_instance_of(ApplicationController).to receive(:current_project).and_return(project)
    allow_any_instance_of(ApplicationController).to receive(:current_role).and_return(role)

    # Mock the authorization methods to always return true
    allow_any_instance_of(ApplicationController).to receive(:can?).and_return(true)
    allow_any_instance_of(ApplicationController).to receive(:authorize!).and_return(true)

    # Mock the authorize_resources methods
    allow_any_instance_of(ApplicationController).to receive(:authorize_and_load_collection!).and_return(true)
    allow_any_instance_of(ApplicationController).to receive(:authorize_and_load_instance!).and_return(true)

    { user: user, project: project, role: role }
  end

  # Helper to create authenticated case manager
  def create_authenticated_case_manager(project_id: 1)
    auth_data = mock_authentication_and_authorization
    case_manager = create(:case_manager, user_id: auth_data[:user].id, project_id: project_id)

    # Mock the current_case_manager helper if it exists
    if ApplicationController.instance_methods.include?(:current_case_manager)
      allow_any_instance_of(ApplicationController).to receive(:current_case_manager).and_return(case_manager)
    end

    { **auth_data, case_manager: case_manager }
  end

  # Helper to create authenticated admin user
  def create_authenticated_admin_user
    mock_authentication_and_authorization(
      user: create_test_user(role_name: 'admin', global: true),
      role: create_test_role(name: 'admin', scope: 'global_role')
    )
  end

  # Helper to create authenticated supervisor
  def create_authenticated_supervisor(project_id: 1)
    auth_data = mock_authentication_and_authorization(
      user: create_test_user(role_name: 'supervisor'),
      role: create_test_role(name: 'supervisor')
    )
    case_manager = create(:case_manager, :supervisor, user_id: auth_data[:user].id, project_id: project_id)

    { **auth_data, case_manager: case_manager }
  end

  # Helper for request specs - generates simple JWT token for testing
  def authenticated_headers(user: nil, project: nil, role: nil)
    user ||= create_test_user
    project ||= create_test_project
    role ||= create_test_role

    # For testing, we'll use a simple fake token since the auth is mocked anyway
    {
      'Authorization' => "Bearer fake-jwt-token-for-testing",
      'Content-Type' => 'application/json'
    }
  end

  # Helper for API testing
  def json_response
    JSON.parse(response.body)
  end

  def expect_json_api_response
    expect(response.content_type).to include('application/json')
    expect(json_response).to have_key('data')
  end

  def expect_json_api_error_response
    expect(response.content_type).to include('application/json')
    expect(json_response).to have_key('errors')
  end

  # Helper to mock collection loading for authorize_resources
  def mock_collection_loading(model_class, collection_data = [])
    instance_var_name = "@#{model_class.name.underscore.pluralize}"
    allow_any_instance_of(ApplicationController).to receive(:instance_variable_set)
      .with(instance_var_name, anything)
      .and_return(collection_data)
    allow_any_instance_of(ApplicationController).to receive(:instance_variable_get)
      .with(instance_var_name)
      .and_return(collection_data)
  end

  # Helper to mock instance loading for authorize_resources
  def mock_instance_loading(model_class, instance_data)
    instance_var_name = "@#{model_class.name.underscore}"
    allow_any_instance_of(ApplicationController).to receive(:instance_variable_set)
      .with(instance_var_name, anything)
      .and_return(instance_data)
    allow_any_instance_of(ApplicationController).to receive(:instance_variable_get)
      .with(instance_var_name)
      .and_return(instance_data)
  end
end
