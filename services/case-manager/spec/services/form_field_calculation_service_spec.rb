require 'rails_helper'

RSpec.describe FormFieldCalculationService do
  describe '.calculate' do
    describe 'age_from_date' do
      it 'calculates age from date string' do
        birth_date = 20.years.ago.to_date.to_s
        age = described_class.calculate('age_from_date', birth_date)
        expect(age).to eq(20)
      end

      it 'calculates age from Date object' do
        birth_date = 25.years.ago.to_date
        age = described_class.calculate('age_from_date', birth_date)
        expect(age).to be_between(24, 25)
      end

      it 'returns nil for invalid date' do
        age = described_class.calculate('age_from_date', nil)
        expect(age).to be_nil
      end
    end

    describe 'sum' do
      it 'calculates sum of values' do
        result = described_class.calculate('sum', [10, 20, 30])
        expect(result).to eq(60)
      end

      it 'handles empty array' do
        result = described_class.calculate('sum', [])
        expect(result).to eq(0)
      end

      it 'handles nil values' do
        result = described_class.calculate('sum', [10, nil, 20])
        expect(result).to eq(30)
      end
    end

    describe 'difference' do
      it 'calculates difference between two values' do
        result = described_class.calculate('difference', [50, 20])
        expect(result).to eq(30)
      end

      it 'handles nil values' do
        result = described_class.calculate('difference', [nil, 10])
        expect(result).to eq(-10)
      end
    end

    describe 'percentage' do
      it 'calculates percentage' do
        result = described_class.calculate('percentage', [25, 100])
        expect(result).to eq(25.0)
      end

      it 'handles zero total' do
        result = described_class.calculate('percentage', [25, 0])
        expect(result).to eq(0)
      end
    end

    describe 'average' do
      it 'calculates average of values' do
        result = described_class.calculate('average', [10, 20, 30])
        expect(result).to eq(20.0)
      end

      it 'handles empty array' do
        result = described_class.calculate('average', [])
        expect(result).to eq(0)
      end
    end

    describe 'count_non_empty' do
      it 'counts non-empty values' do
        result = described_class.calculate('count_non_empty', ['a', '', 'b', nil, 'c'])
        expect(result).to eq(3)
      end
    end

    describe 'multiply' do
      it 'multiplies two values' do
        result = described_class.calculate('multiply', [5, 4])
        expect(result).to eq(20)
      end
    end

    describe 'concat' do
      it 'concatenates values' do
        result = described_class.calculate('concat', ['Hello', 'World'])
        expect(result).to eq('Hello World')
      end
    end
  end

  describe '.calculate_field_value' do
    let(:form_template) { create(:form_template) }
    let(:form_section) { create(:form_section, form_template: form_template) }
    
    context 'age calculation field' do
      let(:field) do
        create(:form_field,
          form_section: form_section,
          field_type: 'calculated',
          calculation_formula: 'age_from_date',
          depends_on: ['birth_date'].to_json
        )
      end

      it 'calculates age from birth date' do
        form_data = { 'birth_date' => 25.years.ago.to_date.to_s }
        result = described_class.calculate_field_value(field, form_data)
        expect(result).to be_between(24, 25)
      end
    end

    context 'sum calculation field' do
      let(:field) do
        create(:form_field,
          form_section: form_section,
          field_type: 'calculated',
          calculation_formula: 'sum',
          depends_on: ['field1', 'field2', 'field3'].to_json
        )
      end

      it 'calculates sum of dependent fields' do
        form_data = { 'field1' => 10, 'field2' => 20, 'field3' => 30 }
        result = described_class.calculate_field_value(field, form_data)
        expect(result).to eq(60)
      end
    end

    context 'custom formula field' do
      let(:field) do
        create(:form_field,
          form_section: form_section,
          field_type: 'calculated',
          calculation_formula: '{{field1}} + {{field2}} * 2',
          depends_on: ['field1', 'field2'].to_json
        )
      end

      it 'evaluates custom formula' do
        form_data = { 'field1' => 10, 'field2' => 5 }
        result = described_class.calculate_field_value(field, form_data)
        expect(result).to eq(20) # 10 + 5 * 2
      end
    end
  end

  describe '.calculate_all_fields' do
    let(:form_template) { create(:form_template) }
    let(:form_section) { create(:form_section, form_template: form_template) }
    let!(:age_field) do
      create(:form_field,
        form_section: form_section,
        field_name: 'age',
        field_type: 'calculated',
        calculation_formula: 'age_from_date',
        depends_on: ['birth_date'].to_json
      )
    end
    let!(:sum_field) do
      create(:form_field,
        form_section: form_section,
        field_name: 'total',
        field_type: 'calculated',
        calculation_formula: 'sum',
        depends_on: ['value1', 'value2'].to_json
      )
    end

    it 'calculates all calculated fields' do
      form_data = {
        'birth_date' => 30.years.ago.to_date.to_s,
        'value1' => 15,
        'value2' => 25
      }

      results = described_class.calculate_all_fields(form_template, form_data)
      
      expect(results['age']).to eq(30)
      expect(results['total']).to eq(40)
    end
  end

  describe '.recalculate_dependent_fields' do
    let(:form_template) { create(:form_template) }
    let(:form_section) { create(:form_section, form_template: form_template) }
    let!(:dependent_field) do
      create(:form_field,
        form_section: form_section,
        field_name: 'calculated_field',
        field_type: 'calculated',
        calculation_formula: 'sum',
        depends_on: ['source_field', 'other_field'].to_json
      )
    end

    it 'recalculates fields that depend on changed field' do
      form_data = { 'source_field' => 10, 'other_field' => 20 }
      
      results = described_class.recalculate_dependent_fields('source_field', form_template, form_data)
      
      expect(results['calculated_field']).to eq(30)
    end
  end
end
