FactoryBot.define do
  factory :service do
    name { Faker::Company.name + " Service" }
    description { Faker::Lorem.paragraph(sentence_count: 3) }
    category { %w[health education legal social financial housing employment].sample }
    provider_type { %w[internal external partner].sample }
    project_id { 1 }

    trait :health do
      category { "health" }
      name { "Health Service - " + Faker::Medical.medicine }
    end

    trait :education do
      category { "education" }
      name { "Education Service - " + Faker::Educator.course_name }
    end

    trait :legal do
      category { "legal" }
      name { "Legal Service - " + Faker::Company.profession }
    end

    trait :social do
      category { "social" }
      name { "Social Service - " + Faker::Community.name }
    end

    trait :financial do
      category { "financial" }
      name { "Financial Service - " + Faker::Finance.credit_card(:mastercard) }
    end

    trait :housing do
      category { "housing" }
      name { "Housing Service - " + Faker::Address.community }
    end

    trait :employment do
      category { "employment" }
      name { "Employment Service - " + Faker::Job.title }
    end

    trait :internal do
      provider_type { "internal" }
    end

    trait :external do
      provider_type { "external" }
    end

    trait :partner do
      provider_type { "partner" }
    end
  end
end
