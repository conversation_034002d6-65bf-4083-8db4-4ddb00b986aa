FactoryBot.define do
  factory :form_template do
    sequence(:name) { |n| "form_template_#{n}" }
    sequence(:title) { |n| "Form Template #{n}" }
    description { 'Test form template' }
    sequence(:sequence_order)
    target_role { 'both' }
    active { true }
    project_id { 24 }
  end

  factory :form_section do
    association :form_template
    sequence(:name) { |n| "section_#{n}" }
    sequence(:title) { |n| "Section #{n}" }
    description { 'Test form section' }
    sequence(:display_order)
    is_required { true }
    visible { true }
  end

  factory :form_field do
    association :form_section
    sequence(:field_name) { |n| "field_#{n}" }
    sequence(:label) { |n| "Field #{n}" }
    field_type { 'text' }
    data_type { 'string' }
    sequence(:display_order)
    required { false }
    visible { true }
    help_text { 'Test help text' }
    placeholder { 'Enter value' }

    trait :calculated do
      field_type { 'calculated' }
      calculation_formula { 'sum' }
      depends_on { ['field1', 'field2'].to_json }
      auto_calculate { true }
    end

    trait :lookup do
      field_type { 'lookup' }
      lookup_source_type { 'StaffPosition' }
    end

    trait :required_field do
      required { true }
    end
  end

  factory :form_field_option do
    association :form_field
    sequence(:option_key) { |n| "option_#{n}" }
    sequence(:option_value) { |n| "Option #{n}" }
    sequence(:display_order)
    active { true }
  end

  factory :form_field_validation do
    association :form_field
    validation_type { 'required' }
    validation_value { 'true' }
    error_message { 'This field is required' }
    active { true }
    sequence(:display_order)
  end

  factory :form_submission do
    association :form_template
    association :case
    created_by_id { SecureRandom.uuid }
    status { 'form_draft' }
    form_data { {} }
  end
end
