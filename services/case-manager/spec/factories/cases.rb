FactoryBot.define do
  factory :case do
    # Use assigned_user_id instead of case_manager_id since CaseManager model was removed
    assigned_user_id { SecureRandom.uuid } # User ID from AtharAuth system
    created_by_id { SecureRandom.uuid } # Required field
    project_id { 1 }
    status { "active" }
    approval_status { nil }
    started_at { Faker::Time.between(from: 6.months.ago, to: Time.current) }

    # Required fields based on validations
    case_type { "general" }
    priority_level { 2 }
    confidentiality_level { 1 }
    case_number { Faker::Alphanumeric.unique.alphanumeric(number: 10).upcase }

    # Add beneficiary cache data instead
    beneficiary_cache { {
      "id" => Faker::Number.unique.number(digits: 6),
      "name" => Faker::Name.name,
      "age" => Faker::Number.between(from: 1, to: 80),
      "gender" => ["male", "female", "other"].sample
    } }
    closed_at { nil }

    trait :active do
      status { "active" }
      closed_at { nil }
    end

    trait :draft do
      status { "draft" }
      closed_at { nil }
    end

    trait :closed do
      status { "closed" }
      closed_at { Faker::Time.between(from: started_at, to: Time.current) }
    end

    trait :suspended do
      status { "suspended" }
      closed_at { nil }
    end

    trait :pending_approval do
      approval_status { "pending" }
    end

    trait :approved do
      approval_status { "approved" }
    end

    trait :rejected do
      approval_status { "rejected" }
    end

    trait :with_services do
      after(:create) do |case_record|
        create_list(:service, 2, case: case_record)
      end
    end

    trait :complete_workflow do
      after(:create) do |case_record|
        create_list(:service, 3, case: case_record)
      end
    end
  end
end
