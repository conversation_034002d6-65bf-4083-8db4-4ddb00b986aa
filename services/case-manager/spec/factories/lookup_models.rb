FactoryBot.define do
  factory :staff_position do
    sequence(:name) { |n| "Staff Position #{n}" }
    sequence(:name_ar) { |n| "منصب الموظف #{n}" }
    sequence(:code) { |n| "SP#{n}" }
    department { 'Protection' }
    active { true }
    sequence(:display_order)
    project_id { 24 }
  end

  factory :partner_agency do
    sequence(:name) { |n| "Partner Agency #{n}" }
    sequence(:name_ar) { |n| "وكالة شريكة #{n}" }
    agency_type { 'ngo' }
    contact_email { '<EMAIL>' }
    active { true }
    sequence(:display_order)
    project_id { 24 }
  end

  factory :geographic_location do
    sequence(:name) { |n| "Location #{n}" }
    sequence(:name_ar) { |n| "موقع #{n}" }
    location_type { 'city' }
    active { true }
    sequence(:display_order)
    project_id { 24 }

    trait :country do
      location_type { 'country' }
      iso_code { 'JO' }
    end

    trait :governorate do
      location_type { 'governorate' }
      association :parent_location, factory: [:geographic_location, :country]
    end
  end

  factory :case_type do
    sequence(:name) { |n| "Case Type #{n}" }
    sequence(:name_ar) { |n| "نوع الحالة #{n}" }
    category { 'general_protection' }
    active { true }
    sequence(:display_order)
    project_id { 24 }
  end

  factory :service_category do
    sequence(:name) { |n| "Service Category #{n}" }
    sequence(:name_ar) { |n| "فئة الخدمة #{n}" }
    sector { 'social' }
    target_group { 'all_groups' }
    active { true }
    sequence(:display_order)
    project_id { 24 }
  end

  factory :referral_source do
    sequence(:name) { |n| "Referral Source #{n}" }
    sequence(:name_ar) { |n| "مصدر الإحالة #{n}" }
    source_type { 'organization' }
    active { true }
    sequence(:display_order)
    project_id { 24 }
  end

  factory :field_type_definition do
    sequence(:name) { |n| "field_type_#{n}" }
    input_type { 'text' }
    data_type { 'string' }
    description { 'Test field type' }
    validation_schema { { supported_validations: ['required'] }.to_json }
    default_config { { placeholder: 'Enter value' }.to_json }
    active { true }
    version { 1 }

    trait :text_type do
      name { 'text' }
      input_type { 'text' }
      data_type { 'string' }
    end

    trait :number_type do
      name { 'number' }
      input_type { 'number' }
      data_type { 'integer' }
    end

    trait :lookup_type do
      name { 'lookup_single' }
      input_type { 'lookup_single' }
      data_type { 'string' }
    end
  end
end
