# frozen_string_literal: true

require "rails_helper"

RSpec.describe "Api::Permissions", type: :request do
  let(:case_manager_token) do
    {
      "s" => 456,                    # user.id
      "i" => 1640995200,            # issued.at
      "e" => 1641081600,            # expires.at
      "t" => "session",             # token.type
      "sc" => "cm",                 # scope
      "u" => {                      # user context
        "n" => "Ahmed Ali",         # user.name
        "m" => "<EMAIL>", # user.email
        "g" => false,               # user.global
        "ty" => "employee"          # user.type
      },
      "ss" => {                     # session context
        "r" => {                    # session.role (nested object)
          "n" => "case_manager",    # session.role.name
          "l" => 10,                # session.role.level
          "s" => "project_based"    # session.role.scope
        },
        "p" => ["read:case", "create:case", "update:case"], # session.permissions
        "pr" => {                   # session.project (nested object)
          "id" => 2,                # session.project.id
          "n" => "Beta Project",    # session.project.name
          "d" => "A test project",  # session.project.description
          "st" => "active"          # session.project.status
        }
      },
      "a" => {                      # access context
        "ty" => "project",          # access.type
        "op" => [                   # access.other_projects
          { "id" => 3, "n" => "Gamma Project", "r" => "supervisor" }
        ]
      }
    }
  end

  let(:hr_manager_token) do
    {
      "s" => 789,
      "i" => 1640995200,
      "e" => 1641081600,
      "t" => "session",
      "sc" => "cm",
      "u" => {
        "n" => "Sarah Manager",
        "m" => "<EMAIL>",
        "g" => true  # Global user
      },
      "ss" => {
        "r" => {
          "n" => "hr_manager",
          "l" => 30,
          "s" => "global_role"
        },
        "p" => ["read:case", "read:beneficiary", "manage:user", "manage:case_manager"]
      },
      "a" => {
        "ty" => "global",
        "prs" => {
          "1" => "Alpha Project",
          "2" => "Beta Project",
          "3" => "Gamma Project"
        }
      }
    }
  end

  before do
    # Mock the authentication
    allow_any_instance_of(ApplicationController).to receive(:authenticate_api_user!).and_return(true)
    allow_any_instance_of(ApplicationController).to receive(:user_signed_in?).and_return(true)
  end

  describe "GET /api/permissions" do
    context "with case manager token" do
      before do
        allow_any_instance_of(ApplicationController).to receive(:decoded_token).and_return(case_manager_token)
      end

      it "returns comprehensive permissions data" do
        get "/api/permissions"

        expect(response).to have_http_status(:ok)
        
        json_response = JSON.parse(response.body)
        permissions = json_response["permissions"]

        expect(permissions).to include("session", "resources", "cross_project", "user", "capabilities")
      end

      it "returns correct session context" do
        get "/api/permissions"

        json_response = JSON.parse(response.body)
        session = json_response["permissions"]["session"]

        expect(session["project_id"]).to eq(2)
        expect(session["project_name"]).to eq("Beta Project")
        expect(session["role"]).to eq("case_manager")
        expect(session["permissions"]).to include("read:case", "create:case", "update:case")
        expect(session["scope"]).to eq("cm")
      end

      it "returns correct resource permissions" do
        get "/api/permissions"

        json_response = JSON.parse(response.body)
        resources = json_response["permissions"]["resources"]

        expect(resources["cases"]["actions"]).to include("read", "create", "update")
        expect(resources["cases"]["full_permissions"]).to include("read:case", "create:case", "update:case")
        expect(resources["cases"]["can_manage"]).to be false
      end

      it "returns correct cross-project context" do
        get "/api/permissions"

        json_response = JSON.parse(response.body)
        cross_project = json_response["permissions"]["cross_project"]

        expect(cross_project["can_access_other_projects"]).to be true
        expect(cross_project["other_projects"]).to be_an(Array)
        expect(cross_project["other_projects"].first["id"]).to eq(3)
        expect(cross_project["access_type"]).to eq("project")
      end

      it "returns correct user context" do
        get "/api/permissions"

        json_response = JSON.parse(response.body)
        user = json_response["permissions"]["user"]

        expect(user["id"]).to eq(456)
        expect(user["name"]).to eq("Ahmed Ali")
        expect(user["email"]).to eq("<EMAIL>")
        expect(user["global"]).to be false
        expect(user["role_flags"]["case_manager"]).to be true
        expect(user["role_flags"]["supervisor"]).to be false
      end

      it "returns correct system capabilities" do
        get "/api/permissions"

        json_response = JSON.parse(response.body)
        capabilities = json_response["permissions"]["capabilities"]

        expect(capabilities["can_access_cross_project"]).to be true
        expect(capabilities["can_manage_case_managers"]).to be false
        expect(capabilities["system_scope"]).to eq("cm")
      end
    end

    context "with HR manager token (global user)" do
      before do
        allow_any_instance_of(ApplicationController).to receive(:decoded_token).and_return(hr_manager_token)
      end

      it "returns global user permissions" do
        get "/api/permissions"

        json_response = JSON.parse(response.body)
        permissions = json_response["permissions"]

        # Session context for global user
        expect(permissions["session"]["project_id"]).to be_nil
        expect(permissions["session"]["role"]).to eq("hr_manager")

        # User context
        expect(permissions["user"]["global"]).to be true
        expect(permissions["user"]["role_flags"]["hr_manager"]).to be true
        expect(permissions["user"]["role_flags"]["can_manage_users"]).to be true

        # Cross-project context
        expect(permissions["cross_project"]["access_type"]).to eq("global")
        expect(permissions["cross_project"]["accessible_projects"]).to include("1", "2", "3")

        # Capabilities
        expect(permissions["capabilities"]["can_manage_case_managers"]).to be true
      end
    end

    context "when user is not authenticated" do
      before do
        allow_any_instance_of(ApplicationController).to receive(:authenticate_api_user!).and_raise(StandardError.new("Unauthorized"))
      end

      it "returns unauthorized status" do
        expect { get "/api/permissions" }.to raise_error(StandardError, "Unauthorized")
      end
    end
  end
end
