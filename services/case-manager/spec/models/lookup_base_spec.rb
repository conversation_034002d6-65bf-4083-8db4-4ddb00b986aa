require 'rails_helper'

RSpec.describe LookupBase, type: :model do
  # Test with ServiceCategory as a concrete subclass
  let(:test_class) { ServiceCategory }

  let(:project_id) { 24 }
  let!(:global_lookup) { test_class.create!(name: 'Global Item', name_ar: 'عنصر عالمي', sector: 'social', target_group: 'all_groups', project_id: nil) }
  let!(:project_lookup) { test_class.create!(name: 'Project Item', name_ar: 'عنصر المشروع', sector: 'health', target_group: 'children', project_id: project_id) }

  describe 'validations' do
    it 'validates presence of name' do
      item = test_class.new(name_ar: 'اختبار', sector: 'social', target_group: 'all_groups')
      expect(item).not_to be_valid
      expect(item.errors[:name]).to include("can't be blank")
    end

    it 'validates presence of name_ar' do
      item = test_class.new(name: 'Test', sector: 'social', target_group: 'all_groups')
      expect(item).not_to be_valid
      expect(item.errors[:name_ar]).to include("can't be blank")
    end
  end

  describe 'associations' do
    it 'belongs to project optionally' do
      item = test_class.new(name: 'Test', name_ar: 'اختبار', sector: 'social', target_group: 'all_groups')
      expect(item).to be_valid
    end
  end

  describe 'scopes' do
    describe '.active' do
      let!(:inactive_lookup) { test_class.create!(name: 'Inactive', name_ar: 'غير نشط', sector: 'legal', target_group: 'men', active: false) }

      it 'returns only active lookups' do
        expect(test_class.active).to include(global_lookup, project_lookup)
        expect(test_class.active).not_to include(inactive_lookup)
      end
    end

    describe '.for_project' do
      context 'with project_id' do
        it 'returns global and project-specific lookups' do
          results = test_class.for_project(project_id)
          expect(results).to include(global_lookup, project_lookup)
        end
      end

      context 'with nil project_id' do
        it 'returns only global lookups' do
          results = test_class.for_project(nil)
          expect(results).to include(global_lookup)
          expect(results).not_to include(project_lookup)
        end
      end
    end

    describe '.ordered' do
      let!(:first_item) { test_class.create!(name: 'First', name_ar: 'الأول', sector: 'education', target_group: 'women', display_order: 1, project_id: 999) }
      let!(:second_item) { test_class.create!(name: 'Second', name_ar: 'الثاني', sector: 'financial', target_group: 'elderly', display_order: 2, project_id: 999) }

      it 'returns items ordered by display_order and name' do
        ordered_items = test_class.where(project_id: 999).ordered.to_a
        expect(ordered_items).to eq([first_item, second_item])
      end
    end
  end

  describe '#api_representation' do
    it 'returns correct hash structure' do
      representation = global_lookup.api_representation

      expect(representation).to include(
        id: global_lookup.id,
        name: 'Global Item',
        name_ar: 'عنصر عالمي',
        active: true,
        sector: 'social',
        target_group: 'all_groups'
      )
      expect(representation).to have_key(:display_order)
      expect(representation).to have_key(:created_at)
      expect(representation).to have_key(:updated_at)
      # project_id is nil and gets compacted out
      expect(representation).not_to have_key(:project_id)
    end

    it 'includes project_id when present' do
      representation = project_lookup.api_representation
      expect(representation).to have_key(:project_id)
      expect(representation[:project_id]).to eq(project_id)
    end
  end
end
