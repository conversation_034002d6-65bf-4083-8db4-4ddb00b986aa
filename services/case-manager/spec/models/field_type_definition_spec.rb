require 'rails_helper'

RSpec.describe FieldTypeDefinition, type: :model do
  let(:field_type) { create(:field_type_definition) }

  describe 'validations' do
    it { should validate_presence_of(:name) }

    it 'validates uniqueness of name' do
      existing = create(:field_type_definition, name: 'unique_name')
      duplicate = build(:field_type_definition, name: 'unique_name')
      expect(duplicate).not_to be_valid
      expect(duplicate.errors[:name]).to include('has already been taken')
    end

    it { should validate_presence_of(:input_type) }
    
    it 'validates input_type inclusion' do
      expect(field_type).to validate_inclusion_of(:input_type).in_array(%w[
        text textarea number decimal date datetime time
        select radio checkbox file image signature
        location coordinates phone email url
        rich_text markdown rating scale slider
        yes_no true_false currency percentage
        barcode qr_code lookup_single lookup_multiple
        calculated readonly hidden multi_step_wizard
        repeatable_group conditional_section
      ])
    end

    it 'validates JSON format for validation_schema' do
      field_type.validation_schema = 'invalid json'
      expect(field_type).not_to be_valid
      expect(field_type.errors[:validation_schema]).to include('is invalid')
    end

    it 'validates JSON format for default_config' do
      field_type.default_config = 'invalid json'
      expect(field_type).not_to be_valid
      expect(field_type.errors[:default_config]).to include('is invalid')
    end
  end

  describe 'associations' do
    it { should have_many(:form_fields).with_foreign_key('field_type').with_primary_key('name') }
  end

  describe 'enums' do
    it 'defines data_type enum' do
      expect(FieldTypeDefinition.data_types.keys).to include(
        'string', 'integer', 'decimal', 'boolean', 'date', 'datetime', 'json', 'array', 'object'
      )
    end
  end

  describe 'scopes' do
    let!(:active_field_type) { create(:field_type_definition, active: true) }
    let!(:inactive_field_type) { create(:field_type_definition, active: false) }

    describe '.active' do
      it 'returns only active field types' do
        expect(FieldTypeDefinition.active).to include(active_field_type)
        expect(FieldTypeDefinition.active).not_to include(inactive_field_type)
      end
    end

    describe '.by_input_type' do
      let!(:text_field) { create(:field_type_definition, input_type: 'text') }
      let!(:number_field) { create(:field_type_definition, input_type: 'number') }

      it 'filters by input type' do
        expect(FieldTypeDefinition.by_input_type('text')).to include(text_field)
        expect(FieldTypeDefinition.by_input_type('text')).not_to include(number_field)
      end
    end
  end

  describe '#render_config' do
    let(:field_type) do
      create(:field_type_definition, 
        default_config: { placeholder: 'Enter text', max_length: 100 }.to_json
      )
    end

    context 'without field instance' do
      it 'returns default config' do
        config = field_type.render_config
        expect(config['placeholder']).to eq('Enter text')
        expect(config['max_length']).to eq(100)
      end
    end

    context 'with field instance' do
      let(:form_field) do
        double('FormField', 
          field_config: { max_length: 200, required: true }.to_json,
          lookup_source_type: nil
        )
      end

      it 'merges field-specific config with default config' do
        config = field_type.render_config(form_field)
        expect(config['placeholder']).to eq('Enter text')
        expect(config['max_length']).to eq(200) # Overridden
        expect(config['required']).to eq(true) # Added
      end
    end

    context 'with lookup field' do
      let(:lookup_field_type) { create(:field_type_definition, input_type: 'lookup_single') }
      let(:form_field) do
        double('FormField',
          field_config: nil,
          lookup_source_type: 'StaffPosition',
          lookup_options: [{ id: 1, name: 'Manager' }]
        )
      end

      it 'includes lookup options' do
        config = lookup_field_type.render_config(form_field)
        expect(config['options']).to eq([{ id: 1, name: 'Manager' }])
      end
    end
  end

  describe '#supports_validation?' do
    let(:field_type) do
      create(:field_type_definition,
        validation_schema: {
          supported_validations: ['required', 'min_length', 'max_length']
        }.to_json
      )
    end

    it 'returns true for supported validations' do
      expect(field_type.supports_validation?('required')).to be true
      expect(field_type.supports_validation?('min_length')).to be true
    end

    it 'returns false for unsupported validations' do
      expect(field_type.supports_validation?('email')).to be false
    end
  end

  describe '#default_validation_rules' do
    let(:field_type) do
      create(:field_type_definition,
        validation_schema: {
          default_rules: { max_length: 255, required: false }
        }.to_json
      )
    end

    it 'returns default validation rules' do
      rules = field_type.default_validation_rules
      expect(rules['max_length']).to eq(255)
      expect(rules['required']).to eq(false)
    end
  end

  describe '.for_field_type' do
    let!(:text_field_type) { create(:field_type_definition, name: 'text') }

    it 'finds field type by name' do
      expect(FieldTypeDefinition.for_field_type('text')).to eq(text_field_type)
    end
  end

  describe '.by_category' do
    let!(:text_field) { create(:field_type_definition, input_type: 'text') }
    let!(:date_field) { create(:field_type_definition, input_type: 'date') }
    let!(:select_field) { create(:field_type_definition, input_type: 'select') }

    it 'groups field types by category' do
      categories = FieldTypeDefinition.by_category
      
      expect(categories['Basic Input']).to include(text_field)
      expect(categories['Date & Time']).to include(date_field)
      expect(categories['Selection']).to include(select_field)
    end
  end
end
