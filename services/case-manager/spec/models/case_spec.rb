require 'rails_helper'

RSpec.describe Case, type: :model do
  describe 'validations' do
    subject { build(:case) }

    it { should validate_presence_of(:assigned_user_id) }
    it { should validate_presence_of(:created_by_id) }
    it { should validate_presence_of(:project_id) }
    it { should validate_presence_of(:case_number) }

    # Test enum validations differently since they use Rails enums
    it 'validates status enum values' do
      expect(subject).to define_enum_for(:status).with_values(
        case_draft: "draft",
        case_ready_for_approval: "ready_for_approval",
        case_pending_approval: "pending_approval",
        case_approved: "approved",
        case_active: "active",
        case_closed: "closed",
        case_suspended: "suspended",
        case_transferred: "transferred"
      ).backed_by_column_of_type(:string)
    end

    it 'validates case_type enum values' do
      expect(subject).to define_enum_for(:case_type).with_values(
        general: "general",
        protection: "protection",
        child_protection: "child_protection",
        gbv: "gbv",
        education: "education",
        health: "health",
        livelihood: "livelihood"
      ).backed_by_column_of_type(:string)
    end

    it 'validates priority_level enum values' do
      expect(subject).to define_enum_for(:priority_level).with_values(
        low: 1, medium: 2, high: 3, urgent: 4, critical: 5
      ).backed_by_column_of_type(:integer)
    end

    it 'validates confidentiality_level enum values' do
      expect(subject).to define_enum_for(:confidentiality_level).with_values(
        public_access: 0, restricted: 1, confidential: 2
      ).backed_by_column_of_type(:integer)
    end

    it 'validates approval_status enum values' do
      expect(subject).to define_enum_for(:approval_status).with_values(
        pending: "pending", approved: "approved", rejected: "rejected"
      ).backed_by_column_of_type(:string)
    end
  end

  describe 'associations' do
    # Note: Services are now a generic catalog, not tied to specific cases
    it { should have_many(:form_submissions).dependent(:destroy) }
    it { should have_many(:comments).dependent(:destroy) }
    it { should have_many(:case_documents).dependent(:destroy) }
  end

  describe 'scopes' do
    let!(:active_case) { create(:case, :active, project_id: 1) }
    let!(:draft_case) { create(:case, :draft, project_id: 1) }
    let!(:closed_case) { create(:case, :closed, project_id: 1) }
    let!(:suspended_case) { create(:case, :suspended, project_id: 1) }

    it 'filters by status correctly' do
      expect(Case.case_active).to include(active_case)
      expect(Case.case_draft).to include(draft_case)
      expect(Case.case_closed).to include(closed_case)
      expect(Case.case_suspended).to include(suspended_case)
      expect(Case.in_progress).to include(active_case, draft_case)
    end
  end

  describe 'project-based filtering' do
    let!(:project1_case) { create(:case, project_id: 1) }
    let!(:project2_case) { create(:case, project_id: 2) }

    it 'can be filtered by project_id' do
      expect(Case.where(project_id: 1)).to include(project1_case)
      expect(Case.where(project_id: 1)).not_to include(project2_case)
    end
  end

  describe 'instance methods' do
    let(:case_record) { create(:case, :active, project_id: 1) }

    it 'correctly identifies active cases' do
      expect(case_record.is_active?).to be true
    end

    it 'correctly identifies closed cases' do
      closed_case = create(:case, :closed, project_id: 1)
      expect(closed_case.is_closed?).to be true
    end

    it 'calculates duration in days' do
      case_record.update(started_at: 10.days.ago)
      expect(case_record.duration_in_days).to eq(10)
    end
  end
end
