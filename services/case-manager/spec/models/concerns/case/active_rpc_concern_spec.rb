# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Case::ActiveRpcConcern, type: :model do
  let(:case_record) { create(:case, assigned_user_id: 123, created_by_id: "550e8400-e29b-41d4-a716-446655440001") }

  describe 'ActiveRpc configuration' do
    it 'includes AtharRpc::ActiveRpc' do
      expect(Case.ancestors).to include(AtharRpc::ActiveRpc)
    end

    it 'includes Case::ActiveRpcConcern' do
      expect(Case.ancestors).to include(Case::ActiveRpcConcern)
    end

    it 'has active_rpc_config defined' do
      expect(Case).to respond_to(:active_rpc_config)
    end
  end

  describe 'assigned user methods' do
    describe '#assigned_user_name' do
      context 'when assigned_user_id is present' do
        it 'returns fallback when gRPC fails' do
          expect(case_record.assigned_user_name).to eq("User #123")
        end
      end

      context 'when assigned_user_id is nil' do
        let(:case_record) { create(:case, assigned_user_id: nil) }

        it 'returns fallback message' do
          expect(case_record.assigned_user_name).to eq("User #")
        end
      end
    end

    describe '#assigned_user_email' do
      context 'when assigned_user_id is present' do
        it 'returns nil when gRPC fails' do
          expect(case_record.assigned_user_email).to be_nil
        end
      end

      context 'when assigned_user_id is nil' do
        let(:case_record) { create(:case, assigned_user_id: nil) }

        it 'returns nil' do
          expect(case_record.assigned_user_email).to be_nil
        end
      end
    end

    describe '#assigned_user' do
      context 'when assigned_user_id is present' do
        it 'returns nil when gRPC fails' do
          expect(case_record.assigned_user).to be_nil
        end
      end

      context 'when assigned_user_id is nil' do
        let(:case_record) { create(:case, assigned_user_id: nil) }

        it 'returns nil' do
          expect(case_record.assigned_user).to be_nil
        end
      end
    end
  end

  describe 'created by user methods' do
    describe '#created_by_name' do
      context 'when created_by_id is present' do
        it 'returns fallback when gRPC fails' do
          expect(case_record.created_by_name).to eq("User #550e8400-e29b-41d4-a716-446655440001")
        end
      end

      context 'when created_by_id is nil' do
        let(:case_record) { create(:case, created_by_id: nil) }

        it 'returns fallback message' do
          expect(case_record.created_by_name).to eq("User #")
        end
      end
    end

    describe '#created_by_email' do
      context 'when created_by_id is present' do
        it 'returns nil when gRPC fails' do
          expect(case_record.created_by_email).to be_nil
        end
      end

      context 'when created_by_id is nil' do
        let(:case_record) { create(:case, created_by_id: nil) }

        it 'returns nil' do
          expect(case_record.created_by_email).to be_nil
        end
      end
    end

    describe '#created_by_user' do
      context 'when created_by_id is present' do
        it 'returns nil when gRPC fails' do
          expect(case_record.created_by_user).to be_nil
        end
      end

      context 'when created_by_id is nil' do
        let(:case_record) { create(:case, created_by_id: nil) }

        it 'returns nil' do
          expect(case_record.created_by_user).to be_nil
        end
      end
    end
  end

  describe 'user status methods' do
    describe '#assigned_user_active?' do
      it 'returns false when gRPC fails' do
        expect(case_record.assigned_user_active?).to be false
      end
    end

    describe '#created_by_user_active?' do
      it 'returns false when gRPC fails' do
        expect(case_record.created_by_user_active?).to be false
      end
    end
  end

  describe 'scopes' do
    it 'defines assigned user filtering scopes' do
      expect(Case).to respond_to(:assigned_user_name_eq)
      expect(Case).to respond_to(:assigned_user_name_cont)
      expect(Case).to respond_to(:assigned_user_email_eq)
      expect(Case).to respond_to(:assigned_user_email_cont)
    end

    it 'defines created by user filtering scopes' do
      expect(Case).to respond_to(:created_by_name_eq)
      expect(Case).to respond_to(:created_by_name_cont)
      expect(Case).to respond_to(:created_by_email_eq)
      expect(Case).to respond_to(:created_by_email_cont)
    end

    it 'defines bulk loading scope' do
      expect(Case).to respond_to(:with_user_data)
    end
  end

  describe 'Ransack integration' do
    it 'includes user scopes in ransackable_scopes' do
      scopes = Case.ransackable_scopes
      expect(scopes).to include('assigned_user_name_eq')
      expect(scopes).to include('created_by_name_cont')
    end

    it 'includes user attributes in ransackable_attributes' do
      attributes = Case.ransackable_attributes
      expect(attributes).to include('assigned_user_name')
      expect(attributes).to include('created_by_email')
    end
  end
end
