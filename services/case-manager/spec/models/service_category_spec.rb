require 'rails_helper'

RSpec.describe ServiceCategory, type: :model do
  let(:project_id) { 24 }

  describe 'validations' do
    subject { build(:service_category, project_id: project_id) }

    it { should validate_presence_of(:name) }
    it { should validate_presence_of(:name_ar) }
    it { should validate_presence_of(:sector) }
    it { should validate_presence_of(:target_group) }
  end

  describe 'enums' do
    it 'defines sector enum' do
      expect(ServiceCategory.sectors.keys).to include(
        'health', 'education', 'legal', 'social', 'financial', 'housing', 'employment'
      )
    end

    it 'defines target_group enum' do
      expect(ServiceCategory.target_groups.keys).to include(
        'children', 'women', 'men', 'elderly', 'disabled', 'all_groups'
      )
    end
  end

  describe 'scopes' do
    let!(:health_service) { create(:service_category, sector: 'health', project_id: project_id) }
    let!(:education_service) { create(:service_category, sector: 'education', project_id: project_id) }
    let!(:children_service) { create(:service_category, target_group: 'children', project_id: project_id) }

    describe '.by_sector' do
      it 'filters by sector' do
        expect(ServiceCategory.by_sector('health')).to include(health_service)
        expect(ServiceCategory.by_sector('health')).not_to include(education_service)
      end
    end

    describe '.by_target_group' do
      it 'filters by target group' do
        expect(ServiceCategory.by_target_group('children')).to include(children_service)
      end
    end
  end

  describe '#api_representation' do
    let(:service_category) { create(:service_category, project_id: project_id) }

    it 'includes sector and target_group in representation' do
      representation = service_category.api_representation
      
      expect(representation).to include(
        :id, :name, :name_ar, :active, :project_id, :display_order,
        :sector, :target_group
      )
    end
  end
end
