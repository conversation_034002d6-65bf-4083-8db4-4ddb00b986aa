module ApiDocumentation
  extend ActiveSupport::Concern

  class_methods do
    def jsonapi_with_docs(description)
      <<-HTML
        #{description}
#{'        '}
        <h3>JSON:API Specification</h3>
        <p>This endpoint follows the <a href="https://jsonapi.org/" target="_blank">JSON:API specification</a>.</p>
#{'        '}
        <h4>Response Format</h4>
        <p>Successful responses will have the following structure:</p>
        <pre><code>{
  "data": {
    "id": "1",
    "type": "resource_type",
    "attributes": {
      // Resource attributes
    },
    "relationships": {
      // Related resources
    }
  },
  "meta": {
    // Additional metadata
  },
  "included": [
    // Included related resources (if requested)
  ]
}</code></pre>

        <h4>Error Format</h4>
        <p>Error responses will have the following structure:</p>
        <pre><code>{
  "errors": [
    {
      "id": "unique_error_id",
      "status": "422",
      "code": "validation_failed",
      "title": "Validation Failed",
      "detail": "Name can't be blank",
      "source": {
        "pointer": "/data/attributes/name"
      }
    }
  ]
}</code></pre>

        <h4>Filtering</h4>
        <p>Use the <code>filter</code> parameter to filter results:</p>
        <ul>
          <li><code>filter[field_name]=value</code> - Exact match</li>
          <li><code>q[field_name_cont]=value</code> - Contains search</li>
          <li><code>q[field_name_eq]=value</code> - Exact match search</li>
        </ul>

        <h4>Sorting</h4>
        <p>Use the <code>sort</code> parameter to sort results:</p>
        <ul>
          <li><code>sort=field_name</code> - Ascending order</li>
          <li><code>sort=-field_name</code> - Descending order</li>
          <li><code>sort=field1,-field2</code> - Multiple fields</li>
        </ul>

        <h4>Pagination</h4>
        <p>Use pagination parameters to control result sets:</p>
        <ul>
          <li><code>page[number]=1</code> - Page number (default: 1)</li>
          <li><code>page[size]=25</code> - Items per page (default: 25, max: 100)</li>
        </ul>

        <h4>Including Related Resources</h4>
        <p>Use the <code>include</code> parameter to include related resources:</p>
        <ul>
          <li><code>include=relationship_name</code> - Include single relationship</li>
          <li><code>include=rel1,rel2</code> - Include multiple relationships</li>
        </ul>

        <h4>Sparse Fieldsets</h4>
        <p>Use the <code>fields</code> parameter to request specific fields:</p>
        <ul>
          <li><code>fields[resource_type]=field1,field2</code> - Only include specified fields</li>
        </ul>

        <h4>Authentication</h4>
        <p>All endpoints require authentication via Bearer token in the Authorization header:</p>
        <pre><code>Authorization: Bearer your_jwt_token_here</code></pre>

        <h4>Rate Limiting</h4>
        <p>API requests are rate limited. Check response headers for current limits:</p>
        <ul>
          <li><code>X-RateLimit-Limit</code> - Request limit per window</li>
          <li><code>X-RateLimit-Remaining</code> - Remaining requests in current window</li>
          <li><code>X-RateLimit-Reset</code> - Time when the rate limit resets</li>
        </ul>
      HTML
    end

    def standard_error_responses
      error code: 401, desc: "Unauthorized - Invalid or missing authentication token"
      error code: 403, desc: "Forbidden - Insufficient permissions for this action"
      error code: 404, desc: "Not Found - The requested resource does not exist"
      error code: 422, desc: "Unprocessable Entity - Validation errors in request data"
      error code: 429, desc: "Too Many Requests - Rate limit exceeded"
      error code: 500, desc: "Internal Server Error - An unexpected error occurred"
    end

    def collection_responses
      returns code: 200, desc: "Successful response with collection of resources"
      standard_error_responses
    end

    def resource_responses
      returns code: 200, desc: "Successful response with single resource"
      standard_error_responses
    end

    def create_responses
      returns code: 201, desc: "Resource created successfully"
      standard_error_responses
    end

    def update_responses
      returns code: 200, desc: "Resource updated successfully"
      standard_error_responses
    end

    def delete_responses
      returns code: 204, desc: "Resource deleted successfully"
      standard_error_responses
    end
  end
end
