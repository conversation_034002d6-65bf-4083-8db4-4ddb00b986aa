class Api::LookupBaseController < ApplicationController
  before_action :set_lookup_item, only: [:show, :update, :destroy]
  before_action :set_project_scope

  # GET /api/{lookup_resource}
  def index
    @lookup_items = lookup_model.active.for_project(current_project_id).ordered
    
    # Apply filtering if provided
    @lookup_items = @lookup_items.where("name ILIKE ?", "%#{params[:search]}%") if params[:search].present?
    
    render json: {
      data: @lookup_items.map(&:api_representation),
      meta: {
        total: @lookup_items.count,
        search: params[:search]
      }
    }
  end

  # GET /api/{lookup_resource}/1
  def show
    render json: { data: @lookup_item.api_representation }
  end

  # POST /api/{lookup_resource}
  def create
    @lookup_item = lookup_model.new(lookup_params)
    @lookup_item.project_id = current_project_id unless params[:global] == 'true'

    if @lookup_item.save
      render json: { data: @lookup_item.api_representation }, status: :created
    else
      render json: { errors: @lookup_item.errors }, status: :unprocessable_entity
    end
  end

  # PATCH/PUT /api/{lookup_resource}/1
  def update
    if @lookup_item.update(lookup_params)
      render json: { data: @lookup_item.api_representation }
    else
      render json: { errors: @lookup_item.errors }, status: :unprocessable_entity
    end
  end

  # DELETE /api/{lookup_resource}/1
  def destroy
    @lookup_item.update(active: false)  # Soft delete
    head :no_content
  end

  private

  def set_lookup_item
    @lookup_item = lookup_model.find(params[:id])
  end

  def set_project_scope
    # Ensure user has access to the project
    unless current_project_id
      render json: { error: 'Project context required' }, status: :bad_request
    end
  end

  def current_project_id
    # This should be set by the application controller based on user context
    params[:project_id] || current_user&.project_id
  end

  def lookup_model
    raise NotImplementedError, "Subclasses must implement lookup_model"
  end

  def lookup_params
    raise NotImplementedError, "Subclasses must implement lookup_params"
  end
end
