module Api
  class CaseDocumentsController < ApplicationController
    include AtharAuth::ResourceAuthorization

    before_action :authenticate_session!
    authorize_resources

    api! "Lists case documents"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :case_id, String, desc: "Filter by case ID"
    param :document_type, String, desc: "Filter by document type (assessment, consent, identification, etc.)"
    param :file_type, String, desc: "Filter by file type (pdf, image, document)"
    param_group :pagination_params
    param_group :include_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Returns a list of documents attached to cases.
      Supports filtering by case, document type, and file type.
      Requires permission: <code>:read, :case_document</code>.
    HTML
    )
    returns code: 200, desc: "List of case documents"

    def index
      apply_filters(@case_documents) do |filtered_and_sorted|
        records, meta = paginate(filtered_and_sorted)
        serialize_response(records, meta: meta)
      end
    end

    api! "Retrieves a specific case document"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the case document"
    param_group :include_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Fetches detailed information about a specific case document by ID.
      Includes file metadata and download information.
      Requires permission: <code>:read, :case_document</code>.
    HTML
    )
    returns code: 200, desc: "Case document details"

    def show
      serialize_response(@case_document)
    end

    api! "Uploads a new case document"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :case_document, Hash, required: true, desc: "Document details" do
      param :case_id, String, required: true, desc: "ID of the case"
      param :file, File, required: true, desc: "File to upload"
      param :document_type, String, required: true, desc: "Type of document (assessment, consent, identification, medical, legal, photo, other)"
      param :description, String, desc: "Description of the document"
    end
    param_group :include_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Uploads a new document and attaches it to a case.
      Validates file type and size restrictions.
      Requires permission: <code>:create, :case_document</code>.
    HTML
    )
    returns code: 201, desc: "Document uploaded successfully"
    error code: 422, desc: "Validation errors or file upload errors"

    def create
      @case_document = CaseDocument.new(create_params)
      @case_document.uploaded_by = current_user

      # Handle file upload
      if params[:case_document][:file].present?
        file = params[:case_document][:file]
        @case_document.file_name = file.original_filename
        @case_document.file_size = file.size
        @case_document.file_type = determine_file_type(file)
        
        # Attach file using Active Storage
        @case_document.attachment.attach(file)
      end

      if @case_document.save
        serialize_response(@case_document, status: :created)
      else
        serialize_errors(@case_document.errors)
      end
    end

    api :PUT, "/case_documents/:id", "Updates an existing case document"
    api :PATCH, "/case_documents/:id", "Partially updates a case document"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the case document"
    param :case_document, Hash, required: true, desc: "Updated document fields" do
      param :document_type, String, desc: "Type of document (assessment, consent, identification, medical, legal, photo, other)"
      param :description, String, desc: "Description of the document"
    end
    param_group :include_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Updates an existing case document's metadata.
      File replacement requires deleting and re-uploading.
      Requires permission: <code>:update, :case_document</code>.
    HTML
    )
    returns code: 200, desc: "Document updated successfully"
    error code: 422, desc: "Validation errors"

    def update
      if @case_document.update(update_params)
        serialize_response(@case_document.reload)
      else
        serialize_errors(@case_document.errors)
      end
    end

    api! "Deletes a case document"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the case document"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Deletes a case document and its associated file.
      This action cannot be undone.
      Requires permission: <code>:destroy, :case_document</code>.
    HTML
    )
    returns code: 204, desc: "Document deleted successfully"

    def destroy
      @case_document.destroy!
      head :no_content
    end

    api! "Download case document file"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the case document"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Downloads the actual file content of a case document.
      Returns the file with appropriate content-type headers.
      Requires permission: <code>:read, :case_document</code>.
    HTML
    )
    returns code: 200, desc: "File content"
    error code: 404, desc: "File not found"

    def download
      unless @case_document.attachment.attached?
        return render json: { error: 'File not found' }, status: :not_found
      end

      # Generate a secure download URL or serve the file directly
      if Rails.env.production?
        # In production, redirect to signed URL for direct download from cloud storage
        redirect_to @case_document.attachment.url, allow_other_host: true
      else
        # In development, serve file directly through Rails
        send_data @case_document.attachment.download,
                  filename: @case_document.file_name,
                  type: @case_document.attachment.content_type,
                  disposition: 'attachment'
      end
    end

    api! "Get document preview/thumbnail"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the case document"
    param :size, String, desc: "Preview size (thumbnail, small, medium)"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Returns a preview or thumbnail of the document if supported.
      Works for images and PDFs with preview generation.
      Requires permission: <code>:read, :case_document</code>.
    HTML
    )
    returns code: 200, desc: "Preview image"
    error code: 404, desc: "Preview not available"

    def preview
      size = params[:size] || 'thumbnail'
      
      if @case_document.can_generate_preview?
        preview_variant = @case_document.attachment.variant(resize_to_limit: preview_dimensions(size))
        redirect_to preview_variant, allow_other_host: true
      else
        render json: { error: 'Preview not available for this file type' }, status: :not_found
      end
    end

    private

    def create_params
      params.require(:case_document).permit(
        :case_id,
        :document_type,
        :description
      )
    end

    def update_params
      params.fetch(:case_document, {}).permit(
        :document_type,
        :description
      )
    end

    def determine_file_type(file)
      content_type = file.content_type
      case content_type
      when /^image\//
        'image'
      when 'application/pdf'
        'pdf'
      when /^application\/(msword|vnd\.openxmlformats-officedocument\.wordprocessingml\.document)$/
        'document'
      when /^application\/(vnd\.ms-excel|vnd\.openxmlformats-officedocument\.spreadsheetml\.sheet)$/
        'spreadsheet'
      else
        'other'
      end
    end

    def preview_dimensions(size)
      case size
      when 'thumbnail'
        [150, 150]
      when 'small'
        [300, 300]
      when 'medium'
        [600, 600]
      else
        [150, 150]
      end
    end
  end
end
