# frozen_string_literal: true

module Api
  class MeController < ApplicationController
    include AtharAuth::ResourceAuthorization

    before_action :authenticate_session!

    api! "Returns current user information"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param_group :include_params
    param_group :fields_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Returns the current authenticated user's information within the case management context.
      Follows JSON:API specification and includes user data with case management context.

      <b>Data Included:</b>
      <ul>
        <li><strong>User Profile:</strong> Basic user data, role information</li>
        <li><strong>Project Context:</strong> Project assignment and global user status</li>
        <li><strong>Role Information:</strong> Current role and role-specific flags</li>
        <li><strong>Access Control:</strong> Role-based access determination</li>
      </ul>

      <b>Authentication:</b>
      Requires a valid session token. Users must have case management access.

      <b>User Types:</b>
      <ul>
        <li><strong>Case Manager:</strong> Standard case management access</li>
        <li><strong>Supervisor:</strong> Enhanced case management access</li>
        <li><strong>Psychologist:</strong> Same access as case manager</li>
      </ul>

      <b>Response Type:</b>
      Returns a User resource with case management context.
    HTML
    )
    returns code: 200, desc: "Current user information in JSON:API format"
    error code: 403, desc: "Access denied - user has no access to case management system"
    error code: 401, desc: "Authentication required"

    def show
      # Check if user has any CM permissions
      unless has_cm_access?
        serialize_errors({ detail: "User has no access to the case management system" }, :forbidden)
        return
      end

      serialize_response(current_user)
    end

    private

    def has_cm_access?
      # Only specific roles are allowed in the case management system
      allowed_roles = %w[case_manager supervisor psychologist]
      allowed_roles.include?(current_user.role&.name)
    end
  end
end
