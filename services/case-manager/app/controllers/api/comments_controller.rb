module Api
  class CommentsController < ApplicationController
    include AtharAuth::ResourceAuthorization

    before_action :authenticate_session!
    authorize_resources

    api! "Lists comments"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :case_id, String, desc: "Filter by case ID"
    param :commentable_type, String, desc: "Filter by commentable type (Case, FormField)"
    param :commentable_id, Integer, desc: "Filter by commentable ID"
    param :status, String, desc: "Filter by status (open, resolved)"
    param_group :pagination_params
    param_group :include_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Returns a list of comments for cases or form fields.
      Supports filtering by case, commentable object, and status.
      Requires permission: <code>:read, :comment</code>.
    HTML
    )
    returns code: 200, desc: "List of comments"

    def index
      apply_filters(@comments) do |filtered_and_sorted|
        records, meta = paginate(filtered_and_sorted)
        serialize_response(records, meta: meta)
      end
    end

    api! "Retrieves a specific comment"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the comment"
    param_group :include_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Fetches detailed information about a specific comment by ID.
      Includes thread information if comment is part of a conversation.
      Requires permission: <code>:read, :comment</code>.
    HTML
    )
    returns code: 200, desc: "Comment details"

    def show
      serialize_response(@comment)
    end

    api! "Creates a new comment"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :comment, Hash, required: true, desc: "Comment details" do
      param :case_id, String, required: true, desc: "ID of the case"
      param :commentable_type, String, required: true, desc: "Type of object being commented on (Case, FormField)"
      param :commentable_id, Integer, required: true, desc: "ID of the object being commented on"
      param :content, String, required: true, desc: "Comment content"
      param :comment_type, String, desc: "Type of comment (note, question, concern, suggestion)"
      param :parent_comment_id, Integer, desc: "ID of parent comment for threaded replies"
    end
    param_group :include_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Creates a new comment on a case or form field.
      Supports threaded comments through parent_comment_id.
      Requires permission: <code>:create, :comment</code>.
    HTML
    )
    returns code: 201, desc: "Comment created successfully"
    error code: 422, desc: "Validation errors"

    def create
      @comment = Comment.new(create_params)
      @comment.user = current_user

      if @comment.save
        serialize_response(@comment, status: :created)
      else
        serialize_errors(@comment.errors)
      end
    end

    api :PUT, "/comments/:id", "Updates an existing comment"
    api :PATCH, "/comments/:id", "Partially updates a comment"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the comment"
    param :comment, Hash, required: true, desc: "Updated comment fields" do
      param :content, String, desc: "Updated comment content"
      param :comment_type, String, desc: "Type of comment (note, question, concern, suggestion)"
    end
    param_group :include_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Updates an existing comment's content or type.
      Only the comment author or supervisors can update comments.
      Requires permission: <code>:update, :comment</code>.
    HTML
    )
    returns code: 200, desc: "Comment updated successfully"
    error code: 422, desc: "Validation errors"

    def update
      # Only allow author or supervisors to update comments
      unless can_modify_comment?(@comment)
        return render json: { error: 'Access denied' }, status: :forbidden
      end

      if @comment.update(update_params)
        serialize_response(@comment.reload)
      else
        serialize_errors(@comment.errors)
      end
    end

    api! "Resolve a comment"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the comment"
    param :resolution_note, String, desc: "Optional note about the resolution"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Marks a comment as resolved with optional resolution note.
      Only supervisors or the comment author can resolve comments.
      Requires permission: <code>:update, :comment</code>.
    HTML
    )
    returns code: 200, desc: "Comment resolved successfully"
    error code: 422, desc: "Cannot resolve comment"

    def resolve
      unless can_resolve_comment?(@comment)
        return render json: { error: 'Access denied to resolve comment' }, status: :forbidden
      end

      if @comment.resolve!(current_user, params[:resolution_note])
        render json: {
          message: 'Comment resolved successfully',
          comment_id: @comment.id,
          status: @comment.status,
          resolved_at: @comment.resolved_at,
          resolved_by: @comment.resolved_by&.name
        }
      else
        serialize_errors(@comment.errors)
      end
    end

    api! "Reopen a resolved comment"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the comment"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Reopens a previously resolved comment.
      Only supervisors can reopen resolved comments.
      Requires permission: <code>:update, :comment</code>.
    HTML
    )
    returns code: 200, desc: "Comment reopened successfully"

    def reopen
      unless current_user.supervisor?
        return render json: { error: 'Only supervisors can reopen comments' }, status: :forbidden
      end

      if @comment.reopen!
        render json: {
          message: 'Comment reopened successfully',
          comment_id: @comment.id,
          status: @comment.status
        }
      else
        serialize_errors(@comment.errors)
      end
    end

    api! "Get comment thread"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the root comment"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Returns the complete comment thread starting from the specified comment.
      Includes all replies and nested conversations.
      Requires permission: <code>:read, :comment</code>.
    HTML
    )
    returns code: 200, desc: "Comment thread"

    def thread
      thread_comments = @comment.thread_comments
      render json: {
        root_comment: @comment,
        thread_comments: thread_comments,
        total_replies: thread_comments.count
      }
    end

    private

    def create_params
      params.require(:comment).permit(
        :case_id,
        :commentable_type,
        :commentable_id,
        :content,
        :comment_type,
        :parent_comment_id
      )
    end

    def update_params
      params.fetch(:comment, {}).permit(
        :content,
        :comment_type
      )
    end

    def can_modify_comment?(comment)
      return true if current_user.supervisor?
      return true if comment.user_id == current_user.id
      false
    end

    def can_resolve_comment?(comment)
      return true if current_user.supervisor?
      return true if comment.user_id == current_user.id
      false
    end
  end
end
