class Api::ServiceCategoriesController < Api::LookupBaseController
  # Additional filtering by sector and target group
  def index
    @lookup_items = lookup_model.active.for_project(current_project_id).ordered
    
    # Apply filtering
    @lookup_items = @lookup_items.where("name ILIKE ?", "%#{params[:search]}%") if params[:search].present?
    @lookup_items = @lookup_items.where(sector: params[:sector]) if params[:sector].present?
    @lookup_items = @lookup_items.where(target_group: params[:target_group]) if params[:target_group].present?
    
    render json: {
      data: @lookup_items.map(&:api_representation),
      meta: {
        total: @lookup_items.count,
        search: params[:search],
        sector: params[:sector],
        target_group: params[:target_group]
      }
    }
  end

  private

  def lookup_model
    ServiceCategory
  end

  def lookup_params
    params.require(:service_category).permit(:name, :name_ar, :sector, :target_group, :active, :display_order)
  end
end
