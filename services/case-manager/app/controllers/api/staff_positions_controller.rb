class Api::StaffPositionsController < Api::LookupBaseController
  # Additional filtering by department
  def index
    @lookup_items = lookup_model.active.for_project(current_project_id).ordered
    
    # Apply filtering
    @lookup_items = @lookup_items.where("name ILIKE ?", "%#{params[:search]}%") if params[:search].present?
    @lookup_items = @lookup_items.by_department(params[:department]) if params[:department].present?
    
    render json: {
      data: @lookup_items.map(&:api_representation),
      meta: {
        total: @lookup_items.count,
        search: params[:search],
        department: params[:department]
      }
    }
  end

  private

  def lookup_model
    StaffPosition
  end

  def lookup_params
    params.require(:staff_position).permit(:name, :name_ar, :code, :department, :active, :display_order)
  end
end
