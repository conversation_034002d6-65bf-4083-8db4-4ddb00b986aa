class Api::GeographicLocationsController < Api::LookupBaseController
  # GET /api/geographic_locations/1/children
  def children
    @location = GeographicLocation.find(params[:id])
    @children = @location.child_locations.active.ordered
    
    render json: {
      data: @children.map(&:api_representation),
      meta: {
        parent: @location.api_representation,
        total: @children.count
      }
    }
  end

  # Additional filtering by location type and parent
  def index
    @lookup_items = lookup_model.active.for_project(current_project_id).ordered
    
    # Apply filtering
    @lookup_items = @lookup_items.where("name ILIKE ?", "%#{params[:search]}%") if params[:search].present?
    @lookup_items = @lookup_items.where(location_type: params[:location_type]) if params[:location_type].present?
    @lookup_items = @lookup_items.children_of(params[:parent_id]) if params[:parent_id].present?
    
    render json: {
      data: @lookup_items.map(&:api_representation),
      meta: {
        total: @lookup_items.count,
        search: params[:search],
        location_type: params[:location_type],
        parent_id: params[:parent_id]
      }
    }
  end

  private

  def lookup_model
    GeographicLocation
  end

  def lookup_params
    params.require(:geographic_location).permit(:name, :name_ar, :location_type, :iso_code, :parent_location_id, :active, :display_order)
  end
end
