module Api
  class CasesController < ApplicationController
    include AtharAuth::ResourceAuthorization

    before_action :authenticate_session!

    authorize_resources

    api! "Lists all cases"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param_group :pagination_params
    param_group :filter_params
    param_group :sort_params
    param_group :include_params
    param_group :fields_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Returns a list of all cases.
      Supports filtering, sorting, and pagination.
      Requires permission: <code>:read, :case</code>.
    HTML
    )
    returns code: 200, desc: "List of cases"

    def index
      # ✅ @cases is automatically prepared by authorize_resources with project filtering
      apply_filters(@cases) do |filtered_and_sorted|
        records, meta = paginate(filtered_and_sorted)
        serialize_response(records, meta: meta)
      end
    end

    api! "Retrieves a specific case"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the case"
    param_group :include_params
    param_group :fields_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Fetches detailed information about a specific case by ID.
      Requires permission: <code>:read, :case</code>.
    HTML
    )
    returns code: 200, desc: "Case details"

    def show
      # ✅ @case is automatically prepared by authorize_resources with project validation
      serialize_response(@case)
    end

    api! "Creates a new case"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :case, Hash, required: true, desc: "Case details" do
      param :assigned_user_id, String, required: true, desc: "ID of the assigned user"
      param :case_type, String, required: true, desc: "Case type (general, protection, child_protection, gbv, education, health, livelihood)"
      param :priority_level, Integer, desc: "Priority level (1-5, where 5 is critical)"
      param :confidentiality_level, Integer, desc: "Confidentiality level (0=public, 1=restricted, 2=confidential)"
      param :started_at, DateTime, desc: "Case start date and time"
    end
    param_group :include_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Creates a new case record with progressive form completion workflow.
      Links a beneficiary with a case manager and sets up initial form submissions.
      Requires permission: <code>:create, :case</code>.
    HTML
    )
    returns code: 201, desc: "Case created successfully"
    error code: 422, desc: "Validation errors"

    def create
      # ✅ Authorization checked by authorize_resources, now create new case
      @case = Case.new(create_params)
      @case.project_id = current_project&.id if current_project
      @case.created_by = current_user

      if @case.save
        serialize_response(@case, status: :created)
      else
        serialize_errors(@case.errors)
      end
    end

    api :PUT, "/cases/:id", "Updates an existing case"
    api :PATCH, "/cases/:id", "Partially updates a case"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the case"
    param :case, Hash, required: true, desc: "Updated case fields" do
      param :assigned_user_id, String, desc: "ID of the assigned user"
      param :case_type, String, desc: "Case type (general, protection, child_protection, gbv, education, health, livelihood)"
      param :priority_level, Integer, desc: "Priority level (1-5, where 5 is critical)"
      param :confidentiality_level, Integer, desc: "Confidentiality level (0=public, 1=restricted, 2=confidential)"
      param :started_at, DateTime, desc: "Case start date and time"
      param :closed_at, DateTime, desc: "Case closure date and time"
    end
    param_group :include_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Updates an existing case's information.
      Note: beneficiary_id cannot be updated through this endpoint.
      Requires permission: <code>:update, :case</code>.
    HTML
    )
    returns code: 200, desc: "Case updated successfully"
    error code: 422, desc: "Validation errors"

    def update
      # ✅ @case is automatically prepared by authorize_resources with project validation
      if @case.update(update_params)
        serialize_response(@case.reload)
      else
        serialize_errors(@case.errors)
      end
    end

    api! "Deletes a case"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the case"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Deletes a case by ID.
      Requires permission: <code>:destroy, :case</code>.
    HTML
    )
    returns code: 204, desc: "Case deleted successfully"

    def destroy
      # ✅ @case is automatically prepared by authorize_resources with project validation
      @case.destroy!
      head :no_content
    end

    api! "Submit case for approval"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the case"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Submits a case for approval after all required forms are completed.
      Validates that all prerequisite forms are completed before submission.
      Requires permission: <code>:update, :case</code>.
    HTML
    )
    returns code: 200, desc: "Case submitted for approval successfully"
    error code: 422, desc: "Case cannot be submitted (forms incomplete or validation errors)"

    def submit_for_approval
      # ✅ @case is automatically prepared by authorize_resources with project validation
      unless @case.can_submit_for_approval?
        return render json: {
          error: "Case cannot be submitted for approval",
          details: "All required forms must be completed before submission",
          form_completion_status: @case.form_completion_summary
        }, status: :unprocessable_entity
      end

      begin
        @case.submit_for_approval!(current_user)
        render json: {
          message: "Case submitted for approval successfully",
          case_id: @case.id,
          case_number: @case.case_number,
          status: @case.status,
          form_completion_status: @case.form_completion_summary
        }
      rescue StandardError => e
        render json: {
          error: "Failed to submit case for approval",
          details: e.message
        }, status: :unprocessable_entity
      end
    end

    api! "Get case form completion progress"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the case"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Returns detailed form completion progress for a case.
      Shows which forms are completed, in progress, or not started.
      Requires permission: <code>:read, :case</code>.
    HTML
    )
    returns code: 200, desc: "Form completion progress"

    def progress
      # ✅ @case is automatically prepared by authorize_resources with project validation
      render json: {
        case_id: @case.id,
        case_number: @case.case_number,
        status: @case.status,
        overall_completion_percentage: @case.overall_completion_percentage,
        can_submit_for_approval: @case.can_submit_for_approval?,
        next_required_form: @case.next_required_form&.name,
        form_completion_summary: @case.form_completion_summary
      }
    end

    private

    # ✅ REMOVED: set_case method no longer needed - authorize_resources handles resource loading

    def create_params
      params.require(:case).permit(
        :assigned_user_id,
        :case_type,
        :priority_level,
        :confidentiality_level,
        :started_at
      )
    end

    def update_params
      params.fetch(:case, {}).permit(
        :assigned_user_id,
        :case_type,
        :priority_level,
        :confidentiality_level,
        :started_at,
        :closed_at
      )
    end

    # ✅ REMOVED: All authorization methods replaced by authorize_resources
    # The authorize_resources method automatically handles:
    # - Permission checking (can?(:action, :case))
    # - Project-based filtering for collections
    # - Resource loading with project validation
    # - Standard error responses (401/403)
    # - Instance variable preparation (@cases, @case)
  end
end
