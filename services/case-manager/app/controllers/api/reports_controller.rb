module Api
  class ReportsController < ApplicationController
    include AtharAuth::ResourceAuthorization

    before_action :authenticate_session!

    api! "Generate a report"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :report_type, String, desc: "Type of report: 'case_summary', 'beneficiary_demographics', 'service_delivery', 'workflow_performance', 'user_activity', 'form_completion', 'team_performance'"
    param :format, String, desc: "Export format: 'json', 'csv', 'pdf'"
    param :date_range, String, desc: "Date range: 'this_month', 'last_month', 'this_year', 'custom'"
    param :start_date, String, desc: "Start date for custom range (YYYY-MM-DD)"
    param :end_date, String, desc: "End date for custom range (YYYY-MM-DD)"
    param :filters, Hash, desc: "Additional filters for the report"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Generates comprehensive reports based on case management data.
      Supports multiple report types and export formats.
      Requires permission: <code>:read, :reports</code>.

      <b>Report Types:</b>
      - case_summary: Overview of cases with status, type, and priority breakdowns
      - beneficiary_demographics: Demographics analysis of beneficiaries
      - service_delivery: Service delivery statistics and performance
      - workflow_performance: Workflow efficiency and bottleneck analysis
      - user_activity: Team member activity and performance
      - form_completion: Form completion rates and analytics
      - team_performance: Team-wide performance metrics (supervisors only)

      <b>Export Formats:</b>
      - json: Structured JSON data (default)
      - csv: Comma-separated values for spreadsheet import
      - pdf: Formatted PDF document
    HTML
    )
    returns code: 200, desc: "Generated report data"

    def create
      unless current_user.can?(:read, :reports)
        return render json: { error: 'Access denied' }, status: :forbidden
      end

      generator = ReportGenerator.new(
        report_type: params[:report_type],
        format: params[:format] || 'json',
        date_range: parse_date_range,
        filters: params[:filters],
        user: current_user
      )

      unless generator.valid?
        return render json: { 
          error: 'Invalid report parameters', 
          details: generator.errors.full_messages 
        }, status: :unprocessable_entity
      end

      report_data = generator.generate

      if report_data
        render json: {
          report: report_data,
          meta: {
            generated_at: Time.current,
            user_id: current_user.id,
            report_type: params[:report_type],
            format: params[:format] || 'json'
          }
        }
      else
        render json: { error: 'Failed to generate report' }, status: :internal_server_error
      end
    end

    api! "Get available report types"
    header "Authorization", "Scoped session token as Bearer token", required: true
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Returns list of available report types and their descriptions.
      Helps frontend applications build report generation interfaces.
      Requires permission: <code>:read, :reports</code>.
    HTML
    )
    returns code: 200, desc: "Available report types"

    def types
      unless current_user.can?(:read, :reports)
        return render json: { error: 'Access denied' }, status: :forbidden
      end

      report_types = [
        {
          type: 'case_summary',
          name: 'Case Summary Report',
          description: 'Overview of cases with status, type, and priority breakdowns',
          available_to: ['case_manager', 'supervisor', 'project_manager']
        },
        {
          type: 'beneficiary_demographics',
          name: 'Beneficiary Demographics Report',
          description: 'Demographics analysis of beneficiaries',
          available_to: ['case_manager', 'supervisor', 'project_manager']
        },
        {
          type: 'service_delivery',
          name: 'Service Delivery Report',
          description: 'Service delivery statistics and performance',
          available_to: ['case_manager', 'supervisor', 'project_manager']
        },
        {
          type: 'workflow_performance',
          name: 'Workflow Performance Report',
          description: 'Workflow efficiency and bottleneck analysis',
          available_to: ['supervisor', 'project_manager']
        },
        {
          type: 'user_activity',
          name: 'User Activity Report',
          description: 'Team member activity and performance',
          available_to: ['supervisor', 'project_manager']
        },
        {
          type: 'form_completion',
          name: 'Form Completion Report',
          description: 'Form completion rates and analytics',
          available_to: ['case_manager', 'supervisor', 'project_manager']
        },
        {
          type: 'team_performance',
          name: 'Team Performance Report',
          description: 'Team-wide performance metrics',
          available_to: ['supervisor', 'project_manager']
        }
      ]

      # Filter reports based on user role
      user_role = current_user.role&.name
      available_reports = report_types.select do |report|
        report[:available_to].include?(user_role)
      end

      render json: {
        report_types: available_reports,
        export_formats: ReportGenerator::EXPORT_FORMATS,
        date_range_options: [
          { value: 'this_month', label: 'This Month' },
          { value: 'last_month', label: 'Last Month' },
          { value: 'this_year', label: 'This Year' },
          { value: 'custom', label: 'Custom Range' }
        ]
      }
    end

    api! "Get report templates"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :report_type, String, desc: "Report type to get template for"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Returns report template structure and available filters for a specific report type.
      Helps frontend applications build dynamic report configuration forms.
      Requires permission: <code>:read, :reports</code>.
    HTML
    )
    returns code: 200, desc: "Report template structure"

    def template
      unless current_user.can?(:read, :reports)
        return render json: { error: 'Access denied' }, status: :forbidden
      end

      report_type = params[:report_type]
      
      unless ReportGenerator::REPORT_TYPES.include?(report_type)
        return render json: { error: 'Invalid report type' }, status: :bad_request
      end

      template_data = case report_type
                     when 'case_summary'
                       case_summary_template
                     when 'beneficiary_demographics'
                       demographics_template
                     when 'service_delivery'
                       service_delivery_template
                     when 'workflow_performance'
                       workflow_performance_template
                     when 'user_activity'
                       user_activity_template
                     when 'form_completion'
                       form_completion_template
                     when 'team_performance'
                       team_performance_template
                     else
                       { message: 'Template not available for this report type' }
                     end

      render json: {
        report_type: report_type,
        template: template_data
      }
    end

    private

    def parse_date_range
      return nil unless params[:date_range].present?

      case params[:date_range]
      when 'this_month'
        Date.current.beginning_of_month..Date.current
      when 'last_month'
        1.month.ago.beginning_of_month..1.month.ago.end_of_month
      when 'this_year'
        Date.current.beginning_of_year..Date.current
      when 'custom'
        start_date = Date.parse(params[:start_date]) if params[:start_date]
        end_date = Date.parse(params[:end_date]) if params[:end_date]
        start_date && end_date ? (start_date..end_date) : nil
      else
        nil
      end
    rescue Date::Error
      nil
    end

    def case_summary_template
      {
        available_filters: {
          status: Case.statuses.keys,
          case_type: Case.case_types.keys,
          priority_level: (1..5).to_a
        },
        sample_structure: {
          title: "Case Summary Report",
          summary: {
            total_cases: "Number",
            cases_by_status: "Hash",
            cases_by_type: "Hash",
            cases_by_priority: "Hash",
            completion_rate: "Percentage"
          },
          cases: "Array of case summaries"
        }
      }
    end

    def demographics_template
      {
        available_filters: {
          age_range: ['0-17', '18-59', '60+'],
          gender: ['male', 'female', 'other'],
          nationality: 'Dynamic based on data'
        },
        sample_structure: {
          title: "Beneficiary Demographics Report",
          summary: {
            total_beneficiaries: "Number",
            age_distribution: "Hash",
            gender_distribution: "Hash",
            nationality_distribution: "Hash"
          }
        }
      }
    end

    def service_delivery_template
      {
        available_filters: {
          service_type: 'Dynamic based on services',
          service_status: ['pending', 'in_progress', 'completed', 'cancelled']
        },
        sample_structure: {
          title: "Service Delivery Report",
          summary: {
            total_services: "Number",
            services_by_type: "Hash",
            services_by_status: "Hash",
            average_service_duration: "Number (days)"
          }
        }
      }
    end

    def workflow_performance_template
      {
        available_filters: {
          workflow_stage: ['draft', 'in_progress', 'pending_approval', 'completed']
        },
        sample_structure: {
          title: "Workflow Performance Report",
          summary: {
            average_case_duration: "Number (days)",
            form_completion_rates: "Hash",
            approval_processing_time: "Number (days)",
            bottlenecks: "Array"
          }
        }
      }
    end

    def user_activity_template
      return {} unless current_user.supervisor? || current_user.project_manager?

      {
        available_filters: {
          user_role: ['case_manager', 'supervisor'],
          activity_type: ['case_creation', 'form_submission', 'approval']
        },
        sample_structure: {
          title: "User Activity Report",
          summary: {
            total_team_members: "Number",
            active_users: "Number",
            average_cases_per_user: "Number"
          },
          user_activities: "Array of user activity summaries"
        }
      }
    end

    def form_completion_template
      {
        available_filters: {
          form_template: FormTemplate.pluck(:name),
          completion_status: ['draft', 'in_progress', 'completed']
        },
        sample_structure: {
          title: "Form Completion Report",
          summary: {
            total_forms: "Number",
            completion_rate: "Percentage",
            average_completion_time: "Number (days)"
          },
          form_analytics: "Hash with detailed analytics"
        }
      }
    end

    def team_performance_template
      return {} unless current_user.supervisor? || current_user.project_manager?

      {
        available_filters: {
          team_member: 'Dynamic based on team',
          performance_metric: ['cases_completed', 'efficiency_score', 'workload']
        },
        sample_structure: {
          title: "Team Performance Report",
          team_metrics: "Array of team member metrics",
          performance_trends: "Hash with trend data",
          efficiency_metrics: "Hash with efficiency calculations"
        }
      }
    end
  end
end
