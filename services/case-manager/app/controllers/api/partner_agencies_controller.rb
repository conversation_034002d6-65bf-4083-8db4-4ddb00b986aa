class Api::PartnerAgenciesController < Api::LookupBaseController
  # Additional filtering by agency type
  def index
    @lookup_items = lookup_model.active.for_project(current_project_id).ordered
    
    # Apply filtering
    @lookup_items = @lookup_items.where("name ILIKE ?", "%#{params[:search]}%") if params[:search].present?
    @lookup_items = @lookup_items.by_type(params[:agency_type]) if params[:agency_type].present?
    
    render json: {
      data: @lookup_items.map(&:api_representation),
      meta: {
        total: @lookup_items.count,
        search: params[:search],
        agency_type: params[:agency_type]
      }
    }
  end

  private

  def lookup_model
    PartnerAgency
  end

  def lookup_params
    params.require(:partner_agency).permit(:name, :name_ar, :agency_type, :contact_email, :active, :display_order)
  end
end
