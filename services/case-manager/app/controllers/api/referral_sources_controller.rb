class Api::ReferralSourcesController < Api::LookupBaseController
  # Additional filtering by source type
  def index
    @lookup_items = lookup_model.active.for_project(current_project_id).ordered
    
    # Apply filtering
    @lookup_items = @lookup_items.where("name ILIKE ?", "%#{params[:search]}%") if params[:search].present?
    @lookup_items = @lookup_items.where(source_type: params[:source_type]) if params[:source_type].present?
    
    render json: {
      data: @lookup_items.map(&:api_representation),
      meta: {
        total: @lookup_items.count,
        search: params[:search],
        source_type: params[:source_type]
      }
    }
  end

  private

  def lookup_model
    ReferralSource
  end

  def lookup_params
    params.require(:referral_source).permit(:name, :name_ar, :source_type, :active, :display_order)
  end
end
