class Api::CaseTypesController < Api::LookupBaseController
  # Additional filtering by category
  def index
    @lookup_items = lookup_model.active.for_project(current_project_id).ordered
    
    # Apply filtering
    @lookup_items = @lookup_items.where("name ILIKE ?", "%#{params[:search]}%") if params[:search].present?
    @lookup_items = @lookup_items.where(category: params[:category]) if params[:category].present?
    
    render json: {
      data: @lookup_items.map(&:api_representation),
      meta: {
        total: @lookup_items.count,
        search: params[:search],
        category: params[:category]
      }
    }
  end

  private

  def lookup_model
    CaseType
  end

  def lookup_params
    params.require(:case_type).permit(:name, :name_ar, :category, :active, :display_order)
  end
end
