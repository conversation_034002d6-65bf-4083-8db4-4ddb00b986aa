module Api
  class FormSubmissionsController < ApplicationController
    include AtharAuth::ResourceAuthorization

    before_action :authenticate_session!
    authorize_resources

    api! "Lists form submissions for a case"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :case_id, String, desc: "Filter by case ID"
    param :form_template_id, Integer, desc: "Filter by form template ID"
    param :status, String, desc: "Filter by status (form_draft, form_in_progress, form_submitted, form_completed)"
    param_group :pagination_params
    param_group :include_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Returns a list of form submissions.
      Supports filtering by case, form template, and status.
      Requires permission: <code>:read, :form_submission</code>.
    HTML
    )
    returns code: 200, desc: "List of form submissions"

    def index
      apply_filters(@form_submissions) do |filtered_and_sorted|
        records, meta = paginate(filtered_and_sorted)
        serialize_response(records, meta: meta)
      end
    end

    api! "Retrieves a specific form submission"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the form submission"
    param_group :include_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Fetches detailed information about a specific form submission by ID.
      Requires permission: <code>:read, :form_submission</code>.
    HTML
    )
    returns code: 200, desc: "Form submission details"

    def show
      serialize_response(@form_submission)
    end

    api! "Creates a new form submission"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :form_submission, Hash, required: true, desc: "Form submission details" do
      param :case_id, String, required: true, desc: "ID of the case"
      param :form_template_id, Integer, required: true, desc: "ID of the form template"
      param :form_data, Hash, required: true, desc: "Form field data (field_id => value)"
      param :status, String, desc: "Submission status (form_draft, form_in_progress, form_submitted, form_completed)"
    end
    param_group :include_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Creates a new form submission for a case.
      Validates form data against form template requirements.
      Requires permission: <code>:create, :form_submission</code>.
    HTML
    )
    returns code: 201, desc: "Form submission created successfully"
    error code: 422, desc: "Validation errors"

    def create
      @form_submission = FormSubmission.new(create_params)
      @form_submission.created_by = current_user
      @form_submission.updated_by = current_user

      if @form_submission.save
        serialize_response(@form_submission, status: :created)
      else
        serialize_errors(@form_submission.errors)
      end
    end

    api :PUT, "/form_submissions/:id", "Updates an existing form submission"
    api :PATCH, "/form_submissions/:id", "Partially updates a form submission"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the form submission"
    param :form_submission, Hash, required: true, desc: "Updated form submission fields" do
      param :form_data, Hash, desc: "Form field data (field_id => value)"
      param :status, String, desc: "Submission status (form_draft, form_in_progress, form_submitted, form_completed)"
    end
    param_group :include_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Updates an existing form submission's data and status.
      Validates form data against form template requirements.
      Requires permission: <code>:update, :form_submission</code>.
    HTML
    )
    returns code: 200, desc: "Form submission updated successfully"
    error code: 422, desc: "Validation errors"

    def update
      @form_submission.updated_by = current_user
      
      if @form_submission.update(update_params)
        serialize_response(@form_submission.reload)
      else
        serialize_errors(@form_submission.errors)
      end
    end

    api! "Submit form for completion"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the form submission"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Marks a form submission as completed and validates all required fields.
      Updates the case's form completion status.
      Requires permission: <code>:update, :form_submission</code>.
    HTML
    )
    returns code: 200, desc: "Form submitted successfully"
    error code: 422, desc: "Form validation errors"

    def submit
      unless @form_submission.can_be_submitted?
        return render json: {
          error: 'Form cannot be submitted',
          details: 'All required fields must be completed',
          validation_errors: @form_submission.validation_errors
        }, status: :unprocessable_entity
      end

      if @form_submission.submit!
        render json: {
          message: 'Form submitted successfully',
          form_submission_id: @form_submission.id,
          status: @form_submission.status,
          completion_percentage: @form_submission.completion_percentage
        }
      else
        serialize_errors(@form_submission.errors)
      end
    end

    api! "Calculate field values"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the form submission"
    param :field_ids, Array, desc: "Array of field IDs to calculate"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Calculates values for calculated fields based on current form data.
      Returns updated field values for frontend display.
      Requires permission: <code>:read, :form_submission</code>.
    HTML
    )
    returns code: 200, desc: "Calculated field values"

    def calculate_fields
      field_ids = params[:field_ids] || []
      calculated_values = @form_submission.calculate_field_values(field_ids)
      
      render json: {
        form_submission_id: @form_submission.id,
        calculated_values: calculated_values
      }
    end

    private

    def create_params
      params.require(:form_submission).permit(
        :case_id,
        :form_template_id,
        :status,
        form_data: {}
      )
    end

    def update_params
      params.fetch(:form_submission, {}).permit(
        :status,
        form_data: {}
      )
    end
  end
end
