module Api
  class ServicesController < ApplicationController
    include AtharAuth::ResourceAuthorization

    before_action :authenticate_session!

    # ✅ NEW: Use method call style authorization (like acts_as_approvable)
    authorize_resources

    api! "Lists all services"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param_group :pagination_params
    param_group :filter_params
    param_group :sort_params
    param_group :include_params
    param_group :fields_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Returns a list of services provided by the organization or third-party.
      Supports filtering, sorting, and pagination.
      Requires permission: <code>:read, :service</code>.
    HTML
    )
    returns code: 200, desc: "List of services"

    def index
      apply_filters(@services) do |filtered_and_sorted|
        records, meta = paginate(filtered_and_sorted)
        serialize_response(records, meta: meta)
      end
    end

    api! "Retrieves a specific service"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the service"
    param_group :include_params
    param_group :fields_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Fetches detailed information about a specific service by ID.
      Requires permission: <code>:read, :service</code>.
    HTML
    )
    returns code: 200, desc: "Service details"

    def show
      serialize_response(@service)
    end

    api! "Creates a new service"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :service, Hash, required: true, desc: "Service details" do
      param :name, String, required: true, desc: "Name of the service"
      param :description, String, required: true, desc: "Detailed description of the service"
      param :category, String, required: true, desc: "Service category (health, education, legal, social, financial)"
      param :provider_type, String, required: true, desc: "Provider type (internal, external, partner)"
      # Note: Services are now a generic catalog, not tied to specific cases
    end
    param_group :include_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Creates a new service record.
      Services can be provided internally or by external partners.
      Requires permission: <code>:create, :service</code>.
    HTML
    )
    returns code: 201, desc: "Service created successfully"
    error code: 422, desc: "Validation errors"

    def create
      @service = Service.new(create_params)

      if @service.save
        serialize_response(@service, status: :created)
      else
        serialize_errors(@service.errors)
      end
    end

    api :PUT, "/services/:id", "Updates an existing service"
    api :PATCH, "/services/:id", "Partially updates a service"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the service"
    param :service, Hash, required: true, desc: "Updated service fields" do
      param :name, String, desc: "Name of the service"
      param :description, String, desc: "Detailed description of the service"
      param :category, String, desc: "Service category (health, education, legal, social, financial)"
      param :provider_type, String, desc: "Provider type (internal, external, partner)"
      # Note: Services are now a generic catalog, not tied to specific cases
    end
    param_group :include_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Updates an existing service's information.
      At least one field should be provided in the service parameter.
      Requires permission: <code>:update, :service</code>.
    HTML
    )
    returns code: 200, desc: "Service updated successfully"
    error code: 422, desc: "Validation errors"

    def update
      if @service.update(update_params)
        serialize_response(@service.reload)
      else
        serialize_errors(@service.errors)
      end
    end

    api! "Deletes a service"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the service"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Deletes a service by ID.
      Requires permission: <code>:destroy, :service</code>.
    HTML
    )
    returns code: 204, desc: "Service deleted successfully"

    def destroy
      @service.destroy!
      head :no_content
    end

    private

    def set_service
      @service = Service.find(params[:id])
    rescue ActiveRecord::RecordNotFound
      serialize_errors({ detail: "Service not found" }, :not_found)
    end

    def create_params
      params.require(:service).permit(
        :name,
        :description,
        :category,
        :provider_type,
        # Note: case_plan_id removed - services are now a generic catalog
      )
    end

    def update_params
      params.fetch(:service, {}).permit(
        :name,
        :description,
        :category,
        :provider_type,
        # Note: case_plan_id removed - services are now a generic catalog
      )
    end
  end
end
