module Api
  class ChartsController < ApplicationController
    include AtharAuth::ResourceAuthorization

    before_action :authenticate_session!

    api! "Get pie chart data"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :chart_type, String, desc: "Type of pie chart: 'cases_by_status', 'cases_by_type', 'cases_by_priority', 'shared_cases', 'group_cases'"
    param :date_range, String, desc: "Date range filter"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Returns pie chart data for various case management visualizations.
      Data is formatted for frontend chart libraries like Chart.js or D3.
      Requires permission: <code>:read, :charts</code>.

      <b>Chart Types:</b>
      - cases_by_status: Cases grouped by status
      - cases_by_type: Cases grouped by type
      - cases_by_priority: Cases grouped by priority level
      - shared_cases: My cases vs supervised cases
      - group_cases: Group vs individual cases
    HTML
    )
    returns code: 200, desc: "Pie chart data"

    def pie
      unless current_user.can?(:read, :charts)
        return render json: { error: 'Access denied' }, status: :forbidden
      end

      chart_type = params[:chart_type]
      service = DashboardStatisticsService.new(current_user, parse_date_range)

      chart_data = case chart_type
                  when 'cases_by_status'
                    cases_by_status_chart
                  when 'cases_by_type'
                    cases_by_type_chart
                  when 'cases_by_priority'
                    cases_by_priority_chart
                  when 'shared_cases'
                    service.shared_cases_statistics
                  when 'group_cases'
                    service.group_cases_statistics
                  else
                    { error: 'Invalid chart type' }
                  end

      if chart_data[:error]
        render json: chart_data, status: :bad_request
      else
        render json: {
          chart_type: chart_type,
          data: format_pie_chart_data(chart_data),
          meta: {
            generated_at: Time.current,
            total_items: chart_data.values.sum
          }
        }
      end
    end

    api! "Get line chart data"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :chart_type, String, desc: "Type of line chart: 'case_trends', 'form_completion_trends', 'approval_trends'"
    param :period, String, desc: "Time period: 'daily', 'weekly', 'monthly'"
    param :date_range, String, desc: "Date range filter"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Returns line chart data for trend analysis and time-series visualizations.
      Data includes labels and datasets formatted for chart libraries.
      Requires permission: <code>:read, :charts</code>.

      <b>Chart Types:</b>
      - case_trends: Case creation and completion trends over time
      - form_completion_trends: Form completion rates over time
      - approval_trends: Approval processing times and volumes
    HTML
    )
    returns code: 200, desc: "Line chart data"

    def line
      unless current_user.can?(:read, :charts)
        return render json: { error: 'Access denied' }, status: :forbidden
      end

      chart_type = params[:chart_type]
      period = params[:period] || 'monthly'

      chart_data = case chart_type
                  when 'case_trends'
                    case_trends_chart(period)
                  when 'form_completion_trends'
                    form_completion_trends_chart(period)
                  when 'approval_trends'
                    approval_trends_chart(period)
                  else
                    { error: 'Invalid chart type' }
                  end

      if chart_data[:error]
        render json: chart_data, status: :bad_request
      else
        render json: {
          chart_type: chart_type,
          period: period,
          data: chart_data,
          meta: {
            generated_at: Time.current
          }
        }
      end
    end

    api! "Get bar chart data"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :chart_type, String, desc: "Type of bar chart: 'workload_distribution', 'team_performance', 'service_delivery'"
    param :date_range, String, desc: "Date range filter"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Returns bar chart data for comparative analysis and distribution visualization.
      Includes horizontal and vertical bar chart configurations.
      Requires permission: <code>:read, :charts</code>.

      <b>Chart Types:</b>
      - workload_distribution: Case distribution across team members
      - team_performance: Performance metrics by team member
      - service_delivery: Service delivery statistics by type
    HTML
    )
    returns code: 200, desc: "Bar chart data"

    def bar
      unless current_user.can?(:read, :charts)
        return render json: { error: 'Access denied' }, status: :forbidden
      end

      chart_type = params[:chart_type]

      chart_data = case chart_type
                  when 'workload_distribution'
                    workload_distribution_chart
                  when 'team_performance'
                    team_performance_chart
                  when 'service_delivery'
                    service_delivery_chart
                  else
                    { error: 'Invalid chart type' }
                  end

      if chart_data[:error]
        render json: chart_data, status: :bad_request
      else
        render json: {
          chart_type: chart_type,
          data: chart_data,
          meta: {
            generated_at: Time.current
          }
        }
      end
    end

    api! "Get dashboard widgets data"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :widget_types, Array, desc: "Array of widget types to include"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Returns data for multiple dashboard widgets in a single request.
      Optimized for dashboard loading performance.
      Requires permission: <code>:read, :charts</code>.

      <b>Available Widgets:</b>
      - statistics_cards: Key metrics cards
      - recent_activity: Recent activity feed
      - pending_tasks: Pending tasks summary
      - quick_charts: Small chart widgets
    HTML
    )
    returns code: 200, desc: "Dashboard widgets data"

    def widgets
      unless current_user.can?(:read, :charts)
        return render json: { error: 'Access denied' }, status: :forbidden
      end

      widget_types = params[:widget_types] || ['statistics_cards', 'recent_activity', 'pending_tasks']
      service = DashboardStatisticsService.new(current_user, parse_date_range)

      widgets_data = {}

      widget_types.each do |widget_type|
        widgets_data[widget_type] = case widget_type
                                   when 'statistics_cards'
                                     statistics_cards_data(service)
                                   when 'recent_activity'
                                     service.recent_observations_data
                                   when 'pending_tasks'
                                     pending_tasks_data
                                   when 'quick_charts'
                                     quick_charts_data(service)
                                   else
                                     { error: "Unknown widget type: #{widget_type}" }
                                   end
      end

      render json: {
        widgets: widgets_data,
        meta: {
          generated_at: Time.current,
          user_role: current_user.role&.name
        }
      }
    end

    private

    def parse_date_range
      return nil unless params[:date_range].present?

      case params[:date_range]
      when 'this_month'
        Date.current.beginning_of_month..Date.current
      when 'last_month'
        1.month.ago.beginning_of_month..1.month.ago.end_of_month
      when 'this_year'
        Date.current.beginning_of_year..Date.current
      when 'custom'
        start_date = Date.parse(params[:start_date]) if params[:start_date]
        end_date = Date.parse(params[:end_date]) if params[:end_date]
        start_date && end_date ? (start_date..end_date) : nil
      else
        1.month.ago..Date.current
      end
    rescue Date::Error
      1.month.ago..Date.current
    end

    def format_pie_chart_data(data)
      {
        labels: data.keys.map(&:to_s).map(&:humanize),
        datasets: [{
          data: data.values,
          backgroundColor: generate_colors(data.keys.length),
          borderWidth: 1
        }]
      }
    end

    def generate_colors(count)
      colors = [
        '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', 
        '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
      ]
      colors.cycle.take(count)
    end

    def cases_by_status_chart
      accessible_cases = current_user.accessible_cases
      accessible_cases = accessible_cases.where(created_at: parse_date_range) if parse_date_range
      accessible_cases.group(:status).count
    end

    def cases_by_type_chart
      accessible_cases = current_user.accessible_cases
      accessible_cases = accessible_cases.where(created_at: parse_date_range) if parse_date_range
      accessible_cases.group(:case_type).count
    end

    def cases_by_priority_chart
      accessible_cases = current_user.accessible_cases
      accessible_cases = accessible_cases.where(created_at: parse_date_range) if parse_date_range
      accessible_cases.group(:priority_level).count
    end

    def case_trends_chart(period)
      accessible_cases = current_user.accessible_cases
      date_range = parse_date_range || (3.months.ago..Date.current)

      created_data = case period
                    when 'daily'
                      accessible_cases.where(created_at: date_range).group_by_day(:created_at).count
                    when 'weekly'
                      accessible_cases.where(created_at: date_range).group_by_week(:created_at).count
                    else
                      accessible_cases.where(created_at: date_range).group_by_month(:created_at).count
                    end

      completed_data = case period
                      when 'daily'
                        accessible_cases.case_closed.where(closed_at: date_range).group_by_day(:closed_at).count
                      when 'weekly'
                        accessible_cases.case_closed.where(closed_at: date_range).group_by_week(:closed_at).count
                      else
                        accessible_cases.case_closed.where(closed_at: date_range).group_by_month(:closed_at).count
                      end

      {
        labels: created_data.keys.map(&:to_s),
        datasets: [
          {
            label: 'Cases Created',
            data: created_data.values,
            borderColor: '#36A2EB',
            backgroundColor: 'rgba(54, 162, 235, 0.1)',
            fill: true
          },
          {
            label: 'Cases Completed',
            data: completed_data.values,
            borderColor: '#4BC0C0',
            backgroundColor: 'rgba(75, 192, 192, 0.1)',
            fill: true
          }
        ]
      }
    end

    def form_completion_trends_chart(period)
      accessible_cases = current_user.accessible_cases
      form_submissions = FormSubmission.joins(:case).where(cases: { id: accessible_cases.ids })
      date_range = parse_date_range || (3.months.ago..Date.current)

      completion_data = case period
                       when 'daily'
                         form_submissions.form_completed.where(submitted_at: date_range).group_by_day(:submitted_at).count
                       when 'weekly'
                         form_submissions.form_completed.where(submitted_at: date_range).group_by_week(:submitted_at).count
                       else
                         form_submissions.form_completed.where(submitted_at: date_range).group_by_month(:submitted_at).count
                       end

      {
        labels: completion_data.keys.map(&:to_s),
        datasets: [{
          label: 'Forms Completed',
          data: completion_data.values,
          borderColor: '#FFCE56',
          backgroundColor: 'rgba(255, 206, 86, 0.1)',
          fill: true
        }]
      }
    end

    def approval_trends_chart(period)
      return { error: 'Access denied' } unless current_user.supervisor? || current_user.project_manager?

      accessible_cases = current_user.accessible_cases
      date_range = parse_date_range || (3.months.ago..Date.current)

      approval_data = case period
                     when 'daily'
                       accessible_cases.case_approved.where(updated_at: date_range).group_by_day(:updated_at).count
                     when 'weekly'
                       accessible_cases.case_approved.where(updated_at: date_range).group_by_week(:updated_at).count
                     else
                       accessible_cases.case_approved.where(updated_at: date_range).group_by_month(:updated_at).count
                     end

      {
        labels: approval_data.keys.map(&:to_s),
        datasets: [{
          label: 'Cases Approved',
          data: approval_data.values,
          borderColor: '#9966FF',
          backgroundColor: 'rgba(153, 102, 255, 0.1)',
          fill: true
        }]
      }
    end

    def workload_distribution_chart
      return { error: 'Access denied' } unless current_user.supervisor? || current_user.project_manager?

      service = DashboardStatisticsService.new(current_user, parse_date_range)
      workload_data = service.workload_distribution_analytics

      {
        labels: workload_data.map { |w| w[:user_name] },
        datasets: [
          {
            label: 'Active Cases',
            data: workload_data.map { |w| w[:active_cases] },
            backgroundColor: '#36A2EB'
          },
          {
            label: 'Pending Cases',
            data: workload_data.map { |w| w[:pending_cases] },
            backgroundColor: '#FFCE56'
          }
        ]
      }
    end

    def team_performance_chart
      return { error: 'Access denied' } unless current_user.supervisor?

      service = DashboardStatisticsService.new(current_user, parse_date_range)
      workload_data = service.workload_distribution_analytics

      {
        labels: workload_data.map { |w| w[:user_name] },
        datasets: [{
          label: 'Efficiency Score',
          data: workload_data.map { |w| w[:efficiency_score] },
          backgroundColor: '#4BC0C0'
        }]
      }
    end

    def service_delivery_chart
      accessible_cases = current_user.accessible_cases
      services = Service.joins(:case).where(cases: { id: accessible_cases.ids })
      services = services.where(created_at: parse_date_range) if parse_date_range

      service_data = services.group(:service_type).count

      {
        labels: service_data.keys.map(&:humanize),
        datasets: [{
          label: 'Services Delivered',
          data: service_data.values,
          backgroundColor: generate_colors(service_data.keys.length)
        }]
      }
    end

    def statistics_cards_data(service)
      {
        total_files: service.total_files_count,
        total_services: service.total_services_count,
        total_current_cases: service.total_current_cases_count,
        pending_tasks: service.employees_statistics[:pending_tasks]
      }
    end

    def pending_tasks_data
      accessible_cases = current_user.accessible_cases

      {
        pending_approvals: accessible_cases.case_pending_approval.count,
        overdue_assessments: accessible_cases.case_draft.where('created_at < ?', 7.days.ago).count,
        forms_in_progress: FormSubmission.joins(:case)
                                        .where(cases: { id: accessible_cases.ids })
                                        .form_in_progress
                                        .count
      }
    end

    def quick_charts_data(service)
      {
        shared_cases: format_pie_chart_data(service.shared_cases_statistics),
        group_cases: format_pie_chart_data(service.group_cases_statistics),
        current_cases: format_pie_chart_data(service.current_cases_statistics)
      }
    end
  end
end
