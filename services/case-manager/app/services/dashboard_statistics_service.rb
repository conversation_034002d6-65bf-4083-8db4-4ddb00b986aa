# Dashboard Statistics Service - Matches Arabic interface requirements
class DashboardStatisticsService
  def initialize(user, date_range = nil)
    @user = user
    @date_range = date_range || (1.year.ago..Date.current)
    @current_month = Date.current.beginning_of_month..Date.current
  end

  # Main dashboard statistics matching Arabic interface
  def dashboard_overview
    {
      # Top row statistics (total files, services, current cases)
      total_files: total_files_count,
      total_services: total_services_count,
      total_current_cases: total_current_cases_count,

      # Pie chart data
      shared_cases_chart: shared_cases_statistics,
      group_cases_chart: group_cases_statistics,
      current_cases_chart: current_cases_statistics,

      # Recent observations section
      recent_observations: recent_observations_data,

      # Current cases with others section
      current_cases_with_others: current_cases_with_others_data,

      # Employees section
      employees_data: employees_statistics,

      # Team sharing table
      team_sharing_table: team_sharing_table_data
    }
  end

  # Top statistics cards
  def total_files_count
    @user.accessible_cases.where(created_at: @date_range).count
  end

  def total_services_count
    accessible_services.where(created_at: @date_range).count
  end

  def total_current_cases_count
    @user.accessible_cases.case_active.count
  end

  # Pie chart statistics - simplified without case sharing
  def shared_cases_statistics
    cases = @user.accessible_cases.where(created_at: @date_range)

    {
      my_cases: cases.where(case_manager: @user).count,
      supervised_cases: @user.supervisor? ? cases.where.not(case_manager: @user).count : 0,
      total: cases.count
    }
  end

  def group_cases_statistics
    cases = @user.accessible_cases.where(created_at: @date_range)

    {
      group_cases: cases.where(case_type: ['group', 'community']).count,
      individual_cases: cases.where.not(case_type: ['group', 'community']).count,
      total: cases.count
    }
  end

  def current_cases_statistics
    cases = @user.accessible_cases.case_active

    {
      high_priority: cases.where(priority_level: [4, 5]).count,
      medium_priority: cases.where(priority_level: [2, 3]).count,
      low_priority: cases.where(priority_level: 1).count,
      total: cases.count
    }
  end

  # Recent observations (الملاحظات الجديدة)
  def recent_observations_data
    observations = accessible_case_comments.where(created_at: @current_month)
                                          .includes(:case, :user)
                                          .order(created_at: :desc)
                                          .limit(5)

    observations.map do |comment|
      {
        id: comment.id,
        case_number: comment.case.case_number,
        beneficiary_name: comment.case.beneficiary_name,
        comment_content: comment.content.truncate(100),
        created_by: comment.user.name,
        created_at: comment.created_at,
        case_manager: comment.case.case_manager&.name
      }
    end
  end

  # Current cases with others - simplified without case sharing
  def current_cases_with_others_data
    # For supervisors, show cases they supervise but don't manage
    return [] unless @user.supervisor?

    supervised_cases = Case.joins(:case_manager)
                          .where(case_managers: { project_id: @user.project_id })
                          .where.not(case_manager: @user)
                          .includes(:case_manager)
                          .limit(5)

    supervised_cases.map do |case_obj|
      {
        id: case_obj.id,
        case_number: case_obj.case_number,
        beneficiary_name: case_obj.beneficiary_name,
        case_manager: case_obj.case_manager.name,
        priority: case_obj.priority_level,
        status: case_obj.status
      }
    end
  end

  # Employees statistics (الموظفات)
  def employees_statistics
    if @user.supervisor?
      team_members = User.where(project_id: @user.project_id)
                        .where.not(id: @user.id)
    else
      team_members = User.where(id: @user.id)
    end

    {
      total_employees: team_members.count,
      active_case_managers: team_members.joins(:managed_cases)
                                      .where(cases: { status: ['case_active', 'case_approved'] })
                                      .distinct.count,
      pending_tasks: count_pending_tasks_for_team(team_members)
    }
  end

  # Team sharing table (تم تشاركها مع فريقي)
  def team_sharing_table_data
    return [] unless @user.supervisor?

    team_cases = Case.joins(:case_manager)
                    .where(case_managers: { project_id: @user.project_id })
                    .where(created_at: @current_month)
                    .includes(:case_manager)
                    .order(created_at: :desc)
                    .limit(10)

    team_cases.map do |case_obj|
      {
        case_number: case_obj.case_number,
        beneficiary_name: case_obj.beneficiary_name,
        case_manager: case_obj.case_manager.name,
        status: case_obj.status,
        priority: case_obj.priority_level,
        created_at: case_obj.created_at,
        last_activity: case_obj.updated_at
      }
    end
  end

  # Advanced analytics methods
  def case_completion_trends
    cases = @user.accessible_cases.where(created_at: @date_range)
    
    monthly_data = cases.group_by_month(:created_at).count
    completion_data = cases.case_closed.group_by_month(:closed_at).count
    
    {
      monthly_created: monthly_data,
      monthly_completed: completion_data,
      completion_rate: calculate_completion_rate(cases)
    }
  end

  def form_completion_analytics
    accessible_cases = @user.accessible_cases
    form_submissions = FormSubmission.joins(:case).where(cases: { id: accessible_cases.ids })
    
    {
      total_forms: form_submissions.count,
      completed_forms: form_submissions.form_completed.count,
      in_progress_forms: form_submissions.form_in_progress.count,
      average_completion_time: calculate_average_form_completion_time(form_submissions),
      completion_by_template: form_submissions.joins(:form_template)
                                            .group('form_templates.name')
                                            .group(:status)
                                            .count
    }
  end

  def workload_distribution_analytics
    return {} unless @user.supervisor?

    team_members = User.where(project_id: @user.project_id)
    
    team_members.map do |member|
      active_cases = member.managed_cases.case_active.count
      pending_cases = member.managed_cases.case_pending_approval.count
      completed_this_month = member.managed_cases.case_closed
                                  .where(closed_at: @current_month)
                                  .count
      
      {
        user_id: member.id,
        user_name: member.name,
        active_cases: active_cases,
        pending_cases: pending_cases,
        completed_this_month: completed_this_month,
        total_workload: active_cases + pending_cases,
        efficiency_score: calculate_efficiency_score(member)
      }
    end
  end

  private

  def accessible_services
    if @user.project_manager?
      Service.all
    else
      Service.joins(:case).where(cases: { project_id: @user.project_id })
    end
  end

  def accessible_case_comments
    if @user.project_manager?
      Comment.all
    else
      Comment.joins(:case).where(cases: { project_id: @user.project_id })
    end
  end

  def count_pending_tasks_for_team(team_members)
    # Count pending approvals, overdue assessments, etc.
    pending_approvals = Case.joins(:case_manager)
                           .where(case_managers: { user_id: team_members.pluck(:id) })
                           .case_pending_approval
                           .count

    overdue_assessments = count_overdue_assessments_for_team(team_members)

    pending_approvals + overdue_assessments
  end

  def count_overdue_assessments_for_team(team_members)
    # Cases that have been in draft for more than 7 days
    Case.joins(:case_manager)
        .where(case_managers: { user_id: team_members.pluck(:id) })
        .case_draft
        .where('cases.created_at < ?', 7.days.ago)
        .count
  end

  def calculate_completion_rate(cases)
    return 0 if cases.empty?
    
    completed_cases = cases.case_closed.count
    (completed_cases.to_f / cases.count * 100).round(2)
  end

  def calculate_average_form_completion_time(submissions)
    completed_submissions = submissions.form_completed.where.not(submitted_at: nil)
    return 0 if completed_submissions.empty?

    total_time = completed_submissions.sum do |submission|
      (submission.submitted_at - submission.created_at) / 1.day
    end

    (total_time / completed_submissions.count).round(2)
  end

  def calculate_efficiency_score(user)
    # Simple efficiency calculation based on case completion rate
    total_cases = user.managed_cases.count
    return 0 if total_cases.zero?
    
    completed_cases = user.managed_cases.case_closed.count
    (completed_cases.to_f / total_cases * 100).round(1)
  end
end
