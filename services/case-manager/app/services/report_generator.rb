# Report Generator for various report types
class ReportGenerator
  include ActiveModel::Model

  attr_accessor :report_type, :date_range, :filters, :user, :format

  REPORT_TYPES = %w[
    case_summary
    beneficiary_demographics
    service_delivery
    workflow_performance
    user_activity
    form_completion
    team_performance
    custom
  ].freeze

  EXPORT_FORMATS = %w[json csv pdf].freeze

  validates :report_type, inclusion: { in: REPORT_TYPES }
  validates :user, presence: true
  validates :format, inclusion: { in: EXPORT_FORMATS }, allow_blank: true

  def initialize(attributes = {})
    super
    @format ||= 'json'
    @date_range ||= (1.month.ago..Date.current)
  end

  def generate
    return false unless valid?

    report_data = case report_type
                  when 'case_summary'
                    generate_case_summary_report
                  when 'beneficiary_demographics'
                    generate_demographics_report
                  when 'service_delivery'
                    generate_service_delivery_report
                  when 'workflow_performance'
                    generate_workflow_performance_report
                  when 'user_activity'
                    generate_user_activity_report
                  when 'form_completion'
                    generate_form_completion_report
                  when 'team_performance'
                    generate_team_performance_report
                  else
                    generate_custom_report
                  end

    format_report(report_data)
  end

  private

  def generate_case_summary_report
    cases = user.accessible_cases.includes(:case_manager, :created_by)
    cases = apply_date_filter(cases) if date_range.present?
    cases = apply_filters(cases) if filters.present?

    {
      title: "Case Summary Report",
      generated_at: Time.current,
      date_range: format_date_range,
      user_info: user_summary,
      summary: {
        total_cases: cases.count,
        cases_by_status: cases.group(:status).count,
        cases_by_type: cases.group(:case_type).count,
        cases_by_priority: cases.group(:priority_level).count,
        average_duration: calculate_average_duration(cases.case_closed),
        completion_rate: calculate_completion_rate(cases)
      },
      cases: cases.limit(1000).map(&:case_summary)
    }
  end

  def generate_demographics_report
    cases = user.accessible_cases
    cases = apply_date_filter(cases) if date_range.present?

    # Extract beneficiary data from cached JSON
    beneficiary_data = cases.map { |c| c.beneficiary_data || {} }.compact

    {
      title: "Beneficiary Demographics Report",
      generated_at: Time.current,
      date_range: format_date_range,
      user_info: user_summary,
      summary: {
        total_beneficiaries: beneficiary_data.count,
        age_distribution: calculate_age_distribution(beneficiary_data),
        gender_distribution: calculate_gender_distribution(beneficiary_data),
        nationality_distribution: calculate_nationality_distribution(beneficiary_data),
        location_distribution: calculate_location_distribution(beneficiary_data)
      },
      detailed_data: beneficiary_data.first(500) # Limit for performance
    }
  end

  def generate_service_delivery_report
    cases = user.accessible_cases.includes(:services)
    cases = apply_date_filter(cases) if date_range.present?

    services = Service.joins(:case).where(cases: { id: cases.ids })

    {
      title: "Service Delivery Report",
      generated_at: Time.current,
      date_range: format_date_range,
      user_info: user_summary,
      summary: {
        total_services: services.count,
        services_by_type: services.group(:service_type).count,
        services_by_status: services.group(:status).count,
        average_service_duration: calculate_average_service_duration(services),
        most_requested_services: services.group(:service_type).count.sort_by(&:last).reverse.first(5)
      },
      services: services.limit(1000).map(&:service_summary)
    }
  end

  def generate_workflow_performance_report
    cases = user.accessible_cases
    cases = apply_date_filter(cases) if date_range.present?

    form_submissions = FormSubmission.joins(:case).where(cases: { id: cases.ids })

    {
      title: "Workflow Performance Report",
      generated_at: Time.current,
      date_range: format_date_range,
      user_info: user_summary,
      summary: {
        total_workflows: cases.count,
        average_case_duration: calculate_average_duration(cases.case_closed),
        form_completion_rates: calculate_form_completion_rates(form_submissions),
        approval_processing_time: calculate_approval_processing_time(cases),
        bottlenecks: identify_workflow_bottlenecks(cases, form_submissions)
      },
      performance_metrics: {
        cases_by_completion_time: group_cases_by_completion_time(cases.case_closed),
        forms_by_completion_time: group_forms_by_completion_time(form_submissions.form_completed)
      }
    }
  end

  def generate_user_activity_report
    return {} unless user.supervisor? || user.project_manager?

    team_members = User.where(project_id: user.project_id)
    cases = Case.joins(:case_manager).where(case_managers: { user_id: team_members.ids })
    cases = apply_date_filter(cases) if date_range.present?

    {
      title: "User Activity Report",
      generated_at: Time.current,
      date_range: format_date_range,
      user_info: user_summary,
      summary: {
        total_team_members: team_members.count,
        active_users: team_members.joins(:managed_cases).distinct.count,
        total_activities: calculate_total_activities(team_members),
        average_cases_per_user: cases.count.to_f / team_members.count
      },
      user_activities: team_members.map { |member| user_activity_summary(member) }
    }
  end

  def generate_form_completion_report
    cases = user.accessible_cases
    form_submissions = FormSubmission.joins(:case).where(cases: { id: cases.ids })
    form_submissions = apply_date_filter_to_submissions(form_submissions) if date_range.present?

    {
      title: "Form Completion Report",
      generated_at: Time.current,
      date_range: format_date_range,
      user_info: user_summary,
      summary: {
        total_forms: form_submissions.count,
        completed_forms: form_submissions.form_completed.count,
        in_progress_forms: form_submissions.form_in_progress.count,
        average_completion_time: calculate_average_form_completion_time(form_submissions),
        completion_rate: calculate_form_completion_rate(form_submissions)
      },
      form_analytics: {
        completion_by_template: form_submissions.joins(:form_template)
                                              .group('form_templates.name')
                                              .group(:status)
                                              .count,
        completion_trends: form_submissions.form_completed
                                         .group_by_week(:submitted_at)
                                         .count
      }
    }
  end

  def generate_team_performance_report
    return {} unless user.supervisor? || user.project_manager?

    service = DashboardStatisticsService.new(user, date_range)

    {
      title: "Team Performance Report",
      generated_at: Time.current,
      date_range: format_date_range,
      user_info: user_summary,
      team_metrics: service.workload_distribution_analytics,
      performance_trends: service.case_completion_trends,
      efficiency_metrics: calculate_team_efficiency_metrics
    }
  end

  def generate_custom_report
    # Placeholder for custom report generation
    {
      title: "Custom Report",
      generated_at: Time.current,
      date_range: format_date_range,
      user_info: user_summary,
      message: "Custom report generation not yet implemented"
    }
  end

  # Helper methods
  def apply_date_filter(cases)
    cases.where(created_at: date_range)
  end

  def apply_date_filter_to_submissions(submissions)
    submissions.where(created_at: date_range)
  end

  def apply_filters(cases)
    return cases unless filters.is_a?(Hash)

    cases = cases.where(status: filters[:status]) if filters[:status].present?
    cases = cases.where(case_type: filters[:case_type]) if filters[:case_type].present?
    cases = cases.where(priority_level: filters[:priority_level]) if filters[:priority_level].present?
    cases
  end

  def format_report(data)
    case format
    when 'csv'
      convert_to_csv(data)
    when 'pdf'
      convert_to_pdf(data)
    else
      data
    end
  end

  def convert_to_csv(data)
    # CSV conversion logic would go here
    # For now, return JSON with CSV format indicator
    { format: 'csv', data: data, message: 'CSV export not yet implemented' }
  end

  def convert_to_pdf(data)
    # PDF conversion logic would go here
    # For now, return JSON with PDF format indicator
    { format: 'pdf', data: data, message: 'PDF export not yet implemented' }
  end

  def user_summary
    {
      id: user.id,
      name: user.name,
      role: user.role&.name,
      project_id: user.project_id
    }
  end

  def format_date_range
    return nil unless date_range
    
    {
      start_date: date_range.begin,
      end_date: date_range.end,
      formatted: "#{date_range.begin.strftime('%B %d, %Y')} - #{date_range.end.strftime('%B %d, %Y')}"
    }
  end

  def calculate_average_duration(cases)
    return 0 if cases.empty?

    total_duration = cases.sum do |case_obj|
      next 0 unless case_obj.closed_at && case_obj.created_at
      (case_obj.closed_at - case_obj.created_at) / 1.day
    end

    (total_duration / cases.count).round(2)
  end

  def calculate_completion_rate(cases)
    return 0 if cases.empty?
    
    completed_cases = cases.case_closed.count
    (completed_cases.to_f / cases.count * 100).round(2)
  end

  def calculate_age_distribution(beneficiary_data)
    ages = beneficiary_data.map { |b| b['age'] }.compact
    return {} if ages.empty?

    {
      minors: ages.count { |age| age < 18 },
      adults: ages.count { |age| age >= 18 && age < 60 },
      elderly: ages.count { |age| age >= 60 }
    }
  end

  def calculate_gender_distribution(beneficiary_data)
    beneficiary_data.group_by { |b| b['gender'] }.transform_values(&:count)
  end

  def calculate_nationality_distribution(beneficiary_data)
    beneficiary_data.group_by { |b| b['nationality'] }.transform_values(&:count)
  end

  def calculate_location_distribution(beneficiary_data)
    beneficiary_data.group_by { |b| b['city'] || b['location'] }.transform_values(&:count)
  end

  def calculate_average_service_duration(services)
    return 0 if services.empty?

    completed_services = services.select { |s| s.completed_at && s.created_at }
    return 0 if completed_services.empty?

    total_duration = completed_services.sum do |service|
      (service.completed_at - service.created_at) / 1.day
    end

    (total_duration / completed_services.count).round(2)
  end

  def calculate_form_completion_rates(form_submissions)
    return {} if form_submissions.empty?

    total_forms = form_submissions.count
    completed_forms = form_submissions.form_completed.count

    {
      total: total_forms,
      completed: completed_forms,
      completion_rate: (completed_forms.to_f / total_forms * 100).round(2)
    }
  end

  def calculate_approval_processing_time(cases)
    approved_cases = cases.case_approved.where.not(approved_at: nil)
    return 0 if approved_cases.empty?

    total_time = approved_cases.sum do |case_obj|
      next 0 unless case_obj.approved_at && case_obj.submitted_for_approval_at
      (case_obj.approved_at - case_obj.submitted_for_approval_at) / 1.day
    end

    (total_time / approved_cases.count).round(2)
  end

  def identify_workflow_bottlenecks(cases, form_submissions)
    bottlenecks = []

    # Check for cases stuck in draft for too long
    long_draft_cases = cases.case_draft.where('created_at < ?', 14.days.ago).count
    if long_draft_cases > 0
      bottlenecks << {
        type: 'long_draft_cases',
        count: long_draft_cases,
        description: 'Cases in draft status for more than 14 days'
      }
    end

    # Check for pending approvals taking too long
    long_pending_approvals = cases.case_pending_approval.where('updated_at < ?', 7.days.ago).count
    if long_pending_approvals > 0
      bottlenecks << {
        type: 'long_pending_approvals',
        count: long_pending_approvals,
        description: 'Cases pending approval for more than 7 days'
      }
    end

    # Check for incomplete forms
    incomplete_forms = form_submissions.form_in_progress.where('created_at < ?', 7.days.ago).count
    if incomplete_forms > 0
      bottlenecks << {
        type: 'incomplete_forms',
        count: incomplete_forms,
        description: 'Forms in progress for more than 7 days'
      }
    end

    bottlenecks
  end

  def group_cases_by_completion_time(cases)
    return {} if cases.empty?

    cases.group_by do |case_obj|
      next 'unknown' unless case_obj.closed_at && case_obj.created_at

      duration = (case_obj.closed_at - case_obj.created_at) / 1.day

      case duration
      when 0..7
        '0-7 days'
      when 8..30
        '8-30 days'
      when 31..90
        '31-90 days'
      else
        '90+ days'
      end
    end.transform_values(&:count)
  end

  def group_forms_by_completion_time(form_submissions)
    return {} if form_submissions.empty?

    form_submissions.group_by do |submission|
      next 'unknown' unless submission.submitted_at && submission.created_at

      duration = (submission.submitted_at - submission.created_at) / 1.day

      case duration
      when 0..1
        '0-1 days'
      when 2..7
        '2-7 days'
      when 8..14
        '8-14 days'
      else
        '14+ days'
      end
    end.transform_values(&:count)
  end

  def calculate_total_activities(team_members)
    # Simple activity calculation based on case management actions
    team_members.sum do |member|
      member.managed_cases.count +
      Comment.joins(:case).where(cases: { case_manager: member }, user: member).count
    end
  end

  def user_activity_summary(user)
    {
      user_id: user.id,
      user_name: user.name,
      role: user.role&.name,
      total_cases: user.managed_cases.count,
      active_cases: user.managed_cases.case_active.count,
      completed_cases: user.managed_cases.case_closed.count,
      comments_made: Comment.joins(:case).where(cases: { case_manager: user }, user: user).count,
      last_activity: user.managed_cases.maximum(:updated_at) || user.updated_at
    }
  end

  def calculate_average_form_completion_time(form_submissions)
    completed_submissions = form_submissions.form_completed.where.not(submitted_at: nil)
    return 0 if completed_submissions.empty?

    total_time = completed_submissions.sum do |submission|
      (submission.submitted_at - submission.created_at) / 1.day
    end

    (total_time / completed_submissions.count).round(2)
  end

  def calculate_form_completion_rate(form_submissions)
    return 0 if form_submissions.empty?

    completed_forms = form_submissions.form_completed.count
    (completed_forms.to_f / form_submissions.count * 100).round(2)
  end

  def calculate_team_efficiency_metrics
    return {} unless user.supervisor? || user.project_manager?

    team_members = User.where(project_id: user.project_id)

    {
      average_case_completion_time: calculate_team_average_completion_time(team_members),
      team_productivity_score: calculate_team_productivity_score(team_members),
      collaboration_index: calculate_collaboration_index(team_members)
    }
  end

  def calculate_team_average_completion_time(team_members)
    all_cases = Case.joins(:case_manager).where(case_managers: { user_id: team_members.ids })
    calculate_average_duration(all_cases.case_closed)
  end

  def calculate_team_productivity_score(team_members)
    total_cases = Case.joins(:case_manager).where(case_managers: { user_id: team_members.ids }).count
    return 0 if total_cases.zero?

    completed_cases = Case.joins(:case_manager)
                         .where(case_managers: { user_id: team_members.ids })
                         .case_closed
                         .count

    (completed_cases.to_f / total_cases * 100).round(1)
  end

  def calculate_collaboration_index(team_members)
    # Simple collaboration metric based on comments and case interactions
    total_comments = Comment.joins(:case)
                           .where(cases: { case_manager: team_members })
                           .count

    total_cases = Case.joins(:case_manager)
                     .where(case_managers: { user_id: team_members.ids })
                     .count

    return 0 if total_cases.zero?

    (total_comments.to_f / total_cases).round(2)
  end
end
