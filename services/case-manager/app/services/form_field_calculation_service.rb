class FormFieldCalculationService
  # Supported calculation types with their implementations
  CALCULATIONS = {
    'age_from_date' => ->(date_value) {
      return nil unless date_value
      date = date_value.is_a?(String) ? Date.parse(date_value) : date_value
      ((Date.current - date) / 365.25).floor
    },

    'sum' => ->(values) {
      Array(values).compact.map(&:to_f).sum
    },

    'difference' => ->(values) {
      vals = Array(values).map(&:to_f)
      return 0 if vals.size < 2
      vals[0] - vals[1]
    },

    'percentage' => ->(values) {
      vals = Array(values).map(&:to_f)
      return 0 if vals.size < 2 || vals[1].zero?
      (vals[0] / vals[1] * 100).round(2)
    },

    'average' => ->(values) {
      arr = Array(values).compact.map(&:to_f)
      arr.empty? ? 0 : (arr.sum / arr.size).round(2)
    },

    'count_non_empty' => ->(values) {
      Array(values).count { |v| v.present? }
    },

    'multiply' => ->(values) {
      vals = Array(values).map(&:to_f)
      return 0 if vals.size < 2
      vals[0] * vals[1]
    },

    'divide' => ->(values) {
      vals = Array(values).map(&:to_f)
      return 0 if vals.size < 2 || vals[1].zero?
      vals[0] / vals[1]
    },

    'max' => ->(values) {
      Array(values).compact.map(&:to_f).max || 0
    },

    'min' => ->(values) {
      Array(values).compact.map(&:to_f).min || 0
    },

    'concat' => ->(values) {
      Array(values).compact.join(' ')
    }
  }.freeze

  def self.calculate(calculation_type, value)
    calculator = CALCULATIONS[calculation_type]
    return nil unless calculator

    begin
      calculator.call(value)
    rescue => e
      Rails.logger.error "Field calculation error: #{e.message}"
      nil
    end
  end

  def self.calculate_field_value(field, form_data)
    return nil unless field.calculated_field?

    case field.calculation_formula
    when 'age_from_date'
      source_value = get_dependency_value(field, form_data, 0)
      calculate('age_from_date', source_value)
    when 'sum'
      source_values = field.calculation_dependencies.map { |dep| form_data[dep] }
      calculate('sum', source_values)
    when 'difference'
      deps = field.calculation_dependencies
      return nil unless deps.size >= 2
      values = deps.map { |dep| form_data[dep] }
      calculate('difference', values)
    when 'multiply'
      deps = field.calculation_dependencies
      return nil unless deps.size >= 2
      values = deps.map { |dep| form_data[dep] }
      calculate('multiply', values)
    when 'percentage'
      deps = field.calculation_dependencies
      return nil unless deps.size >= 2
      values = deps.map { |dep| form_data[dep] }
      calculate('percentage', values)
    when 'average'
      source_values = field.calculation_dependencies.map { |dep| form_data[dep] }
      calculate('average', source_values)
    when 'concat'
      source_values = field.calculation_dependencies.map { |dep| form_data[dep] }
      calculate('concat', source_values)
    else
      # Handle custom calculations or formulas
      evaluate_custom_calculation(field, form_data)
    end
  end

  def self.calculate_all_fields(form_template, form_data)
    calculated_values = {}
    
    form_template.form_fields.select(&:calculated_field?).each do |field|
      if field.should_calculate_for?(form_data)
        calculated_values[field.field_name] = calculate_field_value(field, form_data)
      end
    end

    calculated_values
  end

  def self.recalculate_dependent_fields(changed_field_name, form_template, form_data)
    dependent_fields = form_template.form_fields.select do |field|
      field.calculated_field? && field.calculation_dependencies.include?(changed_field_name)
    end

    calculated_values = {}
    dependent_fields.each do |field|
      calculated_values[field.field_name] = calculate_field_value(field, form_data)
    end

    calculated_values
  end

  private

  def self.get_dependency_value(field, form_data, index)
    deps = field.calculation_dependencies
    return nil if deps.empty? || index >= deps.size
    
    form_data[deps[index]]
  end

  def self.evaluate_custom_calculation(field, form_data)
    # For complex calculations that need custom logic
    # This can be extended based on specific requirements
    
    formula = field.calculation_formula
    return nil unless formula.present?

    # Simple formula evaluation with field substitution
    # Replace {{field_name}} with actual values
    field.calculation_dependencies.each do |dep_field|
      value = form_data[dep_field] || 0
      formula = formula.gsub("{{#{dep_field}}}", value.to_s)
    end

    # Safe evaluation - only allow basic math operations
    if formula.match?(/\A[\d\s\+\-\*\/\(\)\.]+\z/)
      begin
        eval(formula)
      rescue
        nil
      end
    else
      nil
    end
  end
end
