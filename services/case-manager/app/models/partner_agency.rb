# Partner Agencies
class PartnerAgency < LookupBase
  validates :contact_email, format: { with: URI::MailTo::EMAIL_REGEXP }, allow_blank: true

  enum :agency_type, {
    ngo: 0,
    ingo: 1,
    government: 2,
    un_agency: 3,
    local_org: 4
  }

  scope :by_type, ->(type) { where(agency_type: type) }

  def api_representation
    super.merge({
      agency_type: agency_type,
      contact_email: contact_email
    })
  end
end
