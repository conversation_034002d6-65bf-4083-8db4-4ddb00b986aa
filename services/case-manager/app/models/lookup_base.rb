# Base class for all lookup models with project scoping
class LookupBase < ApplicationRecord
  self.abstract_class = true

  belongs_to :project, optional: true  # nil = global, project_id = project-specific

  validates :name, presence: true
  validates :name_ar, presence: true  # Arabic name
  validates :active, inclusion: { in: [true, false] }  # Keep boolean validation for clarity

  scope :active, -> { where(active: true) }
  scope :ordered, -> { order(:display_order, :name) }
  scope :for_project, ->(project_id) {
    where("project_id IS NULL OR project_id = ?", project_id)
  }
  scope :global_only, -> { where(project_id: nil) }
  scope :project_specific, ->(project_id) { where(project_id: project_id) }

  def display_name(locale = I18n.locale)
    locale.to_s == 'ar' ? name_ar : name
  end

  def api_representation
    {
      id: id,
      name: name,
      name_ar: name_ar,
      code: respond_to?(:code) ? code : nil,
      active: active,
      project_id: project_id,
      display_order: display_order,
      created_at: created_at,
      updated_at: updated_at
    }.compact
  end
end
