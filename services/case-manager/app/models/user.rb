# frozen_string_literal: true

class User < AtharAuth::Models::User
  # Inherit all common attributes from AtharAuth::Models::User:
  # - id, name, email, global, project_id, user_type, scope, permissions
  # - global_user?, project_based_user?, can?(permission)
  # - from_token_data factory method with rich associations

  # CM-specific associations
  has_many :managed_cases, class_name: "Case", foreign_key: :assigned_user_id, primary_key: :id
  has_many :created_cases, class_name: "Case", foreign_key: :created_by_id, primary_key: :id
  has_many :case_comments, class_name: "Comment", foreign_key: :user_id, primary_key: :id
  has_many :uploaded_documents, class_name: "CaseDocument", foreign_key: :uploaded_by_id, primary_key: :id

  # Enhanced role checking methods
  def case_manager?
    role&.name&.include?("case_manager")
  end

  def supervisor?
    role&.name&.include?("supervisor")
  end



  # CM-specific domain methods
  def accessible_cases
    if global_user?
      Case.all
    elsif supervisor?
      Case.where(project_id: project_id)
    elsif case_manager?
      managed_cases.where(project_id: project_id)
    else
      Case.none
    end
  end

  def can_manage_case?(case_obj)
    return true if global_user?
    return true if supervisor? && case_obj.project_id == project_id
    return true if case_manager? && case_obj.assigned_user_id == id && case_obj.project_id == project_id

    false
  end

  def can_approve_cases?
    can?('approve:case')
  end

  def can_create_cases?
    can?('create:case')
  end

  def can_view_case?(case_obj)
    return true if can_manage_case?(case_obj)
    return true if case_obj.confidentiality_level == 'public_access'
    return true if case_obj.confidentiality_level == 'internal' && project_id == case_obj.project_id

    false
  end

  def accessible_form_templates
    if global_user?
      FormTemplate.all
    else
      FormTemplate.where(project_id: project_id).active
    end
  end

  def can_access_form_template?(template)
    return false unless template.active?
    return true if global_user?
    return false unless template.project_id == project_id

    template.can_be_accessed_by?(self)
  end

  def case_workload_summary
    return {} unless case_manager?

    cases = managed_cases.where(project_id: project_id)

    {
      total_cases: cases.count,
      active_cases: cases.case_active.count,
      pending_approval_cases: cases.case_pending_approval.count,
      draft_cases: cases.case_draft.count,
      closed_cases: cases.case_closed.count,
      high_priority_cases: cases.where(priority_level: [ 4, 5 ]).count,
      overdue_cases: cases.where("created_at < ?", 30.days.ago).case_active.count
    }
  end

  def supervision_summary
    return {} unless supervisor?

    project_cases = Case.where(project_id: project_id)

    {
      total_cases: project_cases.count,
      cases_needing_approval: project_cases.pending_approval.count,
      active_cases: project_cases.active.count,
      overdue_cases: project_cases.where("created_at < ?", 30.days.ago).active.count,
      case_managers_count: User.joins(:managed_cases)
                              .where(cases: { project_id: project_id })
                              .distinct.count,
      unresolved_comments: Comment.joins(:case)
                                 .where(cases: { project_id: project_id })
                                 .open.count
    }
  end

  # Additional utility methods for case management
  def active_cases_count
    managed_cases.where(project_id: project_id, status: [ "open", "in_progress", "active" ]).count
  end

  def total_cases_count
    managed_cases.where(project_id: project_id).count
  end

  # Check if user supervises other users (has supervised cases not assigned to them)
  def supervises_others?
    return false unless supervisor?
    Case.where(project_id: project_id).where.not(assigned_user_id: id).exists?
  end
end
