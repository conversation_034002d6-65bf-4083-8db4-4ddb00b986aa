class FormSection < ApplicationRecord
  include Athar::Commons::Models::Concerns::Ransackable

  # Associations
  belongs_to :form_template
  has_many :form_fields, dependent: :destroy
  belongs_to :display_condition_field, class_name: '<PERSON><PERSON>ield', optional: true

  # Validations
  validates :name, presence: true, length: { minimum: 2, maximum: 100 }
  validates :title, presence: true, length: { minimum: 2, maximum: 200 }
  validates :display_order, presence: true, numericality: { greater_than: 0 }
  validates :display_order, uniqueness: { scope: :form_template_id }

  # Scopes
  scope :visible, -> { where(visible: true) }
  scope :required, -> { where(is_required: true) }
  scope :ordered, -> { order(:display_order) }

  # Ransack configuration
  def self.ransackable_attributes(auth_object = nil)
    %w[name title display_order is_required visible created_at updated_at]
  end

  def self.ransackable_associations(auth_object = nil)
    %w[form_template form_fields display_condition_field]
  end

  # Instance methods
  def should_display?(form_data = {}, user = nil)
    return false unless visible?

    # Check user role condition
    if display_condition_user_role.present?
      return false unless user&.role&.name&.include?(display_condition_user_role)
    end

    # Check project type condition
    if display_condition_project_type.present?
      return false unless user&.project&.project_type == display_condition_project_type
    end

    # Check field value condition
    if display_condition_field.present? && display_condition_value.present?
      field_value = form_data[display_condition_field.field_name]
      return field_value.to_s == display_condition_value.to_s
    end

    true
  end

  def completion_percentage(form_data = {})
    return 0 if form_fields.empty?
    
    completed_fields = form_fields.count do |field|
      field.is_completed?(form_data)
    end
    
    (completed_fields.to_f / form_fields.count * 100).round(2)
  end

  def required_fields_completed?(form_data = {})
    required_fields = form_fields.where(required: true)
    return true if required_fields.empty?
    
    required_fields.all? { |field| field.is_completed?(form_data) }
  end

  def section_structure
    {
      id: id,
      name: name,
      title: title,
      description: description,
      display_order: display_order,
      is_required: is_required,
      visible: visible,
      display_conditions: {
        field_id: display_condition_field_id,
        value: display_condition_value,
        user_role: display_condition_user_role,
        project_type: display_condition_project_type
      },
      fields: form_fields.ordered.map(&:field_structure)
    }
  end

  def duplicate_for_template(new_template)
    new_section = self.dup
    new_section.form_template = new_template
    
    if new_section.save
      form_fields.each do |field|
        field.duplicate_for_section(new_section)
      end
    end
    
    new_section
  end



  def total_fields_count
    form_fields.count
  end

  def required_fields_count
    form_fields.where(required: true).count
  end
end
