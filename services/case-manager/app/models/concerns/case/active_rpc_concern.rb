# frozen_string_literal: true

# Concern for ActiveRpc configuration for Case model user relationships
# Provides access to assigned user and created by user data from Core service
module Case::ActiveRpcConcern
  extend ActiveSupport::Concern

  included do
    # Configure gRPC integration for assigned user
    # Uses assigned_user_id field to fetch user data
    active_rpc :core, :User, foreign_key: :assigned_user_id, method_name: :assigned_user_data do
      attribute :name, :string, default: "Unknown User"
      attribute :email, :string, default: "<EMAIL>"
      attribute :user_roles_list, :user_role_collection, default: []
      attribute :avatar_attributes, :avatar_attributes_type, default: {}
      attribute :status, :string, default: "active"

      # Define query configuration
      query_config(
        searchable: [:name, :email, :status],
        filterable: [:name, :email, :status],
        sortable: [:name, :email, :status],
        includable: [:user_roles, :avatar_attributes]
      )

      # Define available scopes
      scope :with_role, "Filter users by role name"
      scope :with_permission, "Filter users by permission name"
      scope :in_project, "Filter users by project"
      scope :in_projects, "Filter users by multiple projects"
    end

    # Configure gRPC integration for created by user
    # Uses created_by_id field to fetch user data
    active_rpc :core, :User, foreign_key: :created_by_id, method_name: :created_by_data do
      attribute :name, :string, default: "Unknown User"
      attribute :email, :string, default: "<EMAIL>"
      attribute :user_roles_list, :user_role_collection, default: []
      attribute :avatar_attributes, :avatar_attributes_type, default: {}
      attribute :status, :string, default: "active"

      # Define query configuration
      query_config(
        searchable: [:name, :email, :status],
        filterable: [:name, :email, :status],
        sortable: [:name, :email, :status],
        includable: [:user_roles, :avatar_attributes]
      )

      # Define available scopes
      scope :with_role, "Filter users by role name"
      scope :with_permission, "Filter users by permission name"
      scope :in_project, "Filter users by project"
      scope :in_projects, "Filter users by multiple projects"
    end

    # Scopes for filtering cases by assigned user attributes
    scope :assigned_user_name_eq, ->(value) { with_assigned_user_data.filter_rpc(name_eq: value) }
    scope :assigned_user_name_cont, ->(value) { with_assigned_user_data.filter_rpc(name_cont: value) }
    scope :assigned_user_email_eq, ->(value) { with_assigned_user_data.filter_rpc(email_eq: value) }
    scope :assigned_user_email_cont, ->(value) { with_assigned_user_data.filter_rpc(email_cont: value) }

    # Scopes for filtering cases by created by user attributes
    scope :created_by_name_eq, ->(value) { with_created_by_data.filter_rpc(name_eq: value) }
    scope :created_by_name_cont, ->(value) { with_created_by_data.filter_rpc(name_cont: value) }
    scope :created_by_email_eq, ->(value) { with_created_by_data.filter_rpc(email_eq: value) }
    scope :created_by_email_cont, ->(value) { with_created_by_data.filter_rpc(email_cont: value) }

    # Bulk loading scopes
    scope :with_user_data, -> { with_assigned_user_data.with_created_by_data }
  end

  # Helper methods for accessing user data
  # These replace the TODO methods in the Case model

  # Assigned user helper methods
  def assigned_user_name
    return "User ##{assigned_user_id}" unless assigned_user_id.present?

    # Access the name attribute from the assigned user data
    # ActiveRpc will automatically fetch the data if not cached
    assigned_user_data&.message&.name rescue "User ##{assigned_user_id}"
  end

  def assigned_user_email
    return nil unless assigned_user_id.present?

    # Access the email attribute from the assigned user data
    assigned_user_data&.message&.email rescue nil
  end

  def assigned_user
    return nil unless assigned_user_id.present?

    # Return the full user data response
    assigned_user_data&.message
  end

  # Created by user helper methods
  def created_by_name
    return "User ##{created_by_id}" unless created_by_id.present?

    # Access the name attribute from the created by user data
    created_by_data&.message&.name rescue "User ##{created_by_id}"
  end

  def created_by_email
    return nil unless created_by_id.present?

    # Access the email attribute from the created by user data
    created_by_data&.message&.email rescue nil
  end

  def created_by_user
    return nil unless created_by_id.present?

    # Return the full user data response
    created_by_data&.message
  end

  # Convenience methods for checking user status
  def assigned_user_active?
    assigned_user_data&.message&.status == "active" rescue false
  end

  def created_by_user_active?
    created_by_data&.message&.status == "active" rescue false
  end

  module ClassMethods
    # Allow RPC filtering scopes to be used by Ransack
    def ransackable_scopes(auth_object = nil)
      base_scopes = defined?(super) ? super(auth_object) : []
      user_scopes = %w[
        assigned_user_name_eq assigned_user_name_cont
        assigned_user_email_eq assigned_user_email_cont
        created_by_name_eq created_by_name_cont
        created_by_email_eq created_by_email_cont
      ]
      (base_scopes + user_scopes).uniq
    end

    def ransackable_attributes(auth_object = nil)
      base_attributes = defined?(super) ? super(auth_object) : []
      user_attributes = %w[
        assigned_user_name assigned_user_email
        created_by_name created_by_email
      ]
      (base_attributes + user_attributes).uniq
    end
  end
end
