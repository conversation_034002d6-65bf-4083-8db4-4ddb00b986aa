class FormTemplate < ApplicationRecord
  # include Athar::Commons::Models::Concerns::Ransackable  # Temporarily disabled due to Rails 8.0 compatibility

  # Associations
  has_many :form_sections, dependent: :destroy
  has_many :form_fields, through: :form_sections
  has_many :form_submissions, dependent: :destroy

  # Validations
  validates :name, presence: true, uniqueness: true, length: { minimum: 2, maximum: 100 }
  validates :title, presence: true, length: { minimum: 2, maximum: 200 }
  validates :sequence_order, presence: true, numericality: { greater_than_or_equal_to: 0 }
  validates :project_id, presence: true

  # Scopes
  scope :active, -> { where(active: true) }
  scope :inactive, -> { where(active: false) }

  scope :by_target_role, ->(role) { where(target_role: [ role, "both" ]) if role.present? }
  scope :ordered, -> { order(:sequence_order, :name) }
  scope :for_project, ->(project_id) { where(project_id: project_id) if project_id.present? }

  # Enums

  enum :target_role, {
    case_manager: "case_manager",
    supervisor: "supervisor",
    both: "both"
  }

  # Ransack configuration
  def self.ransackable_attributes(auth_object = nil)
    %w[name title target_role sequence_order active project_id created_at updated_at]
  end

  def self.ransackable_associations(auth_object = nil)
    %w[form_sections form_fields form_submissions]
  end

  # Class methods
  # Define form sequence for progressive completion (based on actual forms)
  def self.form_sequence
    {
      'consent_assent' => [],
      'registration_rapid_assessment' => [ 'consent_assent' ],
      'comprehensive_assessment' => [ 'consent_assent', 'registration_rapid_assessment' ],
      'service_planning' => [ 'consent_assent', 'registration_rapid_assessment', 'comprehensive_assessment' ]
    }
  end

  def self.setup_prerequisites!
    form_sequence.each do |form_name, prerequisites|
      template = find_by(name: form_name)
      next unless template

      template.update!(prerequisite_forms: prerequisites)
    end
  end

  # Class methods
  # Define form sequence for progressive completion (based on actual forms)
  def self.form_sequence
    {
      'consent_assent' => [],
      'registration_rapid_assessment' => ['consent_assent'],
      'comprehensive_assessment' => ['consent_assent', 'registration_rapid_assessment'],
      'case_plan_implementation' => ['consent_assent', 'registration_rapid_assessment', 'comprehensive_assessment']
    }
  end

  def self.setup_prerequisites!
    form_sequence.each do |form_name, prerequisites|
      template = find_by(name: form_name)
      next unless template

      template.update!(prerequisite_forms: prerequisites)
    end
  end

  # Instance methods
  def can_be_accessed_by?(user)
    return false unless active?
    return true if target_role == "both"

    case target_role
    when "case_manager"
      user.case_manager?
    when "supervisor"
      user.supervisor?
    else
      false
    end
  end

  def prerequisites_met?(case_obj)
    return true if prerequisite_forms.blank?

    prerequisite_forms.all? do |prereq_name|
      prereq_template = FormTemplate.find_by(name: prereq_name, project_id: project_id)
      next false unless prereq_template

      submission = case_obj.form_submissions.find_by(form_template: prereq_template)
      submission&.form_completed?
    end
  end

  def total_fields_count
    form_fields.count
  end

  def required_fields_count
    form_fields.where(required: true).count
  end

  def form_structure
    {
      id: id,
      name: name,
      title: title,
      description: description,
      target_role: target_role,
      sequence_order: sequence_order,
      sections: form_sections.includes(:form_fields).ordered.map(&:section_structure)
    }
  end

  def duplicate_for_project(new_project_id, new_name = nil)
    new_template = self.dup
    new_template.name = new_name || "#{name}_copy_#{Time.current.to_i}"
    new_template.project_id = new_project_id
    new_template.active = false

    if new_template.save
      form_sections.each do |section|
        section.duplicate_for_template(new_template)
      end
    end

    new_template
  end
end
