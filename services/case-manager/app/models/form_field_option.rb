class FormFieldOption < ApplicationRecord
  include Athar::Commons::Models::Concerns::Ransackable

  # Associations
  belongs_to :form_field

  # Validations
  validates :option_key, presence: true, length: { minimum: 1, maximum: 100 }
  validates :option_value, presence: true, length: { minimum: 1, maximum: 200 }
  validates :display_order, presence: true, numericality: { greater_than: 0 }
  validates :option_key, uniqueness: { scope: :form_field_id }

  # Scopes
  scope :active, -> { where(active: true) }
  scope :inactive, -> { where(active: false) }
  scope :ordered, -> { order(:display_order) }

  # Ransack configuration
  def self.ransackable_attributes(auth_object = nil)
    %w[option_key option_value display_order active created_at updated_at]
  end

  def self.ransackable_associations(auth_object = nil)
    %w[form_field]
  end

  # Instance methods
  def option_structure
    {
      id: id,
      option_key: option_key,
      option_value: option_value,
      display_order: display_order,
      active: active,
      metadata: metadata
    }
  end

  def duplicate_for_field(new_field)
    new_option = self.dup
    new_option.form_field = new_field
    new_option.save
    new_option
  end

  def is_selected?(form_data)
    field_value = form_data[form_field.field_name]
    
    case form_field.field_type
    when 'select', 'radio'
      field_value.to_s == option_key.to_s
    when 'checkbox'
      field_value.is_a?(Array) && field_value.include?(option_key.to_s)
    else
      false
    end
  end

  def conditional_metadata(key)
    metadata&.dig(key)
  end

  def has_metadata?(key)
    metadata&.key?(key)
  end
end
