# Service Categories
class ServiceCategory < LookupBase
  has_many :services

  # Validations
  validates :sector, presence: true
  validates :target_group, presence: true

  enum :sector, {
    health: 0,
    education: 1,
    legal: 2,
    social: 3,
    financial: 4,
    housing: 5,
    employment: 6
  }

  enum :target_group, {
    children: 0,
    women: 1,
    men: 2,
    elderly: 3,
    disabled: 4,
    all_groups: 5
  }

  # Additional scopes for filtering
  scope :by_sector, ->(sector) { where(sector: sector) }
  scope :by_target_group, ->(target_group) { where(target_group: target_group) }

  def api_representation
    super.merge({
      sector: sector,
      target_group: target_group
    })
  end
end
