# Geographic Locations (Hierarchical)
class GeographicLocation < LookupBase
  belongs_to :parent_location, class_name: 'GeographicLocation', optional: true
  has_many :child_locations, class_name: 'GeographicLocation', foreign_key: 'parent_location_id'

  validates :iso_code, presence: true, if: -> { country? }

  enum :location_type, {
    country: 0,
    governorate: 1,
    district: 2,
    city: 3,
    neighborhood: 4
  }

  scope :countries, -> { where(location_type: 'country') }
  scope :governorates, -> { where(location_type: 'governorate') }
  scope :cities, -> { where(location_type: 'city') }
  scope :children_of, ->(parent) { where(parent_location: parent) }

  def api_representation
    super.merge({
      location_type: location_type,
      iso_code: iso_code,
      parent_location_id: parent_location_id,
      has_children: child_locations.active.any?
    })
  end
end
