class FieldTypeDefinition < ApplicationRecord
  # Associations
  has_many :form_fields, foreign_key: 'field_type', primary_key: 'name'

  # Validations
  validates :name, presence: true, uniqueness: true
  validates :input_type, presence: true, inclusion: {
    in: %w[
      text textarea number decimal
      date datetime time
      select radio checkbox
      file image signature
      location coordinates
      phone email url
      rich_text markdown
      rating scale slider
      yes_no true_false
      currency percentage
      barcode qr_code
      lookup_single lookup_multiple
      calculated readonly hidden
      multi_step_wizard
      repeatable_group
      conditional_section
    ]
  }
  validates :validation_schema, format: { with: /\A\{.*\}\z/, allow_blank: true } # JSON schema
  validates :default_config, format: { with: /\A\{.*\}\z/, allow_blank: true } # JSON config

  # Enums
  enum :data_type, {
    string: 0,
    integer: 1,
    decimal: 2,
    boolean: 3,
    date: 4,
    datetime: 5,
    json: 6,
    array: 7,
    object: 8
  }

  # Scopes
  scope :active, -> { where(active: true) }
  scope :by_input_type, ->(type) { where(input_type: type) }
  scope :by_data_type, ->(type) { where(data_type: type) }

  # Instance methods
  def render_config(field_instance = nil)
    config = JSON.parse(default_config || '{}')

    # Merge with field-specific configuration if provided
    if field_instance&.field_config.present?
      begin
        field_config = JSON.parse(field_instance.field_config)
        config.merge!(field_config)
      rescue JSON::ParserError
        Rails.logger.warn "Invalid JSON in field_config for field #{field_instance.id}"
      end
    end

    # Add lookup data if applicable
    if input_type.include?('lookup') && field_instance&.lookup_source_type.present?
      config['options'] = field_instance.lookup_options || []
    end

    config
  end

  def supports_validation?(validation_type)
    return false unless validation_schema.present?
    
    begin
      schema = JSON.parse(validation_schema)
      schema.dig('supported_validations')&.include?(validation_type)
    rescue JSON::ParserError
      false
    end
  end

  def default_validation_rules
    return {} unless validation_schema.present?
    
    begin
      schema = JSON.parse(validation_schema)
      schema.dig('default_rules') || {}
    rescue JSON::ParserError
      {}
    end
  end

  # Class methods
  def self.for_field_type(field_type)
    find_by(name: field_type)
  end

  def self.available_input_types
    distinct.pluck(:input_type).sort
  end

  def self.by_category
    {
      'Basic Input' => where(input_type: %w[text textarea number decimal]),
      'Date & Time' => where(input_type: %w[date datetime time]),
      'Selection' => where(input_type: %w[select radio checkbox yes_no true_false]),
      'File & Media' => where(input_type: %w[file image signature]),
      'Location' => where(input_type: %w[location coordinates]),
      'Contact' => where(input_type: %w[phone email url]),
      'Rich Content' => where(input_type: %w[rich_text markdown]),
      'Interactive' => where(input_type: %w[rating scale slider]),
      'Financial' => where(input_type: %w[currency percentage]),
      'Advanced' => where(input_type: %w[barcode qr_code lookup_single lookup_multiple calculated readonly hidden]),
      'Complex' => where(input_type: %w[multi_step_wizard repeatable_group conditional_section])
    }
  end
end
