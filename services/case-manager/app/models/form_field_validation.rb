class FormFieldValidation < ApplicationRecord
  # include Athar::Commons::Models::Concerns::Ransackable  # Temporarily disabled due to Rails 8.0 compatibility

  # Associations
  belongs_to :form_field

  # Validations
  validates :validation_value, presence: true
  validates :error_message, presence: true, length: { minimum: 5, maximum: 200 }
  validates :priority, numericality: { greater_than_or_equal_to: 0 }

  # Scopes
  scope :active, -> { where(active: true) }
  scope :inactive, -> { where(active: false) }
  scope :ordered, -> { order(:priority, :id) }
  scope :by_type, ->(type) { where(validation_type: type) if type.present? }

  # Enums
  enum :validation_type, {
    required: 'required',
    min_length: 'min_length',
    max_length: 'max_length',
    min_value: 'min_value',
    max_value: 'max_value',
    pattern: 'pattern',
    email: 'email',
    phone: 'phone',
    date_range: 'date_range',
    custom: 'custom'
  }

  # Ransack configuration
  def self.ransackable_attributes(auth_object = nil)
    %w[validation_type validation_value error_message active priority created_at updated_at]
  end

  def self.ransackable_associations(auth_object = nil)
    %w[form_field]
  end

  # Instance methods
  def validate_value(value)
    return { valid: true } unless active?
    
    case validation_type
    when 'required'
      validate_required(value)
    when 'min_length'
      validate_min_length(value)
    when 'max_length'
      validate_max_length(value)
    when 'min_value'
      validate_min_value(value)
    when 'max_value'
      validate_max_value(value)
    when 'pattern'
      validate_pattern(value)
    when 'email'
      validate_email(value)
    when 'phone'
      validate_phone(value)
    when 'date_range'
      validate_date_range(value)
    when 'custom'
      validate_custom(value)
    else
      { valid: true }
    end
  end

  def validation_structure
    {
      id: id,
      validation_type: validation_type,
      validation_value: validation_value,
      error_message: error_message,
      active: active,
      priority: priority
    }
  end

  def duplicate_for_field(new_field)
    new_validation = self.dup
    new_validation.form_field = new_field
    new_validation.save
    new_validation
  end

  private

  def validate_required(value)
    if value.blank?
      { valid: false, error: error_message }
    else
      { valid: true }
    end
  end

  def validate_min_length(value)
    min_len = validation_value.to_i
    if value.to_s.length < min_len
      { valid: false, error: error_message }
    else
      { valid: true }
    end
  end

  def validate_max_length(value)
    max_len = validation_value.to_i
    if value.to_s.length > max_len
      { valid: false, error: error_message }
    else
      { valid: true }
    end
  end

  def validate_min_value(value)
    min_val = validation_value.to_f
    if value.to_f < min_val
      { valid: false, error: error_message }
    else
      { valid: true }
    end
  end

  def validate_max_value(value)
    max_val = validation_value.to_f
    if value.to_f > max_val
      { valid: false, error: error_message }
    else
      { valid: true }
    end
  end

  def validate_pattern(value)
    pattern = Regexp.new(validation_value)
    if value.to_s.match?(pattern)
      { valid: true }
    else
      { valid: false, error: error_message }
    end
  rescue RegexpError
    { valid: true } # Invalid regex, skip validation
  end

  def validate_email(value)
    email_pattern = /\A[\w+\-.]+@[a-z\d\-]+(\.[a-z\d\-]+)*\.[a-z]+\z/i
    if value.to_s.match?(email_pattern)
      { valid: true }
    else
      { valid: false, error: error_message }
    end
  end

  def validate_phone(value)
    # Simple phone validation - can be enhanced
    phone_pattern = /\A[\+]?[\d\s\-\(\)]{7,15}\z/
    if value.to_s.match?(phone_pattern)
      { valid: true }
    else
      { valid: false, error: error_message }
    end
  end

  def validate_date_range(value)
    begin
      date_value = Date.parse(value.to_s)
      range_config = JSON.parse(validation_value)
      
      min_date = range_config['min_date'] ? Date.parse(range_config['min_date']) : nil
      max_date = range_config['max_date'] ? Date.parse(range_config['max_date']) : nil
      
      if min_date && date_value < min_date
        return { valid: false, error: error_message }
      end
      
      if max_date && date_value > max_date
        return { valid: false, error: error_message }
      end
      
      { valid: true }
    rescue
      { valid: false, error: error_message }
    end
  end

  def validate_custom(value)
    # Custom validation logic can be implemented here
    # For now, just return valid
    { valid: true }
  end
end
