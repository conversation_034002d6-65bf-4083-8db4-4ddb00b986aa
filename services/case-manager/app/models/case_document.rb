class CaseDocument < ApplicationRecord
  # include Athar::Commons::Models::Concerns::Ransackable  # Temporarily disabled due to Rails 8.0 compatibility

  # Active Storage integration
  has_one_attached :file

  # Associations
  belongs_to :case, class_name: 'Case', foreign_key: 'case_id'

  # Validations
  validates :case_id, presence: true
  validates :uploaded_by_id, presence: true
  validates :file_name, presence: true, length: { minimum: 1, maximum: 255 }
  validates :file_type, presence: true
  validates :file_size, presence: true, numericality: { greater_than: 0 }
  validate :file_size_limit
  validate :allowed_file_types

  # Scopes
  scope :by_document_type, ->(type) { where(document_type: type) if type.present? }
  scope :recent, -> { order(created_at: :desc) }
  scope :by_file_type, ->(type) { where(file_type: type) if type.present? }
  scope :uploaded_by, ->(user_id) { where(uploaded_by_id: user_id) if user_id.present? }

  # Enums
  enum :document_type, {
    identification: 'identification',
    legal_document: 'legal_document',
    medical_record: 'medical_record',
    assessment_form: 'assessment_form',
    case_plan: 'case_plan',
    photo: 'photo',
    other: 'other'
  }

  # Constants
  MAX_FILE_SIZE = 10.megabytes
  ALLOWED_FILE_TYPES = %w[
    image/jpeg image/png image/gif image/webp
    application/pdf
    application/msword application/vnd.openxmlformats-officedocument.wordprocessingml.document
    application/vnd.ms-excel application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
    text/plain text/csv
  ].freeze

  # Ransack configuration
  def self.ransackable_attributes(auth_object = nil)
    %w[case_id uploaded_by_id file_name file_type file_size document_type description created_at updated_at]
  end

  def self.ransackable_associations(auth_object = nil)
    %w[case]
  end

  # Instance methods
  def file_url
    return nil unless file.attached?
    
    # Generate a signed URL for secure access
    Rails.application.routes.url_helpers.rails_blob_url(file, only_path: true)
  end

  def file_download_url
    return nil unless file.attached?
    
    Rails.application.routes.url_helpers.rails_blob_url(file, disposition: 'attachment', only_path: true)
  end

  def is_image?
    file_type.start_with?('image/')
  end

  def is_pdf?
    file_type == 'application/pdf'
  end

  def is_document?
    file_type.include?('word') || file_type.include?('excel') || file_type.include?('text')
  end

  def file_size_human
    ActiveSupport::NumberHelper.number_to_human_size(file_size)
  end

  def can_be_viewed_by?(user)
    # Case managers can view documents for their cases
    # Supervisors can view all documents in their project
    return true if user.supervisor?
    return true if user.case_manager? && case_record.case_manager_id == user.id
    return true if uploaded_by_id == user.id
    
    false
  end

  def can_be_deleted_by?(user)
    # Only uploader or supervisor can delete
    return true if user.supervisor?
    return true if uploaded_by_id == user.id
    
    false
  end

  def document_summary
    {
      id: id,
      case_id: case_id,
      file_name: file_name,
      file_type: file_type,
      file_size: file_size,
      file_size_human: file_size_human,
      document_type: document_type,
      description: description,
      uploaded_by_id: uploaded_by_id,
      file_url: file_url,
      file_download_url: file_download_url,
      is_image: is_image?,
      is_pdf: is_pdf?,
      is_document: is_document?,
      metadata: metadata,
      created_at: created_at,
      updated_at: updated_at
    }
  end

  def generate_thumbnail
    return unless is_image? && file.attached?
    
    begin
      # Generate thumbnail using Active Storage variants
      file.variant(resize_to_limit: [200, 200])
    rescue => e
      Rails.logger.error "Failed to generate thumbnail for document #{id}: #{e.message}"
      nil
    end
  end

  def extract_metadata
    return unless file.attached?
    
    metadata_hash = {}
    
    begin
      if is_image?
        # Extract image metadata
        metadata_hash[:dimensions] = extract_image_dimensions
      elsif is_pdf?
        # Extract PDF metadata
        metadata_hash[:pages] = extract_pdf_page_count
      end
      
      metadata_hash[:uploaded_at] = created_at
      metadata_hash[:original_filename] = file.filename.to_s
      
      update_column(:metadata, metadata_hash)
    rescue => e
      Rails.logger.error "Failed to extract metadata for document #{id}: #{e.message}"
    end
  end

  def self.storage_usage_for_case(case_id)
    where(case_id: case_id).sum(:file_size)
  end

  def self.document_count_by_type(case_id)
    where(case_id: case_id).group(:document_type).count
  end

  private

  def file_size_limit
    return unless file_size
    
    if file_size > MAX_FILE_SIZE
      errors.add(:file_size, "must be less than #{ActiveSupport::NumberHelper.number_to_human_size(MAX_FILE_SIZE)}")
    end
  end

  def allowed_file_types
    return unless file_type
    
    unless ALLOWED_FILE_TYPES.include?(file_type)
      errors.add(:file_type, "is not allowed. Allowed types: #{ALLOWED_FILE_TYPES.join(', ')}")
    end
  end

  def extract_image_dimensions
    return nil unless file.attached? && is_image?
    
    # This would require image processing gems like MiniMagick or Vips
    # For now, return placeholder
    { width: nil, height: nil }
  end

  def extract_pdf_page_count
    return nil unless file.attached? && is_pdf?
    
    # This would require PDF processing gems like pdf-reader
    # For now, return placeholder
    nil
  end
end
