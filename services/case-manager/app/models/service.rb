class Service < ApplicationRecord
  include Athar::Commons::Models::Concerns::Ransackable

  # Enums
  enum :category, {
    health: "health",
    education: "education",
    legal: "legal",
    social: "social",
    financial: "financial",
    housing: "housing",
    employment: "employment"
  }

  enum :provider_type, {
    internal: "internal",
    external: "external",
    partner: "partner"
  }

  # Validations
  validates :name, presence: true, length: { minimum: 2, maximum: 100 }
  validates :description, presence: true, length: { minimum: 10, maximum: 1000 }
  validates :category, presence: true
  validates :provider_type, presence: true

  # Callbacks
  before_validation :set_default_status, on: :create

  # Scopes
  scope :by_category, ->(category) { where(category: category) if category.present? }
  scope :by_provider_type, ->(provider_type) { where(provider_type: provider_type) if provider_type.present? }
  scope :recent, -> { order(created_at: :desc) }

  # Ransack configuration
  def self.ransackable_attributes(auth_object = nil)
    %w[name description category provider_type created_at updated_at]
  end

  def self.ransackable_associations(auth_object = nil)
    %w[]
  end

  # Instance methods
  # Note: Enum methods are automatically provided:
  # - provider_type: internal?, external?, partner?
  # - category: health?, education?, legal?, social?, financial?, housing?, employment?



  # Class methods for statistics
  def self.category_counts
    group(:category).count
  end

  def self.provider_type_counts
    group(:provider_type).count
  end

  def self.most_common_category
    category_counts.max_by { |_, count| count }&.first
  end

  def self.most_common_provider_type
    provider_type_counts.max_by { |_, count| count }&.first
  end

  private

  def set_default_status
    # Services don't have a status field in the current schema
    # This is a placeholder for future enhancement
  end
end
