class Comment < ApplicationRecord
  # include Athar::Commons::Models::Concerns::Ransackable  # Temporarily disabled due to Rails 8.0 compatibility

  # Associations
  belongs_to :case, class_name: 'Case', foreign_key: 'case_id'
  belongs_to :user
  belongs_to :commentable, polymorphic: true
  belongs_to :parent_comment, class_name: 'Comment', optional: true
  belongs_to :resolved_by, class_name: 'User', optional: true
  has_many :replies, class_name: 'Comment', foreign_key: 'parent_comment_id', dependent: :destroy

  # Validations
  validates :case_id, presence: true
  validates :user_id, presence: true
  validates :content, presence: true, length: { minimum: 5, maximum: 2000 }
  validates :comment_type, presence: true
  validates :status, presence: true

  # Scopes
  scope :case_level, -> { where(comment_type: 0) }
  scope :field_level, -> { where(comment_type: 1) }
  scope :open, -> { where(status: 0) }
  scope :resolved, -> { where(status: 1) }
  scope :archived, -> { where(status: 2) }
  scope :recent, -> { order(created_at: :desc) }
  scope :top_level, -> { where(parent_comment_id: nil) }
  scope :replies, -> { where.not(parent_comment_id: nil) }

  # Enums
  enum :comment_type, {
    case_level: 0,
    field_level: 1
  }

  enum :status, {
    open: 0,
    resolved: 1,
    archived: 2
  }

  # Ransack configuration
  def self.ransackable_attributes(auth_object = nil)
    %w[case_id user_id content comment_type status resolved_at resolved_by_id created_at updated_at]
  end

  def self.ransackable_associations(auth_object = nil)
    %w[case user commentable parent_comment replies resolved_by]
  end

  # Instance methods
  def resolve!(resolved_by_user, resolution_note = nil)
    return false if resolved?

    resolved_by_id = resolved_by_user.is_a?(User) ? resolved_by_user.id : resolved_by_user

    update!(
      status: :resolved,
      resolved_at: Time.current,
      resolved_by_id: resolved_by_id
    )
  end

  def reopen!
    return false unless resolved?
    
    update!(
      status: :open,
      resolved_at: nil,
      resolved_by_id: nil
    )
  end

  def archive!
    update!(status: :archived)
  end

  def can_be_resolved_by?(user)
    # Case managers can resolve their own comments
    # Supervisors can resolve any comments
    return true if user.supervisor?
    return true if user_id == user.id
    return true if user.case_manager? && commentable.case_manager_id == user.id
    
    false
  end

  def can_be_edited_by?(user)
    # Only the comment author can edit within 24 hours
    return false if created_at < 24.hours.ago
    user_id == user.id
  end

  def field_name
    return nil unless field_level?
    commentable_type == 'FormField' ? commentable.field_name : nil
  end

  def field_label
    return nil unless field_level?
    commentable_type == 'FormField' ? commentable.label : nil
  end

  def comment_thread
    if parent_comment_id.present?
      parent_comment.comment_thread
    else
      [self] + replies.includes(:replies).order(:created_at)
    end
  end

  def reply_count
    replies.count
  end

  def is_reply?
    parent_comment_id.present?
  end

  def is_top_level?
    parent_comment_id.nil?
  end

  def comment_summary
    {
      id: id,
      case_id: case_id,
      user_id: user_id,
      content: content,
      comment_type: comment_type,
      status: status,
      commentable: {
        type: commentable_type,
        id: commentable_id,
        field_name: field_name,
        field_label: field_label
      },
      parent_comment_id: parent_comment_id,
      reply_count: reply_count,
      resolved_at: resolved_at,
      resolved_by_id: resolved_by_id,
      created_at: created_at,
      updated_at: updated_at
    }
  end

  def notification_data
    {
      comment_id: id,
      case_id: case_id,
      case_number: commentable.case_number,
      comment_type: comment_type,
      content_preview: content.truncate(100),
      commentable_info: {
        type: commentable_type,
        field_name: field_name,
        field_label: field_label
      },
      author_id: user_id,
      created_at: created_at
    }
  end

  def self.for_case_dashboard(case_id)
    where(case_id: case_id)
      .includes(:parent_comment, :replies)
      .top_level
      .recent
      .limit(10)
  end

  def self.unresolved_count_for_case(case_id)
    where(case_id: case_id, status: :open).count
  end

  def self.field_comments_for_form(case_id, form_template_id)
    joins("JOIN form_fields ON comments.commentable_type = 'FormField' AND comments.commentable_id = form_fields.id")
      .joins("JOIN form_sections ON form_fields.form_section_id = form_sections.id")
      .where(case_id: case_id)
      .where("form_sections.form_template_id = ?", form_template_id)
      .field_level
  end
end
