class ServiceSerializer
  include JSONAPI::Serializer

  attributes :name, :description, :category, :provider_type, :created_at, :updated_at

  # Computed attributes
  attribute :is_internal do |service|
    service.internal?
  end

  attribute :is_external do |service|
    service.external?
  end

  attribute :is_partner do |service|
    service.partner?
  end

  attribute :is_health_service do |service|
    service.health?
  end

  attribute :is_education_service do |service|
    service.education?
  end

  attribute :is_legal_service do |service|
    service.legal?
  end

  attribute :is_social_service do |service|
    service.social?
  end

  attribute :is_financial_service do |service|
    service.financial?
  end

  attribute :is_housing_service do |service|
    service.housing?
  end

  attribute :is_employment_service do |service|
    service.employment?
  end

  # Relationships
  # Note: Service is a generic catalog, not tied to specific cases
end
