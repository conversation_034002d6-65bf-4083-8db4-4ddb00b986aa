class CaseSerializer
  include JSONAPI::Serializer

  attributes :assigned_user_id, :status, :approval_status,
             :started_at, :closed_at, :created_at, :updated_at

  # Computed attributes
  attribute :duration_in_days do |case_record|
    case_record.duration_in_days
  end

  attribute :is_active do |case_record|
    case_record.is_active?
  end

  attribute :is_closed do |case_record|
    case_record.is_closed?
  end

  attribute :is_suspended do |case_record|
    case_record.is_suspended?
  end

  attribute :requires_approval do |case_record|
    case_record.requires_approval?
  end

  attribute :is_approved do |case_record|
    case_record.is_approved?
  end

  attribute :is_rejected do |case_record|
    case_record.is_rejected?
  end

  attribute :can_be_closed do |case_record|
    case_record.can_be_closed?
  end

  # Relationships
  belongs_to :assigned_user, record_type: :user, serializer: :user
  # Note: Services are now a generic catalog, not tied to specific cases
end
