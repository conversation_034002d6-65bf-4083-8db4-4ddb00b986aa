# frozen_string_literal: true

class UserSerializer
  include JSONAPI::Serializer

  set_type :user

  # Basic attributes from User model
  attributes :id, :name, :email

  # User context attributes
  attribute :user_id do |user|
    user.id
  end

  attribute :avatar_url do |user|
    # Case management system doesn't have avatars, return nil
    nil
  end

  # Project information
  attribute :project_id do |user|
    user.project&.id
  end

  attribute :project_name do |user|
    user.project&.name
  end

  attribute :global_user do |user|
    user.global_user?
  end

  # Role information
  attribute :role_name do |user|
    user.role&.name
  end

  attribute :role_display_name do |user|
    user.role&.name&.humanize
  end

  # Relationships - create a synthetic user_role to match People service pattern
  has_many :user_roles, serializer: UserRoleSerializer do |user|
    # Create a synthetic user_role object since AtharAuth::User has direct role/project associations
    if user.role
      [
        OpenStruct.new(
          id: user.id.to_s,  # Use user ID as the user_role ID
          is_default: true,
          role: user.role,
          project: user.project
        )
      ]
    else
      []
    end
  end

  belongs_to :role, serializer: RoleSerializer, id_method_name: :name do |user|
    user.role
  end

  belongs_to :project, serializer: ProjectSerializer do |user|
    user.project
  end
end
