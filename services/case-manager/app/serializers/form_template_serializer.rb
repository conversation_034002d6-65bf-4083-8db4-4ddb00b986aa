class FormTemplateSerializer
  include JSONAPI::Serializer

  # Basic attributes
  attributes :name, :title, :description, :active, :target_role,
             :sequence_order, :prerequisite_forms, :project_id, :created_at, :updated_at

  # Computed attributes
  attribute :form_type do |object|
    object.name # Use name as form_type for compatibility
  end

  attribute :sections_count do |object|
    object.form_sections.count
  end

  attribute :fields_count do |object|
    object.form_sections.sum { |section| section.form_fields.count }
  end

  attribute :estimated_completion_time do |object|
    # Estimate 30 seconds per field
    fields_count = object.form_sections.sum { |section| section.form_fields.count }
    (fields_count * 0.5).round # minutes
  end

  # Relationships (commented out until related serializers are created)
  # has_many :form_sections
  # has_many :form_submissions

end
