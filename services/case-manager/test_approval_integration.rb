#!/usr/bin/env ruby
# Test script for Case Management Approval Integration
# Run this from the case-manager service directory: ruby test_approval_integration.rb

require_relative 'config/environment'

puts "🧪 Testing Case Management Approval Integration"
puts "=" * 60

# Test 1: Check if Case model has approval integration
puts "\n1. Testing Case model approval integration..."

begin
  # Check if Case model includes Approvable concern
  if Case.included_modules.any? { |mod| mod.to_s.include?('Approvable') }
    puts "✅ Case model includes Approvable concern"
  else
    puts "❌ Case model does not include Approvable concern"
  end

  # Test approval methods
  case_instance = Case.new
  
  approval_methods = %w[
    approval_action
    system_name
    approval_context
    on_approval_status_change
    submit_for_approval
    approval_status
    approval_approved?
    approval_pending?
    approval_rejected?
  ]
  
  missing_methods = approval_methods.reject { |method| case_instance.respond_to?(method) }
  
  if missing_methods.empty?
    puts "✅ All required approval methods are present"
  else
    puts "❌ Missing approval methods: #{missing_methods.join(', ')}"
  end

rescue => e
  puts "❌ Error testing Case model: #{e.message}"
end

# Test 2: Check approval context generation
puts "\n2. Testing approval context generation..."

begin
  # Create a test case (without saving to DB)
  test_case = Case.new(
    case_number: "TEST-2025-0001",
    case_type: "general_protection",
    priority_level: 3,
    case_manager_id: 1,
    project_id: 1,
    created_by_id: 1,
    beneficiary_cache: { 'name' => 'Test Beneficiary' }
  )
  
  context = test_case.approval_context
  
  required_keys = %w[case_id case_number case_type priority_level case_manager_id project_id created_by_id]
  missing_keys = required_keys.reject { |key| context.key?(key.to_sym) }
  
  if missing_keys.empty?
    puts "✅ Approval context contains all required keys"
    puts "   Context keys: #{context.keys.join(', ')}"
  else
    puts "❌ Missing context keys: #{missing_keys.join(', ')}"
  end

rescue => e
  puts "❌ Error testing approval context: #{e.message}"
end

# Test 3: Check form completion methods
puts "\n3. Testing form completion methods..."

begin
  test_case = Case.new(project_id: 1)
  
  form_methods = %w[
    form_completion_summary
    can_submit_for_approval?
    overall_completion_percentage
    next_required_form
  ]
  
  missing_form_methods = form_methods.reject { |method| test_case.respond_to?(method) }
  
  if missing_form_methods.empty?
    puts "✅ All form completion methods are present"
  else
    puts "❌ Missing form completion methods: #{missing_form_methods.join(', ')}"
  end

rescue => e
  puts "❌ Error testing form completion methods: #{e.message}"
end

# Test 4: Check User model enhancements
puts "\n4. Testing User model case management enhancements..."

begin
  user_instance = User.new
  
  user_methods = %w[
    case_manager?
    supervisor?
    project_manager?
    can_manage_case?
    can_approve_cases?
    can_create_cases?
    accessible_cases
    case_workload_summary
    supervision_summary
  ]
  
  missing_user_methods = user_methods.reject { |method| user_instance.respond_to?(method) }
  
  if missing_user_methods.empty?
    puts "✅ All User case management methods are present"
  else
    puts "❌ Missing User methods: #{missing_user_methods.join(', ')}"
  end

rescue => e
  puts "❌ Error testing User model: #{e.message}"
end

# Test 5: Check model associations
puts "\n5. Testing model associations..."

begin
  associations_test = {
    'Case' => %w[form_submissions comments case_documents created_by],
    'User' => %w[managed_cases created_cases case_comments uploaded_documents],
    'FormTemplate' => %w[form_sections form_submissions],
    'FormSubmission' => %w[form_template case form_field_values],
    'Comment' => %w[case user commentable]
  }
  
  associations_test.each do |model_name, expected_associations|
    model_class = model_name.constantize
    actual_associations = model_class.reflect_on_all_associations.map(&:name).map(&:to_s)
    
    missing_associations = expected_associations - actual_associations
    
    if missing_associations.empty?
      puts "✅ #{model_name} has all expected associations"
    else
      puts "❌ #{model_name} missing associations: #{missing_associations.join(', ')}"
    end
  end

rescue => e
  puts "❌ Error testing associations: #{e.message}"
end

puts "\n" + "=" * 60
puts "🏁 Test completed!"
puts "\nNext steps:"
puts "1. Run database migrations: rails db:migrate"
puts "2. Run database seeds: rails db:seed"
puts "3. Test with actual data in development environment"
puts "4. Run RSpec tests: bundle exec rspec"
