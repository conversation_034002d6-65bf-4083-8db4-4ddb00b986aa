name: athar-case-manager

services:
  app:
    hostname: case-manager-app
    build:
      context: ./
      dockerfile: ./development.dockerfile
      args:
        BUNDLE_GEM__FURY__IO: ${BUNDLE_GEM__FURY__IO}
    #command: tail -f /dev/null
    #command: debug
    # command: server
    env_file:
      - docker-compose.env
    ports:
      - "1234:1234"
    depends_on:
      - case-manager-db
      # - redis
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.cm.rule=Host(`cm.athar.test`)"
      - "traefik.http.routers.cm.entrypoints=web,websecure"
      - "traefik.http.services.cm.loadbalancer.server.port=3001"
      - "traefik.http.routers.cm.tls=false"

      # Health Check Configuration
      - "traefik.http.services.cm.loadbalancer.healthcheck.path=/up"
      - "traefik.http.services.cm.loadbalancer.healthcheck.interval=10s"
      - "traefik.http.services.cm.loadbalancer.healthcheck.timeout=3s"
    networks:
      - athar-network
    volumes:
      - ./config/master.key:/rails/config/master.key
      - ./:/rails
      - /Users/<USER>/workspace/athar/athar-ems/gems/auth-gem:/Users/<USER>/workspace/athar/athar-ems/gems/auth-gem
      - /Users/<USER>/workspace/athar/athar-ems/gems/rpc-gem:/Users/<USER>/workspace/athar/athar-ems/gems/rpc-gem
      - /Users/<USER>/workspace/athar/athar-ems/gems/commons-gem:/Users/<USER>/workspace/athar/athar-ems/gems/commons-gem
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: "1.0"
        reservations:
          memory: 512M
          cpus: "0.5"

  # sidekiq:
  #   hostname: case-manager-sidekiq
  #   build:
  #     context: ./
  #     dockerfile: ./development.dockerfile
  #     args:
  #       BUNDLE_GEM__FURY__IO: ${BUNDLE_GEM__FURY__IO}
  #   command: bundle exec sidekiq -C config/sidekiq.yml
  #   env_file:
  #     - docker-compose.env
  #   networks:
  #     - athar-network
  #   depends_on:
  #     - case-manager-db
  #     - redis
  #   volumes:
  #     - ./config/master.key:/rails/config/master.key
  #     - ./:/rails
  #   labels:
  #     - "traefik.enable=false"
  #   deploy:
  #     resources:
  #       limits:
  #         memory: 1G
  #         cpus: '1.0'
  #       reservations:
  #         memory: 256M
  #         cpus: '0.25'

  case-manager-db:
    image: postgres:17
    container_name: case-manager-db
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_DB: case_manager_athar_db
    networks:
      - athar-network
    ports:
      - "5435:5432"
    labels:
      - "traefik.enable=false"
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: "1.0"
        reservations:
          memory: 128M
          cpus: "0.25"

  # redis:
  #   image: redis:7-alpine
  #   container_name: case-manager-redis
  #   networks:
  #     - athar-network
  #   ports:
  #     - "6380:6379"
  #   labels:
  #     - "traefik.enable=false"
  #   deploy:
  #     resources:
  #       limits:
  #         memory: 256M
  #         cpus: "0.5"
  #       reservations:
  #         memory: 64M
  #         cpus: "0.1"

networks:
  athar-network:
    external: true
    name: athar-backend_athar-network
