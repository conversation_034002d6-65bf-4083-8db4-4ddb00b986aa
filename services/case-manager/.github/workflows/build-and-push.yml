name: <PERSON>uild and Push Docker Image

on:
  push:
    branches: [ develop ]
  release:
    types: [ published ]
  workflow_dispatch:
    inputs:
      ref:
        description: 'Tag or branch ref to use for image tag'
        required: false
        default: 'latest'

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Docker Hub
        uses: docker/login-action@v3
        with:
          registry: ${{ vars.REGISTRY_HOST }}
          username: ${{ vars.REGISTRY_USERNAME }}
          password: ${{ secrets.REGISTRY_PASSWORD }}

      - name: Determine Image Tag
        id: image_tag
        run: |
          if [ "${GITHUB_EVENT_NAME}" = "release" ]; then
            tag=${GITHUB_REF#refs/tags/}
          elif [ "${GITHUB_EVENT_NAME}" = "push" ] && [ "${GITHUB_REF}" = "refs/heads/develop" ]; then
            tag="develop"
          else
            tag="${{ github.event.inputs.ref }}"
          fi
          echo "Image tag is: $tag"
          echo "tag=$tag" >> $GITHUB_OUTPUT

      - name: Build and Push Docker Image
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./infra/production.dockerfile
          push: true
          tags: ${{ vars.REGISTRY_HOST }}/${{ vars.REGISTRY_USERNAME }}/case-manager:${{ steps.image_tag.outputs.tag }}
          build-args: |
            BUNDLE_GEM__FURY__IO=${{ secrets.BUNDLE_GEM__FURY__IO }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Trigger QA Deployment
        if: github.event_name == 'push' && github.ref == 'refs/heads/develop'
        uses: peter-evans/repository-dispatch@v3
        with:
          token: ${{ secrets.ATHAR_PAT }}
          repository: athar-association/athar-ems
          event-type: qa-deploy-trigger
          client-payload: '{"service": "case-manager", "tag": "${{ steps.image_tag.outputs.tag }}"}'
