# Case Management API Documentation

## Overview

This document outlines the API structure for the Athar Case Management System based on the comprehensive architecture plan. The API follows JSON:API specification and includes authentication, authorization, and comprehensive case management workflows.

## Base Configuration

- **Base URL**: `/api/v1`
- **Format**: JSON:API compliant
- **Authentication**: Bearer token (AtharAuth integration)
- **Authorization**: Role-based with project scoping
- **Pagination**: JSON:API standard with metadata

## Authentication

All API requests require authentication via Bearer token:

```http
Authorization: Bearer <session_token>
```

### Authentication Flow

```http
POST /api/session/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "secure_password",
  "project_id": "humanitarian_project_123"
}
```

**Response:**

```json
{
  "data": {
    "id": "user_123",
    "type": "user",
    "attributes": {
      "email": "<EMAIL>",
      "name": "<PERSON>",
      "roles": ["case_manager"],
      "project_id": "humanitarian_project_123"
    }
  },
  "meta": {
    "session_token": "eyJhbGciOiJIUzI1NiJ9...",
    "expires_at": "2025-01-21T10:00:00Z"
  }
}
```

## Core API Endpoints

### 1. Cases Management

#### List Cases

```http
GET /api/v1/cases?page[number]=1&page[size]=25&filter[status]=in_progress&include=case_manager,beneficiary
Authorization: Bearer <token>
```

**Response:**

```json
{
  "data": [
    {
      "id": "case_123",
      "type": "case",
      "attributes": {
        "case_number": "CASE-2025-001",
        "status": "in_progress",
        "priority_level": "high",
        "created_at": "2025-01-20T10:00:00Z",
        "updated_at": "2025-01-20T15:30:00Z",
        "form_completion_progress": {
          "total": 4,
          "completed": 2,
          "percentage": 50,
          "next_required_form": "comprehensive_assessment"
        },
        "approval_status": "pending",
        "has_unresolved_comments": true
      },
      "relationships": {
        "beneficiary": {
          "data": { "id": "ben_456", "type": "beneficiary" }
        },
        "case_manager": {
          "data": { "id": "cm_789", "type": "case_manager" }
        },
        "approval_request": {
          "data": { "id": "apr_101", "type": "approval_request" }
        }
      }
    }
  ],
  "included": [
    {
      "id": "ben_456",
      "type": "beneficiary",
      "attributes": {
        "first_name": "محمد",
        "last_name": "الأحمد",
        "age": 16,
        "sex": "male",
        "nationality_status": "refugee",
        "displacement_status": "asylum_seeker_refugee"
      }
    },
    {
      "id": "cm_789",
      "type": "case_manager",
      "attributes": {
        "name": "Sarah Johnson",
        "email": "<EMAIL>",
        "active_cases_count": 12
      }
    }
  ],
  "meta": {
    "total_count": 250,
    "current_page": 1,
    "per_page": 25,
    "total_pages": 10
  }
}
```

#### Create Case

```http
POST /api/v1/cases
Authorization: Bearer <token>
Content-Type: application/json

{
  "data": {
    "type": "case",
    "attributes": {
      "beneficiary_id": "ben_456",
      "assigned_user_id": "user_789",
      "status": "open",
      "priority_level": "medium",
      "started_at": "2025-01-20T10:00:00Z"
    }
  }
}
```

#### Get Case Progress

```http
GET /api/v1/cases/123/progress
Authorization: Bearer <token>
```

**Response:**

```json
{
  "data": {
    "id": "case_123",
    "type": "case_progress",
    "attributes": {
      "form_completion_status": {
        "consent_assent": {
          "completed": true,
          "completed_at": "2025-01-20T10:30:00Z",
          "form_submission_id": "fs_001"
        },
        "registration_rapid_assessment": {
          "completed": true,
          "completed_at": "2025-01-20T11:45:00Z",
          "form_submission_id": "fs_002"
        },
        "comprehensive_assessment": {
          "completed": false,
          "required": true,
          "can_start": true
        },
        "case_plan_implementation": {
          "completed": false,
          "required": true,
          "can_start": false,
          "blocked_by": ["comprehensive_assessment"]
        }
      },
      "overall_progress": {
        "percentage": 50,
        "completed_forms": 2,
        "total_required_forms": 4,
        "next_action": "Complete Comprehensive Assessment Form"
      },
      "approval_eligibility": {
        "can_submit_for_approval": false,
        "missing_requirements": [
          "comprehensive_assessment",
          "case_plan_implementation"
        ]
      }
    }
  }
}
```

#### Submit Case for Approval

```http
POST /api/v1/cases/123/submit_for_approval
Authorization: Bearer <token>
Content-Type: application/json

{
  "data": {
    "type": "approval_submission",
    "attributes": {
      "notes": "All required forms completed. Case ready for supervisor review."
    }
  }
}
```

### 2. Form Management

#### Get Form Template

```http
GET /api/v1/form_templates/registration_rapid_assessment?case_id=123&locale=en
Authorization: Bearer <token>
```

**Response:**

```json
{
  "data": {
    "id": "registration_rapid_assessment",
    "type": "form_template",
    "attributes": {
      "name": "registration_rapid_assessment",
      "title": "1.B REGISTRATION and RAPID ASSESSMENT FORM",
      "description": "Initial registration and rapid assessment of protection needs",
      "sequence_order": 2,
      "is_active": true,
      "prerequisite_forms": ["consent_assent"]
    },
    "relationships": {
      "form_sections": {
        "data": [
          { "id": "case_information", "type": "form_section" },
          { "id": "child_personal_details", "type": "form_section" },
          { "id": "care_arrangements", "type": "form_section" },
          { "id": "caregiver_information", "type": "form_section" },
          { "id": "child_protection_risks", "type": "form_section" },
          { "id": "family_details", "type": "form_section" }
        ]
      }
    }
  },
  "included": [
    {
      "id": "case_information",
      "type": "form_section",
      "attributes": {
        "name": "case_information",
        "title": "1. CASE INFORMATION",
        "display_order": 1,
        "is_required": true
      },
      "relationships": {
        "form_fields": {
          "data": [
            { "id": "date_case_identified", "type": "form_field" },
            { "id": "case_id", "type": "form_field" },
            { "id": "child_identification_method", "type": "form_field" }
          ]
        }
      }
    }
  ]
}
```

#### Submit Form Data

```http
POST /api/v1/form_submissions
Authorization: Bearer <token>
Content-Type: application/json

{
  "data": {
    "type": "form_submission",
    "attributes": {
      "case_id": "case_123",
      "form_template_name": "registration_rapid_assessment",
      "form_data": {
        "case_information": {
          "date_case_identified": "2025-01-20",
          "date_case_registered": "2025-01-20",
          "case_id": "CASE-2025-001",
          "caseworker_id": "CW-789",
          "agency": "Athar Association",
          "child_identification_method": "family_member"
        },
        "child_personal_details": {
          "first_name": "محمد",
          "last_name": "الأحمد",
          "date_of_birth": "2008-03-15",
          "sex": "male",
          "age": 16,
          "age_estimated": "no",
          "birth_registration_status": "birth_registered",
          "nationality_status": "other_nationality",
          "displacement_status": "asylum_seeker_refugee"
        }
      },
      "status": "completed"
    }
  }
}
```

### 3. Comments System

#### Case-Level Comments

##### List Case Comments

```http
GET /api/v1/cases/123/comments?include=user,replies
Authorization: Bearer <token>
```

##### Create Case Comment

```http
POST /api/v1/cases/123/comments
Authorization: Bearer <token>
Content-Type: application/json

{
  "data": {
    "type": "comment",
    "attributes": {
      "content": "Case requires additional documentation for family tracing.",
      "comment_type": "case_comment",
      "priority": "high"
    }
  }
}
```

#### Field-Level Comments

##### Create Field Comment

```http
POST /api/v1/form_fields/date_of_birth/comments
Authorization: Bearer <token>
Content-Type: application/json

{
  "data": {
    "type": "comment",
    "attributes": {
      "content": "Date of birth needs verification - documents show different date.",
      "comment_type": "field_comment",
      "case_id": "case_123"
    }
  }
}
```

##### Resolve Comment

```http
PATCH /api/v1/cases/123/comments/456/resolve
Authorization: Bearer <token>
Content-Type: application/json

{
  "data": {
    "type": "comment",
    "attributes": {
      "resolution_notes": "Documentation verified and updated."
    }
  }
}
```

### 4. Approval Workflow

#### List Pending Approvals (Supervisor)

```http
GET /api/v1/case_approvals?filter[status]=pending&include=case,case_manager
Authorization: Bearer <token>
```

#### Approve/Reject Case

```http
PATCH /api/v1/case_approvals/123
Authorization: Bearer <token>
Content-Type: application/json

{
  "data": {
    "type": "case_approval",
    "attributes": {
      "status": "approved",
      "notes": "All documentation complete. Case approved for service delivery.",
      "approved_at": "2025-01-20T16:00:00Z"
    }
  }
}
```

## Error Responses

### Standard Error Format

```json
{
  "errors": [
    {
      "id": "validation_error",
      "status": "422",
      "code": "VALIDATION_FAILED",
      "title": "Validation Failed",
      "detail": "First name is required",
      "source": {
        "pointer": "/data/attributes/first_name"
      }
    }
  ]
}
```

### Common Error Codes

- `401` - Unauthorized (invalid/expired token)
- `403` - Forbidden (insufficient permissions)
- `404` - Resource not found
- `422` - Validation errors
- `429` - Rate limit exceeded
- `500` - Internal server error

## Filtering and Sorting

### Available Filters

- `filter[status]` - Case status (open, in_progress, closed, suspended)
- `filter[priority_level]` - Priority (low, medium, high, urgent)
- `filter[assigned_user_id]` - Assigned user
- `filter[created_at][gte]` - Created after date
- `filter[approval_status]` - Approval status

### Sorting Options

- `sort=created_at` - Sort by creation date
- `sort=-updated_at` - Sort by last update (descending)
- `sort=priority_level,created_at` - Multiple sort fields

## Rate Limiting

- **Standard endpoints**: 100 requests per minute
- **Form submissions**: 20 requests per minute
- **File uploads**: 10 requests per minute

## Field Calculations

### Calculate Single Field

```http
POST /api/v1/fields/age/calculate
Authorization: Bearer <token>
Content-Type: application/json

{
  "data": {
    "type": "field_calculation",
    "attributes": {
      "depends_on": {
        "date_of_birth": "2008-03-15"
      },
      "calculation_type": "age_from_date"
    }
  }
}
```

**Response:**

```json
{
  "data": {
    "id": "age",
    "type": "calculated_field",
    "attributes": {
      "calculated_value": 16,
      "calculation_performed_at": "2025-01-20T10:00:00Z"
    }
  }
}
```

### Calculate All Form Fields

```http
POST /api/v1/forms/registration_rapid_assessment/calculate_all
Authorization: Bearer <token>
Content-Type: application/json

{
  "data": {
    "type": "form_calculation",
    "attributes": {
      "form_data": {
        "date_of_birth": "2008-03-15",
        "household_size_adults": 2,
        "household_size_children": 3
      }
    }
  }
}
```

## Dashboard and Metrics

### Supervisor Dashboard

```http
GET /api/v1/dashboard?include=case_metrics,team_metrics,comment_metrics
Authorization: Bearer <token>
```

**Response:**

```json
{
  "data": {
    "id": "supervisor_dashboard",
    "type": "dashboard",
    "attributes": {
      "case_metrics": {
        "total_cases": 150,
        "pending_approval": 12,
        "in_progress": 89,
        "completed_this_month": 23,
        "overdue_cases": 5
      },
      "team_metrics": {
        "total_case_managers": 8,
        "average_caseload": 18.75,
        "top_performers": [
          {
            "assigned_user_id": "user_123",
            "name": "Ahmed Al-Rashid",
            "completed_cases": 8,
            "avg_completion_time": 12.5
          }
        ]
      },
      "comment_metrics": {
        "total_unresolved_comments": 34,
        "case_level_comments": 18,
        "field_level_comments": 16,
        "high_priority_comments": 7
      },
      "form_completion_metrics": {
        "consent_assent": {
          "completion_rate": 98.5,
          "avg_completion_time_minutes": 15
        },
        "registration_rapid_assessment": {
          "completion_rate": 94.2,
          "avg_completion_time_minutes": 45
        }
      }
    }
  }
}
```

### Case Manager Metrics

```http
GET /api/v1/dashboard/case_metrics?assigned_user_id=user_789
Authorization: Bearer <token>
```

## Lookup Data Endpoints

### Geographic Locations

```http
GET /api/v1/geographic_locations?filter[country]=Syria&include=children
Authorization: Bearer <token>
```

### Service Categories

```http
GET /api/v1/service_categories?filter[active]=true
Authorization: Bearer <token>
```

### Referral Sources

```http
GET /api/v1/referral_sources?sort=name
Authorization: Bearer <token>
```

## Real-time Features

### WebSocket Connection

```javascript
// Connect to case updates
const socket = new WebSocket("wss://api.athar.org/cable");

// Subscribe to case updates
socket.send(
  JSON.stringify({
    command: "subscribe",
    identifier: JSON.stringify({
      channel: "CaseChannel",
      case_id: "case_123",
    }),
  })
);

// Listen for updates
socket.onmessage = function (event) {
  const data = JSON.parse(event.data);
  if (data.type === "case_updated") {
    // Handle case update
    console.log("Case updated:", data.case);
  }
};
```

### Real-time Comment Notifications

```javascript
// Subscribe to comment updates for a case
socket.send(
  JSON.stringify({
    command: "subscribe",
    identifier: JSON.stringify({
      channel: "CommentChannel",
      case_id: "case_123",
    }),
  })
);
```

## File Upload Support

### Upload Case Documents

```http
POST /api/v1/cases/123/documents
Authorization: Bearer <token>
Content-Type: multipart/form-data

{
  "document": <file_binary>,
  "document_type": "identification",
  "description": "Child's birth certificate"
}
```

**Response:**

```json
{
  "data": {
    "id": "doc_456",
    "type": "document",
    "attributes": {
      "filename": "birth_certificate.pdf",
      "document_type": "identification",
      "file_size": 245760,
      "uploaded_at": "2025-01-20T10:00:00Z",
      "download_url": "/api/v1/documents/doc_456/download"
    }
  }
}
```

## Bulk Operations

### Bulk Case Assignment

```http
POST /api/v1/cases/bulk_assign
Authorization: Bearer <token>
Content-Type: application/json

{
  "data": {
    "type": "bulk_assignment",
    "attributes": {
      "case_ids": ["case_123", "case_124", "case_125"],
      "assigned_user_id": "user_789",
      "reason": "Workload redistribution"
    }
  }
}
```

### Bulk Status Update

```http
PATCH /api/v1/cases/bulk_update
Authorization: Bearer <token>
Content-Type: application/json

{
  "data": {
    "type": "bulk_update",
    "attributes": {
      "case_ids": ["case_123", "case_124"],
      "updates": {
        "status": "in_progress",
        "priority_level": "high"
      }
    }
  }
}
```

## Webhooks (Future Enhancement)

The system will support webhooks for real-time notifications:

- Case status changes
- Form completions
- Approval decisions
- Comment additions

### Webhook Registration

```http
POST /api/v1/webhooks
Authorization: Bearer <token>
Content-Type: application/json

{
  "data": {
    "type": "webhook",
    "attributes": {
      "url": "https://external-system.org/webhooks/case-updates",
      "events": ["case.status_changed", "case.approved", "comment.created"],
      "secret": "webhook_secret_key"
    }
  }
}
```
