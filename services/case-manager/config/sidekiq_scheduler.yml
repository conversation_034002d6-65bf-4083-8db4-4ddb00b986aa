# Case Management Scheduled Jobs

# Daily case status review
#case_status_review:
#  cron: "0 9 * * 1-5"  # Run at 9am on weekdays
#  class: CaseStatusReviewJob
#  queue: case_management
#  description: "Reviews and updates case statuses based on business rules"
#  enabled: false

# Weekly case plan review
#case_plan_review:
#  cron: "0 10 * * 1"  # Run at 10am every Monday
#  class: CasePlanReviewJob
#  queue: case_management
#  description: "Reviews case plans for progress and updates"
#  enabled: false

# Monthly case statistics generation
#case_statistics_generation:
#  cron: "0 2 1 * *"  # Run at 2am on the 1st day of every month
#  class: CaseStatisticsJob
#  queue: default
#  description: "Generates monthly case management statistics and reports"
#  enabled: false
