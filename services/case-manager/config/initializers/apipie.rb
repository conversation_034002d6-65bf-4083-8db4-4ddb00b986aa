Apipie.configure do |config|
  config.app_name                = "AtharCaseManager"
  config.api_base_url            = "/api"
  config.doc_base_url            = "/apipie"
  # where is your API defined?
  config.api_controllers_matcher = [
    "#{Rails.root}/app/controllers/**/*.rb",
    "#{Gem.loaded_specs['athar_auth'].full_gem_path}/app/controllers/**/*.rb"
  ]
  config.validate = false
  config.app_info["1.0"] = "Case Management API for Athar EMS"
  config.default_version = "1.0"
  config.copyright = "&copy; 2025 Athar Association"
  config.markup = Apipie::Markup::Markdown.new
  config.show_all_examples = true
  config.authenticate = proc do
    # Add authentication for API docs if needed
    true
  end
end

# Parameter groups will be defined in individual controllers for now
# This version of Apipie doesn't support global parameter groups
