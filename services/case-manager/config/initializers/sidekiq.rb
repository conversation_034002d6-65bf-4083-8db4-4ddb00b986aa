require 'sidekiq'
require 'sidekiq-scheduler'
require 'sidekiq-scheduler/web' # Ensure the web UI is loaded

Sidekiq.configure_server do |config|
  config.redis = { url: ENV['REDIS_URL'] || 'redis://localhost:6379/0' }

  # Load the scheduler configuration
  config.on(:startup) do
    schedule_file = File.join(Rails.root, 'config/sidekiq_scheduler.yml')

    if File.exist?(schedule_file)
      schedule = YAML.load_file(schedule_file)

      # Print the loaded schedule for debugging
      puts "Loaded schedule: #{schedule.inspect}"

      # Set the schedule only if it's a hash
      if schedule.is_a?(Hash)
        Sidekiq.schedule = schedule
      else
        # Create an empty hash if the schedule is nil or false
        Sidekiq.schedule = {}
      end

      # Enable the scheduler
      Sidekiq::Scheduler.enabled = true

      # Reload the schedule
      Sidekiq::Scheduler.instance.reload_schedule!

      puts "Loaded scheduler configuration from #{schedule_file}"
    else
      puts "WARNING: Scheduler configuration file not found at #{schedule_file}"
    end
  end

  # Configure Sidekiq Statistic - Commented out due to compatibility issues
  # Sidekiq::Statistic.configure do |statistic_config|
  #   statistic_config.log_file = Rails.root.join('log', 'sidekiq.log')
  # end
end

Sidekiq.configure_client do |config|
  config.redis = { url: ENV['REDIS_URL'] || 'redis://localhost:6379/0' }
end
