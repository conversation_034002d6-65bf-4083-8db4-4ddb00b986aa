Rails.application.routes.draw do
  apipie

  namespace :api do
    # Current user endpoint for case management context
    get 'me', to: 'me#show'

    resources :services

    # Enhanced cases with progressive form completion
    resources :cases do
      member do
        post :submit_for_approval  # Submit case for approval after forms complete
        get :progress             # Get form completion progress
      end

      # Case-level comments
      resources :comments, controller: 'comments', except: [ :index ] do
        member do
          patch :resolve    # Resolve a comment
          patch :reopen     # Reopen a resolved comment
          get :thread       # Get comment thread
        end
      end

      # Get all case comments
      get :comments, to: 'comments#index'
    end

    # Form management
    resources :form_templates, only: [ :index, :show ] do
      member do
        get :render_data      # Get form with rendering data
        post :validate_data   # Validate form data
      end
    end

    resources :form_submissions do
      member do
        post :submit          # Submit form for completion
        post :calculate_fields # Calculate field values
      end
    end

    # Comments (field-level and case-level)
    resources :comments do
      member do
        patch :resolve        # Resolve a comment
        patch :reopen         # Reopen a resolved comment
        get :thread           # Get comment thread
      end
    end

    # Case documents
    resources :case_documents do
      member do
        get :download         # Download file
        get :preview          # Get preview/thumbnail
      end
    end

    # Dashboard and analytics - Enhanced with comprehensive reporting
    resource :dashboard, only: [ :show ] do
      collection do
        get :index           # Main dashboard overview
        get :case_metrics    # Detailed case metrics
        get :team_metrics    # Team performance metrics
        get :analytics       # Advanced analytics data
      end
    end

    # Reports endpoints
    resources :reports, only: [ :create ] do
      collection do
        get :types          # Available report types
        get :template       # Report template structure
      end
    end

    # Charts and visualization endpoints
    resource :charts, only: [] do
      collection do
        get :pie            # Pie chart data
        get :line           # Line chart data
        get :bar            # Bar chart data
        get :widgets        # Dashboard widgets data
      end
    end

    # Lookup data resources
    resources :staff_positions
    resources :partner_agencies
    resources :geographic_locations do
      member do
        get :children
      end
    end
    resources :case_types
    resources :service_categories
    resources :referral_sources

    # Mount shared session endpoints from auth gem
    mount AtharAuth::Engine, at: "session"
  end

  # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
  # Can be used by load balancers and uptime monitors to verify that the app is live.
  get "up" => "rails/health#show", as: :rails_health_check

  # Sidekiq Web UI
  require "sidekiq/web"

  # Make sure sidekiq-scheduler web UI is loaded
  begin
    require "sidekiq-scheduler/web"
  rescue LoadError
    # Handle the case where sidekiq-scheduler is not available
    puts "WARNING: sidekiq-scheduler/web could not be loaded"
  end

  # Secure the Sidekiq Web UI with basic auth in production
  if Rails.env.production?
    Sidekiq::Web.use Rack::Auth::Basic do |username, password|
      # Replace with a real check in production
      ActiveSupport::SecurityUtils.secure_compare(username, ENV["SIDEKIQ_USERNAME"]) &
        ActiveSupport::SecurityUtils.secure_compare(password, ENV["SIDEKIQ_PASSWORD"])
    end
  end

  mount Sidekiq::Web => "/sidekiq"

  # Defines the root path route ("/")
  root "application#index"
end
