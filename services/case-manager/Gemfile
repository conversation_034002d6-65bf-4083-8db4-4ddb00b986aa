source "https://rubygems.org"

# Bundle edge Rails instead: gem "rails", github: "rails/rails", branch: "main"
gem "rails", "~> 8.0.1"
# Use postgresql as the database for Active Record
gem "pg", "~> 1.1"
# Use the Puma web server [https://github.com/puma/puma]
gem "puma", ">= 5.0"
# Build JSON APIs with ease [https://github.com/rails/jbuilder]
# gem "jbuilder"

# Use Active Model has_secure_password [https://guides.rubyonrails.org/active_model_basics.html#securepassword]
# gem "bcrypt", "~> 3.1.7"

# Windows does not include zoneinfo files, so bundle the tzinfo-data gem
gem "tzinfo-data", platforms: %i[ mingw mswin x64_mingw jruby ]

# Use the database-backed adapters for Rails.cache, Active Job, and Action Cable
gem "solid_cache"
gem "solid_queue"
gem "solid_cable"

# Reduces boot times through caching; required in config/boot.rb
gem "bootsnap", require: false

# Deploy this application anywhere as a Docker container [https://kamal-deploy.org]
# gem "kamal", require: false

# Add HTTP asset caching/compression and X-Sendfile acceleration to Puma [https://github.com/basecamp/thruster/]
gem "thruster", require: false

# Use Active Storage variants [https://guides.rubyonrails.org/active_storage_overview.html#transforming-images]
# gem "image_processing", "~> 1.2"

# Use Rack CORS for handling Cross-Origin Resource Sharing (CORS), making cross-origin Ajax possible
gem "rack-cors"

# APIs
gem "jsonapi-serializer", "~> 2.2"
gem "apipie-rails", github: "shqear93/apipie-rails"
gem "commonmarker"

gem "jsonapi.rb"
gem "ransack"
gem "pagy"

# Database
gem "scenic"
gem "pg_search"

# Validations
gem "validates_timeliness"
gem "validates_overlap"

# Phone number handling
gem "phonofy", "~> 0.2.0"

# Markdown Processor
gem "rexml"
gem "maruku", "~> 0.7.3"

# Core Extensions
gem "require_all"

# Background Processing
gem "sidekiq"
gem "sidekiq-scheduler"

# ActiveStorage
gem "aws-sdk-s3", require: false
gem "image_processing", "~> 1.14"

# Export
gem "csv"

group :development, :test do
  # See https://guides.rubyonrails.org/debugging_rails_applications.html#debugging-with-the-debug-gem
  gem "debug", platforms: %i[ mri mingw mswin x64_mingw ], require: "debug/prelude"

  # Static analysis for security vulnerabilities [https://brakemanscanner.org/]
  gem "brakeman", require: false

  # Omakase Ruby styling [https://github.com/rails/rubocop-rails-omakase/]
  gem "rubocop-rails-omakase", require: false

  # Testing framework
  gem "rspec-rails"
  gem "factory_bot_rails"
  gem "faker"
  gem "shoulda-matchers"
  gem "database_cleaner-active_record"
  gem "rails-controller-testing"
  gem "mocha"
end

group :test do
  gem "rspec-sidekiq"
end

source "https://gem.fury.io/athar11/" do
  gem "athar_auth", path: "/Users/<USER>/workspace/athar/athar-ems/gems/auth-gem"
  # gem "athar_auth", "2.0.2"

  gem "athar_rpc", path: "/Users/<USER>/workspace/athar/athar-ems/gems/rpc-gem"
  # gem "athar_rpc", "0.4.2"

  gem "athar_commons", path: "/Users/<USER>/workspace/athar/athar-ems/gems/commons-gem"
  # gem "athar_commons", "0.3.5"
end
