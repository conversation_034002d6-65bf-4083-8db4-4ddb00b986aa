# Case Management System Architecture Plan

## Based on Primero.org Patterns

### Executive Summary

This document outlines the comprehensive modification plan to transform the case-manager service into a robust, Primero-inspired case management system. The plan focuses on enhancing the existing foundation while implementing proven patterns from humanitarian case management systems.

### Current State Analysis

#### ✅ Already Implemented

- **Unified Authentication**: AtharAuth integration with `User < AtharAuth::Models::User`
- **Role-based Authorization**: User roles (case_manager, supervisor) with role-based access control
- **Core Models**: Case, Service models with User associations (no separate CaseManager model)
- **Project-based Multi-tenancy**: project_id scoping throughout the system
- **Progressive Approval Workflow**: Case approval after form completion using Athar::Commons approval system
- **Permission System**: User access control with `accessible_cases`, `can_manage_case?`

#### 🔄 Needs Enhancement

- **Comprehensive Form Builder**: Advanced field types with dropdowns, validations, and conditional logic
- **Progressive Form Completion**: Sequential form completion with validation (consent → registration → assessment → case plan)
- **Case-only Approval System**: Simplified approval workflow where cases are approved after required forms complete
- **Enhanced User Roles**: Focus on case_manager and supervisor roles only
- **Advanced Field Types**: Rich form fields including lookups, calculations, and dependencies
- **Collaboration Features**: Comments, notes, and case history
- **Reporting & Analytics**: Dashboard and comprehensive reporting
- **Document Management**: File attachments and document handling

---

## Phase 1: Enhanced Database Schema

### 1.1 Comprehensive Form Builder System

Enhanced form system with advanced field types and validation:

```ruby
# Form Templates - Define reusable form structures
class FormTemplate < ApplicationRecord
  has_many :form_sections, dependent: :destroy
  has_many :form_submissions, dependent: :destroy
  has_many :form_field_options, through: :form_sections

  validates :name, presence: true, uniqueness: true

  enum form_type: {
    consent_assent: 0,
    registration_rapid_assessment: 1,
    comprehensive_assessment: 2,
    case_plan_implementation: 3,
    review: 4,
    case_closure: 5
  }

  enum target_role: {
    case_manager: 0,
    supervisor: 1,
    both: 2
  }

  # JSON field to store prerequisite form names
  store :prerequisite_forms, coder: JSON

  scope :for_role, ->(role) { where("target_role = ? OR target_role = 'both'", role) }
  scope :active, -> { where(active: true) }

  def requires_prerequisite?
    prerequisite_forms.present? && prerequisite_forms.any?
  end

  # Define form sequence for progressive completion (based on actual forms)
  def self.form_sequence
    {
      'consent_assent' => [],
      'registration_rapid_assessment' => ['consent_assent'],
      'comprehensive_assessment' => ['consent_assent', 'registration_rapid_assessment'],
      'case_plan_implementation' => ['consent_assent', 'registration_rapid_assessment', 'comprehensive_assessment']
    }
  end

  def self.setup_prerequisites!
    form_sequence.each do |form_name, prerequisites|
      template = find_by(name: form_name)
      next unless template

      template.update!(prerequisite_forms: prerequisites)
    end
  end
end

# Form Sections - Logical groupings within forms
class FormSection < ApplicationRecord
  belongs_to :form_template
  has_many :form_fields, dependent: :destroy

  validates :name, presence: true
  validates :display_order, presence: true, uniqueness: { scope: :form_template_id }
  validates :title, presence: true

  # Database-side conditional display using scopes
  scope :visible_for_context, ->(form_data_hash, user_context) {
    # Start with all sections
    scope = all

    # Apply form data conditions
    if form_data_hash.present?
      scope = scope.where(
        build_form_data_conditions(form_data_hash)
      )
    end

    # Apply user context conditions
    if user_context.present?
      scope = scope.where(
        build_user_context_conditions(user_context)
      )
    end

    scope
  }

  scope :accessible_for_form_submission, ->(form_submission) {
    # Get completion status of all sections
    sections_with_completion = joins(
      "LEFT JOIN (#{completion_subquery(form_submission.id)}) AS completions ON form_sections.id = completions.section_id"
    ).select(
      'form_sections.*, completions.is_complete, completions.completion_percentage'
    )

    # Filter based on sequential completion requirement
    sections_with_completion.where(
      sequential_access_condition
    )
  }

  # Check if section is complete for given form submission
  def completion_status_for(form_submission)
    required_fields = form_fields.where(required: true)
    return { total_required: 0, completed: 0, percentage: 100, is_complete: true } if required_fields.empty?

    completed_count = required_fields.joins(
      "LEFT JOIN json_each(?) AS form_data ON form_data.key = CAST(form_fields.id AS TEXT)",
      form_submission.form_data.to_json
    ).where(
      "form_data.value IS NOT NULL AND json_extract(form_data.value, '$.value') IS NOT NULL AND json_extract(form_data.value, '$.value') != ''"
    ).count

    {
      total_required: required_fields.count,
      completed: completed_count,
      percentage: (completed_count.to_f / required_fields.count * 100).round,
      is_complete: completed_count == required_fields.count
    }
  end

  private

  def self.build_form_data_conditions(form_data_hash)
    # Build SQL conditions based on form data
    # Example: display_condition_field_id and display_condition_value columns
    conditions = []

    form_data_hash.each do |field_id, field_data|
      value = field_data.dig('value')
      next unless value.present?

      # Match sections that depend on this field value
      conditions << sanitize_sql([
        "(display_condition_field_id = ? AND display_condition_value = ?) OR display_condition_field_id IS NULL",
        field_id.to_i, value.to_s
      ])
    end

    conditions.empty? ? "1=1" : "(#{conditions.join(' OR ')})"
  end

  def self.build_user_context_conditions(user_context)
    # Build SQL conditions based on user context
    conditions = ["1=1"] # Default to show all

    if user_context[:user_role].present?
      conditions << sanitize_sql([
        "display_condition_user_role IS NULL OR display_condition_user_role = ?",
        user_context[:user_role]
      ])
    end

    if user_context[:project_type].present?
      conditions << sanitize_sql([
        "display_condition_project_type IS NULL OR display_condition_project_type = ?",
        user_context[:project_type]
      ])
    end

    conditions.join(' AND ')
  end

  def self.completion_subquery(case_form_id)
    # Subquery to calculate completion status for each section
    <<~SQL
      SELECT
        form_sections.id as section_id,
        CASE
          WHEN COUNT(required_fields.id) = 0 THEN 1
          WHEN COUNT(completed_fields.field_id) = COUNT(required_fields.id) THEN 1
          ELSE 0
        END as is_complete,
        CASE
          WHEN COUNT(required_fields.id) = 0 THEN 100
          ELSE ROUND((COUNT(completed_fields.field_id) * 100.0) / COUNT(required_fields.id))
        END as completion_percentage
      FROM form_sections
      LEFT JOIN form_fields required_fields ON required_fields.form_section_id = form_sections.id
        AND required_fields.required = 1
      LEFT JOIN (
        SELECT
          form_fields.id as field_id,
          form_fields.form_section_id
        FROM form_fields
        JOIN json_each((SELECT form_data FROM form_submissions WHERE id = #{form_submission_id})) AS form_data
          ON form_data.key = CAST(form_fields.id AS TEXT)
        WHERE json_extract(form_data.value, '$.value') IS NOT NULL
          AND json_extract(form_data.value, '$.value') != ''
          AND form_fields.required = 1
      ) completed_fields ON completed_fields.form_section_id = form_sections.id
      GROUP BY form_sections.id
    SQL
  end

  def self.sequential_access_condition
    # All previous sections must be complete for strict progression
    <<~SQL
      (
        display_order = 1 OR
        NOT EXISTS (
          SELECT 1 FROM form_sections prev_sections
          LEFT JOIN (#{completion_subquery('?')}) prev_completions
            ON prev_sections.id = prev_completions.section_id
          WHERE prev_sections.form_template_id = form_sections.form_template_id
            AND prev_sections.display_order < form_sections.display_order
            AND (prev_completions.is_complete IS NULL OR prev_completions.is_complete = 0)
        )
      )
    SQL
  end
end

# Comprehensive Form Fields with advanced types
class FormField < ApplicationRecord
  belongs_to :form_section
  has_many :form_field_options, dependent: :destroy
  has_many :form_field_validations, dependent: :destroy
  has_many :dependent_fields, class_name: 'FormField', foreign_key: 'parent_field_id'
  belongs_to :parent_field, class_name: 'FormField', optional: true

  validates :field_name, presence: true, uniqueness: { scope: :form_section_id }
  validates :field_type, inclusion: {
    in: %w[
      text textarea number decimal date datetime time
      select_box multi_select radio_button checkbox
      file_upload image_upload signature
      lookup_single lookup_multiple
      calculated readonly hidden
      location coordinates
      phone email url
      rich_text markdown
      rating scale
      yes_no true_false
      currency percentage
      barcode qr_code
    ]
  }
  validates :display_order, presence: true

  enum data_type: {
    string: 0,
    integer: 1,
    decimal: 2,
    boolean: 3,
    date: 4,
    datetime: 5,
    json: 6,
    array: 7
  }

  # Field behavior and validation
  validates :validation_rules, format: { with: /\A\{.*\}\z/, allow_blank: true } # JSON format
  validates :calculation_formula, presence: true, if: -> { field_type == 'calculated' }
  validates :lookup_source_type, presence: true, if: -> { field_type.include?('lookup') }

  # Enhanced calculation support with simplified configuration
  store :field_config, accessors: [
    :auto_calculate, :depends_on, :calculation, :readonly,
    :editable_when, :placeholder_when_editable
  ], coder: JSON

  # Calculation field helpers
  def calculated_field?
    auto_calculate.present? || field_type == 'calculated'
  end

  def calculation_dependencies
    return [] unless depends_on.present?
    depends_on.is_a?(Array) ? depends_on : [depends_on]
  end

  def should_calculate_for?(form_data)
    return false unless calculated_field?
    return true if depends_on.blank?

    calculation_dependencies.any? { |dep| form_data[dep].present? }
  end

  scope :required, -> { where(required: true) }
  scope :visible, -> { where(visible: true) }
  scope :by_type, ->(type) { where(field_type: type) }

  # Comment system integration
  has_many :comments, as: :commentable, dependent: :destroy

  def has_comments?
    comments.exists?
  end

  def unresolved_comments
    comments.where(status: 'active')
  end

  def comment_count
    comments.count
  end

  def latest_comment
    comments.recent.first
  end
end

# Field Options for dropdowns, radio buttons, etc.
class FormFieldOption < ApplicationRecord
  belongs_to :form_field

  validates :option_key, presence: true, uniqueness: { scope: :form_field_id }
  validates :option_value, presence: true
  validates :display_order, presence: true

  scope :active, -> { where(active: true) }
  scope :ordered, -> { order(:display_order) }
end

# Field Validations - Complex validation rules
class FormFieldValidation < ApplicationRecord
  belongs_to :form_field

  validates :validation_type, inclusion: {
    in: %w[
      required min_length max_length min_value max_value
      pattern email phone url date_range
      unique_in_case unique_in_project unique_globally
      depends_on conditional_required
      custom_function
    ]
  }
  validates :validation_value, presence: true
  validates :error_message, presence: true

  scope :active, -> { where(active: true) }
end

# Form Submissions - Instances of forms filled for specific cases
class FormSubmission < ApplicationRecord
  belongs_to :case
  belongs_to :form_template
  belongs_to :created_by, class_name: 'User'
  belongs_to :updated_by, class_name: 'User', optional: true

  # Form submissions are data collection only - NO approval needed
  # Approval happens at the Case level after required forms are complete
  enum status: {
    draft: 0,
    in_progress: 1,
    completed: 2  # Simplified - no approval statuses needed
  }
  validates :form_data, presence: true

  # JSON column to cache completion status for performance
  # Structure: { "section_id": { "completed": 2, "total_required": 3, "percentage": 67, "is_complete": false } }
  store :completion_status, coder: JSON

  # Form sequence validation - ensure prerequisites are met
  validate :form_sequence_respected

  # Auto-update case beneficiary cache when registration form changes
  after_save :update_case_beneficiary_cache, if: :registration_form?
  after_create :submit_for_completion_if_ready
  after_update :update_completion_status!, if: :saved_change_to_form_data?

  def submit_for_completion_if_ready
    # Check if form is complete and update case accordingly
    if form_complete?
      update!(status: 'completed')
      case.update_beneficiary_cache! if registration_form?
      case.check_approval_readiness! # Check if case can be submitted for approval
    end
  end

  def form_complete?
    # Check if all required fields are filled
    form_template.form_sections.each do |section|
      required_fields = section.form_fields.where(required: true)
      required_fields.each do |field|
        field_value = get_field_value(field.id)
        return false if field_value.blank?
      end
    end
    true
  end

  def get_field_value(field_id)
    form_data.dig(field_id.to_s, 'value')
  end

  def set_field_value(field_id, value, user)
    self.form_data ||= {}
    self.form_data[field_id.to_s] = {
      'value' => value,
      'updated_at' => Time.current.iso8601,
      'updated_by' => user.id
    }
  end

  def consent_assent_form?
    form_template.name == 'consent_assent'
  end

  def registration_form?
    form_template.name == 'registration_rapid_assessment'
  end

  def assessment_form?
    form_template.name == 'comprehensive_assessment'
  end

  def case_plan_form?
    form_template.name == 'case_plan_implementation'
  end

  private

  def form_sequence_respected
    return unless form_template.requires_prerequisite?

    prerequisites = form_template.prerequisite_forms || []
    prerequisites.each do |prereq_name|
      unless case.form_completed?(prereq_name)
        errors.add(:base, "Must complete #{prereq_name} form first")
      end
    end
  end

  def on_approval_status_change(new_status, previous_status)
    Rails.logger.info("FormSubmission callback: status changed from #{previous_status} to #{new_status}")
    update!(status: new_status)
  end

  def approval_context
    {
      form_type: form_template.name,
      case_type: case.case_type,
      priority: case.priority_level
    }
  end

  # JSON field to store dynamic form data using FormField IDs as keys
  # Structure: {
  #   "123": { "value": "أحمد محمد", "metadata": { "updated_at": "timestamp", "updated_by": "user_id" } },
  #   "124": { "value": "2024-01-15", "metadata": { "updated_at": "timestamp", "updated_by": "user_id" } },
  #   "125": { "value": 100, "formula": "field_123 + field_124", "calculated_at": "timestamp" }
  # }

  scope :by_status, ->(status) { where(status: status) }
  scope :pending_approval, -> { where(status: 'submitted') }
  scope :approved, -> { where(status: 'approved') }

  # Get field value by FormField ID
  def get_field_value(form_field_id)
    form_data.dig(form_field_id.to_s, 'value')
  end

  # Set field value by FormField ID
  def set_field_value(form_field_id, value, user)
    form_data[form_field_id.to_s] = {
      'value' => value,
      'metadata' => {
        'updated_at' => Time.current.iso8601,
        'updated_by' => user.id
      }
    }
  end

  # Get field value with metadata
  def get_field_data(form_field_id)
    form_data[form_field_id.to_s] || {}
  end

  # Validate all form fields
  def validate_form_data
    form_template.form_sections.includes(:form_fields).each do |section|
      section.form_fields.each do |field|
        validate_field_data(field)
      end
    end
  end

  # Calculate dependent/calculated fields
  def calculate_dependent_fields
    form_template.form_sections.includes(:form_fields).each do |section|
      section.form_fields.where(field_type: 'calculated').each do |field|
        calculate_field_value(field)
      end
    end
  end

  # Get form data organized by sections for display
  def organized_form_data
    result = {}

    form_template.form_sections.includes(:form_fields).each do |section|
      result[section.name] = {
        section_info: {
          id: section.id,
          name: section.name,
          title: section.title || section.name.humanize
        },
        fields: {}
      }

      section.form_fields.each do |field|
        field_data = get_field_data(field.id)
        result[section.name][:fields][field.field_name] = {
          field_info: {
            id: field.id,
            field_name: field.field_name,
            label: field.label,
            field_type: field.field_type
          },
          value: field_data['value'],
          metadata: field_data['metadata']
        }
      end
    end

    result
  end

  private

  def validate_field_data(field)
    field_value = get_field_value(field.id)

    field.form_field_validations.active.each do |validation|
      case validation.validation_type
      when 'required'
        errors.add("field_#{field.id}", validation.error_message) if field_value.blank?
      when 'min_length'
        errors.add("field_#{field.id}", validation.error_message) if field_value.to_s.length < validation.validation_value.to_i
      when 'pattern'
        errors.add("field_#{field.id}", validation.error_message) unless field_value.to_s.match?(Regexp.new(validation.validation_value))
      # Add more validation types as needed
      end
    end
  end

  def calculate_field_value(field)
    # Implement calculation logic based on field.calculation_formula
    # Parse formulas like "field_123 + field_124 * 0.1" and calculate values
    # Store calculated result using set_field_value
  end

  # Simplified completion tracking - cache in JSON column
  def update_completion_status!
    completion_data = {}

    form_template.form_sections.each do |section|
      required_fields = section.form_fields.where(required: true)
      completed_fields = required_fields.count { |field| get_field_value(field.id).present? }

      completion_data[section.id.to_s] = {
        completed: completed_fields,
        total_required: required_fields.count,
        percentage: required_fields.empty? ? 100 : (completed_fields.to_f / required_fields.count * 100).round,
        is_complete: completed_fields == required_fields.count
      }
    end

    update_column(:completion_status, completion_data.to_json)
  end

  def section_completion_status(section_id)
    return { completed: 0, total_required: 0, percentage: 100, is_complete: true } unless completion_status

    status_data = JSON.parse(completion_status)
    status_data[section_id.to_s] || { completed: 0, total_required: 0, percentage: 0, is_complete: false }
  end

  private

  def registration_form?
    form_template.name == 'registration_rapid_assessment'
  end

  def update_case_beneficiary_cache
    case.update_beneficiary_cache!
  end
end
```

### 1.2 Progressive Form Completion System

Simplified Case-only approval system using Athar::Commons approval framework:

```ruby
# No complex workflow models needed - using built-in Rails enum status
# Cases progress through: draft → ready_for_approval → pending_approval → approved → active → closed

# Form sequence validation handles progression logic
# No complex transition models needed

# Progressive form completion handles all approval requirements
# No complex requirement models needed
```

### 1.3 Collaboration & Communication System

```ruby
# Comments - Two-level commenting system (Case-level and Field-level)
class Comment < ApplicationRecord
  belongs_to :case
  belongs_to :user
  belongs_to :commentable, polymorphic: true  # Can be Case or FormField
  belongs_to :parent_comment, class_name: 'Comment', optional: true  # For replies
  has_many :replies, class_name: 'Comment', foreign_key: 'parent_comment_id', dependent: :destroy

  validates :content, presence: true, length: { minimum: 1, maximum: 1000 }

  enum comment_type: {
    case_comment: 0,    # Comments on the entire case
    field_comment: 1    # Comments on specific form fields
  }

  enum status: {
    active: 0,
    resolved: 1,
    archived: 2
  }, default: :active

  scope :case_level, -> { where(comment_type: 'case_comment') }
  scope :field_level, -> { where(comment_type: 'field_comment') }
  scope :recent, -> { order(created_at: :desc) }
  scope :top_level, -> { where(parent_comment_id: nil) }
  scope :unresolved, -> { where(status: 'active') }

  def has_replies?
    replies.exists?
  end

  def reply_count
    replies.count
  end

  def can_be_resolved_by?(user)
    # Field comments can be resolved by supervisors or the original commenter
    # Case comments can be resolved by supervisors
    return true if user.supervisor?
    return true if field_comment? && self.user == user
    false
  end

  def resolve!(user)
    return false unless can_be_resolved_by?(user)
    update!(status: :resolved)
  end
end

# Removed CaseAssignment model - not needed
# Case simply belongs_to :assigned_user (User)
# User roles (case_manager, supervisor) handle permissions through role-based access control

# Removed CaseActivity model - not needed
# Simple logging or Rails built-in auditing is sufficient

# Removed CaseManager model - not needed
# Cases are assigned directly to Users, access control via User roles
```

### 1.4 Document Management System

```ruby
# Case Documents - File attachments for cases
class CaseDocument < ApplicationRecord
  belongs_to :case
  belongs_to :uploaded_by, class_name: 'User'

  validates :file_name, presence: true
  validates :file_type, presence: true
  validates :file_size, presence: true, numericality: { greater_than: 0 }
  validates :document_type, inclusion: {
    in: %w[identification medical legal assessment photo consent other]
  }

  # File storage using Active Storage
  has_one_attached :file
end
```

---

## Phase 2: Enhanced Models & Business Logic

### 2.1 Enhanced Case Model with Distributed Approval Integration

```ruby
class Case < ApplicationRecord
  # Include the distributed approval system for case status changes
  include Athar::Commons::Models::Concerns::ActsAsApprovable

  # Simple approval integration like people service
  acts_as_approvable

  # ... existing associations ...

  # New associations for enhanced functionality
  has_many :form_submissions, dependent: :destroy
  has_many :comments, dependent: :destroy
  has_many :case_comments, -> { case_level }, class_name: 'Comment'
  has_many :field_comments, -> { field_level }, class_name: 'Comment'
  has_many :case_documents, dependent: :destroy

  belongs_to :assigned_user, class_name: 'User'  # User assigned to work on this case
  belongs_to :created_by, class_name: 'User'  # User who created the case

  # Cache beneficiary data from forms for performance (case list views)
  # This replaces the need for a separate Beneficiary model
  store :beneficiary_cache, accessors: [
    :beneficiary_name, :beneficiary_age, :beneficiary_gender,
    :beneficiary_nationality, :beneficiary_phone, :beneficiary_id_number
  ], coder: JSON

  # Enhanced validations
  validates :case_number, presence: true, uniqueness: true

  enum case_type: {
    child_protection: 0,
    gbv: 1,
    general: 2,
    social_services: 3
  }

  # Use Rails enum for status management with progressive form completion
  enum status: {
    draft: 0,                # Forms being filled
    ready_for_approval: 1,   # All required forms complete, can submit for approval
    pending_approval: 2,     # Submitted for supervisor review
    approved: 3,            # Supervisor approved
    active: 4,              # Case is active and being worked
    closed: 5,              # Case completed
    suspended: 6            # Case temporarily suspended
  }, default: :draft

  enum priority_level: {
    low: 0,
    medium: 1,
    high: 2,
    urgent: 3
  }, default: :medium

  enum confidentiality_level: {
    public: 0,
    restricted: 1,
    confidential: 2
  }, default: :public

  # Progressive form completion - Core logic for Case-only approval
  def can_submit_for_approval?
    consent_assent_complete? && registration_complete? && assessment_complete? && case_plan_complete?
  end

  def consent_assent_complete?
    form_completed?('consent_assent')
  end

  def registration_complete?
    form_completed?('registration_rapid_assessment')
  end

  def assessment_complete?
    form_completed?('comprehensive_assessment')
  end

  def case_plan_complete?
    form_completed?('case_plan_implementation')
  end

  def form_completed?(form_name)
    form_submissions.joins(:form_template)
                   .where(form_templates: { name: form_name })
                   .where(status: 'completed').exists?
  end

  def completed_form_names
    form_submissions.joins(:form_template)
                   .where(status: 'completed')
                   .pluck('form_templates.name')
  end

  def form_completion_progress
    required_forms = ['consent_assent', 'registration_rapid_assessment', 'comprehensive_assessment', 'case_plan_implementation']
    completed_forms = completed_form_names & required_forms

    {
      total: required_forms.length,
      completed: completed_forms.length,
      percentage: (completed_forms.length.to_f / required_forms.length * 100).round,
      next_required: (required_forms - completed_forms).first,
      can_submit: completed_forms.length == required_forms.length,
      completed_forms: completed_forms,
      remaining_forms: required_forms - completed_forms
    }
  end

  def check_approval_readiness!
    if can_submit_for_approval? && draft?
      update!(status: :ready_for_approval)
    end
  end

  def submit_for_approval!(user)
    return false unless can_submit_for_approval?
    return false unless ready_for_approval?

    create_approval_request!(
      requestor: user,
      metadata: {
        forms_completed: completed_form_names,
        submission_summary: generate_submission_summary,
        case_type: case_type,
        priority_level: priority_level
      }.to_json
    )

    update!(status: :pending_approval)
  end

  def generate_submission_summary
    {
      beneficiary_name: beneficiary_name,
      case_type: case_type,
      priority_level: priority_level,
      forms_count: form_submissions.completed.count,
      completion_date: Time.current.iso8601
    }
  end

  # Beneficiary data methods (replaces Beneficiary model)
  def registration_form
    form_submissions.joins(:form_template)
                   .where(form_templates: { name: 'registration_rapid_assessment' })
                   .first
  end

  # Update cached beneficiary data when forms change
  def update_beneficiary_cache!
    return unless registration_form

    self.beneficiary_name = registration_form.get_field_value('full_name')
    self.beneficiary_age = registration_form.get_field_value('age')
    self.beneficiary_gender = registration_form.get_field_value('gender')
    self.beneficiary_nationality = registration_form.get_field_value('nationality')
    self.beneficiary_phone = registration_form.get_field_value('phone_number')
    self.beneficiary_id_number = registration_form.get_field_value('id_number')

    save!
  end

  # Enhanced scopes (using progressive form completion status)
  scope :by_case_type, ->(type) { where(case_type: type) if type.present? }
  scope :by_priority, ->(priority) { where(priority_level: priority) if priority.present? }
  scope :by_status, ->(status) { where(status: status) if status.present? }
  scope :urgent, -> { where(priority_level: 'urgent') }
  scope :high_priority, -> { where(priority_level: ['high', 'urgent']) }
  scope :pending_approval, -> { where(status: 'pending_approval') }
  scope :ready_for_approval, -> { where(status: 'ready_for_approval') }
  scope :approved, -> { where(status: 'approved') }
  scope :active_cases, -> { where(status: 'active') }
  scope :closed, -> { where(status: 'closed') }
  scope :in_progress, -> { where(status: ['draft', 'ready_for_approval', 'pending_approval', 'approved', 'active']) }

  # Callbacks
  before_create :generate_case_number

  # Approval system integration - Case approval after form completion
  def approval_action
    "case_approval"  # Approval for complete case (not status change)
  end

  def system_name
    "case-manager"
  end

  def on_approval_status_change(new_status, previous_status)
    Rails.logger.info("Case approval changed from #{previous_status} to #{new_status}")
    case new_status.to_s
    when 'approved'
      handle_case_approval_granted
    when 'rejected'
      handle_case_approval_rejected
    end
  end

  def approval_context
    progress = form_completion_progress
    {
      case_id: id,
      case_number: case_number,
      case_type: case_type,
      priority_level: priority_level,
      status: status,
      beneficiary_name: beneficiary_name,
      assigned_user_id: assigned_user_id,
      project_id: project_id,
      forms_completed: progress[:completed_forms],
      completion_percentage: progress[:percentage],
      submission_summary: generate_submission_summary
    }
  end

  # Primero-inspired metadata tracking
  def case_metadata
    {
      case_number: case_number,
      created_by: created_by&.name,
      assigned_user: assigned_user&.name,
      supervising_user: supervising_user&.name,
      record_created_at: created_at,
      last_updated: updated_at,
      status: status,
      priority_level: priority_level,
      case_type: case_type
    }
  end

  # Case status change methods (using built-in approval flow)
  def can_change_status_to?(new_status, user)
    # Basic permission checks
    return false unless user.case_manager? || user.supervisor?
    return false if status == new_status.to_s

    # Users can only change cases assigned to them (unless they're supervisors)
    return false if user.case_manager? && assigned_user != user

    # Check valid status transitions
    valid_transitions = {
      'draft' => ['active'],
      'active' => ['in_progress', 'suspended', 'closed'],
      'in_progress' => ['active', 'pending_closure', 'suspended'],
      'pending_closure' => ['closed', 'active'],
      'suspended' => ['active'],
      'closed' => [] # Closed cases can only be reopened by supervisors
    }

    return false unless valid_transitions[status]&.include?(new_status.to_s)

    # Some transitions require supervisor approval
    supervisor_required = ['closed', 'suspended']
    if supervisor_required.include?(new_status.to_s) && !user.supervisor?
      return false
    end

    true
  end

  # Simple status updates - no complex workflow needed
  def activate!
    return false unless approved?
    update!(status: :active)
  end

  def close!
    return false unless active?
    update!(status: :closed)
  end

  # Simplified case management - no complex workflow needed
    update!(closed_at: nil)
  end

  def requires_supervisor_attention?
    return true if approval_request&.pending?
    return true if priority_level == 'urgent'
    return true if status == 'pending_approval'
    return true if has_unresolved_comments?
    return true if updated_at < 7.days.ago  # No recent activity

    false
  end

  # Comment system methods
  def recent_activity
    comments.recent.limit(10)
  end

  def has_unresolved_comments?
    comments.unresolved.exists?
  end

  def unresolved_field_comments_count
    field_comments.unresolved.count
  end

  def latest_case_comment
    case_comments.recent.first
  end

  private

  def handle_case_approval_granted
    # Case has been approved by supervisor - make it active
    update!(status: :approved)
    Rails.logger.info "Case #{case_number}: Approved by supervisor and ready to become active"

    # Optionally auto-activate the case
    # update!(status: :active) if should_auto_activate?
  end

  def handle_case_approval_rejected
    # Case approval rejected - back to ready_for_approval status
    update!(status: :ready_for_approval)
    Rails.logger.info "Case #{case_number}: Approval rejected, case worker can revise and resubmit"
  end

  def should_auto_activate?
    # Define logic for when approved cases should automatically become active
    # For now, require manual activation
    false
  end

  # Callback handler for approval status changes from distributed system
  def handle_approval_status_change(new_status, previous_status)
    return unless approval_request

    case new_status.to_s
    when 'approved'
      handle_approval_granted
    when 'rejected'
      handle_approval_rejected
    when 'canceled'
      handle_approval_canceled
    end
  end

  private

  def handle_approval_granted
    # Case approval granted - activate the case
    update!(status: :approved)
    Rails.logger.info "Case #{case_number}: Approved by supervisor"
  end

  def handle_approval_rejected
    metadata = JSON.parse(approval_request.metadata || '{}')
    justification = metadata['justification']

    log_activity(
      User.find(approval_request.requestor_id),
      'approval_rejected',
      "Status change rejected: #{justification}"
    )
  end

  def handle_approval_canceled
    metadata = JSON.parse(approval_request.metadata || '{}')
    justification = metadata['justification']

    log_activity(
      User.find(approval_request.requestor_id),
      'approval_canceled',
      "Status change canceled: #{justification}"
    )
  end

  def log_activity(user, activity_type, description, related_record = nil)
    # Simple logging - can be replaced with Rails built-in auditing
    Rails.logger.info("Case #{case_number}: #{activity_type} by #{user.name} - #{description}")
  end

  private

  def generate_case_number
    year = Date.current.year
    sequence = Case.where('created_at >= ?', Date.current.beginning_of_year).count + 1
    self.case_number = "CASE-#{year}-#{sequence.to_s.rjust(4, '0')}"
  end

  def set_initial_status
    initial_status = CaseStatus.find_by(name: 'draft') || CaseStatus.first
    update_column(:current_status_id, initial_status.id) if initial_status
  end

  def handle_status_change_actions(old_status, new_status, user)
    case new_status.name
    when 'active'
      update!(started_at: Time.current) if started_at.nil?
    when 'closed'
      update!(closed_at: Time.current) if closed_at.nil?
    when 'suspended'
      log_activity(user, 'case_suspended', 'Case has been suspended')
    end
  end

  def notify_supervisor_for_approval(approval)
    # Implementation for notifying supervisor
    # This could be email, in-app notification, etc.
  end
end

# Enhanced User model with distributed approval system integration
class User < AtharAuth::Models::User
  # ... existing associations and methods ...

  # Remove social_worker and field_officer methods, focus on core roles
  def case_manager?
    role&.name&.include?("case_manager")
  end

  def supervisor?
    role&.name&.include?("supervisor")
  end

  # Enhanced access control methods for distributed approval system
  def can_approve_case_changes?
    supervisor?
  end

  def can_manage_case_status?(case_obj)
    return true if supervisor?
    return true if case_manager? && case_obj.assigned_user_id == id
    false
  end

  # Get approval requests from distributed system that this user can approve
  def accessible_approval_requests
    return [] unless supervisor?

    approval_service = Athar::Commons::Services::ApprovalService.new
    approval_service.approvable_by(id)
  end

  # Get case-specific approval requests
  def accessible_case_approvals
    accessible_approval_requests.select do |request|
      request.approvable_type == 'Case' && can_access_case?(request.approvable)
    end
  end

  def pending_approval_count
    accessible_case_approvals.count
  end

  def cases_requiring_attention
    accessible_cases.select(&:requires_supervisor_attention?)
  end

  # Comment system metrics
  def unresolved_comments_count
    Comment.joins(:case)
           .where(cases: { id: accessible_cases.pluck(:id) })
           .where(status: 'active')
           .count
  end

  def recent_comments
    Comment.joins(:case)
           .where(cases: { id: accessible_cases.pluck(:id) })
           .includes(:user, :case)
           .recent
           .limit(10)
  end

  def field_comments_requiring_attention
    Comment.joins(:case)
           .where(cases: { id: accessible_cases.pluck(:id) })
           .where(comment_type: 'field_comment', status: 'active')
           .count
  end

  # Get cases pending approval that this user can approve
  def cases_pending_approval
    if global_user?
      Case.approval_workflow_pending
    else
      Case.approval_workflow_pending.where(project_id: project_id)
    end.select { |case_obj| case_obj.approval_request&.can_be_approved_by?(id) }
  end

  # Approve a case status change using distributed system
  def approve_case_status_change!(case_obj, comment = nil)
    return false unless can_approve_case_changes?
    return false unless case_obj.approval_request

    approval_service = Athar::Commons::Services::ApprovalService.new
    result = approval_service.approve(case_obj.approval_request, id, comment)

    if result.success?
      case_obj.log_activity(self, 'approval_granted',
                           "Case status change approved#{comment ? ": #{comment}" : ""}")
    end

    result
  end

  # Reject a case status change using distributed system
  def reject_case_status_change!(case_obj, reason)
    return false unless can_approve_case_changes?
    return false unless case_obj.approval_request

    approval_service = Athar::Commons::Services::ApprovalService.new
    result = approval_service.reject(case_obj.approval_request, id, reason)

    if result.success?
      case_obj.log_activity(self, 'approval_rejected',
                           "Case status change rejected: #{reason}")
    end

    result
  end

  private

  def can_access_case?(case_obj)
    return true if global_user?
    return true if supervisor? && case_obj.project_id == project_id
    return true if case_manager? && case_obj.assigned_user_id == id
    false
  end
end
```

### 1.4 Distributed Approval System Integration

Integration with Athar::Commons distributed approval framework:

```ruby
# Approval Service Integration
class CaseApprovalService
  include Athar::Commons::Services

  def initialize
    @approval_service = ApprovalService.new
    @approval_client = ApprovalClient.new
  end

  # Create approval workflow for case status changes
  def create_status_change_workflow(case_obj, target_status, user, justification)
    workflow_config = target_status.approval_workflow_config

    context = {
      case_id: case_obj.id,
      case_type: case_obj.case_type,
      priority_level: case_obj.priority_level,
      current_status: case_obj.status,
      justification: justification,
      project_id: case_obj.project_id
    }

    @approval_client.generate_sequence(
      workflow_config[:action],
      workflow_config[:subject_class],
      workflow_config[:system_name],
      user.id,
      case_obj.project_id,
      context
    )
  end

  # Get pending approvals for a supervisor
  def pending_approvals_for_supervisor(supervisor_id, project_id = nil)
    approvals = @approval_service.approvable_by(supervisor_id)

    # Filter for case approvals
    case_approvals = approvals.select { |approval| approval.approvable_type == 'Case' }

    # Filter by project if specified
    if project_id
      case_approvals.select { |approval| approval.approvable.project_id == project_id }
    else
      case_approvals
    end
  end

  # Approve case status change
  def approve_status_change(approval_request, approver_id, comment = nil)
    result = @approval_service.approve(approval_request, approver_id, comment)

    if result.success?
      # The callback in the Case model will handle the actual status change
      Rails.logger.info "Case status change approved by user #{approver_id}"
    else
      Rails.logger.error "Failed to approve case status change: #{result.message}"
    end

    result
  end

  # Reject case status change
  def reject_status_change(approval_request, approver_id, reason)
    result = @approval_service.reject(approval_request, approver_id, reason)

    if result.success?
      Rails.logger.info "Case status change rejected by user #{approver_id}"
    else
      Rails.logger.error "Failed to reject case status change: #{result.message}"
    end

    result
  end
end

# Form Field Calculation Service - Handles backend field calculations
class FormFieldCalculationService
  # Supported calculation types with their implementations
  CALCULATIONS = {
    'age_from_date' => ->(date_value) {
      return nil unless date_value
      date = date_value.is_a?(String) ? Date.parse(date_value) : date_value
      ((Date.current - date) / 365.25).floor
    },

    'sum' => ->(values) {
      Array(values).compact.map(&:to_f).sum
    },

    'difference' => ->(val1, val2) {
      (val1.to_f || 0) - (val2.to_f || 0)
    },

    'percentage' => ->(part, total) {
      total&.positive? ? (part.to_f / total * 100).round(2) : 0
    },

    'average' => ->(values) {
      arr = Array(values).compact.map(&:to_f)
      arr.empty? ? 0 : (arr.sum / arr.size).round(2)
    },

    'count_non_empty' => ->(values) {
      Array(values).count { |v| v.present? }
    }
  }.freeze

  def self.calculate(calculation_type, value)
    calculator = CALCULATIONS[calculation_type]
    return nil unless calculator

    begin
      calculator.call(value)
    rescue => e
      Rails.logger.error "Field calculation error: #{e.message}"
      nil
    end
  end

  def self.calculate_field_value(field, form_data)
    return nil unless field.calculated_field?

    case field.calculation
    when 'age_from_date'
      source_value = form_data[field.depends_on]
      calculate('age_from_date', source_value)
    when 'sum'
      source_values = field.calculation_dependencies.map { |dep| form_data[dep] }
      calculate('sum', source_values)
    when 'difference'
      deps = field.calculation_dependencies
      return nil unless deps.size >= 2
      calculate('difference', form_data[deps[0]], form_data[deps[1]])
    else
      # Handle custom calculations or formulas
      evaluate_custom_calculation(field, form_data)
    end
  end

  private

  def self.evaluate_custom_calculation(field, form_data)
    # For complex calculations that need custom logic
    # This can be extended based on specific requirements
    nil
  end
end

# Case Approval Controller for handling approval requests
class Api::CaseApprovalsController < ApplicationController
  include AtharAuth::ResourceAuthorization

  before_action :authenticate_session!
  before_action :ensure_supervisor!

  # GET /api/case_approvals - List pending approvals for current supervisor
  def index
    service = CaseApprovalService.new
    @approvals = service.pending_approvals_for_supervisor(
      current_user.id,
      current_user.global_user? ? nil : current_user.project_id
    )

    render json: {
      data: @approvals.map { |approval| serialize_approval(approval) },
      meta: { count: @approvals.size }
    }
  end

  # POST /api/case_approvals/:id/approve - Approve a case status change
  def approve
    approval_request = find_approval_request
    return render_not_found unless approval_request

    service = CaseApprovalService.new
    result = service.approve_status_change(
      approval_request,
      current_user.id,
      params[:comment]
    )

    if result.success?
      render json: { message: 'Case status change approved successfully' }
    else
      render json: { error: result.message }, status: :unprocessable_entity
    end
  end

  # POST /api/case_approvals/:id/reject - Reject a case status change
  def reject
    approval_request = find_approval_request
    return render_not_found unless approval_request

    reason = params[:reason]
    return render json: { error: 'Rejection reason is required' }, status: :bad_request if reason.blank?

    service = CaseApprovalService.new
    result = service.reject_status_change(approval_request, current_user.id, reason)

    if result.success?
      render json: { message: 'Case status change rejected successfully' }
    else
      render json: { error: result.message }, status: :unprocessable_entity
    end
  end

  private

  def ensure_supervisor!
    unless current_user.supervisor?
      render json: { error: 'Only supervisors can manage approvals' }, status: :forbidden
    end
  end

  def find_approval_request
    # This would need to be implemented based on how approval requests are stored/accessed
    # in the distributed system
    nil
  end

  def serialize_approval(approval)
    {
      id: approval.id,
      case_id: approval.approvable.id,
      case_number: approval.approvable.case_number,
      status: approval.approvable.status,
      requested_by: approval.requestor_id,
      requested_at: approval.created_at,
      justification: JSON.parse(approval.metadata || '{}')['justification'],
      priority: approval.approvable.priority_level
    }
  end

  def render_not_found
    render json: { error: 'Approval request not found' }, status: :not_found
  end
end
```

---

---

## Phase 3: Approval Workflow Seeds for Case Management

### 3.1 Hierarchical Case Approval Workflow

Integration with Athar::Commons distributed approval system with smart hierarchical bypass logic:

**Approval Flow Logic:**

- **Case Manager initiates** → Skip their own approval → **Supervisor** → **Project Manager**
- **Supervisor initiates** → Skip Case Manager approval → **Supervisor** → **Project Manager**
- **Project Manager initiates** → Skip both → **Project Manager** (or auto-approve)

**Key Principle:** The initiator never approves their own request.

```ruby
# db/seeds/08_case_management_approval_workflows.rb

puts "Creating case management approval workflows..."

# Case Approval Workflow with Hierarchical Bypass Logic
case_approval_workflow = ApprovalWorkflow.find_or_create_by!(
  name: "Case Approval",
  action: "case_approval",
  subject_class: "Case",
  system_name: "case-manager"
) do |workflow|
  workflow.description = "Hierarchical approval workflow for case management with smart bypass"
  workflow.active = true
  workflow.required_context_keys = %w[case_type priority_level forms_completed creator_user_id creator_role project_id]
  workflow.empty_steps_behavior = "auto_approve" # For PM-initiated cases
end

# Step 1: User Review (Skip if creator has case_manager role or higher)
user_review_step = case_approval_workflow.approval_steps.find_or_create_by!(sequence: 1) do |step|
  step.name = "User Review"
  step.approval_type = "any"
  step.dynamic_approver_method = "users_with_case_manager_role_for_project"
  step.description = "Review by users with case_manager role"
  step.condition = "!['case_manager', 'supervisor', 'project_manager'].include?(creator_role)"
  step.skip_if_no_approvers = true
end

# Step 2: Supervisor Approval (Skip if creator is Supervisor or Project Manager)
supervisor_step = case_approval_workflow.approval_steps.find_or_create_by!(sequence: 2) do |step|
  step.name = "Supervisor Approval"
  step.approval_type = "any"
  step.dynamic_approver_method = "case_supervisors_for_project"
  step.description = "Supervisor approval for case activation"
  step.condition = "['case_manager'].include?(creator_role)" # Only for users with case_manager role
  step.skip_if_no_approvers = false
end

# Step 3: Project Manager Approval (Skip only if creator is Project Manager)
project_manager_step = case_approval_workflow.approval_steps.find_or_create_by!(sequence: 3) do |step|
  step.name = "Project Manager Approval"
  step.approval_type = "any"
  step.dynamic_approver_method = "project_managers_for_project"
  step.description = "Project manager final approval"
  step.condition = "['case_manager', 'supervisor'].include?(creator_role)" # For users with case_manager and supervisor roles
  step.skip_if_no_approvers = false
end

# Case Status Change Approval Workflow
case_status_workflow = ApprovalWorkflow.find_or_create_by!(
  name: "Case Status Change Approval",
  action: "change_case_status",
  subject_class: "Case",
  system_name: "case-manager"
) do |workflow|
  workflow.description = "Approval workflow for case status changes"
  workflow.active = true
  workflow.required_context_keys = %w[current_status target_status justification creator_role priority_level]
  workflow.empty_steps_behavior = "error"
end

# Status change follows same hierarchy
status_supervisor_step = case_status_workflow.approval_steps.find_or_create_by!(sequence: 1) do |step|
  step.name = "Supervisor Approval"
  step.approval_type = "any"
  step.dynamic_approver_method = "case_supervisors_for_project"
  step.description = "Supervisor approval for status changes"
  step.condition = "['case_manager'].include?(creator_role)"
  step.skip_if_no_approvers = false
end

status_pm_step = case_status_workflow.approval_steps.find_or_create_by!(sequence: 2) do |step|
  step.name = "Project Manager Approval"
  step.approval_type = "any"
  step.dynamic_approver_method = "project_managers_for_project"
  step.description = "Project manager approval for status changes"
  step.condition = "['case_manager', 'supervisor'].include?(creator_role)"
  step.skip_if_no_approvers = false
end

# Case Closure Approval Workflow
case_closure_workflow = ApprovalWorkflow.find_or_create_by!(
  name: "Case Closure Approval",
  action: "close_case",
  subject_class: "Case",
  system_name: "case-manager"
) do |workflow|
  workflow.description = "Approval workflow for case closure"
  workflow.active = true
  workflow.required_context_keys = %w[closure_reason case_type priority_level creator_role]
  workflow.empty_steps_behavior = "error"
end

# Closure requires supervisor approval minimum
closure_supervisor_step = case_closure_workflow.approval_steps.find_or_create_by!(sequence: 1) do |step|
  step.name = "Supervisor Approval"
  step.approval_type = "any"
  step.dynamic_approver_method = "case_supervisors_for_project"
  step.description = "Supervisor approval for case closure"
  step.condition = "['case_manager'].include?(creator_role)"
  step.skip_if_no_approvers = false
end

closure_pm_step = case_closure_workflow.approval_steps.find_or_create_by!(sequence: 2) do |step|
  step.name = "Project Manager Approval"
  step.approval_type = "any"
  step.dynamic_approver_method = "project_managers_for_project"
  step.description = "Project manager approval for case closure"
  step.condition = "['case_manager', 'supervisor'].include?(creator_role)"
  step.skip_if_no_approvers = false
end

# Case Transfer Approval Workflow
case_transfer_workflow = ApprovalWorkflow.find_or_create_by!(
  name: "Case Transfer Approval",
  action: "transfer_case",
  subject_class: "Case",
  system_name: "case-manager"
) do |workflow|
  workflow.description = "Approval workflow for case transfers"
  workflow.active = true
  workflow.required_context_keys = %w[current_assigned_user target_assigned_user transfer_reason creator_role]
  workflow.empty_steps_behavior = "error"
end

# Transfer requires supervisor approval
transfer_supervisor_step = case_transfer_workflow.approval_steps.find_or_create_by!(sequence: 1) do |step|
  step.name = "Supervisor Approval"
  step.approval_type = "any"
  step.dynamic_approver_method = "case_supervisors_for_project"
  step.description = "Supervisor approval for case transfer"
  step.condition = "['case_manager'].include?(creator_role)"
  step.skip_if_no_approvers = false
end

transfer_pm_step = case_transfer_workflow.approval_steps.find_or_create_by!(sequence: 2) do |step|
  step.name = "Project Manager Approval"
  step.approval_type = "any"
  step.dynamic_approver_method = "project_managers_for_project"
  step.description = "Project manager approval for case transfer"
  step.condition = "['case_manager', 'supervisor'].include?(creator_role)"
  step.skip_if_no_approvers = false
end

puts "✅ Case management approval workflows created!"
```

### 3.2 Dynamic Approver Methods for Core Service

These methods need to be added to the Core service approval engine:

```ruby
# In services/core/app/services/approval_engine.rb or similar

# Users with case_manager role (excluding creator)
def users_with_case_manager_role_for_project(context)
  project_id = context[:project_id]
  creator_id = context[:creator_user_id]

  User.joins(:role)
      .where(project_id: project_id)
      .where(roles: { name: 'case_manager' })
      .where.not(id: creator_id)  # Exclude the creator
      .pluck(:id)
end

# Supervisor Approvers (excluding creator)
def case_supervisors_for_project(context)
  project_id = context[:project_id]
  creator_id = context[:creator_user_id]

  User.joins(:role)
      .where(project_id: project_id)
      .where(roles: { name: 'supervisor' })
      .where.not(id: creator_id)  # Exclude the creator
      .pluck(:id)
end

# Project Manager Approvers (excluding creator)
def project_managers_for_project(context)
  project_id = context[:project_id]
  creator_id = context[:creator_user_id]

  User.joins(:role)
      .where(project_id: project_id)
      .where(roles: { name: 'project_manager' })
      .where.not(id: creator_id)  # Exclude the creator
      .pluck(:id)
end
```

### 3.3 Case Model Approval Integration

Enhanced Case model with proper approval context:

```ruby
class Case < ApplicationRecord
  include Athar::Commons::Models::Concerns::ActsAsApprovable
  acts_as_approvable

  def approval_context
    {
      case_id: id,
      case_number: case_number,
      case_type: case_type,
      priority_level: priority_level,
      project_id: project_id,
      creator_user_id: created_by_id,
      creator_role: created_by&.role&.name,
      forms_completed: completed_form_names,
      completion_percentage: form_completion_progress[:percentage],
      current_status: status,
      assigned_user_id: assigned_user_id
    }
  end

  def approval_action
    "case_approval"
  end

  def system_name
    "case-manager"
  end

  # Status change approval
  def submit_status_change_for_approval!(target_status, user, justification)
    create_approval_request!(
      requestor: user,
      action: "change_case_status",
      metadata: {
        current_status: status,
        target_status: target_status,
        justification: justification,
        creator_role: user.role&.name
      }.to_json
    )
  end

  # Case closure approval
  def submit_closure_for_approval!(user, closure_reason)
    create_approval_request!(
      requestor: user,
      action: "close_case",
      metadata: {
        closure_reason: closure_reason,
        creator_role: user.role&.name,
        case_type: case_type,
        priority_level: priority_level
      }.to_json
    )
  end

  # Case transfer approval
  def submit_transfer_for_approval!(user, target_assigned_user, transfer_reason)
    create_approval_request!(
      requestor: user,
      action: "transfer_case",
      metadata: {
        current_assigned_user: assigned_user_id,
        target_assigned_user: target_assigned_user.id,
        transfer_reason: transfer_reason,
        creator_role: user.role&.name
      }.to_json
    )
  end
end
```

---

## Phase 4: Seeds Implementation for Humanitarian Forms

### 3.1 Form Templates Seeds Structure

Based on the humanitarian forms analysis, we need to create seeds for the standard case management forms:

#### Seeds File Structure

```ruby
# db/seeds/form_templates.rb
class FormTemplateSeeds
  def self.create_all
    create_core_forms
    create_supplementary_forms
    create_specialized_forms
  end

  private

  def self.create_core_forms
    create_consent_assent_form
    create_registration_rapid_assessment_form
    create_comprehensive_assessment_form
    create_case_plan_implementation_form
    create_review_form
    create_closure_form
  end

  def self.create_supplementary_forms
    create_referral_form
    create_case_conference_form
    create_transfer_form
  end

  # Detailed Referral Form based on actual form image
  def self.create_referral_form
    template = FormTemplate.find_or_create_by!(name: 'referral_form') do |t|
      t.title = 'Referral Form'
      t.description = 'Form for referring cases to other services or agencies'
      t.form_type = 'supplementary'
      t.sequence_order = 1
      t.is_active = true
    end

    # Section 1: Type of Service Requested
    service_section = template.form_sections.find_or_create_by!(name: 'service_requested') do |s|
      s.title = 'Type of Service Requested'
      s.display_order = 1
      s.is_required = true
    end

    service_fields = [
      {
        name: 'services_requested',
        label: 'Type of service requested',
        field_type: 'checkbox_group',
        required: true,
        display_order: 1,
        field_config: {
          options: [
            { key: 'health_services', value: 'Health services' },
            { key: 'nutrition_services', value: 'Nutrition services' },
            { key: 'food_security', value: 'Food security services' },
            { key: 'livelihood_services', value: 'Livelihood services' },
            { key: 'cash_assistance', value: 'Cash assistance services' },
            { key: 'mental_health_psychosocial', value: 'Mental health and psycho-social support services' },
            { key: 'family_tracing', value: 'Family Tracing and Reunification services' },
            { key: 'gbv_services', value: 'GBV services' },
            { key: 'alternative_care', value: 'Alternative care services' },
            { key: 'documentation_registration', value: 'Documentation and civil registration' },
            { key: 'legal_justice', value: 'Legal and justice services' },
            { key: 'education_services', value: 'Education services' },
            { key: 'shelter_services', value: 'Shelter services (not places of care)' },
            { key: 'water_sanitation', value: 'Water, sanitation and hygiene services' },
            { key: 'specialized_disability', value: 'Specialized services for children with disabilities' }
          ]
        }
      },
      {
        name: 'needs_description',
        label: 'Description of the needs',
        field_type: 'textarea',
        required: true,
        display_order: 2,
        validation_rules: { max_length: 1000 },
        help_text: 'Describe the needs, relevant services already provided or interventions undertaken, and any other relevant details for the service provider.'
      },
      {
        name: 'expected_outcome',
        label: 'Expected outcome of the service requested',
        field_type: 'textarea',
        required: true,
        display_order: 3,
        validation_rules: { max_length: 1000 },
        help_text: 'Describe what you and the person being referred is hoping to achieve through the referral'
      }
    ]

    create_fields_for_section(service_section, service_fields)

    # Section 2: Case Personal Details
    personal_section = template.form_sections.find_or_create_by!(name: 'case_personal_details') do |s|
      s.title = 'Case Personal Details'
      s.display_order = 2
      s.is_required = true
    end

    personal_fields = [
      {
        name: 'first_name',
        label: 'First name',
        field_type: 'text',
        required: true,
        display_order: 1,
        validation_rules: { min_length: 1, max_length: 50 }
      },
      {
        name: 'middle_name',
        label: 'Middle name / Father\'s name',
        field_type: 'text',
        display_order: 2,
        validation_rules: { max_length: 50 },
        help_text: '(Optional)'
      },
      {
        name: 'last_name',
        label: 'Last Name/ Family Name',
        field_type: 'text',
        required: true,
        display_order: 3,
        validation_rules: { min_length: 1, max_length: 50 }
      },
      {
        name: 'other_names',
        label: 'Other names or spelling of name the person is known by',
        field_type: 'text',
        display_order: 4,
        validation_rules: { max_length: 100 }
      },
      {
        name: 'date_of_birth',
        label: 'Date of birth (DOB)',
        field_type: 'date',
        display_order: 5,
        validation_rules: { max_date: 'today' },
        help_text: 'dd/mm/yyyy'
      },
      {
        name: 'age_estimated',
        label: 'Is the age estimated',
        field_type: 'radio',
        display_order: 6,
        display_condition: 'date_of_birth == null',
        field_config: {
          options: [
            { key: 'yes', value: 'Yes' },
            { key: 'no', value: 'No' }
          ]
        }
      },
      {
        name: 'age',
        label: 'Age',
        field_type: 'number',
        display_order: 7,
        field_config: {
          auto_calculate: true,
          depends_on: 'date_of_birth',
          calculation: 'age_from_date',
          readonly: true,
          editable_when: 'date_of_birth == null',
          placeholder_when_editable: 'Enter estimated age'
        },
        validation_rules: { min: 0, max: 120 }
      },
      {
        name: 'sex',
        label: 'Sex',
        field_type: 'radio',
        required: true,
        display_order: 8,
        field_config: {
          options: [
            { key: 'male', value: 'Male' },
            { key: 'female', value: 'Female' },
            { key: 'non_binary', value: 'Non-binary' },
            { key: 'other', value: 'Other' }
          ]
        }
      },
      {
        name: 'nationality',
        label: 'Nationality',
        field_type: 'text',
        required: true,
        display_order: 9,
        help_text: 'create options for your context'
      },
      {
        name: 'id_type',
        label: 'ID Type',
        field_type: 'text',
        display_order: 10
      },
      {
        name: 'id_number',
        label: 'ID Number',
        field_type: 'text',
        display_order: 11
      },
      {
        name: 'current_address',
        label: 'Current Address',
        field_type: 'textarea',
        display_order: 12,
        validation_rules: { max_length: 200 }
      },
      {
        name: 'contact_info',
        label: 'How can the child be contacted? If not directly, specify through who',
        field_type: 'textarea',
        display_order: 13,
        validation_rules: { max_length: 200 }
      },
      {
        name: 'phone_number',
        label: 'If available, include phone number',
        field_type: 'text',
        display_order: 14,
        help_text: 'indicate if this is WhatsApp, Viber etc.'
      },
      {
        name: 'languages_spoken',
        label: 'Languages spoken by the child',
        field_type: 'text',
        display_order: 15,
        help_text: 'create options for your context'
      },
      {
        name: 'disability_status',
        label: 'Disability status',
        field_type: 'radio',
        display_order: 16,
        field_config: {
          options: [
            { key: 'no_disabilities', value: 'No disabilities' },
            { key: 'child_with_disabilities', value: 'Child with disabilities (mark below if possible)' }
          ]
        }
      },
      {
        name: 'disability_types',
        label: 'Types of disabilities',
        field_type: 'checkbox_group',
        display_order: 17,
        display_condition: 'disability_status == "child_with_disabilities"',
        field_config: {
          options: [
            { key: 'mental_impairments', value: 'Mental impairments' },
            { key: 'sensory_impairments', value: 'Sensory impairments' },
            { key: 'physical_impairments', value: 'Physical impairments' },
            { key: 'intellectual_impairment', value: 'Intellectual impairment' },
            { key: 'other', value: 'Other, specify' }
          ]
        }
      },
      {
        name: 'other_relevant_info',
        label: 'Any other relevant information to facilitate access or tailor support',
        field_type: 'textarea',
        display_order: 18,
        validation_rules: { max_length: 500 },
        help_text: 'For example if, child has difficulty climbing stairs, preference for female focal point to do intake, etc.'
      }
    ]

    create_fields_for_section(personal_section, personal_fields)
  end

  def self.create_specialized_forms
    create_uasc_forms
    create_gbv_forms if ENV['ENABLE_GBV_FORMS'] == 'true'
  end
end
```

#### Core Form 1A: Consent and Assent Form

```ruby
def self.create_consent_assent_form
  template = FormTemplate.find_or_create_by!(name: 'consent_assent_form') do |t|
    t.title = 'Consent and Assent Form'
    t.description = 'Legal consent for case management services'
    t.form_type = 'core'
    t.sequence_order = 1
    t.is_active = true
  end

  # Section 1: Consent Information
  consent_section = template.form_sections.find_or_create_by!(name: 'consent_information') do |s|
    s.title = 'Consent Information'
    s.display_order = 1
    s.is_required = true
  end

  consent_fields = [
    {
      name: 'consent_given_by',
      label: 'Consent given by',
      field_type: 'select',
      required: true,
      display_order: 1,
      field_config: {
        options: [
          { key: 'child', value: 'Child (if 18+)', enabled_if: 'age >= 18' },
          { key: 'parent', value: 'Parent/Guardian' },
          { key: 'caregiver', value: 'Primary Caregiver' },
          { key: 'other', value: 'Other (specify)' }
        ]
      }
    },
    {
      name: 'consent_date',
      label: 'Date of consent',
      field_type: 'date',
      required: true,
      display_order: 2,
      validation_rules: { max_date: 'today' }
    },
    {
      name: 'consent_type',
      label: 'Type of consent',
      field_type: 'radio',
      required: true,
      display_order: 3,
      field_config: {
        options: [
          { key: 'verbal', value: 'Verbal consent' },
          { key: 'written', value: 'Written consent' }
        ]
      }
    },
    {
      name: 'information_sharing_consent',
      label: 'Consent for information sharing',
      field_type: 'checkbox_group',
      display_order: 4,
      field_config: {
        options: [
          { key: 'within_agency', value: 'Within agency' },
          { key: 'partner_agencies', value: 'With partner agencies' },
          { key: 'government_services', value: 'With government services' },
          { key: 'medical_services', value: 'With medical services' }
        ]
      }
    },
    {
      name: 'consent_limitations',
      label: 'Any limitations on consent',
      field_type: 'textarea',
      display_order: 5,
      validation_rules: { max_length: 500 }
    }
  ]

  consent_fields.each do |field_attrs|
    consent_section.form_fields.find_or_create_by!(name: field_attrs[:name]) do |f|
      field_attrs.each { |key, value| f.send("#{key}=", value) }
      f.field_config = field_attrs[:field_config].to_json if field_attrs[:field_config]
      f.validation_rules = field_attrs[:validation_rules].to_json if field_attrs[:validation_rules]
    end
  end
end
```

#### Core Form 1B: Registration and Rapid Assessment

```ruby
def self.create_registration_rapid_assessment_form
  template = FormTemplate.find_or_create_by!(name: 'registration_rapid_assessment') do |t|
    t.title = '1.B REGISTRATION and RAPID ASSESSMENT FORM'
    t.description = 'Initial registration and rapid assessment of protection needs'
    t.form_type = 'core'
    t.sequence_order = 2
    t.is_active = true
  end

  # Section 1: Case Information
  case_info_section = template.form_sections.find_or_create_by!(name: 'case_information') do |s|
    s.title = '1. CASE INFORMATION'
    s.display_order = 1
    s.is_required = true
  end

  case_info_fields = [
    {
      name: 'date_case_identified',
      label: 'Date case was identified',
      field_type: 'date',
      required: true,
      display_order: 1,
      validation_rules: { max_date: 'today' }
    },
    {
      name: 'date_case_registered',
      label: 'Date case was registered',
      field_type: 'date',
      required: true,
      display_order: 2,
      validation_rules: { max_date: 'today' }
    },
    {
      name: 'date_case_reopened',
      label: 'Data case was re-opened',
      field_type: 'date',
      required: false,
      display_order: 3,
      validation_rules: { max_date: 'today' },
      help_text: 'If case is re-opened update Registration Form sheet with relevant information'
    },
    {
      name: 'case_id',
      label: 'Case ID',
      field_type: 'text',
      required: true,
      display_order: 4,
      validation_rules: { pattern: '^[A-Z0-9-]+$' }
    },
    {
      name: 'caseworker_id',
      label: 'Caseworker ID',
      field_type: 'text',
      required: true,
      display_order: 5
    },
    {
      name: 'agency',
      label: 'Agency',
      field_type: 'text',
      required: true,
      display_order: 6
    },
    {
      name: 'child_identification_method',
      label: 'How was the child identified',
      field_type: 'radio',
      required: true,
      display_order: 7,
      help_text: 'Please avoid editing dropdown',
      field_config: {
        options: [
          { key: 'self_referral', value: 'Self-referral child (him/herself)' },
          { key: 'family_member', value: 'Family member' },
          { key: 'community_member', value: 'Community member' },
          { key: 'media', value: 'Media' },
          { key: 'other_service_provider', value: 'Other service provider, specify sector' }
        ]
      }
    },
    {
      name: 'referring_organization_sector',
      label: 'UN/INGO/NGO, specify sector',
      field_type: 'checkbox_group',
      required: false,
      display_order: 8,
      field_config: {
        conditional_on: 'child_identification_method',
        conditional_value: 'other_service_provider',
        options: [
          { key: 'education', value: 'Education' },
          { key: 'food_security', value: 'Food security' },
          { key: 'health', value: 'Health' },
          { key: 'livelihoods', value: 'Livelihoods' },
          { key: 'nutrition', value: 'Nutrition' },
          { key: 'protection', value: 'Protection' },
          { key: 'child_protection', value: 'Child Protection' },
          { key: 'gbv', value: 'GBV' },
          { key: 'shelter', value: 'Shelter' },
          { key: 'camp_coord_mgmt', value: 'Camp Cord and Camp Mgmt' },
          { key: 'wash', value: 'Water, sanitation, and hygiene (WASH)' },
          { key: 'other', value: 'Other' }
        ]
      }
    }
  ]

  create_fields_for_section(case_info_section, case_info_fields)

  # Section 2: Child Personal Details
  personal_details_section = template.form_sections.find_or_create_by!(name: 'child_personal_details') do |s|
    s.title = '2. CHILD PERSONAL DETAILS'
    s.display_order = 2
    s.is_required = true
  end

  personal_details_fields = [
    {
      name: 'first_name',
      label: 'First name',
      field_type: 'text',
      required: true,
      display_order: 1,
      validation_rules: { min_length: 1, max_length: 50 }
    },
    {
      name: 'middle_name',
      label: 'Middle name / Father\'s name',
      field_type: 'text',
      required: false,
      display_order: 2,
      help_text: '(Optional)',
      validation_rules: { max_length: 50 }
    },
    {
      name: 'last_name',
      label: 'Last name/ Family name',
      field_type: 'text',
      required: true,
      display_order: 3,
      validation_rules: { min_length: 1, max_length: 50 }
    },
    {
      name: 'other_names',
      label: 'Other names or spelling the child is known by',
      field_type: 'text',
      required: false,
      display_order: 4,
      validation_rules: { max_length: 100 }
    },
    {
      name: 'date_of_birth',
      label: 'Date of birth (DOB)',
      field_type: 'date',
      required: true,
      display_order: 5,
      validation_rules: { max_date: 'today' },
      help_text: 'dd/mm/yyyy'
    },
    {
      name: 'sex',
      label: 'Sex - please avoid editing drop',
      field_type: 'radio',
      required: true,
      display_order: 6,
      field_config: {
        options: [
          { key: 'male', value: 'Male' },
          { key: 'female', value: 'Female' },
          { key: 'non_binary', value: 'Non-binary' },
          { key: 'other', value: 'Other' }
        ]
      }
    },
    {
      name: 'age',
      label: 'Age',
      field_type: 'number',
      required: true,
      display_order: 7,
      validation_rules: { min: 0, max: 25 },
      field_config: {
        auto_calculate: true,
        depends_on: 'date_of_birth',
        calculation: 'age_from_date',
        readonly: true,
        editable_when: 'date_of_birth == null'
      }
    },
    {
      name: 'age_estimated',
      label: 'Is the age estimated?',
      field_type: 'radio',
      required: true,
      display_order: 8,
      field_config: {
        options: [
          { key: 'yes', value: 'Yes' },
          { key: 'no', value: 'No' }
        ]
      }
    }
  ]

  create_fields_for_section(personal_details_section, personal_details_fields)

  # Continue Child Personal Details - Birth Registration and Documentation
  birth_registration_fields = [
    {
      name: 'birth_registration_status',
      label: 'Birth registration - please avoid editing dropdown',
      field_type: 'radio',
      required: true,
      display_order: 9,
      field_config: {
        options: [
          { key: 'birth_registered', value: 'Birth registered' },
          { key: 'birth_not_registered', value: 'Birth not registered' },
          { key: 'birth_registration_process_started', value: 'Birth registration process started but not completed' }
        ]
      }
    },
    {
      name: 'has_identification',
      label: 'Does the child have identification (ID card)?',
      field_type: 'radio',
      required: true,
      display_order: 10,
      field_config: {
        options: [
          { key: 'yes', value: 'Yes' },
          { key: 'no', value: 'No' }
        ]
      }
    },
    {
      name: 'identification_type',
      label: 'If yes, specify type of identification and number - Type',
      field_type: 'text',
      required: false,
      display_order: 11,
      field_config: {
        conditional_on: 'has_identification',
        conditional_value: 'yes'
      }
    },
    {
      name: 'identification_number',
      label: 'Identification Number',
      field_type: 'text',
      required: false,
      display_order: 12,
      field_config: {
        conditional_on: 'has_identification',
        conditional_value: 'yes'
      }
    },
    {
      name: 'nationality_status',
      label: 'Nationality status - please avoid editing drop down',
      field_type: 'radio',
      required: true,
      display_order: 13,
      field_config: {
        options: [
          { key: 'national', value: 'National' },
          { key: 'other_nationality', value: 'Other nationality' },
          { key: 'stateless', value: 'Stateless' },
          { key: 'unknown', value: 'Unknown' }
        ]
      }
    },
    {
      name: 'nationality_create_options',
      label: 'Nationality (create options for your context)',
      field_type: 'text',
      required: false,
      display_order: 14,
      help_text: 'Create options for your context'
    },
    {
      name: 'displacement_status',
      label: 'Displacement status - please avoid editing dropdown',
      field_type: 'radio',
      required: true,
      display_order: 15,
      field_config: {
        options: [
          { key: 'host_community', value: 'Host Community' },
          { key: 'asylum_seeker_refugee', value: 'Asylum seeker or refugee' },
          { key: 'internally_displaced', value: 'Internally displaced person' },
          { key: 'returnee', value: 'Returnee' },
          { key: 'migrant', value: 'Migrant' },
          { key: 'other', value: 'Other, specify' }
        ]
      }
    },
    {
      name: 'displacement_status_other',
      label: 'Other displacement status',
      field_type: 'text',
      required: false,
      display_order: 16,
      field_config: {
        conditional_on: 'displacement_status',
        conditional_value: 'other'
      }
    },
    {
      name: 'disability_status',
      label: 'Disability status - please avoid editing drop down',
      field_type: 'radio',
      required: true,
      display_order: 17,
      field_config: {
        options: [
          { key: 'no_disabilities', value: 'No disabilities' },
          { key: 'child_with_disabilities', value: 'Child with disabilities (mark below if possible)' }
        ]
      }
    },
    {
      name: 'disability_types',
      label: 'Child with disabilities (mark below if possible)',
      field_type: 'checkbox_group',
      required: false,
      display_order: 18,
      field_config: {
        conditional_on: 'disability_status',
        conditional_value: 'child_with_disabilities',
        options: [
          { key: 'mental_impairments', value: 'Mental impairments' },
          { key: 'sensory_impairments', value: 'Sensory impairments' },
          { key: 'physical_impairments', value: 'Physical impairments' },
          { key: 'intellectual_impairment', value: 'Intellectual impairment' },
          { key: 'other', value: 'Other, specify' }
        ]
      }
    },
    {
      name: 'marital_status',
      label: 'The child is - please avoid editing drop down',
      field_type: 'radio',
      required: true,
      display_order: 19,
      field_config: {
        options: [
          { key: 'not_married', value: 'Not married' },
          { key: 'planning_marriage', value: 'Planning to get married' },
          { key: 'married', value: 'Married' },
          { key: 'divorced', value: 'Divorced' },
          { key: 'widowed', value: 'Widowed' }
        ]
      }
    },
    {
      name: 'ethnic_affiliation',
      label: '(Optional) Child\'s ethnic affiliation (create options for your context)',
      field_type: 'text',
      required: false,
      display_order: 20,
      help_text: 'Create options for your context'
    },
    {
      name: 'religion',
      label: '(Optional) Child\'s religion (create options for your context)',
      field_type: 'text',
      required: false,
      display_order: 21,
      help_text: 'Create options for your context'
    },
    {
      name: 'languages_spoken',
      label: 'Languages spoken by the child (create options for your context)',
      field_type: 'text',
      required: false,
      display_order: 22,
      help_text: 'Create options for your context'
    }
  ]

  create_fields_for_section(personal_details_section, birth_registration_fields)

  # Section 3: Child Current Care Arrangement/Living Arrangements
  care_arrangements_section = template.form_sections.find_or_create_by!(name: 'care_arrangements') do |s|
    s.title = '3. CHILD CURRENT CARE ARRANGEMENT/ LIVING ARRANGEMENTS'
    s.display_order = 3
    s.is_required = true
  end

  care_arrangements_fields = [
    {
      name: 'type_of_care_arrangement',
      label: 'Type of care arrangement - please avoid editing drop down',
      field_type: 'radio',
      required: true,
      display_order: 1,
      field_config: {
        options: [
          { key: 'child_in_parental_care', value: 'Child in parental care' },
          { key: 'child_without_care_arrangement', value: 'Child without care arrangement' },
          { key: 'child_carer', value: 'Child Carer' },
          { key: 'child_in_child_headed_household', value: 'Child in Child Headed Household' },
          { key: 'child_in_institutional_residential_care', value: 'Child in institutional or residential care' },
          { key: 'child_in_alternative_care', value: 'Child in alternative care' },
          { key: 'child_in_foster_care', value: 'Child in foster care' },
          { key: 'child_in_kinship_care', value: 'Child in kinship care/customary care' },
          { key: 'child_in_other_family_based_care', value: 'Child in other forms of family based care (such as Kafala)' },
          { key: 'child_in_supported_independent_living', value: 'Child in a supported independent living arrangement' },
          { key: 'other', value: 'Other, specify' }
        ]
      }
    },
    {
      name: 'care_arrangement_description',
      label: 'Describe the child\'s current care arrangement',
      field_type: 'textarea',
      required: true,
      display_order: 2,
      help_text: 'For example, include information about the relationship with their caregiver, the ability of the caregiver to meet the needs of the child, etc. Note: if the child is in an alternative care arrangement, include information on how this arrangement was made, if it is permanent or temporary, etc.',
      validation_rules: { max_length: 1000 }
    },
    {
      name: 'current_address',
      label: 'Current address',
      field_type: 'textarea',
      required: true,
      display_order: 3,
      help_text: 'Note down location, address, block, house or shelter number, etc.',
      validation_rules: { max_length: 500 }
    },
    {
      name: 'area_of_living',
      label: '(Optional) Area of living- please avoid editing drop down',
      field_type: 'radio',
      required: false,
      display_order: 4,
      field_config: {
        options: [
          { key: 'urban_non_camp', value: 'Urban - non camp' },
          { key: 'rural_non_camp', value: 'Rural - non camp' },
          { key: 'camp_settlement', value: 'Camp/Settlement' },
          { key: 'other', value: 'Other' }
        ]
      }
    },
    {
      name: 'contact_method',
      label: 'How can the child be contacted? If not directly, specify through who',
      field_type: 'text',
      required: true,
      display_order: 5,
      validation_rules: { max_length: 200 }
    },
    {
      name: 'phone_number',
      label: 'If available, include phone number (indicate if this is WhatsApp, Viber etc.)',
      field_type: 'text',
      required: false,
      display_order: 6,
      validation_rules: { pattern: '^[+]?[0-9\s\-\(\)]+$' }
    }
  ]

  create_fields_for_section(care_arrangements_section, care_arrangements_fields)

  # Caregiver Information Section (conditional)
  caregiver_section = template.form_sections.find_or_create_by!(name: 'caregiver_information') do |s|
    s.title = 'CAREGIVER - If the child is not being cared for by one or both parents, provide personal details of the current caregiver'
    s.display_order = 4
    s.is_required = false
  end

  caregiver_fields = [
    {
      name: 'caregiver_first_name',
      label: 'First name',
      field_type: 'text',
      required: false,
      display_order: 1,
      validation_rules: { max_length: 50 }
    },
    {
      name: 'caregiver_middle_name',
      label: 'Middle name/ Father\'s name',
      field_type: 'text',
      required: false,
      display_order: 2,
      validation_rules: { max_length: 50 }
    },
    {
      name: 'caregiver_last_name',
      label: 'Last name/ Family name',
      field_type: 'text',
      required: false,
      display_order: 3,
      validation_rules: { max_length: 50 }
    },
    {
      name: 'caregiver_date_of_birth',
      label: 'Date of birth (DOB) of the caregiver',
      field_type: 'date',
      required: false,
      display_order: 4,
      validation_rules: { max_date: 'today' }
    },
    {
      name: 'caregiver_sex',
      label: 'Sex of the caregiver - please avoid editing drop down',
      field_type: 'radio',
      required: false,
      display_order: 5,
      field_config: {
        options: [
          { key: 'male', value: 'Male' },
          { key: 'female', value: 'Female' },
          { key: 'non_binary', value: 'Non-binary' },
          { key: 'other', value: 'Other' }
        ]
      }
    },
    {
      name: 'caregiver_age',
      label: 'Age',
      field_type: 'number',
      required: false,
      display_order: 6,
      validation_rules: { min: 0, max: 120 }
    },
    {
      name: 'caregiver_age_estimated',
      label: 'Is the age estimated?',
      field_type: 'radio',
      required: false,
      display_order: 7,
      field_config: {
        options: [
          { key: 'yes', value: 'Yes' },
          { key: 'no', value: 'No' }
        ]
      }
    },
    {
      name: 'caregiver_has_identification',
      label: 'Does the caregiver have identification (ID card)',
      field_type: 'radio',
      required: false,
      display_order: 8,
      field_config: {
        options: [
          { key: 'yes', value: 'Yes' },
          { key: 'no', value: 'No' }
        ]
      }
    },
    {
      name: 'caregiver_identification_type',
      label: 'If yes, specify type of identification and number - Type',
      field_type: 'text',
      required: false,
      display_order: 9,
      field_config: {
        conditional_on: 'caregiver_has_identification',
        conditional_value: 'yes'
      }
    },
    {
      name: 'caregiver_identification_number',
      label: 'Number',
      field_type: 'text',
      required: false,
      display_order: 10,
      field_config: {
        conditional_on: 'caregiver_has_identification',
        conditional_value: 'yes'
      }
    },
    {
      name: 'caregiver_nationality_status',
      label: 'Nationality status - please avoid editing drop down',
      field_type: 'radio',
      required: false,
      display_order: 11,
      field_config: {
        options: [
          { key: 'national', value: 'National' },
          { key: 'other_nationality', value: 'Other nationality' },
          { key: 'stateless', value: 'Stateless' },
          { key: 'unknown', value: 'Unknown' }
        ]
      }
    },
    {
      name: 'caregiver_displacement_status',
      label: 'Displacement status - please avoid editing drop down',
      field_type: 'radio',
      required: false,
      display_order: 12,
      field_config: {
        options: [
          { key: 'host_community', value: 'Host Community' },
          { key: 'asylum_seeker_refugee', value: 'Asylum seeker or refugee' },
          { key: 'internally_displaced', value: 'Internally displaced person' },
          { key: 'returnee', value: 'Returnee' },
          { key: 'migrant', value: 'Migrant' },
          { key: 'other', value: 'Other, specify' }
        ]
      }
    },
    {
      name: 'caregiver_disabilities',
      label: 'Does the caregiver have any disabilities, health issues or other characteristics which can affect care for the child and therefore impact overall risk assessment?',
      field_type: 'radio',
      required: false,
      display_order: 13,
      field_config: {
        options: [
          { key: 'no', value: 'No' },
          { key: 'yes', value: 'Yes' }
        ]
      }
    },
    {
      name: 'caregiver_disabilities_specify',
      label: 'If yes, specify',
      field_type: 'textarea',
      required: false,
      display_order: 14,
      field_config: {
        conditional_on: 'caregiver_disabilities',
        conditional_value: 'yes'
      },
      validation_rules: { max_length: 500 }
    }
  ]

  create_fields_for_section(caregiver_section, caregiver_fields)

  # Caregiver Relationship and Household Information
  caregiver_relationship_fields = [
    {
      name: 'caregiver_related_to_child',
      label: 'Is the caregiver related to the child?',
      field_type: 'radio',
      required: false,
      display_order: 15,
      field_config: {
        options: [
          { key: 'yes', value: 'Yes' },
          { key: 'no', value: 'No' }
        ]
      }
    },
    {
      name: 'caregiver_relationship_to_child',
      label: 'Relationship to the child?',
      field_type: 'text',
      required: false,
      display_order: 16,
      validation_rules: { max_length: 100 }
    },
    {
      name: 'caregiver_knows_family',
      label: 'If not related does the caregiver know the family of the child?',
      field_type: 'radio',
      required: false,
      display_order: 17,
      field_config: {
        conditional_on: 'caregiver_related_to_child',
        conditional_value: 'no',
        options: [
          { key: 'yes', value: 'Yes' },
          { key: 'no', value: 'No' }
        ]
      }
    },
    {
      name: 'caregiver_legal_guardian',
      label: 'Is the caregiver also the legal guardian?',
      field_type: 'radio',
      required: false,
      display_order: 18,
      field_config: {
        options: [
          { key: 'yes', value: 'Yes' },
          { key: 'no', value: 'No' }
        ]
      }
    },
    {
      name: 'care_arrangement_start_date',
      label: 'When did this care arrangement start?',
      field_type: 'date',
      required: false,
      display_order: 19,
      validation_rules: { max_date: 'today' }
    },
    {
      name: 'caregiver_willing_to_continue',
      label: 'Is caregiver willing to continue taking care of the child?',
      field_type: 'radio',
      required: false,
      display_order: 20,
      field_config: {
        options: [
          { key: 'yes', value: 'Yes' },
          { key: 'no', value: 'No' },
          { key: 'if_not_why', value: 'If not, why?' }
        ]
      }
    },
    {
      name: 'caregiver_contact_details',
      label: 'Caregiver\'s phone / contact details',
      field_type: 'text',
      required: false,
      display_order: 21,
      validation_rules: { max_length: 200 }
    }
  ]

  create_fields_for_section(caregiver_section, caregiver_relationship_fields)

  # Household Members Section
  household_section = template.form_sections.find_or_create_by!(name: 'household_members') do |s|
    s.title = 'Members of the household with whom the child is living with'
    s.display_order = 5
    s.is_required = false
  end

  household_fields = [
    {
      name: 'household_members_table',
      label: 'Household Members',
      field_type: 'repeatable_section',
      required: false,
      display_order: 1,
      field_config: {
        repeatable: true,
        max_entries: 20,
        fields: [
          {
            name: 'member_full_name',
            label: 'Full name',
            field_type: 'text',
            required: false,
            validation_rules: { max_length: 100 }
          },
          {
            name: 'member_age',
            label: 'Age',
            field_type: 'number',
            required: false,
            validation_rules: { min: 0, max: 120 }
          },
          {
            name: 'member_relationship',
            label: 'Relationship to the child',
            field_type: 'text',
            required: false,
            validation_rules: { max_length: 50 }
          }
        ]
      }
    },
    {
      name: 'household_size_adults',
      label: 'Size of the household - of adults',
      field_type: 'number',
      required: false,
      display_order: 2,
      validation_rules: { min: 0, max: 50 }
    },
    {
      name: 'household_size_children',
      label: 'Size of the household - of children',
      field_type: 'number',
      required: false,
      display_order: 3,
      validation_rules: { min: 0, max: 50 }
    },
    {
      name: 'main_caregiver_under_18',
      label: 'Is the age of the main caregiver under 18',
      field_type: 'radio',
      required: false,
      display_order: 4,
      field_config: {
        options: [
          { key: 'yes', value: 'Yes' },
          { key: 'no', value: 'No' }
        ]
      }
    }
  ]

  create_fields_for_section(household_section, household_fields)

  # Section 4: Child Protection Risks
  protection_risks_section = template.form_sections.find_or_create_by!(name: 'child_protection_risks') do |s|
    s.title = '4. CHILD PROTECTION RISKS'
    s.display_order = 6
    s.is_required = true
  end

  protection_risks_fields = [
    {
      name: 'protection_situation_description',
      label: 'Briefly describe the child\'s situation and document additional observations which can support risk assessment?',
      field_type: 'textarea',
      required: true,
      display_order: 1,
      help_text: 'This can include care arrangements, health, ...',
      validation_rules: { max_length: 2000 }
    },
    {
      name: 'urgent_case_note',
      label: 'If this is a case of rape or sexual assault, make sure to address urgent needs that require an immediate response and check in with your supervisor for support',
      field_type: 'static_text',
      required: false,
      display_order: 2,
      help_text: 'Important note for case workers'
    },
    {
      name: 'protection_incidents',
      label: 'Child protection risks identified (select all that apply) - please avoid editing drop down, avoid adding any category which may be similar or part of these drop down categories',
      field_type: 'checkbox_group',
      required: false,
      display_order: 3,
      field_config: {
        options: [
          { key: 'physical_violence_abuse', value: 'Physical violence or abuse against a child' },
          { key: 'psychological_violence_abuse', value: 'Psychological violence or abuse against a child' },
          { key: 'sexual_violence_abuse', value: 'Sexual violence or abuse against a child' },
          { key: 'neglect_of_child', value: 'Neglect of the child' },
          { key: 'exploitation', value: 'Exploitation' },
          { key: 'economic_exploitation', value: 'Economic exploitation' },
          { key: 'harmful_hazardous_labour', value: 'Harmful or hazardous labour' },
          { key: 'sexual_exploitation', value: 'Sexual exploitation' },
          { key: 'child_in_conflict_with_law', value: 'Child in conflict with the law or detained' },
          { key: 'recruitment_armed_forces', value: 'Recruitment and use by armed forces and groups' }
        ]
      }
    },
    {
      name: 'protection_vulnerabilities',
      label: 'Vulnerabilities',
      field_type: 'radio',
      required: false,
      display_order: 4,
      field_config: {
        options: [
          { key: 'unaccompanied_child', value: 'Unaccompanied Child' },
          { key: 'separated_child', value: 'Separated Child' },
          { key: 'child_marriage', value: 'Child marriage' },
          { key: 'psychological_distress', value: 'Psychological distress (moderate to severe)' }
        ]
      }
    },
    {
      name: 'justice_system_status',
      label: 'Status in relation to the justice system',
      field_type: 'radio',
      required: false,
      display_order: 5,
      field_config: {
        options: [
          { key: 'no_contact', value: 'No contact with the justice system' },
          { key: 'child_in_contact', value: 'Child in contact with the justice system' },
          { key: 'child_victim_of_offence', value: 'The child is victim of an offence' },
          { key: 'child_witness_of_offence', value: 'The child is witness of an offence' },
          { key: 'child_in_conflict_with_law', value: 'The child is in conflict with the law' },
          { key: 'child_detained', value: 'Child detained' }
        ]
      }
    }
  ]

  create_fields_for_section(protection_risks_section, protection_risks_fields)

  # Section 5: Family Details
  family_details_section = template.form_sections.find_or_create_by!(name: 'family_details') do |s|
    s.title = '5. FAMILY DETAILS'
    s.display_order = 7
    s.is_required = false
  end

  family_details_fields = [
    {
      name: 'family_first_name',
      label: 'First name',
      field_type: 'text',
      required: false,
      display_order: 1,
      validation_rules: { max_length: 50 }
    },
    {
      name: 'family_middle_name',
      label: 'Middle name/Father\'s name',
      field_type: 'text',
      required: false,
      display_order: 2,
      validation_rules: { max_length: 50 }
    },
    {
      name: 'family_last_name',
      label: 'Last name/Family name',
      field_type: 'text',
      required: false,
      display_order: 3,
      validation_rules: { max_length: 50 }
    }
  ]

  create_fields_for_section(family_details_section, family_details_fields)
end

def self.create_fields_for_section(section, fields)
  fields.each do |field_attrs|
    section.form_fields.find_or_create_by!(name: field_attrs[:name]) do |f|
      field_attrs.each { |key, value| f.send("#{key}=", value) unless [:field_config, :validation_rules].include?(key) }
      f.field_config = field_attrs[:field_config].to_json if field_attrs[:field_config]
      f.validation_rules = field_attrs[:validation_rules].to_json if field_attrs[:validation_rules]
    end
  end
end
```

### 3.2 Seeds Integration in Main Seeds File

```ruby
# db/seeds.rb
if Rails.env.development? || Rails.env.staging?
  puts "🌱 Creating Form Templates..."

  require_relative 'seeds/form_templates'
  FormTemplateSeeds.create_all

  puts "✅ Form Templates created successfully!"
  puts "📊 Summary:"
  puts "   - Core Forms: #{FormTemplate.where(form_type: 'core').count}"
  puts "   - Supplementary Forms: #{FormTemplate.where(form_type: 'supplementary').count}"
  puts "   - Total Sections: #{FormSection.count}"
  puts "   - Total Fields: #{FormField.count}"
end
```

### 3.3 Field Calculation Examples

```ruby
# Example calculated fields for different use cases
def self.create_calculation_examples
  # Age calculation from date of birth
  {
    name: 'age',
    field_type: 'number',
    field_config: {
      auto_calculate: true,
      depends_on: 'date_of_birth',
      calculation: 'age_from_date',
      readonly: true,
      editable_when: 'date_of_birth == null'
    }
  }

  # Sum of multiple numeric fields
  {
    name: 'total_score',
    field_type: 'number',
    field_config: {
      auto_calculate: true,
      depends_on: ['score_1', 'score_2', 'score_3'],
      calculation: 'sum',
      readonly: true
    }
  }

  # Percentage calculation
  {
    name: 'completion_percentage',
    field_type: 'number',
    field_config: {
      auto_calculate: true,
      depends_on: ['completed_tasks', 'total_tasks'],
      calculation: 'percentage',
      readonly: true
    }
  }

  # Count of non-empty fields
  {
    name: 'services_count',
    field_type: 'number',
    field_config: {
      auto_calculate: true,
      depends_on: ['service_1', 'service_2', 'service_3', 'service_4'],
      calculation: 'count_non_empty',
      readonly: true
    }
  }
end
```

### 3.4 Conditional Logic Seeds

```ruby
# Add to form_templates.rb
def self.create_conditional_logic_examples
  # UASC-specific sections that only show for unaccompanied children
  uasc_template = FormTemplate.find_by(name: 'uasc_assessment')

  if uasc_template
    uasc_section = uasc_template.form_sections.find_or_create_by!(name: 'uasc_specific') do |s|
      s.title = 'Unaccompanied Child Specific Assessment'
      s.display_order = 1
      s.display_condition = 'accompanied_status == "unaccompanied" && age < 18'
    end

    # Fields specific to UASC cases
    uasc_fields = [
      {
        name: 'separation_circumstances',
        label: 'Circumstances of separation from family',
        field_type: 'textarea',
        required: true,
        validation_rules: { max_length: 500 }
      },
      {
        name: 'family_tracing_consent',
        label: 'Consent for family tracing',
        field_type: 'radio',
        required: true,
        field_config: {
          options: [
            { key: 'yes', value: 'Yes, child consents to family tracing' },
            { key: 'no', value: 'No, child does not consent' },
            { key: 'partial', value: 'Partial consent (specify limitations)' }
          ]
        }
      }
    ]

    create_fields_for_section(uasc_section, uasc_fields)
  end
end
```

### 3.5 Frontend Integration for Calculated Fields

```javascript
// Frontend implementation example for calculated fields
const useFieldCalculation = (field, formData) => {
  const [calculatedValue, setCalculatedValue] = useState(null);
  const [isCalculating, setIsCalculating] = useState(false);

  useEffect(
    () => {
      if (!field.auto_calculate || !field.depends_on) return;

      const dependencies = Array.isArray(field.depends_on)
        ? field.depends_on
        : [field.depends_on];
      const hasRequiredData = dependencies.some((dep) => formData[dep] != null);

      if (hasRequiredData) {
        setIsCalculating(true);

        // Call backend calculation endpoint using field ID
        fetch(`/api/v1/fields/${field.id}/calculate`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            value:
              field.calculation === "age_from_date"
                ? formData[field.depends_on]
                : dependencies.map((dep) => formData[dep]),
          }),
        })
          .then((response) => response.json())
          .then((data) => {
            setCalculatedValue(data.value);
            setIsCalculating(false);
          })
          .catch((error) => {
            console.error("Calculation error:", error);
            setIsCalculating(false);
          });
      } else {
        setCalculatedValue(null);
      }
    },
    dependencies.map((dep) => formData[dep])
  );

  return { calculatedValue, isCalculating };
};

// Usage in form components
const FormField = ({ field, formData, onChange }) => {
  const { calculatedValue, isCalculating } = useFieldCalculation(
    field,
    formData
  );

  const isCalculated = field.auto_calculate && calculatedValue !== null;
  const isEditable =
    !isCalculated && evaluateCondition(field.editable_when, formData);

  return (
    <div className="form-field">
      <label>{field.label}</label>
      <input
        type={field.field_type}
        value={isCalculated ? calculatedValue : formData[field.name] || ""}
        onChange={
          isEditable ? (e) => onChange(field.name, e.target.value) : undefined
        }
        disabled={isCalculated || isCalculating}
        placeholder={
          isCalculating
            ? "Calculating..."
            : isCalculated
            ? "Auto-calculated"
            : field.placeholder_when_editable
        }
        className={isCalculated ? "calculated-field" : ""}
      />
      {isCalculated && (
        <small className="calculation-indicator">
          Calculated from {field.depends_on}
        </small>
      )}
    </div>
  );
};
```

## Phase 4: API Enhancements

### 4.1 Enhanced Controllers Structure

```ruby
# Base controller with common functionality
class Api::V1::BaseController < ApplicationController
  include AtharAuth::Controllers::Concerns::Authenticable

  before_action :authenticate_user!
  before_action :set_current_user

  rescue_from ActiveRecord::RecordNotFound, with: :record_not_found
  rescue_from ActiveRecord::RecordInvalid, with: :record_invalid
  rescue_from Pundit::NotAuthorizedError, with: :user_not_authorized

  private

  def set_current_user
    @current_user = current_user
  end

  def record_not_found(exception)
    render json: { error: 'Record not found' }, status: :not_found
  end

  def record_invalid(exception)
    render json: { errors: exception.record.errors }, status: :unprocessable_entity
  end

  def user_not_authorized
    render json: { error: 'Not authorized' }, status: :forbidden
  end
end

# Field Calculations Controller - Handles real-time field calculations
class Api::V1::FieldCalculationsController < Api::V1::BaseController
  # POST /api/v1/fields/:id/calculate
  def calculate
    field = FormField.find(params[:id])
    return render_not_calculable unless field.calculated_field?

    source_value = calculation_params[:value]
    calculated_value = FormFieldCalculationService.calculate(
      field.calculation,
      source_value
    )

    render json: {
      field_id: field.id,
      field_name: field.field_name,
      value: calculated_value,
      calculated_at: Time.current.iso8601
    }
  end

  # POST /api/v1/forms/:form_id/calculate_all
  def calculate_all_fields
    form_template = FormTemplate.find(params[:form_id])
    form_data = calculation_params[:form_data] || {}

    calculated_values = {}

    form_template.form_fields.select(&:calculated_field?).each do |field|
      if field.should_calculate_for?(form_data)
        calculated_values[field.field_name] = FormFieldCalculationService.calculate_field_value(field, form_data)
      end
    end

    render json: {
      calculated_values: calculated_values,
      calculated_at: Time.current.iso8601
    }
  end

  private

  def calculation_params
    params.permit(:value, form_data: {})
  end

  def render_not_calculable
    render json: { error: 'Field is not calculable' }, status: :unprocessable_entity
  end
end

# Enhanced Cases Controller
class Api::V1::CasesController < Api::V1::BaseController
  before_action :set_case, only: [:show, :update, :destroy, :submit_for_approval, :progress]

  def index
    @cases = policy_scope(Case).includes(:assigned_user, :approval_request)
    @cases = apply_filters(@cases)
    @cases = @cases.page(params[:page]).per(params[:per_page] || 25)

    render json: CaseSerializer.new(@cases, include: [:assigned_user]).serializable_hash
  end

  def show
    authorize @case
    render json: CaseSerializer.new(@case, include: case_includes).serializable_hash
  end

  def create
    @case = Case.new(case_params)
    @case.project_id = current_user.project_id unless current_user.global_user?

    authorize @case

    if @case.save
      render json: CaseSerializer.new(@case).serializable_hash, status: :created
    else
      render json: { errors: @case.errors }, status: :unprocessable_entity
    end
  end

  # NEW: Submit case for approval after form completion
  def submit_for_approval
    authorize @case, :update?

    unless @case.can_submit_for_approval?
      return render json: {
        error: 'Case cannot be submitted for approval',
        progress: @case.form_completion_progress
      }, status: :unprocessable_entity
    end

    if @case.submit_for_approval!(current_user)
      render json: {
        message: 'Case submitted for approval successfully',
        case: CaseSerializer.new(@case).serializable_hash
      }
    else
      render json: { error: 'Failed to submit case for approval' }, status: :unprocessable_entity
    end
  end

  # NEW: Get case form completion progress
  def progress
    authorize @case, :show?

    render json: {
      case_id: @case.id,
      case_number: @case.case_number,
      status: @case.status,
      progress: @case.form_completion_progress
    }
  end

  def update
    authorize @case

    if @case.update(case_params)
      render json: CaseSerializer.new(@case).serializable_hash
    else
      render json: { errors: @case.errors }, status: :unprocessable_entity
    end
  end

  private

  def set_case
    @case = Case.find(params[:id])
  end

  def case_params
    params.require(:case).permit(:beneficiary_id, :assigned_user_id, :case_type,
                                 :priority_level, :confidentiality_level, :description)
  end

  def case_includes
    %w[assigned_user approval_request]
  end

  def apply_filters(scope)
    scope = scope.by_case_type(params[:case_type]) if params[:case_type].present?
    scope = scope.by_priority(params[:priority]) if params[:priority].present?
    scope = scope.where(status: params[:status]) if params[:status].present?
    scope
  end
end

# Case Comments Controller - Case-level comments
class Api::V1::CaseCommentsController < Api::V1::BaseController
  before_action :set_case
  before_action :set_comment, only: [:show, :update, :destroy, :resolve]

  def index
    @comments = @case.case_comments.includes(:user, :replies)
                    .top_level.recent
                    .page(params[:page]).per(params[:per_page] || 20)

    render json: CommentSerializer.new(@comments, include: [:user, :replies]).serializable_hash
  end

  def show
    render json: CommentSerializer.new(@comment, include: [:user, :replies]).serializable_hash
  end

  def create
    @comment = @case.comments.build(comment_params)
    @comment.user = current_user
    @comment.commentable = @case
    @comment.comment_type = 'case_comment'

    authorize @comment

    if @comment.save
      render json: CommentSerializer.new(@comment, include: [:user]).serializable_hash, status: :created
    else
      render json: { errors: @comment.errors }, status: :unprocessable_entity
    end
  end

  def update
    authorize @comment

    if @comment.update(comment_params)
      render json: CommentSerializer.new(@comment, include: [:user]).serializable_hash
    else
      render json: { errors: @comment.errors }, status: :unprocessable_entity
    end
  end

  def destroy
    authorize @comment
    @comment.destroy
    head :no_content
  end

  def resolve
    authorize @comment

    if @comment.resolve!(current_user)
      render json: CommentSerializer.new(@comment).serializable_hash
    else
      render json: { error: 'Cannot resolve comment' }, status: :unprocessable_entity
    end
  end

  private

  def set_case
    @case = policy_scope(Case).find(params[:case_id])
  end

  def set_comment
    @comment = @case.case_comments.find(params[:id])
  end

  def comment_params
    params.require(:comment).permit(:content, :parent_comment_id)
  end
end

# Field Comments Controller - Field-level comments
class Api::V1::FieldCommentsController < Api::V1::BaseController
  before_action :set_form_field
  before_action :set_comment, only: [:show, :update, :destroy, :resolve]

  def index
    @comments = @form_field.comments.includes(:user, :replies)
                           .recent
                           .page(params[:page]).per(params[:per_page] || 10)

    render json: CommentSerializer.new(@comments, include: [:user, :replies]).serializable_hash
  end

  def create
    @comment = Comment.new(comment_params)
    @comment.case = @form_field.form_section.form_template.form_submissions.first&.case # Get associated case
    @comment.user = current_user
    @comment.commentable = @form_field
    @comment.comment_type = 'field_comment'

    authorize @comment

    if @comment.save
      render json: CommentSerializer.new(@comment, include: [:user]).serializable_hash, status: :created
    else
      render json: { errors: @comment.errors }, status: :unprocessable_entity
    end
  end

  def resolve
    authorize @comment

    if @comment.resolve!(current_user)
      render json: CommentSerializer.new(@comment).serializable_hash
    else
      render json: { error: 'Cannot resolve comment' }, status: :unprocessable_entity
    end
  end

  private

  def set_form_field
    @form_field = FormField.find(params[:form_field_id])
  end

  def set_comment
    @comment = @form_field.comments.find(params[:id])
  end

  def comment_params
    params.require(:comment).permit(:content, :parent_comment_id)
  end
end

# Comment Serializer - JSON:API format for comments
class CommentSerializer
  include JSONAPI::Serializer

  attributes :content, :comment_type, :status, :created_at, :updated_at

  belongs_to :user
  belongs_to :case
  belongs_to :commentable, polymorphic: true
  belongs_to :parent_comment, record_type: :comment, serializer: :comment
  has_many :replies, record_type: :comment, serializer: :comment

  attribute :user_name do |comment|
    comment.user.name
  end

  attribute :user_role do |comment|
    comment.user.role&.name
  end

  attribute :reply_count do |comment|
    comment.reply_count
  end

  attribute :can_resolve do |comment, params|
    user = params[:current_user]
    comment.can_be_resolved_by?(user) if user
  end

  attribute :commentable_info do |comment|
    case comment.commentable_type
    when 'Case'
      {
        type: 'case',
        case_number: comment.commentable.case_number
      }
    when 'FormField'
      {
        type: 'field',
        field_name: comment.commentable.field_name,
        field_label: comment.commentable.label
      }
    end
  end
end
```

### 1.3 Comprehensive Field Types & Dropdown Management

Advanced field type system with dynamic options and validation:

```ruby
# Model-Based Lookup System - Each dropdown type has its own model

# Base class for all lookup models with project scoping
class LookupBase < ApplicationRecord
  self.abstract_class = true

  belongs_to :project, optional: true  # nil = global, project_id = project-specific

  validates :name, presence: true
  validates :name_ar, presence: true  # Arabic name
  validates :active, inclusion: { in: [true, false] }  # Keep boolean validation for clarity

  scope :active, -> { where(active: true) }
  scope :ordered, -> { order(:display_order, :name) }
  scope :for_project, ->(project_id) {
    where("project_id IS NULL OR project_id = ?", project_id)
  }
  scope :global_only, -> { where(project_id: nil) }
  scope :project_specific, ->(project_id) { where(project_id: project_id) }

  def display_name(locale = I18n.locale)
    locale.to_s == 'ar' ? name_ar : name
  end

  def api_representation
    {
      id: id,
      name: name,
      name_ar: name_ar,
      code: respond_to?(:code) ? code : nil,
      active: active,
      project_id: project_id,
      display_order: display_order,
      created_at: created_at,
      updated_at: updated_at
    }.compact
  end
end

# Staff Positions
class StaffPosition < LookupBase
  validates :code, presence: true, uniqueness: { scope: :project_id }
  validates :department, presence: true

  scope :by_department, ->(dept) { where(department: dept) }

  def api_representation
    super.merge({
      code: code,
      department: department
    })
  end
end

# Partner Agencies
class PartnerAgency < LookupBase
  validates :contact_email, format: { with: URI::MailTo::EMAIL_REGEXP }, allow_blank: true

  enum agency_type: {
    ngo: 0,
    ingo: 1,
    government: 2,
    un_agency: 3,
    local_org: 4
  }

  scope :by_type, ->(type) { where(agency_type: type) }

  def api_representation
    super.merge({
      agency_type: agency_type,
      contact_email: contact_email
    })
  end
end

# Geographic Locations (Hierarchical)
class GeographicLocation < LookupBase
  belongs_to :parent_location, class_name: 'GeographicLocation', optional: true
  has_many :child_locations, class_name: 'GeographicLocation', foreign_key: 'parent_location_id'

  validates :iso_code, presence: true, if: -> { country? }

  enum location_type: {
    country: 0,
    governorate: 1,
    district: 2,
    city: 3,
    neighborhood: 4
  }

  scope :countries, -> { where(location_type: 'country') }
  scope :governorates, -> { where(location_type: 'governorate') }
  scope :cities, -> { where(location_type: 'city') }
  scope :children_of, ->(parent) { where(parent_location: parent) }

  def api_representation
    super.merge({
      location_type: location_type,
      iso_code: iso_code,
      parent_location_id: parent_location_id,
      has_children: child_locations.active.any?
    })
  end
end

# Case Types
class CaseType < LookupBase
  has_many :cases

  enum category: {
    child_protection: 0,
    gbv: 1,
    general_protection: 2
  }

  def api_representation
    super.merge({
      category: category
    })
  end
end

# Service Categories
class ServiceCategory < LookupBase
  has_many :services

  enum sector: {
    health: 0,
    education: 1,
    legal: 2,
    social: 3,
    financial: 4,
    housing: 5,
    employment: 6
  }

  enum target_group: {
    children: 0,
    women: 1,
    men: 2,
    elderly: 3,
    disabled: 4,
    all: 5
  }

  def api_representation
    super.merge({
      sector: sector,
      target_group: target_group
    })
  end
end

# Referral Sources
class ReferralSource < LookupBase
  enum source_type: {
    self_referral: 0,
    community_member: 1,
    organization: 2,
    government: 3,
    hotline: 4
  }

  def api_representation
    super.merge({
      source_type: source_type
    })
  end
end

# Keep existing business models (Service, etc.) unchanged
# FormField references lookup models for dropdown options
  has_many :child_values, class_name: 'LookupValue', foreign_key: 'parent_value_id'

  validates :value_key, presence: true, uniqueness: { scope: :lookup_table_id }
  validates :display_value, presence: true
  validates :display_order, presence: true

  # Conditional display logic
  validates :display_condition, format: { with: /\A(\w+\s*(==|!=|>|<|>=|<=)\s*\w+(\s*(&&|\|\|)\s*\w+\s*(==|!=|>|<|>=|<=)\s*\w+)*)*\z/, allow_blank: true }

  scope :active, -> { where(active: true) }
  scope :ordered, -> { order(:display_order) }
  scope :root_level, -> { where(parent_value_id: nil) }
  scope :for_parent, ->(parent) { where(parent_value: parent) }

  def has_children?
    child_values.active.any?
  end

  def full_hierarchy_path
    path = [display_value]
    current = parent_value
    while current
      path.unshift(current.display_value)
      current = current.parent_value
    end
    path.join(' > ')
  end
end

# Field Type Definitions - Comprehensive field type system
class FieldTypeDefinition < ApplicationRecord
  has_many :form_fields, foreign_key: 'field_type'
  has_many :field_type_validations, dependent: :destroy

  validates :name, presence: true, uniqueness: true
  validates :input_type, inclusion: {
    in: %w[
      text textarea number decimal
      date datetime time
      select radio checkbox
      file image signature
      location coordinates
      phone email url
      rich_text markdown
      rating scale slider
      yes_no true_false
      currency percentage
      barcode qr_code
      lookup_single lookup_multiple
      calculated readonly hidden
      multi_step_wizard
      repeatable_group
      conditional_section
    ]
  }
  validates :validation_schema, format: { with: /\A\{.*\}\z/, allow_blank: true } # JSON schema

  enum data_type: {
    string: 0,
    integer: 1,
    decimal: 2,
    boolean: 3,
    date: 4,
    datetime: 5,
    json: 6,
    array: 7,
    object: 8
  }

  # Configuration for field behavior
  # Example: { "min_length": 2, "max_length": 100, "pattern": "^[A-Za-z\\s]+$" }
  validates :default_config, format: { with: /\A\{.*\}\z/, allow_blank: true }

  scope :active, -> { where(active: true) }
  scope :by_input_type, ->(type) { where(input_type: type) }

  def render_config(field_instance)
    config = JSON.parse(default_config || '{}')

    # Merge with field-specific configuration
    if field_instance.field_config.present?
      field_config = JSON.parse(field_instance.field_config)
      config.merge!(field_config)
    end

    # Add lookup data if applicable
    if input_type.include?('lookup') && field_instance.lookup_source_type.present?
      # Use polymorphic lookup based on model class name
      config['options'] = field_instance.formatted_lookup_options || []
    end

    config
  end
end

# Enhanced FormField with model-based lookups
class FormField < ApplicationRecord
  # ... existing associations ...
  belongs_to :field_type_definition, foreign_key: 'field_type', primary_key: 'name'

  # For model-based lookups - store the model class name
  validates :lookup_source_type, inclusion: {
    in: %w[StaffPosition PartnerAgency GeographicLocation CaseType ServiceCategory ReferralSource]
  }, allow_blank: true

  # Field-specific configuration (JSON)
  # Examples:
  # Text field: { "placeholder": "Enter name", "max_length": 100 }
  # Select field: { "multiple": true, "searchable": true }
  # Date field: { "min_date": "1900-01-01", "max_date": "today" }
  # Location field: { "enable_gps": true, "map_provider": "google" }
  validates :field_config, format: { with: /\A\{.*\}\z/, allow_blank: true }

  # Get lookup options for this field with project scoping
  def lookup_options(context = {})
    return [] unless lookup_source_type.present?

    # Use project_id from context, current_project, or user's project_id
    project_id = context[:project_id] ||
                 context[:current_project]&.id ||
                 context[:user]&.project_id

    case lookup_source_type
    when 'StaffPosition'
      StaffPosition.active.for_project(project_id).ordered
    when 'PartnerAgency'
      PartnerAgency.active.for_project(project_id).ordered
    when 'GeographicLocation'
      base_scope = GeographicLocation.active.for_project(project_id)
      if context[:parent_location_id]
        base_scope.children_of(context[:parent_location_id]).ordered
      else
        base_scope.countries.ordered
      end
    when 'CaseType'
      CaseType.active.for_project(project_id).ordered
    when 'ServiceCategory'
      ServiceCategory.active.for_project(project_id).ordered
    when 'ReferralSource'
      ReferralSource.active.for_project(project_id).ordered
    else
      []
    end
  end

  # Get formatted options for API response
  def formatted_lookup_options(context = {})
    return [] unless lookup_source_type.present?

    options = lookup_options(context)
    locale = context[:locale] || I18n.locale

    options.map do |option|
      {
        id: option.id,
        key: option.respond_to?(:code) ? option.code : option.id.to_s,
        value: option.display_name(locale),
        metadata: option.api_representation
      }
    end
  end

  # Conditional logic for field display/requirement
  # Example: { "show_if": "gender == 'female'", "required_if": "age < 18" }
  validates :conditional_logic, format: { with: /\A\{.*\}\z/, allow_blank: true }

  def render_configuration
    field_type_definition.render_config(self)
  end

  def is_conditional?
    conditional_logic.present?
  end

  def should_display?(form_data)
    return true unless is_conditional?

    logic = JSON.parse(conditional_logic)
    return true unless logic['show_if']

    evaluate_condition(logic['show_if'], form_data)
  end

  def is_required_for?(form_data)
    return required unless is_conditional?

    logic = JSON.parse(conditional_logic)
    return required unless logic['required_if']

    evaluate_condition(logic['required_if'], form_data)
  end

  def is_enabled_for?(form_data)
    return true unless is_conditional?

    logic = JSON.parse(conditional_logic)
    return true unless logic['enabled_if']

    evaluate_condition(logic['enabled_if'], form_data)
  end

  # Get dependent field relationships
  def dependent_field_config
    return {} unless is_conditional?

    logic = JSON.parse(conditional_logic)
    {
      show_if: logic['show_if'],
      required_if: logic['required_if'],
      enabled_if: logic['enabled_if'],
      depends_on: extract_dependent_fields(logic)
    }
  end

  # Extract which fields this field depends on
  def extract_dependent_fields(logic)
    dependent_fields = []

    %w[show_if required_if enabled_if].each do |condition_type|
      next unless logic[condition_type]

      # Extract field names from conditions like "gender == 'female'"
      field_matches = logic[condition_type].scan(/(\w+)\s*(?:==|!=|>|<|>=|<=)/)
      dependent_fields.concat(field_matches.flatten)
    end

    dependent_fields.uniq
  end

  private

  def build_option_metadata(option)
    metadata = { model_type: option.class.name }

    case option
    when StaffPosition
      metadata[:department] = option.department
    when PartnerAgency
      metadata[:agency_type] = option.agency_type
    when GeographicLocation
      metadata[:location_type] = option.location_type
      metadata[:parent_id] = option.parent_location_id
    end

    metadata
  end

  def evaluate_condition(condition, form_data)
    # Simple condition evaluation using FormField IDs
    # This would be enhanced with a proper expression parser
    # Example: condition = "field_123 == 'female'" where 123 is FormField ID
    # Example: "gender == 'female'" or "age < 18"

    # Parse condition (simplified)
    if condition.match(/(\w+)\s*(==|!=|>|<|>=|<=)\s*['"]?([^'"]+)['"]?/)
      field_name = $1
      operator = $2
      value = $3

      field_value = form_data.dig(field_name, 'value')

      case operator
      when '=='
        field_value.to_s == value
      when '!='
        field_value.to_s != value
      when '>'
        field_value.to_f > value.to_f
      when '<'
        field_value.to_f < value.to_f
      when '>='
        field_value.to_f >= value.to_f
      when '<='
        field_value.to_f <= value.to_f
      else
        false
      end
    else
      false
    end
  end
end

# Predefined Field Types Seed Data
class FieldTypeSeeder
  FIELD_TYPES = [
    {
      name: 'text',
      input_type: 'text',
      data_type: 'string',
      default_config: {
        placeholder: 'Enter text',
        max_length: 255,
        autocomplete: false
      }.to_json
    },
    {
      name: 'textarea',
      input_type: 'textarea',
      data_type: 'string',
      default_config: {
        placeholder: 'Enter detailed text',
        rows: 4,
        max_length: 2000
      }.to_json
    },
    {
      name: 'number',
      input_type: 'number',
      data_type: 'integer',
      default_config: {
        min: 0,
        max: 999999,
        step: 1
      }.to_json
    },
    {
      name: 'decimal',
      input_type: 'number',
      data_type: 'decimal',
      default_config: {
        min: 0,
        max: 999999.99,
        step: 0.01,
        decimal_places: 2
      }.to_json
    },
    {
      name: 'date',
      input_type: 'date',
      data_type: 'date',
      default_config: {
        format: 'YYYY-MM-DD',
        min_date: '1900-01-01',
        max_date: 'today'
      }.to_json
    },
    {
      name: 'select_box',
      input_type: 'select',
      data_type: 'string',
      default_config: {
        searchable: true,
        clearable: true,
        multiple: false
      }.to_json
    },
    {
      name: 'multi_select',
      input_type: 'select',
      data_type: 'array',
      default_config: {
        searchable: true,
        clearable: true,
        multiple: true,
        max_selections: 10
      }.to_json
    },
    {
      name: 'radio_button',
      input_type: 'radio',
      data_type: 'string',
      default_config: {
        layout: 'vertical'
      }.to_json
    },
    {
      name: 'checkbox',
      input_type: 'checkbox',
      data_type: 'boolean',
      default_config: {
        default_value: false
      }.to_json
    },
    {
      name: 'yes_no',
      input_type: 'radio',
      data_type: 'boolean',
      default_config: {
        options: [
          { key: 'yes', value: true, label: 'Yes' },
          { key: 'no', value: false, label: 'No' }
        ]
      }.to_json
    },
    {
      name: 'phone',
      input_type: 'text',
      data_type: 'string',
      default_config: {
        pattern: '^\\+?[1-9]\\d{1,14}$',
        placeholder: '+962 7X XXX XXXX',
        format_mask: true
      }.to_json
    },
    {
      name: 'email',
      input_type: 'email',
      data_type: 'string',
      default_config: {
        pattern: '^[^@]+@[^@]+\\.[^@]+$',
        placeholder: '<EMAIL>'
      }.to_json
    },
    {
      name: 'location',
      input_type: 'location',
      data_type: 'json',
      default_config: {
        enable_gps: true,
        map_provider: 'google',
        zoom_level: 15,
        address_lookup: true
      }.to_json
    },
    {
      name: 'file_upload',
      input_type: 'file',
      data_type: 'string',
      default_config: {
        max_file_size: '10MB',
        allowed_types: ['pdf', 'doc', 'docx', 'jpg', 'png'],
        multiple: false
      }.to_json
    },
    {
      name: 'signature',
      input_type: 'signature',
      data_type: 'string',
      default_config: {
        width: 400,
        height: 200,
        background_color: '#ffffff',
        pen_color: '#000000'
      }.to_json
    },
    {
      name: 'rating',
      input_type: 'rating',
      data_type: 'integer',
      default_config: {
        max_rating: 5,
        icon: 'star',
        allow_half: false
      }.to_json
    },
    {
      name: 'currency',
      input_type: 'number',
      data_type: 'decimal',
      default_config: {
        currency: 'JOD',
        decimal_places: 3,
        min: 0,
        format_display: true
      }.to_json
    }
  ].freeze

  def self.seed!
    FIELD_TYPES.each do |field_type_data|
      FieldTypeDefinition.find_or_create_by(name: field_type_data[:name]) do |field_type|
        field_type.assign_attributes(field_type_data)
        field_type.active = true
      end
    end
  end
end
```

---

## Phase 4: Frontend Integration Patterns

### 4.1 API Response Structure (JSON:API Standard)

Following Primero's API patterns for consistency:

```json
{
  "data": {
    "id": "123",
    "type": "case",
    "attributes": {
      "case_number": "CASE-2025-001",
      "status": "in_progress",
      "priority_level": "high",
      "created_at": "2025-01-20T10:00:00Z"
    },
    "relationships": {
      "beneficiary": {
        "data": { "id": "456", "type": "beneficiary" }
      },
      "assigned_user": {
        "data": { "id": "789", "type": "user" }
      },
      "approval_request": {
        "data": { "id": "101", "type": "approval_request" }
      }
    }
  },
  "included": [
    {
      "id": "456",
      "type": "beneficiary",
      "attributes": {
        "name": "محمد بن مسعود",
        "age": 18,
        "gender": "male"
      }
    }
  ],
  "meta": {
    "total_count": 250,
    "current_page": 1,
    "per_page": 25
  }
}
```

### 4.2 Real-time Updates (WebSocket Integration)

```ruby
# Case Channel for real-time updates
class CaseChannel < ApplicationCable::Channel
  def subscribed
    case_id = params[:case_id]
    case_record = Case.find(case_id)

    # Authorize subscription
    return reject unless CasePolicy.new(current_user, case_record).show?

    stream_from "case_#{case_id}"
  end

  def unsubscribed
    # Cleanup when channel is unsubscribed
  end
end

# Broadcast updates when case changes
class Case < ApplicationRecord
  after_update_commit :broadcast_case_update

  private

  def broadcast_case_update
    ActionCable.server.broadcast(
      "case_#{id}",
      {
        type: 'case_updated',
        case: CaseSerializer.new(self).serializable_hash
      }
    )
  end
end
```

---

## Phase 5: Reporting & Analytics System

### 5.1 Comprehensive Dashboard Statistics System

Based on your Arabic dashboard screenshot, here's the complete statistics system:

```ruby
# Dashboard Statistics Service - Matches your Arabic interface
class DashboardStatisticsService
  def initialize(user, date_range = nil)
    @user = user
    @date_range = date_range || (1.year.ago..Date.current)
    @current_month = Date.current.beginning_of_month..Date.current
  end

  # Main dashboard statistics matching your interface
  def dashboard_overview
    {
      # Top row statistics (28, 166, 24)
      total_files: total_files_count,
      total_services: total_services_count,
      total_current_cases: total_current_cases_count,

      # Pie chart data
      shared_cases_chart: shared_cases_statistics,
      group_cases_chart: group_cases_statistics,
      current_cases_chart: current_cases_statistics,

      # Recent observations section
      recent_observations: recent_observations_data,

      # Current cases with others section
      current_cases_with_others: current_cases_with_others_data,

      # Employees section
      employees_data: employees_statistics,

      # Team sharing table
      team_sharing_table: team_sharing_table_data
    }
  end

  # Top statistics cards
  def total_files_count
    @user.accessible_cases.where(created_at: @date_range).count
  end

  def total_services_count
    accessible_services.where(created_at: @date_range).count
  end

  def total_current_cases_count
    @user.accessible_cases.active.count
  end

  # Pie chart statistics - simplified without case sharing
  def shared_cases_statistics
    cases = @user.accessible_cases.where(created_at: @date_range)

    {
      my_cases: cases.where(assigned_user: @user).count,
      supervised_cases: @user.supervisor? ? cases.where.not(assigned_user: @user).count : 0,
      total: cases.count
    }
  end

  def group_cases_statistics
    cases = @user.accessible_cases.where(created_at: @date_range)

    {
      group_cases: cases.where(case_type: ['group', 'community']).count,
      individual_cases: cases.where.not(case_type: ['group', 'community']).count,
      total: cases.count
    }
  end

  def current_cases_statistics
    cases = @user.accessible_cases.active

    {
      high_priority: cases.where(priority_level: ['high', 'urgent']).count,
      medium_priority: cases.where(priority_level: 'medium').count,
      low_priority: cases.where(priority_level: 'low').count,
      total: cases.count
    }
  end

  # Recent observations (الملاحظات الجديدة)
  def recent_observations_data
    observations = accessible_case_notes.where(created_at: @current_month)
                                      .includes(:case, :created_by)
                                      .order(created_at: :desc)
                                      .limit(5)

    observations.map do |note|
      {
        id: note.id,
        case_number: note.case.case_number,
        beneficiary_name: note.case.beneficiary.name,
        note_content: note.content.truncate(100),
        created_by: note.created_by.name,
        created_at: note.created_at,
        assigned_user: note.case.assigned_user.name
      }
    end
  end

  # Current cases with others - simplified without case sharing
  def current_cases_with_others_data
    # For supervisors, show cases they supervise but don't manage
    return [] unless @user.supervisor?

    supervised_cases = Case.joins(:assigned_user)
                          .where(users: { project_id: @user.project_id })
                          .where.not(assigned_user: @user)
                          .includes(:assigned_user)
                          .limit(5)

    supervised_cases.map do |case_obj|
      {
        id: case_obj.id,
        case_number: case_obj.case_number,
        beneficiary_name: case_obj.beneficiary_name,  # From cached data
        assigned_user: case_obj.assigned_user.name,
        priority: case_obj.priority_level,
        status: case_obj.status
      }
    end
  end

  # Employees statistics (الموظفات)
  def employees_statistics
    if @user.supervisor?
      team_members = User.where(project_id: @user.project_id)
                        .where.not(id: @user.id)
    else
      team_members = User.where(id: @user.id)
    end

    {
      total_employees: team_members.count,
      active_users: team_members.joins(:managed_cases)
                                      .where(cases: { status: ['open', 'in_progress'] })
                                      .distinct.count,
      pending_tasks: count_pending_tasks_for_team(team_members)
    }
  end

  # Team sharing table (تم تشاركها مع فريقي)
  def team_sharing_table_data
    return [] unless @user.supervisor?

    team_cases = Case.joins(:assigned_user)
                    .where(users: { project_id: @user.project_id })
                    .where(created_at: @current_month)
                    .includes(:assigned_user)
                    .order(created_at: :desc)
                    .limit(10)

    team_cases.map do |case_obj|
      {
        case_number: case_obj.case_number,
        beneficiary_name: case_obj.beneficiary_name,
        assigned_user: case_obj.assigned_user.name,
        status: case_obj.status,
        priority: case_obj.priority_level,
        created_at: case_obj.created_at,
        last_activity: case_obj.updated_at
      }
    end
  end

  private

  def calculate_average_duration(cases)
    return 0 if cases.empty?

    total_days = cases.sum(&:duration_in_days)
    total_days / cases.count
  end

  def count_overdue_assessments
    # Logic to count overdue assessments based on form completion
    @user.managed_cases.where(status: 'draft')
          .where('created_at < ?', 7.days.ago)
          .select { |case_obj| !case_obj.assessment_complete? }
          .count
  end
  # Helper methods
  private

  def accessible_services
    if @user.global_user?
      Service.all
    else
      Service.joins(:case).where(cases: { project_id: @user.project_id })
    end
  end

  def accessible_case_notes
    if @user.global_user?
      CaseNote.all
    else
      CaseNote.joins(:case).where(cases: { project_id: @user.project_id })
    end
  end

  def count_pending_tasks_for_team(team_members)
    # Count pending approvals, overdue assessments, etc.
    pending_approvals = Case.joins(:assigned_user)
                           .where(users: { id: team_members.pluck(:id) })
                           .approval_workflow_pending
                           .count

    overdue_assessments = count_overdue_assessments_for_team(team_members)

    pending_approvals + overdue_assessments
  end

  def count_overdue_assessments_for_team(team_members)
    # Implementation for counting overdue assessments
    # This would check for cases that need assessment updates
    0
  end
end

# Dashboard Controller
class Api::DashboardController < ApplicationController
  include AtharAuth::ResourceAuthorization

  before_action :authenticate_session!

  # GET /api/dashboard - Main dashboard statistics
  def index
    service = DashboardStatisticsService.new(current_user, date_range_params)
    dashboard_data = service.dashboard_overview

    render json: {
      data: dashboard_data,
      meta: {
        user_role: current_user.role&.name,
        project_id: current_user.project_id,
        generated_at: Time.current
      }
    }
  end

  # GET /api/dashboard/case_metrics - Detailed case metrics
  def case_metrics
    service = DashboardStatisticsService.new(current_user, date_range_params)

    render json: {
      data: {
        total_files: service.total_files_count,
        current_cases: service.total_current_cases_count,
        shared_cases: service.shared_cases_statistics,
        group_cases: service.group_cases_statistics,
        current_cases_breakdown: service.current_cases_statistics
      }
    }
  end

  # GET /api/dashboard/team_data - Team-specific data for supervisors
  def team_data
    unless current_user.supervisor?
      return render json: { error: 'Only supervisors can access team data' }, status: :forbidden
    end

    service = DashboardStatisticsService.new(current_user, date_range_params)

    render json: {
      data: {
        employees: service.employees_statistics,
        team_sharing: service.team_sharing_table_data,
        current_cases_with_others: service.current_cases_with_others_data
      }
    }
  end

  private

  def date_range_params
    return nil unless params[:date_range].present?

    case params[:date_range]
    when 'this_month'
      Date.current.beginning_of_month..Date.current
    when 'last_month'
      1.month.ago.beginning_of_month..1.month.ago.end_of_month
    when 'this_year'
      Date.current.beginning_of_year..Date.current
    when 'custom'
      start_date = Date.parse(params[:start_date]) if params[:start_date]
      end_date = Date.parse(params[:end_date]) if params[:end_date]
      start_date && end_date ? (start_date..end_date) : nil
    else
      nil
    end
  rescue Date::Error
    nil
  end
end
```

### 5.2 Simplified Case Access System

Simple case access based on user roles - no complex sharing needed:

```ruby
# Simple Case Access - No complex assignment system needed
class Case < ApplicationRecord
  belongs_to :assigned_user, class_name: 'User'
  belongs_to :created_by, class_name: 'User'

  # Simple access control based on user roles
  def accessible_by?(user)
    return true if assigned_user == user          # Assigned user has full access
    return true if user.supervisor? && same_project?(user)  # Supervisors can access all cases in their project
    false
  end

  def editable_by?(user)
    return true if assigned_user == user          # Assigned user can edit
    return true if user.supervisor? && same_project?(user)  # Supervisors can edit
    false
  end

  def can_change_status?(user)
    return true if assigned_user == user          # Assigned user can change status
    return true if user.supervisor? && same_project?(user)  # Supervisors can change status
    false
  end

  private

  def same_project?(user)
    project_id == user.project_id
  end
end

# User Access Control - Simple role-based approach
class User < AtharAuth::Models::User
  # Case access methods
  def accessible_cases
    if supervisor?
      # Supervisors can access all cases in their project
      Case.where(project_id: project_id)
    else
      # Users can only access cases assigned to them
      Case.where(assigned_user: self)
    end
  end

  def can_manage_case?(case_obj)
    return false unless case_obj
    return true if case_obj.assigned_user == self
    return true if supervisor? && case_obj.project_id == project_id
    false
  end
  end
end

# Simple Case Access - No sharing controller needed
# Access control handled through User.accessible_cases and Case.accessible_by? methods
        {
          user_id: assignment.assigned_user.id,
          user_name: assignment.assigned_user.name,
          assignment_type: assignment.assignment_type,
          access_level: assignment.access_level,
          assigned_by: assignment.assigned_by.name,
          assigned_at: assignment.assigned_at
        }
      end
    }
  end

  private

  def set_case
    @case = Case.find(params[:case_id])
  end

  def ensure_can_manage_sharing!
    unless @case.accessible_by?(current_user) &&
           @case.access_level_for(current_user) == 'full_access'
      render json: { error: 'Insufficient permissions to manage case sharing' },
             status: :forbidden
    end
  end
end
```

### 5.3 Report Generation System

```ruby
# Report Generator for various report types
class ReportGenerator
  include ActiveModel::Model

  attr_accessor :report_type, :date_range, :filters, :user

  REPORT_TYPES = %w[
    case_summary
    beneficiary_demographics
    service_delivery
    workflow_performance
    user_activity
    custom
  ].freeze

  validates :user, presence: true

  enum report_type: {
    case_summary: 0,
    beneficiary_demographics: 1,
    service_delivery: 2,
    form_completion: 3,
    approval_workflow: 4,
    workflow_performance: 5,
    user_activity: 6,
    custom: 7
  }

  def generate
    return false unless valid?

    case report_type
    when 'case_summary'
      generate_case_summary_report
    when 'beneficiary_demographics'
      generate_demographics_report
    when 'service_delivery'
      generate_service_delivery_report
    when 'workflow_performance'
      generate_workflow_performance_report
    when 'user_activity'
      generate_user_activity_report
    else
      generate_custom_report
    end
  end

  private

  def generate_case_summary_report
    cases = user.accessible_cases.includes(:assigned_user, :approval_request)
    cases = apply_date_filter(cases) if date_range.present?
    cases = apply_filters(cases) if filters.present?

    {
      title: "Case Summary Report",
      generated_at: Time.current,
      date_range: date_range,
      total_cases: cases.count,
      cases_by_status: cases.group(:status).count,
      cases_by_type: cases.group(:case_type).count,
      cases_by_priority: cases.group(:priority_level).count,
      average_duration: calculate_average_duration(cases.closed),
      cases: cases.limit(1000).map(&:case_summary)
    }
  end

  def generate_demographics_report
    beneficiaries = user.accessible_beneficiaries
    beneficiaries = apply_beneficiary_filters(beneficiaries) if filters.present?

    {
      title: "Beneficiary Demographics Report",
      generated_at: Time.current,
      total_beneficiaries: beneficiaries.count,
      age_distribution: {
        minors: beneficiaries.minors.count,
        adults: beneficiaries.adults.count
      },
      gender_distribution: beneficiaries.group(:gender).count,
      nationality_distribution: beneficiaries.group(:nationality).count,
      city_distribution: beneficiaries.group(:city).count
    }
  end
end
```

---

## Phase 6: Integration & Deployment Strategy

### 6.1 Database Migration Strategy

```ruby
# Migration timeline for implementing the new schema
class CreateFormBuilderTables < ActiveRecord::Migration[7.0]
  def change
    # Field Type Definitions - Master table for field types
    create_table :field_type_definitions do |t|
      t.string :name, null: false, index: { unique: true }
      t.string :input_type, null: false
      t.string :data_type, null: false
      t.json :default_config
      t.json :validation_schema
      t.boolean :active, default: true
      t.timestamps
    end

    create_table :form_templates do |t|
      t.string :name, null: false, index: { unique: true }
      t.string :form_type, null: false
      t.text :description
      t.boolean :active, default: true
      t.integer :version, default: 1
      t.json :prerequisite_forms  # Array of form names that must be completed first
      t.references :project, null: true, foreign_key: true
      t.timestamps
    end

    create_table :form_sections do |t|
      t.references :form_template, null: false, foreign_key: true
      t.string :name, null: false
      t.string :title, null: false
      t.text :description
      t.integer :display_order, null: false
      t.boolean :visible, default: true

      # Database-side conditional display columns
      t.integer :display_condition_field_id, null: true # FormField ID this section depends on
      t.string :display_condition_value, null: true     # Required value to show section
      t.string :display_condition_operator, default: '==' # Comparison operator
      t.string :display_condition_user_role, null: true  # Required user role
      t.string :display_condition_project_type, null: true # Required project type

      t.timestamps

      t.index [:form_template_id, :display_order], unique: true
      t.index [:display_condition_field_id]
      t.index [:display_condition_user_role]
      t.index [:display_condition_project_type]
    end

    create_table :form_fields do |t|
      t.references :form_section, null: false, foreign_key: true
      t.string :field_name, null: false
      t.string :field_type, null: false
      t.string :label, null: false
      t.text :help_text
      t.json :options # For select boxes, radio buttons, etc.
      t.json :validations # Field validation rules
      t.integer :display_order, null: false
      t.boolean :required, default: false
      t.boolean :visible, default: true
      t.timestamps

      t.index [:form_section_id, :field_name], unique: true
      t.index [:form_section_id, :display_order], unique: true
    end

    create_table :form_submissions do |t|
      t.references :case, null: false, foreign_key: true
      t.references :form_template, null: false, foreign_key: true
      t.references :created_by, null: false, foreign_key: { to_table: :users }
      t.references :updated_by, null: true, foreign_key: { to_table: :users }
      t.json :form_data, null: false, default: {}
      t.string :status, default: 'draft' # draft, submitted, approved
      t.datetime :submitted_at
      t.datetime :approved_at
      t.timestamps
    end
  end
end

# Migration to eliminate CaseManager model
class EliminateCaseManagerModel < ActiveRecord::Migration[7.0]
  def change
    # Rename case_manager_id to assigned_user_id for clarity
    rename_column :cases, :case_manager_id, :assigned_user_id

    # Drop case_managers table if it exists
    drop_table :case_managers, if_exists: true
  end
end

# Migration to eliminate CaseManager model and rename field for clarity
class EliminateCaseManagerModel < ActiveRecord::Migration[7.0]
  def change
    # Rename case_manager_id to assigned_user_id for semantic clarity
    rename_column :cases, :case_manager_id, :assigned_user_id

    # Drop case_managers table if it exists
    drop_table :case_managers, if_exists: true do |t|
      t.integer :user_id
      t.integer :supervisor_id
      t.integer :project_id
      t.timestamps
    end
  end
end

class AddEnhancedCaseColumns < ActiveRecord::Migration[7.0]
  def change
    # Add enhanced case columns (using built-in approval flow)
    add_column :cases, :case_type, :string, default: 'general'
    add_column :cases, :priority_level, :integer, default: 1  # medium
    add_column :cases, :confidentiality_level, :integer, default: 0  # public
    add_column :cases, :beneficiary_name, :string  # Cached from forms
    add_column :cases, :beneficiary_age, :integer  # Cached from forms
    add_column :cases, :beneficiary_gender, :string  # Cached from forms
    add_column :cases, :beneficiary_nationality, :string  # Cached from forms
    add_column :cases, :beneficiary_phone, :string  # Cached from forms
    add_column :cases, :beneficiary_id_number, :string  # Cached from forms
  end
end

class CreateComments < ActiveRecord::Migration[7.0]
  def change
    create_table :comments do |t|
      t.references :case, null: false, foreign_key: true
      t.references :user, null: false, foreign_key: true
      t.references :commentable, polymorphic: true, null: false
      t.references :parent_comment, foreign_key: { to_table: :comments }, optional: true
      t.text :content, null: false
      t.integer :comment_type, default: 0  # case_comment: 0, field_comment: 1
      t.integer :status, default: 0        # active: 0, resolved: 1, archived: 2
      t.timestamps
    end

    add_index :comments, [:commentable_type, :commentable_id]
    add_index :comments, [:case_id, :comment_type]
    add_index :comments, :parent_comment_id
    add_index :comments, [:case_id, :status]
    add_index :comments, [:created_at]
  end
end
```

### 6.2 Implementation Phases Timeline

```markdown
## Implementation Timeline (12 weeks)

### Week 1-2: Foundation Setup & CaseManager Elimination

- [ ] **CaseManager Model Elimination:**
  - [ ] Create migration to rename `case_manager_id` to `assigned_user_id` in cases table
  - [ ] Drop `case_managers` table completely
  - [ ] Update Case model: change validations, scopes, and methods to use `assigned_user_id`
  - [ ] Update User model: change `managed_cases` foreign_key to `:assigned_user_id`
  - [ ] Remove `app/models/case_manager.rb`
  - [ ] Remove `app/controllers/api/case_managers_controller.rb`
  - [ ] Remove `app/serializers/case_manager_serializer.rb`
  - [ ] Remove case_managers routes from `config/routes.rb`
  - [ ] Update all existing controllers to use `assigned_user_id` parameter
  - [ ] Update all serializers to include `assigned_user` instead of `case_manager`
  - [ ] Run tests and fix any remaining references
- [ ] Create database migrations for form builder system
- [ ] Implement basic FormTemplate, FormSection, FormField models
- [ ] Create seed data for initial form templates
- [ ] Write comprehensive tests for form builder models

### Week 3-4: Workflow Engine

- [ ] Create workflow definition and state models
- [ ] Implement workflow transition logic
- [ ] Add workflow columns to Case model
- [ ] Create workflow management interface

### Week 5-6: Enhanced Case Management

- [ ] Implement FormSubmission model and dynamic form rendering
- [ ] Add two-level comment system (case-level and field-level comments)
- [ ] Implement comment resolution and threading
- [ ] Add real-time comment notifications
- [ ] Implement simple role-based access control

### Week 7-8: API Enhancements

- [ ] Enhance existing API endpoints with new functionality
- [ ] Implement real-time updates via WebSocket
- [ ] Add comprehensive API documentation
- [ ] Implement API versioning strategy

### Week 9-10: Reporting & Analytics

- [ ] Implement dashboard service and metrics
- [ ] Create report generation system
- [ ] Add data export functionality
- [ ] Implement caching for performance

### Week 11-12: Integration & Testing

- [ ] End-to-end testing of complete workflow
- [ ] Performance optimization and caching
- [ ] Security audit and penetration testing
- [ ] Documentation and training materials
```

### 6.3 Deployment Considerations

```yaml
# docker-compose.production.yml additions
version: "3.8"
services:
  case-manager:
    # ... existing configuration ...
    environment:
      - WORKFLOW_ENGINE_ENABLED=true
      - FORM_BUILDER_ENABLED=true
      - REAL_TIME_UPDATES_ENABLED=true
      - REPORT_CACHE_TTL=3600
    volumes:
      - case_documents:/app/storage/case_documents
      - report_cache:/app/tmp/reports

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes

  sidekiq:
    # ... existing configuration ...
    environment:
      - WORKFLOW_PROCESSOR_ENABLED=true
      - REPORT_GENERATOR_ENABLED=true

volumes:
  case_documents:
  report_cache:
  redis_data:
```

---

## Summary & Next Steps

This comprehensive plan transforms your case-manager service into a robust, Primero-inspired case management system while building on your existing AtharAuth integration and solid foundation.

## 6. Permission Definitions & Role-Based Access Control

### 6.1 Role Hierarchy and Responsibilities

The case management system implements a two-tier role hierarchy with clear separation of responsibilities:

```
Supervisor (Level 2)
    ↓ supervises
Case Manager (Level 1)
```

**Note**: Project Managers have no access to the AtharCM system and are not part of the case management workflow.

#### **Case Manager Role**

- **Primary Responsibility**: Direct case management and beneficiary interaction
- **Access Scope**: Own cases + cases shared with them
- **Key Capabilities**: Create, update, and manage assigned cases through completion

#### **Supervisor Role**

- **Primary Responsibility**: Team oversight and case quality assurance + direct case work when needed
- **Access Scope**: All cases within their project + team management
- **Key Capabilities**: Full CRUD on all case management functions, case approval, team performance monitoring, advanced reporting
- **Special Note**: Can operate independently when no case managers are assigned to the project

#### **Project Manager Role**

- **AtharCM Access**: ❌ **NO ACCESS** - Project Managers are completely excluded from the case management system
- **Approval Workflow**: ❌ **NO APPROVAL PERMISSIONS** - Not part of case approval hierarchy
- **HR Management**: ✅ Handled through AtharPeople system (separate from case management)

### 6.2 Detailed Permission Matrix

#### **Case Management Permissions**

| Permission                 | Case Manager | Supervisor   | Description                  |
| -------------------------- | ------------ | ------------ | ---------------------------- |
| `read:case`                | Own + Shared | Project-wide | View case details            |
| `create:case`              | ✅           | ✅           | Create new cases             |
| `update:case`              | Own + Shared | Project-wide | Edit case information        |
| `destroy:case`             | ❌           | ✅           | Delete/archive cases         |
| `submit_for_approval:case` | Own + Shared | ✅           | Submit cases for approval    |
| `approve:case`             | ❌           | ✅           | Approve submitted cases      |
| `reject:case`              | ❌           | ✅           | Reject submitted cases       |
| `assign:case`              | ❌           | ✅           | Assign cases to team members |

#### **Form Management Permissions**

| Permission                         | Case Manager | Supervisor   | Description               |
| ---------------------------------- | ------------ | ------------ | ------------------------- |
| `read:form_template`               | ✅           | ✅           | View form templates       |
| `create:form_template`             | ❌           | ✅           | Create new form templates |
| `update:form_template`             | ❌           | ✅           | Modify form templates     |
| `read:form_submission`             | Own Cases    | Project-wide | View form submissions     |
| `create:form_submission`           | Own Cases    | Project-wide | Create form submissions   |
| `update:form_submission`           | Own Cases    | Project-wide | Edit form submissions     |
| `submit:form_submission`           | Own Cases    | Project-wide | Submit completed forms    |
| `calculate_fields:form_submission` | Own Cases    | Project-wide | Calculate dynamic fields  |

#### **Comment & Collaboration Permissions**

| Permission        | Case Manager | Supervisor   | Description              |
| ----------------- | ------------ | ------------ | ------------------------ |
| `read:comment`    | Own Cases    | Project-wide | View comments            |
| `create:comment`  | Own Cases    | Project-wide | Add comments             |
| `update:comment`  | Own Comments | Project-wide | Edit comments            |
| `destroy:comment` | Own Comments | Project-wide | Delete comments          |
| `resolve:comment` | Own Cases    | Project-wide | Resolve comment threads  |
| `reopen:comment`  | Own Cases    | Project-wide | Reopen resolved comments |

#### **Document Management Permissions**

| Permission               | Case Manager | Supervisor   | Description            |
| ------------------------ | ------------ | ------------ | ---------------------- |
| `read:case_document`     | Own Cases    | Project-wide | View documents         |
| `create:case_document`   | Own Cases    | Project-wide | Upload documents       |
| `update:case_document`   | Own Cases    | Project-wide | Edit document metadata |
| `destroy:case_document`  | Own Cases    | Project-wide | Delete documents       |
| `download:case_document` | Own Cases    | Project-wide | Download documents     |
| `preview:case_document`  | Own Cases    | Project-wide | Preview documents      |

#### **Reporting & Analytics Permissions**

| Permission          | Case Manager  | Supervisor   | Description               |
| ------------------- | ------------- | ------------ | ------------------------- |
| `read:dashboard`    | Basic View    | Team View    | Access dashboard          |
| `read:charts`       | Basic Charts  | Team Charts  | View chart data           |
| `read:reports`      | Basic Reports | Team Reports | Generate reports          |
| `create:reports`    | ❌            | ✅           | Create custom reports     |
| `export:reports`    | ❌            | ✅           | Export reports (CSV/PDF)  |
| `read:team_metrics` | ❌            | ✅           | View team performance     |
| `read:analytics`    | ❌            | ✅           | Access advanced analytics |

#### **HR Data Access Permissions (AtharPeople Integration)**

| Permission            | Case Manager | Supervisor | Description                    |
| --------------------- | ------------ | ---------- | ------------------------------ |
| `read_own:employee`   | ✅           | ✅         | View own employee profile      |
| `update_own:employee` | ✅           | ✅         | Edit own employee information  |
| `read_own:leave`      | ✅           | ✅         | View own leave requests        |
| `manage_own:leave`    | ✅           | ✅         | Create/edit own leave requests |
| `read_own:attendance` | ✅           | ✅         | View own attendance records    |
| `record:attendance`   | ✅           | ✅         | Record own attendance          |

### 6.3 Data Access Patterns

#### **Users with Case Manager Role Access Pattern**

```ruby
# Users with case_manager role can only access:
def accessible_cases
  Case.where(assigned_user: self)
      .or(Case.joins(:case_shares).where(case_shares: { user: self }))
      .where(project_id: project_id)
end

# Form submissions for accessible cases only
def accessible_form_submissions
  FormSubmission.joins(:case).where(cases: { id: accessible_cases.ids })
end
```

#### **Supervisor Access Pattern**

```ruby
# Supervisors can access all cases in their project
def accessible_cases
  Case.where(project_id: project_id)
end

# Can view team member activities
def supervised_users
  User.joins(:role).where(project_id: project_id, roles: { name: 'case_manager' })
end
```

#### **Project Manager Access Pattern**

```ruby
# Project Managers have NO ACCESS to AtharCM system
def accessible_cases
  # NO ACCESS - Project Managers cannot access case management system
  Case.none
end

# HR management handled through AtharPeople system (separate from CM)
# Note: Project managers are not part of the AtharCM system architecture
def hr_access
  # Project Managers use AtharPeople system for HR management
  # This is completely separate from the case management system
end
```

### 6.4 Permission Implementation

#### **Enhanced Permission Seeds**

```ruby
# services/core/db/seeds/03_permissions.rb - Enhanced CM Permissions

cm_permissions = [
  # Case Management - Granular permissions
  { name: "Read Cases", action: "read", subject_class: "Case", system_name: "cm" },
  { name: "Create Cases", action: "create", subject_class: "Case", system_name: "cm" },
  { name: "Update Cases", action: "update", subject_class: "Case", system_name: "cm" },
  { name: "Delete Cases", action: "destroy", subject_class: "Case", system_name: "cm" },
  { name: "Submit Cases for Approval", action: "submit_for_approval", subject_class: "Case", system_name: "cm" },
  { name: "Approve Cases", action: "approve", subject_class: "Case", system_name: "cm" },
  { name: "Reject Cases", action: "reject", subject_class: "Case", system_name: "cm" },
  { name: "Assign Cases", action: "assign", subject_class: "Case", system_name: "cm" },

  # Form Management - Granular permissions
  { name: "Read Form Templates", action: "read", subject_class: "FormTemplate", system_name: "cm" },
  { name: "Create Form Templates", action: "create", subject_class: "FormTemplate", system_name: "cm" },
  { name: "Update Form Templates", action: "update", subject_class: "FormTemplate", system_name: "cm" },
  { name: "Read Form Submissions", action: "read", subject_class: "FormSubmission", system_name: "cm" },
  { name: "Create Form Submissions", action: "create", subject_class: "FormSubmission", system_name: "cm" },
  { name: "Update Form Submissions", action: "update", subject_class: "FormSubmission", system_name: "cm" },
  { name: "Submit Form Submissions", action: "submit", subject_class: "FormSubmission", system_name: "cm" },
  { name: "Calculate Form Fields", action: "calculate_fields", subject_class: "FormSubmission", system_name: "cm" },

  # Comment Management - Granular permissions
  { name: "Read Comments", action: "read", subject_class: "Comment", system_name: "cm" },
  { name: "Create Comments", action: "create", subject_class: "Comment", system_name: "cm" },
  { name: "Update Comments", action: "update", subject_class: "Comment", system_name: "cm" },
  { name: "Delete Comments", action: "destroy", subject_class: "Comment", system_name: "cm" },
  { name: "Resolve Comments", action: "resolve", subject_class: "Comment", system_name: "cm" },
  { name: "Reopen Comments", action: "reopen", subject_class: "Comment", system_name: "cm" },

  # Document Management - Granular permissions
  { name: "Read Case Documents", action: "read", subject_class: "CaseDocument", system_name: "cm" },
  { name: "Create Case Documents", action: "create", subject_class: "CaseDocument", system_name: "cm" },
  { name: "Update Case Documents", action: "update", subject_class: "CaseDocument", system_name: "cm" },
  { name: "Delete Case Documents", action: "destroy", subject_class: "CaseDocument", system_name: "cm" },
  { name: "Download Case Documents", action: "download", subject_class: "CaseDocument", system_name: "cm" },
  { name: "Preview Case Documents", action: "preview", subject_class: "CaseDocument", system_name: "cm" },

  # Reporting & Analytics - Granular permissions
  { name: "Read Dashboard", action: "read", subject_class: "Dashboard", system_name: "cm" },
  { name: "Read Charts", action: "read", subject_class: "Charts", system_name: "cm" },
  { name: "Read Reports", action: "read", subject_class: "Reports", system_name: "cm" },
  { name: "Create Reports", action: "create", subject_class: "Reports", system_name: "cm" },
  { name: "Export Reports", action: "export", subject_class: "Reports", system_name: "cm" },
  { name: "Read Team Metrics", action: "read", subject_class: "TeamMetrics", system_name: "cm" },
  { name: "Read Analytics", action: "read", subject_class: "Analytics", system_name: "cm" },

  # Legacy permissions (maintain backward compatibility)
  { name: "Manage Beneficiaries", action: "manage", subject_class: "Beneficiary", system_name: "cm" },
  { name: "Read Services", action: "read", subject_class: "Service", system_name: "cm" },
  { name: "Create Services", action: "create", subject_class: "Service", system_name: "cm" },
  { name: "Update Services", action: "update", subject_class: "Service", system_name: "cm" }
]
```

#### **Enhanced Role Permission Mapping**

```ruby
# services/core/db/seeds/05_role_permissions.rb - Enhanced CM Role Mapping

role_permission_map = {
  # Case Manager - Limited permissions for own cases + own HR data
  "case_manager" => [
    # Case management - own cases only
    Permission.where(action: %w[read create update submit_for_approval], subject_class: "Case", system_name: "cm"),

    # Form management - own cases only
    Permission.where(action: %w[read], subject_class: "FormTemplate", system_name: "cm"),
    Permission.where(action: %w[read create update submit calculate_fields], subject_class: "FormSubmission", system_name: "cm"),

    # Comments - own cases only
    Permission.where(action: %w[read create update resolve reopen], subject_class: "Comment", system_name: "cm"),

    # Documents - own cases only
    Permission.where(action: %w[read create update download preview], subject_class: "CaseDocument", system_name: "cm"),

    # Basic reporting
    Permission.where(action: %w[read], subject_class: %w[Dashboard Charts], system_name: "cm"),
    Permission.where(action: %w[read], subject_class: "Reports", system_name: "cm"),

    # Own HR data only (AtharPeople integration)
    Permission.where(action: %w[read_own update_own], subject_class: "Employee", system_name: "people"),
    Permission.where(action: %w[read_own manage_own], subject_class: "Leave", system_name: "people"),
    Permission.where(action: %w[read_own record], subject_class: "AttendanceEvent", system_name: "people"),

    # Legacy permissions
    Permission.where(action: %w[manage], subject_class: "Beneficiary", system_name: "cm"),
    Permission.where(action: %w[read create update], subject_class: "Service", system_name: "cm")
  ].flatten,

  # Supervisor - Project-wide permissions + own HR data
  "supervisor" => [
    # Full case management within project
    Permission.where(action: %w[read create update destroy submit_for_approval approve reject assign], subject_class: "Case", system_name: "cm"),

    # Form management - project-wide + template management
    Permission.where(action: %w[read create update], subject_class: "FormTemplate", system_name: "cm"),
    Permission.where(action: %w[read create update submit calculate_fields], subject_class: "FormSubmission", system_name: "cm"),

    # Comments - project-wide
    Permission.where(action: %w[read create update destroy resolve reopen], subject_class: "Comment", system_name: "cm"),

    # Documents - project-wide
    Permission.where(action: %w[read create update destroy download preview], subject_class: "CaseDocument", system_name: "cm"),

    # Advanced reporting and analytics
    Permission.where(action: %w[read], subject_class: %w[Dashboard Charts TeamMetrics Analytics], system_name: "cm"),
    Permission.where(action: %w[read create export], subject_class: "Reports", system_name: "cm"),

    # Own HR data only (AtharPeople integration)
    Permission.where(action: %w[read_own update_own], subject_class: "Employee", system_name: "people"),
    Permission.where(action: %w[read_own manage_own], subject_class: "Leave", system_name: "people"),
    Permission.where(action: %w[read_own record], subject_class: "AttendanceEvent", system_name: "people"),

    # Legacy permissions
    Permission.where(action: %w[manage], subject_class: "Beneficiary", system_name: "cm"),
    Permission.where(action: %w[read create update], subject_class: "Service", system_name: "cm")
  ].flatten

  # Project Manager - NO ACCESS to AtharCM system
  # Note: Project Managers are completely excluded from case management
  # HR management is handled through AtharPeople system separately
}
```

### Key Benefits:

1. **Scalable Architecture**: Modular design supporting multiple case types and workflows
2. **Flexible Form System**: Dynamic forms adaptable to different humanitarian contexts
3. **Comprehensive Workflow**: State-based case progression with proper authorization
4. **Real-time Collaboration**: Live updates and comprehensive case history
5. **Powerful Reporting**: Dashboard metrics and customizable reports
6. **Security-First**: Role-based access control and audit trails
7. **Granular Permissions**: Fine-grained access control following principle of least privilege
8. **Clear Role Separation**: Well-defined responsibilities and access boundaries

### Ready for Implementation:

The plan is designed to be implemented incrementally, allowing you to deliver value at each phase while maintaining system stability.

---

## Updated API Examples

### **Form Structure API with Commons Gem Integration**

```ruby
# Forms Controller with proper filtering and completion tracking
class Api::FormsController < ApplicationController
  include AtharAuth::ResourceAuthorization

  before_action :authenticate_session!

  api! "Get form structure with conditional sections and completion status"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :form_name, String, required: true, desc: "Form template name"
  param :case_id, Integer, desc: "Case ID for completion status and conditional display"
  param "locale", String, desc: "Locale for field labels (en, ar)"
  description "Returns form structure with sections filtered by conditions and completion status"
  returns code: 200, desc: "Form structure with conditional sections"

  def show
    form_template = FormTemplate.find_by!(name: params[:form_name])
    form_submission = find_or_initialize_form_submission(form_template)

    # Get sections filtered by conditions and accessibility
    visible_sections = get_visible_and_accessible_sections(form_template, form_submission)

    render json: {
      data: {
        form_template: {
          id: form_template.id,
          name: form_template.name,
          form_type: form_template.form_type,
          title: form_template.title
        },
        form_submission: {
          id: form_submission.id,
          status: form_submission.status,
          last_updated: form_submission.updated_at
        },
        sections: visible_sections.map do |section|
          serialize_section_with_completion(section, form_submission)
        end,
        overall_completion: calculate_overall_completion(visible_sections, form_submission)
      }
    }
  end

  private

  def find_or_initialize_form_submission(form_template)
    return FormSubmission.new(form_data: {}) unless params[:case_id]

    case_obj = Case.find(params[:case_id])
    authorize!(:read, :case) # Ensure user can access this case

    FormSubmission.find_or_initialize_by(
      case: case_obj,
      form_template: form_template
    ) do |fs|
      fs.form_data = {}
      fs.status = 'draft'
      cf.created_by = current_user
    end
  end

  def get_visible_and_accessible_sections(form_template, form_submission)
    user_context = {
      user_role: current_user.role&.name,
      project_type: current_project&.project_type
    }

    # Database-side filtering with conditions and completion requirements
    form_template.form_sections
                 .includes(:form_fields)
                 .visible_for_context(form_submission.form_data, user_context)
                 .accessible_for_form_submission(form_submission)
                 .order(:display_order)
  end

  def serialize_section_with_completion(section, form_submission)
    completion_status = section.completion_status_for(form_submission)

    {
      id: section.id,
      name: section.name,
      title: section.title,
      description: section.description,
      display_order: section.display_order,
      completion_status: completion_status,
      accessible: completion_status[:is_complete] || section.display_order == 1 ||
                  previous_sections_complete?(section, form_submission),
      fields: section.form_fields.visible.ordered.map do |field|
        serialize_field_with_lookup(field, form_submission)
      end
    }
  end

  def serialize_field_with_lookup(field, form_submission)
    {
      id: field.id,
      field_name: field.field_name,
      label: field.label,
      field_type: field.field_type,
      required: field.required,
      display_order: field.display_order,
      field_config: JSON.parse(field.field_config || '{}'),
      current_value: form_submission.get_field_value(field.id),
      lookup_options: field.formatted_lookup_options(
        current_project: current_project,
        locale: params[:locale] || 'en'
      ),
      conditional_logic: field.conditional_logic.present? ?
                        JSON.parse(field.conditional_logic) : nil
    }
  end

  def previous_sections_complete?(section, form_submission)
    previous_sections = section.form_template.form_sections
                              .where('display_order < ?', section.display_order)

    previous_sections.all? do |prev_section|
      prev_section.completion_status_for(form_submission)[:is_complete]
    end
  end

  def calculate_overall_completion(sections, form_submission)
    return { percentage: 0, completed_sections: 0, total_sections: 0 } if sections.empty?

    completed_count = sections.count do |section|
      section.completion_status_for(form_submission)[:is_complete]
    end

    {
      percentage: (completed_count.to_f / sections.count * 100).round,
      completed_sections: completed_count,
      total_sections: sections.count
    }
  end
end

# Form Sections Controller for management interface
class Api::FormSectionsController < ApplicationController
  include AtharAuth::ResourceAuthorization

  before_action :authenticate_session!
  authorize_resources

  api! "Lists form sections with filtering and pagination"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param_group :pagination_params
  param_group :filter_params
  param_group :sort_params
  param "filter[form_template_id]", Integer, desc: "Filter by form template"
  param "filter[visible]", String, desc: "Filter by visibility (true/false)"
  description "Returns form sections with commons gem filtering. Requires permission: :read, :form_section"
  returns code: 200, desc: "List of form sections"

  def index
    # @form_sections is set by authorize_resources with project scoping
    apply_filters(@form_sections) do |filtered_and_sorted|
      records, meta = paginate(filtered_and_sorted)
      serialize_response(records, meta: meta)
    end
  end

  # ... CRUD methods for management interface ...
end
```

### **Form Data API**

```ruby
# POST /api/cases/:case_id/forms - Save form data using FormField IDs
def create
  form_submission = FormSubmission.find_or_initialize_by(
    case: @case,
    form_template: FormTemplate.find(params[:form_template_id])
  )

  # Update form data using FormField IDs as keys
  params[:form_data].each do |field_id, field_value|
    form_submission.set_field_value(field_id, field_value, current_user)
  end

  form_submission.status = params[:status] || 'draft'
  form_submission.save!

  # Validate and calculate dependent fields
  form_submission.validate_form_data
  form_submission.calculate_dependent_fields

  render json: {
    data: {
      form_submission_id: form_submission.id,
      organized_data: form_submission.organized_form_data
    },
    message: 'Form saved successfully'
  }
end

# Example request body:
{
  "form_template_id": 1,
  "status": "in_progress",
  "form_data": {
    "123": "أحمد محمد علي",           // FormField ID 123 = interviewer_name
    "124": "2024-01-15",             // FormField ID 124 = interview_date
    "125": "social_worker",          // FormField ID 125 = interviewer_position
    "126": "save_the_children",      // FormField ID 126 = interviewer_agency
    "127": "مخيم الزعتري، الأردن"     // FormField ID 127 = interview_address
  }
}

# Example response:
{
  "data": {
    "form_submission_id": 456,
    "organized_data": {
      "interview_details": {
        "section_info": {
          "id": 10,
          "name": "interview_details",
          "title": "تفاصيل المقابلة"
        },
        "fields": {
          "interviewer_name": {
            "field_info": {
              "id": 123,
              "field_name": "interviewer_name",
              "label": "اسم الشخص الذي يجري المقابلة",
              "field_type": "text"
            },
            "value": "أحمد محمد علي",
            "metadata": {
              "updated_at": "2024-01-15T10:30:00Z",
              "updated_by": 789
            }
          }
        }
      }
    }
  },
  "message": "Form saved successfully"
}
```

### **Dedicated Lookup Controllers with CRUD**

```ruby
# Staff Positions Controller - Serves both dropdown data AND management
class Api::StaffPositionsController < ApplicationController
  include AtharAuth::ResourceAuthorization

  before_action :authenticate_session!

  # Use AtharAuth's authorize_resources for automatic authorization and project scoping
  authorize_resources

  api! "Lists all staff positions"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param "filter[department]", String, desc: "Filter by department"
  description "Returns staff positions for current project. Requires permission: :read, :staff_position"
  returns code: 200, desc: "List of staff positions"

  def index
    # @staff_positions is automatically set by authorize_resources with project scoping
    positions = @staff_positions.by_department(params.dig(:filter, :department)) if params.dig(:filter, :department)
    positions ||= @staff_positions

    render json: {
      data: positions.ordered.map(&:api_representation),
      meta: { total: positions.count }
    }
  end

  api! "Retrieves a specific staff position"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :id, Integer, required: true, desc: "ID of the staff position"
  returns code: 200, desc: "Staff position details"

  def show
    # @staff_position is automatically set by authorize_resources
    render json: { data: @staff_position.api_representation }
  end

  api! "Creates a new staff position"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :staff_position, Hash, required: true do
    param :name, String, required: true, desc: "Position name in English"
    param :name_ar, String, required: true, desc: "Position name in Arabic"
    param :code, String, required: true, desc: "Unique position code"
    param :department, String, required: true, desc: "Department name"
    param :display_order, Integer, desc: "Display order"
  end
  returns code: 201, desc: "Staff position created successfully"

  def create
    # Authorization is handled by authorize_resources
    @staff_position = StaffPosition.new(staff_position_params)
    @staff_position.project_id = current_project&.id if current_project

    if @staff_position.save
      render json: {
        data: @staff_position.api_representation,
        message: 'Staff position created successfully'
      }, status: :created
    else
      serialize_errors(@staff_position.errors)
    end
  end

  api! "Updates a staff position"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :id, Integer, required: true, desc: "ID of the staff position"
  param :staff_position, Hash, required: true do
    param :name, String, desc: "Position name in English"
    param :name_ar, String, desc: "Position name in Arabic"
    param :code, String, desc: "Unique position code"
    param :department, String, desc: "Department name"
    param :display_order, Integer, desc: "Display order"
  end
  returns code: 200, desc: "Staff position updated successfully"

  def update
    # @staff_position and authorization handled by authorize_resources
    if @staff_position.update(staff_position_params)
      render json: {
        data: @staff_position.reload.api_representation,
        message: 'Staff position updated successfully'
      }
    else
      serialize_errors(@staff_position.errors)
    end
  end

  api! "Deletes a staff position"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :id, Integer, required: true, desc: "ID of the staff position"
  returns code: 204, desc: "Staff position deactivated successfully"

  def destroy
    # @staff_position and authorization handled by authorize_resources
    @staff_position.update!(active: false)
    head :no_content
  end

  private

  def staff_position_params
    params.require(:staff_position).permit(:name, :name_ar, :code, :department, :display_order)
  end
end

# Partner Agencies Controller
class Api::PartnerAgenciesController < ApplicationController
  include AtharAuth::ResourceAuthorization

  before_action :authenticate_session!

  # Use AtharAuth's authorize_resources for automatic authorization and project scoping
  authorize_resources

  api! "Lists all partner agencies"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param "filter[agency_type]", String, desc: "Filter by agency type (ngo, ingo, government, un_agency, local_org)"
  description "Returns partner agencies for current project. Requires permission: :read, :partner_agency"
  returns code: 200, desc: "List of partner agencies"

  def index
    # @partner_agencies is automatically set by authorize_resources with project scoping
    agencies = @partner_agencies.by_type(params.dig(:filter, :agency_type)) if params.dig(:filter, :agency_type)
    agencies ||= @partner_agencies

    render json: {
      data: agencies.ordered.map(&:api_representation),
      meta: { total: agencies.count }
    }
  end

  api! "Retrieves a specific partner agency"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :id, Integer, required: true, desc: "ID of the partner agency"
  returns code: 200, desc: "Partner agency details"

  def show
    # @partner_agency is automatically set by authorize_resources
    render json: { data: @partner_agency.api_representation }
  end

  api! "Creates a new partner agency"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :partner_agency, Hash, required: true do
    param :name, String, required: true, desc: "Agency name in English"
    param :name_ar, String, required: true, desc: "Agency name in Arabic"
    param :agency_type, String, required: true, desc: "Type (ngo, ingo, government, un_agency, local_org)"
    param :contact_email, String, desc: "Contact email address"
    param :display_order, Integer, desc: "Display order"
  end
  returns code: 201, desc: "Partner agency created successfully"

  def create
    # Authorization is handled by authorize_resources
    @partner_agency = PartnerAgency.new(partner_agency_params)
    @partner_agency.project_id = current_project&.id if current_project

    if @partner_agency.save
      render json: {
        data: @partner_agency.api_representation,
        message: 'Partner agency created successfully'
      }, status: :created
    else
      serialize_errors(@partner_agency.errors)
    end
  end

  api! "Updates a partner agency"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :id, Integer, required: true, desc: "ID of the partner agency"
  param :partner_agency, Hash, required: true do
    param :name, String, desc: "Agency name in English"
    param :name_ar, String, desc: "Agency name in Arabic"
    param :agency_type, String, desc: "Type (ngo, ingo, government, un_agency, local_org)"
    param :contact_email, String, desc: "Contact email address"
    param :display_order, Integer, desc: "Display order"
  end
  returns code: 200, desc: "Partner agency updated successfully"

  def update
    # @partner_agency and authorization handled by authorize_resources
    if @partner_agency.update(partner_agency_params)
      render json: {
        data: @partner_agency.reload.api_representation,
        message: 'Partner agency updated successfully'
      }
    else
      serialize_errors(@partner_agency.errors)
    end
  end

  api! "Deletes a partner agency"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :id, Integer, required: true, desc: "ID of the partner agency"
  returns code: 204, desc: "Partner agency deactivated successfully"

  def destroy
    # @partner_agency and authorization handled by authorize_resources
    @partner_agency.update!(active: false)
    head :no_content
  end

  private

  def partner_agency_params
    params.require(:partner_agency).permit(:name, :name_ar, :agency_type, :contact_email, :display_order)
  end
end

# Geographic Locations Controller (Hierarchical)
class Api::GeographicLocationsController < ApplicationController
  include AtharAuth::ResourceAuthorization

  before_action :authenticate_session!

  # Use AtharAuth's authorize_resources for automatic authorization and project scoping
  authorize_resources

  api! "Lists geographic locations"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param "filter[location_type]", String, desc: "Filter by type (country, governorate, district, city, neighborhood)"
  param "filter[parent_id]", Integer, desc: "Filter by parent location ID"
  description "Returns geographic locations for current project. Requires permission: :read, :geographic_location"
  returns code: 200, desc: "List of geographic locations"

  def index
    # @geographic_locations is automatically set by authorize_resources with project scoping
    locations = @geographic_locations

    # Apply filters
    locations = locations.where(location_type: params.dig(:filter, :location_type)) if params.dig(:filter, :location_type)
    locations = locations.children_of(params.dig(:filter, :parent_id)) if params.dig(:filter, :parent_id)

    render json: {
      data: locations.ordered.map(&:api_representation),
      meta: { total: locations.count }
    }
  end

  api! "Retrieves a specific geographic location"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :id, Integer, required: true, desc: "ID of the geographic location"
  returns code: 200, desc: "Geographic location details"

  def show
    # @geographic_location is automatically set by authorize_resources
    render json: { data: @geographic_location.api_representation }
  end

  api! "Get child locations of a geographic location"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :id, Integer, required: true, desc: "ID of the parent geographic location"
  description "Returns child locations (e.g., cities within a governorate)"
  returns code: 200, desc: "List of child locations"

  def children
    # @geographic_location is automatically set by authorize_resources
    children = @geographic_location.child_locations.active.ordered

    render json: {
      data: children.map(&:api_representation),
      meta: {
        parent: @geographic_location.api_representation,
        total: children.count
      }
    }
  end

  api! "Creates a new geographic location"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :geographic_location, Hash, required: true do
    param :name, String, required: true, desc: "Location name in English"
    param :name_ar, String, required: true, desc: "Location name in Arabic"
    param :location_type, String, required: true, desc: "Type (country, governorate, district, city, neighborhood)"
    param :parent_location_id, Integer, desc: "Parent location ID"
    param :iso_code, String, desc: "ISO code (required for countries)"
    param :display_order, Integer, desc: "Display order"
  end
  returns code: 201, desc: "Geographic location created successfully"

  def create
    # Authorization is handled by authorize_resources
    @geographic_location = GeographicLocation.new(geographic_location_params)
    @geographic_location.project_id = current_project&.id if current_project

    if @geographic_location.save
      render json: {
        data: @geographic_location.api_representation,
        message: 'Geographic location created successfully'
      }, status: :created
    else
      serialize_errors(@geographic_location.errors)
    end
  end

  api! "Updates a geographic location"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :id, Integer, required: true, desc: "ID of the geographic location"
  param :geographic_location, Hash, required: true do
    param :name, String, desc: "Location name in English"
    param :name_ar, String, desc: "Location name in Arabic"
    param :location_type, String, desc: "Type (country, governorate, district, city, neighborhood)"
    param :parent_location_id, Integer, desc: "Parent location ID"
    param :iso_code, String, desc: "ISO code (required for countries)"
    param :display_order, Integer, desc: "Display order"
  end
  returns code: 200, desc: "Geographic location updated successfully"

  def update
    # @geographic_location and authorization handled by authorize_resources
    if @geographic_location.update(geographic_location_params)
      render json: {
        data: @geographic_location.reload.api_representation,
        message: 'Geographic location updated successfully'
      }
    else
      serialize_errors(@geographic_location.errors)
    end
  end

  api! "Deletes a geographic location"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :id, Integer, required: true, desc: "ID of the geographic location"
  returns code: 204, desc: "Geographic location deactivated successfully"

  def destroy
    # @geographic_location and authorization handled by authorize_resources
    @geographic_location.update!(active: false)
    head :no_content
  end

  private

  def geographic_location_params
    params.require(:geographic_location).permit(
      :name, :name_ar, :location_type, :parent_location_id, :iso_code, :display_order
    )
  end
end

# Routes
Rails.application.routes.draw do
  namespace :api do
    namespace :v1 do
      # Field calculation endpoints - using standard :id parameter
      resources :fields, only: [] do
        member do
          post :calculate
        end
      end

      resources :forms, only: [] do
        member do
          post :calculate_all
        end
      end

      # Case management with progressive form completion
      resources :cases do
        member do
          post :submit_for_approval  # Submit case for approval after forms complete
          get :progress             # Get form completion progress
        end

        # Case-level comments
        resources :comments, controller: 'case_comments', except: [:index] do
          member do
            patch :resolve  # Resolve a comment
          end
        end

        # Get all case comments
        get :comments, to: 'case_comments#index'
      end

      # Field-level comments
      resources :form_fields, only: [] do
        resources :comments, controller: 'field_comments', only: [:index, :create] do
          member do
            patch :resolve  # Resolve a field comment
          end
        end
      end

      resources :form_submissions
      resources :form_templates
    end

    # Lookup data resources
    resources :staff_positions
    resources :partner_agencies
    resources :geographic_locations do
      member do
        get :children
      end
    end
    resources :case_types
    resources :service_categories
    resources :referral_sources

    # Approval and dashboard endpoints
    resources :case_approvals, only: [:index, :update]
    resource :dashboard, only: [:show] do
      get :case_metrics
      get :team_metrics
    end
  end
end
```

Would you like me to proceed with implementing any specific phase, or would you prefer to discuss any particular aspect in more detail?
