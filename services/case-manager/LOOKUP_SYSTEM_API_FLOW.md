# Case Management System API Flow

## Overview

This document explains the complete high-level API flow for the Athar Case Management system, from Cases page navigation through the entire case lifecycle including forms, documents, services, and comments.

## 1. Cases Page Load

**User Action**: Navigate to Cases page (الحالات)

**API Call**:

```
GET /api/cases
```

**Purpose**: Load list of existing cases with basic information, filtering, sorting, and pagination

---

## 2. Create New Case - Initial Setup

**User Action**: Click "إنشاء حالة جديدة" (Create New Case) button

### 2.1 Load Form Templates and Sequence

```
GET /api/form_templates
GET /api/form_templates/sequence
```

### 2.2 Load Lookup Data (Parallel calls)

```
GET /api/case_types
GET /api/staff_positions
GET /api/geographic_locations
GET /api/referral_sources
GET /api/partner_agencies
```

**Purpose**: Get form sequence, prerequisites, and populate all dropdown options

---

## 3. Form Field Interactions

### 3.1 Filter Staff by Department

**User Action**: Select specific department filter

**API Call**:

```
GET /api/staff_positions?department=Protection
```

### 3.2 Search Geographic Locations

**User Action**: Type in location search box

**API Call**:

```
GET /api/geographic_locations?search=عمان
```

### 3.3 Hierarchical Location Selection

**User Action**: Select country → governorate → city

**API Calls**:

```
GET /api/geographic_locations?location_type=country
GET /api/geographic_locations?location_type=governorate&parent_id=1
GET /api/geographic_locations?location_type=city&parent_id=5
```

---

## 4. Case Creation

**User Action**: Fill form and click "حفظ" (Save)

**API Call**:

```
POST /api/cases
```

**Request Body**:

```json
{
  "case": {
    "case_number": "CASE-2025-001",
    "case_type_id": 1,
    "assigned_staff_id": 2,
    "location_id": 15,
    "referral_source_id": 3,
    "beneficiary_data": {
      "name": "أحمد محمد",
      "age": 12
    }
  }
}
```

**Purpose**: Create new case with selected lookup values stored as IDs

---

## 5. Case Details View

**User Action**: After successful creation, redirect to case details

**API Call**:

```
GET /api/cases/123
```

**Purpose**: Load complete case information with lookup relationships resolved

---

## 6. Progressive Form Completion

**User Action**: Start filling required forms for the case

### 6.1 Load Form Template for Case

```
GET /api/form_templates/{template_id}
GET /api/form_templates/{template_id}/render_data
```

### 6.2 Create Form Submission

```
POST /api/form_submissions
```

**Request Body**:

```json
{
  "form_submission": {
    "case_id": "123",
    "form_template_id": 1,
    "form_data": {},
    "status": "form_draft"
  }
}
```

### 6.3 Save Form Progress

```
PATCH /api/form_submissions/{submission_id}
```

### 6.4 Calculate Field Values (if needed)

```
POST /api/form_submissions/{submission_id}/calculate_fields
```

### 6.5 Submit Completed Form

```
POST /api/form_submissions/{submission_id}/submit
```

---

## 7. Case Documents Management

### 7.1 View Case Documents

```
GET /api/case_documents?case_id=123
```

### 7.2 Upload Document

```
POST /api/case_documents
```

**Request Body** (multipart/form-data):

```json
{
  "case_document": {
    "case_id": "123",
    "file": "[FILE_UPLOAD]",
    "document_type": "assessment",
    "description": "Initial assessment document"
  }
}
```

### 7.3 Download Document

```
GET /api/case_documents/{document_id}/download
```

---

## 8. Comments and Collaboration

### 8.1 View Case Comments

```
GET /api/cases/123/comments
```

### 8.2 Add Case Comment

```
POST /api/cases/123/comments
```

**Request Body**:

```json
{
  "comment": {
    "content": "Case requires urgent attention",
    "comment_type": "general"
  }
}
```

### 8.3 Resolve Comment

```
PATCH /api/cases/123/comments/{comment_id}/resolve
```

---

## 9. Service Planning and Management

### 9.1 Load Service Options

```
GET /api/service_categories
GET /api/services
GET /api/partner_agencies?agency_type=ngo
```

### 9.2 Create Service Plan

```
POST /api/cases/123/service_plans
```

**Request Body**:

```json
{
  "service_plan": {
    "planned_service_categories": [1, 3, 5],
    "service_providers": [2, 4],
    "target_completion_date": "2025-06-01"
  }
}
```

---

## 10. Case Approval Workflow

### 10.1 Check Case Progress

```
GET /api/cases/123/progress
```

### 10.2 Submit Case for Approval

```
POST /api/cases/123/submit_for_approval
```

### 10.3 View Approval Requests (for supervisors)

```
GET /api/case_approvals
```

### 10.4 Approve/Reject Case

```
PATCH /api/case_approvals/{approval_id}
```

**Request Body**:

```json
{
  "case_approval": {
    "status": "approved",
    "comments": "All forms completed satisfactorily"
  }
}
```

---

## 11. Dashboard and Reporting

### 11.1 Dashboard Overview

```
GET /api/dashboard
```

### 11.2 Case Metrics

```
GET /api/dashboard/case_metrics
```

### 11.3 Team Performance

```
GET /api/dashboard/team_metrics
```

### 11.4 Charts Data

```
GET /api/charts/pie
GET /api/charts/line
GET /api/charts/bar
GET /api/charts/widgets
```

---

## 12. Case Updates and Status Changes

### 12.1 Update Case Information

```
PATCH /api/cases/123
```

**Request Body**:

```json
{
  "case": {
    "priority_level": 4,
    "assigned_staff_id": 5,
    "status": "in_progress"
  }
}
```

### 12.2 Close Case

```
PATCH /api/cases/123
```

**Request Body**:

```json
{
  "case": {
    "status": "closed",
    "closure_reason": "Services completed successfully"
  }
}
```

---

## Key API Endpoints Summary

### Core Case Management

| Endpoint                              | Method | Purpose               |
| ------------------------------------- | ------ | --------------------- |
| `/api/cases`                          | GET    | List all cases        |
| `/api/cases`                          | POST   | Create new case       |
| `/api/cases/{id}`                     | GET    | Get case details      |
| `/api/cases/{id}`                     | PATCH  | Update case           |
| `/api/cases/{id}/progress`            | GET    | Check form completion |
| `/api/cases/{id}/submit_for_approval` | POST   | Submit for approval   |

### Forms and Submissions

| Endpoint                                      | Method | Purpose                |
| --------------------------------------------- | ------ | ---------------------- |
| `/api/form_templates`                         | GET    | List form templates    |
| `/api/form_templates/{id}/render_data`        | GET    | Get form with data     |
| `/api/form_submissions`                       | POST   | Create form submission |
| `/api/form_submissions/{id}`                  | PATCH  | Update form data       |
| `/api/form_submissions/{id}/submit`           | POST   | Submit completed form  |
| `/api/form_submissions/{id}/calculate_fields` | POST   | Calculate field values |

### Documents and Files

| Endpoint                            | Method | Purpose             |
| ----------------------------------- | ------ | ------------------- |
| `/api/case_documents`               | GET    | List case documents |
| `/api/case_documents`               | POST   | Upload document     |
| `/api/case_documents/{id}/download` | GET    | Download document   |

### Comments and Collaboration

| Endpoint                                | Method | Purpose           |
| --------------------------------------- | ------ | ----------------- |
| `/api/cases/{id}/comments`              | GET    | Get case comments |
| `/api/cases/{id}/comments`              | POST   | Add case comment  |
| `/api/cases/{id}/comments/{id}/resolve` | PATCH  | Resolve comment   |

---

## Key Lookup Endpoints

| Endpoint                    | Purpose               | Filters Available                      |
| --------------------------- | --------------------- | -------------------------------------- |
| `/api/case_types`           | Case categorization   | `category`                             |
| `/api/staff_positions`      | Staff assignment      | `department`, `search`                 |
| `/api/geographic_locations` | Location selection    | `location_type`, `parent_id`, `search` |
| `/api/referral_sources`     | How case was referred | `source_type`, `search`                |
| `/api/partner_agencies`     | Partner organizations | `agency_type`, `search`                |
| `/api/service_categories`   | Service types         | `sector`, `target_group`, `search`     |

---

## Response Format

All lookup endpoints return consistent format:

```json
{
  "data": [
    {
      "id": 1,
      "name": "Case Manager",
      "name_ar": "مدير الحالة",
      "active": true,
      "display_order": 1,
      "project_id": 24
    }
  ],
  "meta": {
    "total": 15,
    "search": null
  }
}
```

---

## Project Scoping

- All lookup APIs automatically filter by user's accessible projects
- Returns global lookups (project_id = null) + project-specific lookups
- User with project 24 access sees: global + project 24 data only

---

## Complete Case Management Flow

```
1. Cases Page Load
   GET /api/cases
   ↓
2. Create New Case
   GET /api/form_templates/sequence
   GET /api/case_types, /api/staff_positions, etc. (Parallel)
   ↓
3. Case Creation
   POST /api/cases
   ↓
4. Progressive Form Completion
   GET /api/form_templates/{id}/render_data
   POST /api/form_submissions
   PATCH /api/form_submissions/{id} (Save progress)
   POST /api/form_submissions/{id}/calculate_fields (If needed)
   POST /api/form_submissions/{id}/submit
   ↓
5. Document Management
   GET /api/case_documents?case_id=123
   POST /api/case_documents (Upload)
   ↓
6. Service Planning
   GET /api/service_categories, /api/services
   POST /api/cases/{id}/service_plans
   ↓
7. Comments & Collaboration
   GET /api/cases/{id}/comments
   POST /api/cases/{id}/comments
   ↓
8. Case Approval
   GET /api/cases/{id}/progress
   POST /api/cases/{id}/submit_for_approval
   GET /api/case_approvals (Supervisor)
   PATCH /api/case_approvals/{id} (Approve/Reject)
   ↓
9. Case Updates & Closure
   PATCH /api/cases/{id} (Update status/info)
   ↓
10. Dashboard & Reporting
    GET /api/dashboard
    GET /api/charts/pie, /api/charts/line, etc.
```

## Key Integration Points

- **Lookup System**: Provides consistent dropdown data across all forms
- **Form System**: Progressive completion with validation and calculations
- **Document System**: File uploads and downloads linked to cases
- **Comment System**: Collaboration and communication on cases
- **Approval System**: Workflow management and quality control
- **Dashboard System**: Metrics and reporting for management oversight
