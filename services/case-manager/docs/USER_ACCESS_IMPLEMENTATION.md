# Case Model User Access Implementation

## Problem Statement

The Case model had `assigned_user_id` and `created_by_id` fields that reference users, but couldn't use standard Rails associations because:

1. **User is not an ActiveRecord model** - User is an `ActiveStruct::Base` from commons gem
2. **Users come from Core service via gRPC** - No local database table
3. **Inconsistent field types** - `assigned_user_id` (integer) vs `created_by_id` (uuid)
4. **No access to user data** - Only had TODO methods returning user IDs

## Solution: ActiveRpc Integration

Following the **Employee model pattern**, we implemented ActiveRpc to establish user relationships.

### Files Created/Modified

1. **`app/models/concerns/case/active_rpc_concern.rb`** - New concern with ActiveRpc configuration
2. **`app/models/case.rb`** - Updated to include ActiveRpc and the concern
3. **`spec/models/concerns/case/active_rpc_concern_spec.rb`** - Test coverage

### Implementation Details

#### 1. ActiveRpc Configuration

```ruby
# Two separate ActiveRpc configurations for dual user relationships
active_rpc :core, :User, foreign_key: :assigned_user_id, method_name: :assigned_user_data do
  attribute :name, :string, default: "Unknown User"
  attribute :email, :string, default: "<EMAIL>"
  attribute :user_roles_list, :user_role_collection, default: []
  attribute :avatar_attributes, :avatar_attributes_type, default: {}
  attribute :status, :string, default: "active"
end

active_rpc :core, :User, foreign_key: :created_by_id, method_name: :created_by_data do
  # Same attributes configuration
end
```

#### 2. Generated Methods

**Assigned User Methods:**
- `assigned_user_name` - User's name from gRPC
- `assigned_user_email` - User's email from gRPC  
- `assigned_user` - Full user object
- `assigned_user_data` - Raw gRPC response
- `assigned_user_active?` - Check if user is active
- `reload_assigned_user` - Force reload from gRPC

**Created By User Methods:**
- `created_by_name` - User's name from gRPC
- `created_by_email` - User's email from gRPC
- `created_by_user` - Full user object
- `created_by_data` - Raw gRPC response
- `created_by_user_active?` - Check if user is active
- `reload_created_by` - Force reload from gRPC

#### 3. Filtering Scopes

```ruby
# Filter cases by assigned user attributes
Case.assigned_user_name_cont('John')
Case.assigned_user_email_eq('<EMAIL>')

# Filter cases by created by user attributes  
Case.created_by_name_cont('Jane')
Case.created_by_email_eq('<EMAIL>')

# Bulk load user data efficiently
Case.with_user_data.limit(10)
```

#### 4. Ransack Integration

```ruby
# Search cases by user attributes
Case.ransack(assigned_user_name_cont: 'John').result
Case.ransack(created_by_email_eq: '<EMAIL>').result
```

### Usage Examples

```ruby
# Basic usage
case = Case.find(1)
puts "Case #{case.case_number} assigned to: #{case.assigned_user_name}"
puts "Created by: #{case.created_by_name} (#{case.created_by_email})"

# Check user status
if case.assigned_user_active?
  puts "Assigned user is active"
end

# Access full user data
user = case.assigned_user
puts "User roles: #{user.user_roles_list}" if user

# Force reload user data
case.reload_assigned_user
case.reload_created_by
```

### Key Benefits

1. **✅ Real user data** - Fetches actual names, emails, roles from Core service
2. **✅ Automatic caching** - ActiveRpc handles caching and lazy loading
3. **✅ Filtering support** - Can filter cases by user attributes
4. **✅ Ransack integration** - Complex search queries supported
5. **✅ Consistent pattern** - Follows Employee model approach
6. **✅ No schema changes** - Uses existing foreign key fields
7. **✅ Graceful fallbacks** - Handles gRPC failures gracefully
8. **✅ Type compatibility** - Handles both integer and uuid foreign keys

### Technical Notes

- **Multiple ActiveRpc configs** - Uses `method_name` option to avoid conflicts
- **Foreign key handling** - ActiveRpc automatically formats IDs for gRPC calls
- **Error handling** - Rescue blocks provide fallbacks when gRPC fails
- **Performance** - Lazy loading and caching minimize gRPC calls
- **Bulk loading** - `with_user_data` scope for efficient batch operations

### Testing

Run the test suite:
```bash
bundle exec rspec spec/models/concerns/case/active_rpc_concern_spec.rb
```

### Migration Path

1. **Before**: `case.assigned_user_name` returned `"User #123"`
2. **After**: `case.assigned_user_name` returns `"John Doe"` (from gRPC)

The implementation is backward compatible - existing code continues to work but now gets real user data instead of placeholder text.
