# syntax=docker.io/docker/dockerfile:1

# -----------------------------
# Base image
FROM node:22.15.0-alpine3.21 AS base
WORKDIR /app
ENV NEXT_TELEMETRY_DISABLED=1

# -----------------------------
# Dependencies stage
FROM base AS deps

# Add compatibility libs and CA certs
RUN apk add --no-cache libc6-compat ca-certificates curl

# Enable corepack and set up Yarn 4.9.1
RUN corepack enable && corepack prepare yarn@4.9.1 --activate

# Copy yarn files
COPY package.json yarn.lock .yarnrc.yml ./

# Check yarn version
RUN yarn -v

# Install all dependencies (including devDependencies)
RUN yarn install --immutable

# -----------------------------
# Builder stage
FROM base AS builder

# Enable corepack and set up Yarn 4.9.1 (same as deps stage)
RUN corepack enable && corepack prepare yarn@4.9.1 --activate

# Inherit installed node_modules from deps
COPY --from=deps /app/node_modules ./node_modules
COPY --from=deps /app/yarn.lock ./yarn.lock
COPY --from=deps /app/.yarnrc.yml ./.yarnrc.yml

# Copy full source
COPY . .

# Add CA certs and fonts fix again (builder needs HTTPS access)
RUN apk add --no-cache ca-certificates curl

# Set environment variables
ENV NODE_ENV=production
ENV NODE_PATH=./src
ENV NEXT_PUBLIC_API_URL=http://localhost:5000
ENV NEXT_PUBLIC_TIMEZONE=Asia/Amman

# Use docker-specific .env file
COPY .env.production .env

# ⚠️ Optional: Skip TLS errors (CI only - not for real prod)
# ENV NODE_TLS_REJECT_UNAUTHORIZED=0

# Build the Next.js application
RUN NODE_OPTIONS="--max-old-space-size=4096" yarn build

# -----------------------------
# Final runtime image
FROM base AS runner

# Add CA certs again (some fonts or APIs might still hit HTTPS)
RUN apk add --no-cache ca-certificates

# Create a secure non-root user
RUN addgroup --system --gid 1001 nodejs && \
    adduser --system --uid 1001 nextjs

# Copy runtime artifacts only
COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# Use non-root user
USER nextjs

# Runtime ENV
ENV NODE_ENV=production
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# Expose port
EXPOSE 3000

# Start app
CMD ["node", "server.js"]
