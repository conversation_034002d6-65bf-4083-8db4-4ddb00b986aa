import useSWR from "swr";
import { AppointmentData } from "../types/appointment-chart";
import { fetchYearlyData } from "./api/appointments";
export function useAppointmentsData(year: string) {
  const { data, error } = useSWR<AppointmentData[]>(
    `/appointment?_year=${year}`,
    fetchYearlyData,
  );

  return {
    appointmentsData: data,
    isError: error,
    isLoading: !error && !data,
  };
}
