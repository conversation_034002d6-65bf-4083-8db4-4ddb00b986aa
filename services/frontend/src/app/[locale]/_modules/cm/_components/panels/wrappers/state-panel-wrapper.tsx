"use client";
import { useEffect, useState } from "react";
import { StatItem } from "../../../types/panels-state";
import StatsPanel from "../state-panel";

const Panels = () => {
  const [loading, setLoading] = useState<boolean>(true);
  const [stats, setStats] = useState<StatItem[]>([]);

  useEffect(() => {
    const simulateApiCall = () => {
      setTimeout(() => {
        setStats([
          { value: 23, label: "تقرير" },
          { value: 10, label: "إيواء" },
          { value: 9, label: "خطة الحالة" },
        ]);
        setLoading(false);
      }, 500);
    };

    simulateApiCall();
  }, []);

  if (loading) {
    return (
      <div className=" bg-gray-100 p-8 flex items-center justify-center">
        <div className="text-gray-600">Loading...</div>
      </div>
    );
  }

  return (
    <>
      <StatsPanel title={"المواصفات"} stats={stats} />
    </>
  );
};

export default Panels;
