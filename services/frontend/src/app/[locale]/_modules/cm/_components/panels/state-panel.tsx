import React from "react";
import { StatItem, StatsPanelProps } from "../../types/panels-state";

const StatItemComponent: React.FC<StatItem> = ({ value, label }) => (
  <div className="flex flex-col items-start px-3 py-5 bg-neutral-50 rounded-lg shadow-sm font-alex">
    <span className=" text-2xl font-medium text-black mb-2">{value}</span>
    <span className="text-xs font-normal text-neutral-600">{label}</span>
  </div>
);

const StatsPanel: React.FC<StatsPanelProps> = ({ title, stats }) => {
  return (
    <div className="w-full px-4 py-5 space-y-4 bg-white rounded-2xl border border-neutral-100">
      <h2 className="text-[16px] font-bold  text-main-2 pb-4">{title}</h2>
      <div className="space-y-4">
        {stats.map((stat, index) => (
          <StatItemComponent
            key={`${stat.label}-${index}`}
            value={stat.value}
            label={stat.label}
          />
        ))}
      </div>
    </div>
  );
};

export default StatsPanel;
