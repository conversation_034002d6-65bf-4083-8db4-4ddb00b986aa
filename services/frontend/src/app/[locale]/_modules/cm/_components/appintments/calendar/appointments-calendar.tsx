"use client";

import { useState, useEffect } from "react";
import { useLocale } from "next-intl";
import { LANGUAGES } from "@/constants/enum";
import Calendar from "@/components/calendar";
import { EventData } from "../../../types/appointment-calendar";
import dynamic from "next/dynamic";
import Loader from "@/components/loader";
import { Locale } from "@/i18n/routing";

// Type for calendar events with extendedProps
type CalendarEvent = {
  id: string;
  title: string;
  start: string;
  extendedProps: {
    eventData: EventData;
  };
};

const EventDetailsDialog = dynamic(() => import("./event-details-dialog"), {
  loading: () => (
    <Loader
      size={80}
      borderWidth={4}
      overlayColor="#000"
      overlayOpacity={0.5}
      overlayClassName="bg-opacity-50"
    />
  ),
  ssr: false,
});

// Sample event data for testing
const sampleEvents: EventData[] = [
  {
    id: "1",
    title: "أحمد محمد علي",
    date: "2025-03-03",
    time: "08:00 - 09:00 صباحًا",
    statusTitle: "عنوان الحالة",
    status: "تحسن تدريجي مع العلاج",
    descriptionTitle: "وصف الحالة",
    description:
      "جلسة متابعة للعلاج النفسي والسلوكي. المستفيد يظهر تحسناً ملحوظاً في التعامل مع الضغوط النفسية ويحتاج لمتابعة دورية لضمان استمرار التقدم.",
    joinMeetingText: "انضم إلى الاجتماع",
    meetingLinkText: "رابط الاجتماع",
  },
];

type AppointmentsCalendarProps = {
  enableDragging?: boolean;
};

export default function AppointmentsCalendar({
  enableDragging = false,
}: AppointmentsCalendarProps) {
  const locale: Locale = useLocale() as Locale;
  const isRtl = locale === LANGUAGES.ARABIC;
  const [selectedEvent, setSelectedEvent] = useState<EventData | null>(null);
  const [events, setEvents] = useState<CalendarEvent[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    async function fetchEvents() {
      try {
        // Simulate a fetch delay
        setLoading(true);
        // For real API call, uncomment the lines below:
        // const response = await fetch("/api/appointments");
        // const data: EventData[] = await response.json();

        // Transform events to include extendedProps for the calendar
        const transformedEvents = sampleEvents.map((event) => ({
          id: event.id,
          title: event.title,
          start: event.date,
          extendedProps: {
            eventData: event,
          },
        }));

        setEvents(transformedEvents);
      } catch (error) {
        console.error("Error fetching events:", error);
      } finally {
        setLoading(false);
      }
    }
    fetchEvents();
  }, []);

  // Handle event click from calendar
  const handleEventClick = (eventData: Record<string, unknown>) => {
    // Extract the actual event data from extendedProps
    const actualEventData = eventData.eventData || eventData;
    if (
      actualEventData &&
      typeof actualEventData === "object" &&
      "id" in actualEventData
    ) {
      setSelectedEvent(actualEventData as EventData);
    }
  };

  // Handle event drop (when dragging is enabled)
  const handleEventDrop = (info: import("@fullcalendar/core").EventDropArg) => {
    console.log("Event dropped:", {
      eventId: info.event.id,
      oldStart: info.oldEvent.start,
      newStart: info.event.start,
      delta: info.delta,
    });

    // Here you would typically update the event in your backend
    // You can add your API call here to update the appointment date
  };

  return (
    <div>
      <Calendar
        events={events}
        locale={locale}
        isRtl={isRtl}
        loading={loading}
        onEventClick={handleEventClick}
        enableDragging={enableDragging}
        onEventDrop={handleEventDrop}
      />
      {selectedEvent && (
        <EventDetailsDialog
          selectedEvent={selectedEvent}
          onClose={() => setSelectedEvent(null)}
        />
      )}
    </div>
  );
}
