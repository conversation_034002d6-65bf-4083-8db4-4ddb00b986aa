"use client";
import { DataTable } from "@/components/table";
import React, { useState } from "react";
import {
  Appointment,
  AppointmentResponse,
} from "../../../types/appointment-table";
import { columns } from "./beneficiaries-appointments-columns";
import { useTranslations } from "next-intl";
import { searchParams } from "@/types";
import { PaginationWithLinks } from "@/components/pagination-with-links";
import { RowSelectionState } from "@tanstack/react-table";
import dynamic from "next/dynamic";
import LoaderPortal from "@/components/loader/loader-portal";

const BeneficiaryDetailsDialog = dynamic(() => import("./beneficiary-details-dialog"), {
  loading: () => <LoaderPortal overlayColor="#000" overlayOpacity={0.6} />,
  ssr: false,
});

const BeneficiariesAppointmentsTable = ({
  data,
  searchParams,
  showPagination = false,
}: {
  data: AppointmentResponse;
  searchParams?: searchParams;
  showPagination?: boolean;
}) => {
  const t = useTranslations();
  const { page, limit } = searchParams ?? { page: 1, limit: 0 };
  const firstResult = (Number(page) - 1) * Number(limit) + 1;
  const lastResult = Math.min(
    Number(data?.totalCount),
    Number(page) * Number(limit),
  );
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [showBeneficiaryDetails, setShowBeneficiaryDetails] = useState(false);
  const [selectedBeneficiary, setSelectedBeneficiary] = useState<Appointment | null>(
    null,
  );

  // Set the selected beneficiary and show the modal
  const handleShowBeneficiaryDetails = (data: Appointment) => {
    const selectedIds = Object.keys(rowSelection).filter(
      (id) => rowSelection[id],
    );
    setSelectedBeneficiary(data);
    setShowBeneficiaryDetails(true);
  };

  return (
    <>
      <DataTable
        data={data.data}
        columns={columns}
        tableContainerClass="min-h-[240px]"
        title={t("cm.table.title")}
        meta={{
          onShowDetails: handleShowBeneficiaryDetails,
        }}
        rowSelection={rowSelection}
        onRowSelectionChange={setRowSelection}
        translationPrefix="cm.table"
        tableId="appointments"
      />
      {showPagination && (
        <div className="w-full pt-[18px]">
          <PaginationWithLinks
            page={Number(page || 1)}
            pageSize={Number(limit || 0)}
            totalCount={Number(data.totalCount || 0)}
            firstLastCounts={{
              firstCount: firstResult ?? 1,
              lastCount: lastResult ?? 1,
            }}
            pageSizeSelectOptions={{
              pageSizeOptions: [5, 10, 25, 30, 45, 50],
              pageSizeSearchParam: "limit",
            }}
            isDisabled={!data.data?.length}
          />
        </div>
      )}
      {showBeneficiaryDetails && (
        <BeneficiaryDetailsDialog
          setShowBeneficiaryDetails={setShowBeneficiaryDetails}
          showBeneficiaryDetails={showBeneficiaryDetails}
          beneficiaryData={selectedBeneficiary}
        />
      )}
    </>
  );
};

export default BeneficiariesAppointmentsTable;
