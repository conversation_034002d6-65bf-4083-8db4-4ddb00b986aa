import StatusCards from "../state-card";

const NewNotes = () => {
  const newNotes = [
    {
      id: "51315",
      userName: "كريم محمد",
      timestamp: "منذ 2 أيام",
      content:
        "تم إضافة مستفيد جديد إلى النظام وتحديد خطة العلاج الأولية. يرجى مراجعة الملف الشخصي والموافقة على الخطة المقترحة.",
      userRole: "مدير النظام",
      adminName: "محمود عبد الله",
      userImage: "",
    },
    {
      id: "51315",
      userName: "زين علي",
      timestamp: "منذ 3 أيام",
      content:
        "تحديث مهم على نظام إدارة الحالات. تم إضافة ميزات جديدة لتتبع تقدم المستفيدين وتحسين عملية التقييم الدوري.",
      userRole: "مدير النظام",
      adminName: "محمود عبد الله",

      userImage: "",
    },
    {
      id: "51315",
      userName: "معاذ ياسين محمد",
      timestamp: "منذ 3 أيام",
      content:
        "إشعار مهم حول تحديث سياسات النظام. يرجى مراجعة التعديلات الجديدة على إجراءات إدارة الحالات والتأكد من الالتزام بها.",
      userRole: "مدير النظام",
      adminName: "محمود عبد الله",
      userImage: "",
    },
    // Add more notes as needed
  ];

  return (
    <>
      <StatusCards variant="new" title="الملاحظات الجديدة" notes={newNotes} />
    </>
  );
};

export default NewNotes;
