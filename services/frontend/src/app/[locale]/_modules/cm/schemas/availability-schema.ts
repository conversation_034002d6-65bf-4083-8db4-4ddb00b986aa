import { z } from "zod";
import { TFunction } from "@/types";
import { timeValidation } from "@/schemas/common-validations";

export const availabilitySchema = (t: TFunction) => {
  return z.object({
    availability: z.array(
      z.object({
        day: z.string(),
        isEnabled: z.boolean(),
        intervals: z.array(
          z.object({
            from: timeValidation(t),
            to: timeValidation(t),
          }),
        ),
      }),
    ),
  });
};
