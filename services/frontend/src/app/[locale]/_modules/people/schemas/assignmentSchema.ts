import { z } from "zod";
import { TFunction } from "@/types";

export const assignmentSchema = (
  t: TFunction,
  getScopeByRoleId?: (roleId: string) => string | undefined,
) => {
  return z
    .object({
      project: z.string().optional(), // Make project optional initially
      role: z
        .string({
          required_error: t("common.form.assignment.role.required"),
        })
        .min(1, {
          message:
            t("common.form.assignment.role.required") || "Role is required",
        }),
      default: z.boolean().optional(),
    })
    .refine(
      (data) => {
        if (!getScopeByRoleId) {
          return !!data.project && data.project.trim().length > 0;
        }

        const scope = getScopeByRoleId(data.role);
        if (scope === "project_based") {
          return !!data.project && data.project.trim().length > 0;
        }
        // For global_role we don't require project
        return true;
      },
      {
        path: ["project"],
        message:
          t("common.form.assignment.project.required") || "Project is required",
      },
    );
};

export type AssignmentSchemaType = z.infer<ReturnType<typeof assignmentSchema>>;
