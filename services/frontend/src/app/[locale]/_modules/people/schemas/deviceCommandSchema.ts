import { z } from "zod";
import { TFunction } from "@/types";

export const deviceCommandSchema = (t: TFunction) => {
  return z.object({
    command: z.string().min(1, {
      message: t(
        "people.devices-page.device-commands.form.command.error.required",
      ),
    }),
    parameters: z
      .array(
        z.object({
          id: z.string(),
          key: z.string().min(1, {
            message: t(
              "people.devices-page.device-commands.form.parameters.key.error.required",
            ),
          }),
          value: z.string().min(1, {
            message: t(
              "people.devices-page.device-commands.form.parameters.value.error.required",
            ),
          }),
        }),
      )
      .optional()
      .default([]),
  });
};

export type DeviceCommandSchemaType = z.infer<
  ReturnType<typeof deviceCommandSchema>
>;
