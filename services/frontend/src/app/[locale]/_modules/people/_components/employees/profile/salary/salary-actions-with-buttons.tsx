"use client";

import { Row } from "@tanstack/react-table";
import {
  SalaryCalculation,
  SalaryCalculationDetail,
} from "../../../../type/employees-salaries";
import { TIncludedEmployee } from "../../../../type/employee-leaves";
import { ApprovalRequestData } from "../../../../type/approval-request";

import { EmployeeSalaryActions } from "./employee-salary-actions";

type SalaryActionsWithButtonsProps<TData> = {
  row: Row<TData>;
  onApproveSalary?: (salaryId: string) => void;
  onRejectSalary?: (salaryId: string) => void;
  onSubmitSalary?: (salaryId: string) => void;
  onPaySalary?: (salaryId: string) => void;
  onRegenerateSlip?: (salaryId: string) => void;
  isApproving?: boolean;
  isRejecting?: boolean;
  isSubmitting?: boolean;
  isPaying?: boolean;
  isRegenerating?: boolean;
  includedData?: (
    | TIncludedEmployee
    | ApprovalRequestData
    | SalaryCalculationDetail
  )[];
};

export const SalaryActionsWithButtons = <TData extends SalaryCalculation>({
  row,
  onApproveSalary,
  onRejectSalary,
  onSubmitSalary,
  onPaySalary,
  onRegenerateSlip,
  isApproving,
  isRejecting,
  isSubmitting,
  isPaying,
  isRegenerating,
  includedData,
}: SalaryActionsWithButtonsProps<TData>) => {
  // Always use the dropdown menu for all actions
  return (
    <EmployeeSalaryActions
      row={row}
      onApproveSalary={onApproveSalary}
      onRejectSalary={onRejectSalary}
      onSubmitSalary={onSubmitSalary}
      onPaySalary={onPaySalary}
      onRegenerateSlip={onRegenerateSlip}
      isApproving={isApproving}
      isRejecting={isRejecting}
      isSubmitting={isSubmitting}
      isPaying={isPaying}
      isRegenerating={isRegenerating}
      includedData={includedData}
    />
  );
};
