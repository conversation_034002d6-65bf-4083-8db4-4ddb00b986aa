"use client";

import ChartHeader from "@/components/chart/chart-header";
import CustomXAxisTick from "@/components/chart/custom-responsive-xAxis";
import { ChartSkeleton } from "@/components/skeletons";
import { LANGUAGES } from "@/constants/enum";
import { getMonthTranslations } from "@/constants/translations-mapping";
import useMediaQuery from "@/hooks/use-media-query";
import { TFunction } from "@/types";
import { useLocale, useTranslations } from "next-intl";
import React, { useEffect, useState } from "react";
import GenericBarChart from "@/components/chart/bar-chart/generic-bar-chart";
import { HighlightCurrentMonthShape } from "@/components/chart/bar-chart/custom-bar";
import { Locale } from "@/i18n/routing";

interface MonthData {
  month: string;
  percentage: number;
  highlight?: boolean;
}

const OrdersPercentageOverYear = () => {
  const sampleData: MonthData[] = [
    { month: "January", percentage: 30 },
    { month: "February", percentage: 55, highlight: true },
    { month: "March", percentage: 40 },
    { month: "April", percentage: 45 },
    { month: "May", percentage: 38 },
    { month: "June", percentage: 50 },
    { month: "July", percentage: 42 },
    { month: "August", percentage: 37 },
    { month: "September", percentage: 48 },
    { month: "October", percentage: 36 },
    { month: "November", percentage: 41 },
    { month: "December", percentage: 28 },
  ];

  const [data, setData] = useState<MonthData[]>([]);
  useEffect(() => {
    setTimeout(() => {
      setData(sampleData);
    }, 2500);
  }, []);

  const locale: Locale = useLocale() as Locale;
  const isAr = locale === LANGUAGES.ARABIC;
  const curryear = new Date().getFullYear().toString();
  const [ordersYear, setOrdersYear] = useState(curryear || "");
  const t = useTranslations() as TFunction;

  // Media queries.
  const isMobile = useMediaQuery("(max-width:480px)");
  const isMediumScreen = useMediaQuery("(max-width:768px)");
  const isLargeScreen = useMediaQuery("(max-width:1200px)");

  // Bar configuration for your chart.
  const bars = [
    {
      dataKey: "percentage",
      fill: "#E5E7EB",
      className: "text-gray-200",
      radius: [6, 6, 0, 0] as [number, number, number, number],
      shape: <HighlightCurrentMonthShape />,
    },
  ];

  // Formatter to translate the canonical key to the current locale.
  const xAxisTickFormatter = (value: string) => {
    return getMonthTranslations(value, t);
  };

  const tooltipLabelFormatter = (label: any) => (
    <span className="text-[10px] font-semibold">
      {`${getMonthTranslations(label, t)}`}
    </span>
  );

  const tooltipFormatter = (value: any, name: any, payload: any) => {
    const label =
      name === "percentage"
        ? t("people.orders-percentage-over-year-chart.tooltip.title")
        : name;
    return [
      <div
        className="flex justify-between gap-1 w-full break-words items-center max-w-full"
        key={name}
      >
        <span className="text-[10px]">{label}</span>
        <span className="text-xs text-black font-semibold">{value ?? 0}</span>
      </div>,
    ];
  };

  // If data is not loaded, show skeleton.
  if (data.length === 0) {
    return (
      <ChartSkeleton
        showSelect={true}
        showLabel={false}
        skeletonWrapperStyle="rounded-[20px] min-h-[380px] max-h-[380px]"
        titleStyle="w-[265px]"
      />
    );
  }

  return (
    <div>
      <ChartHeader
        title={t("people.orders-percentage-over-year-chart.title")}
        titleStyle="sm:text-[20px] sm:font-medium leading-[30px] text-gray-700 font-readex_pro"
        headerClassName="w-full flex justify-between items-center border-b border-gray-100 px-6 pb-5 pt-4 max-h-[84px] sm:max-h-[66px]"
        selectPlaceholder={t("cm.appointmentchart.annual")}
        selectTriggerStyle="max-h-[34px] font-semibold rounded-lg justify-start gap-2"
        selectOptions={[
          { value: "2023", label: "2023" },
          { value: "2024", label: "2024" },
          { value: "2025", label: "2025" },
        ]}
        selectValue={ordersYear}
        onSelectChange={(val) => setOrdersYear(val)}
      />

      <GenericBarChart
        margin={{
          top: 33,
          right: isMediumScreen ? (isAr ? -20 : 10) : isAr ? 0 : 24,
          left: isMediumScreen ? (isAr ? 10 : -10) : isAr ? 24 : 24,
          bottom: 22,
        }}
        chartContainerClass="p-0 h-[313px]"
        data={data}
        xAxisKey="month"
        bars={bars}
        barSize={isMobile ? 16 : 32}
        ticks={[0, 10, 20, 30, 40, 50, 60]}
        stackOffset="none"
        tooltipLabelFormatter={tooltipLabelFormatter}
        tooltipFormatter={tooltipFormatter}
        // Use the custom tick renderer for X-axis.
        xAxisTick={(props) => {
          const fullPayload = data.find(
            (item) => item.month === props.payload.value,
          );
          return (
            <CustomXAxisTick
              {...props}
              fullPayload={fullPayload}
              xAxisTickFormatter={xAxisTickFormatter}
              isMobile={isMobile}
              isLargeScreen={isLargeScreen}
              isMediumScreen={isMediumScreen}
              isAr={isAr}
            />
          );
        }}
        xAxisTickFormatter={xAxisTickFormatter}
        xAxisAngle={isLargeScreen ? -45 : 0}
        gridStrokeDasharray="2 2"
        yAxisTick={{ fill: "#A0A0AB", fontSize: 14 }}
        yAxisDomain={[5, "dataMax"]}
      />
    </div>
  );
};

export default OrdersPercentageOverYear;
