"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useTranslations } from "next-intl";
import { DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Form } from "@/components/ui/form";
import { Button } from "@/components/ui/button";

import { TinputField } from "@/types";
import FormFieldRenderer from "@/components/auth/fields/form-field-renderer";
import ResponsiveDialog from "@/components/responsive-dialog";

type AddAttachmentDialogProps = {
  isOpen: boolean;
  onClose: () => void;
  onUpload: (file: File) => Promise<boolean>;
  isUploading: boolean;
};

// Schema for attachment upload
const attachmentSchema = z.object({
  attachments: z
    .array(z.instanceof(File))
    .min(1, "Please select at least one file")
    .max(1, "Please select only one file at a time"),
});

type AttachmentSchemaType = z.infer<typeof attachmentSchema>;

const AddAttachmentDialog = ({
  isOpen,
  onClose,
  onUpload,
  isUploading,
}: AddAttachmentDialogProps) => {
  const t = useTranslations();

  const form = useForm<AttachmentSchemaType>({
    resolver: zodResolver(attachmentSchema),
    defaultValues: {
      attachments: [],
    },
    mode: "all",
  });

  const handleSubmit = async (data: AttachmentSchemaType) => {
    if (data.attachments && data.attachments.length > 0) {
      const success = await onUpload(data.attachments[0]);
      if (success) {
        form.reset();
        onClose();
      }
    }
  };

  const handleClose = () => {
    form.reset();
    onClose();
  };

  // Form field configuration for attachments
  const attachmentField: TinputField<AttachmentSchemaType> = {
    name: "attachments",
    type: "attachments",
    label: "Attachments",
    placeholder: "Select files to upload",
    accept: "*/*",
    multiple: true,
    maxFiles: 1,
    maxSize: 5000000, // 5MB
    containerClassName: "mt-2",
    buttonClassName: "w-full justify-center text-secondary",
    uploadButtonText: t("common.buttonText.upload"),
  };

  return (
    <ResponsiveDialog
      open={isOpen}
      onOpenChange={handleClose}
      header={
        <div className="flex items-center justify-between p-6 border-b">
          <DialogHeader>
            <DialogTitle>
              {t("people.employees-page.profile.attachments.add-dialog.title")}
            </DialogTitle>
          </DialogHeader>
        </div>
      }
    >
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          <FormFieldRenderer
            fieldConfig={attachmentField}
            form={form}
            isPending={isUploading}
          />

          <div className="flex justify-end gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isUploading}
            >
              {t("common.buttonText.cancel")}
            </Button>
            <Button
              type="submit"
              disabled={
                isUploading ||
                !form.formState.isValid ||
                form.watch("attachments").length === 0
              }
            >
              {isUploading
                ? t("common.buttonText.uploading")
                : t("common.buttonText.upload")}
            </Button>
          </div>
        </form>
      </Form>
    </ResponsiveDialog>
  );
};

export default AddAttachmentDialog;
