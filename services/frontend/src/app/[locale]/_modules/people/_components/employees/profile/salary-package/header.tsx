"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { useTranslations } from "next-intl";
import React, { useState } from "react";
import dynamic from "next/dynamic";
import Loader from "@/components/loader";
import { useEmployeeDetails } from "../../../../hooks/employees/useEmployeeDetails";
import { SalaryPackageHeaderSkeleton } from "../../../../skeletons/employee-profile-header-skeleton";
import SalaryForm from "./salary-form";
import { DialogDescription, DialogTitle } from "@/components/ui/dialog";
import { TSalaryPackageData } from "../../../../type/salary-package";
import { mutate } from "swr";
import LoaderPortal from "@/components/loader/loader-portal";
import { SalaryHistoryChangesModal } from "./salary-history-changes-modal";
import { usePermission } from "@/contexts/PermissionContext";

import { useCurrentEmployee } from "@/hooks/people/getCurrentEmployee";
import {
  submitSalaryPackage,
  cancelSalaryPackage,
} from "../../../../actions/salary-package";
import { useToastMessage } from "@/hooks/use-toast-message";
import { useApprovalMutations } from "@/app/[locale]/_modules/people/hooks/useApprovalMutations";
import { useApprovalPermissions } from "@/app/[locale]/_modules/people/hooks/useApprovalPermissions";
import { getIncludedItem } from "@/app/[locale]/_modules/people/utils/included-data/getIncludedItem";
import { ApprovalRequestData } from "@/app/[locale]/_modules/people/type/approval-request";
import { Check, SendHorizonal, X } from "lucide-react";
import { Edit, Trash } from "../../../../../../../../../public/images/icons";
import { TbLoader } from "react-icons/tb";
import {
  getSalaryPackagePermissions,
  checkIsViewingOwnProfile,
} from "../../../../utils/salary-package-permissions";
import { getSalaryPackageHeaderState } from "../../../../utils/salary-package-header-logic";
import ConfirmDeleteDialog from "@/components/confirm-delete-dialog";

const ResponsiveDialog = dynamic(
  () => import("@/components/responsive-dialog"),
  {
    ssr: false,
    loading: () => <Loader overlayColor="#000" overlayOpacity={0.6} />,
  },
);

const CalculatePeriodModal = dynamic(
  () =>
    import("../calculate-period-modal").then((mod) => mod.CalculatePeriodModal),
  {
    ssr: false,
    loading: () => <LoaderPortal overlayColor="#000" overlayOpacity={0.6} />,
  },
);

type SalaryPackageHeaderProps = {
  title: string;
  employeeId?: string;
};

const SalaryPackageHeader = ({
  title,
  employeeId,
}: SalaryPackageHeaderProps) => {
  const t = useTranslations();
  const { hasPermission } = usePermission();
  const { profile: currentEmployee } = useCurrentEmployee();
  const { showToast } = useToastMessage();
  const [showSalaryForm, setShowSalaryForm] = useState(false);
  const [formMode, setFormMode] = useState<"create" | "update">("create");
  const [showCalculatePeriodModal, setShowCalculatePeriodModal] =
    useState(false);
  const [showSalaryHistoryChanges, setShowSalaryHistoryChanges] =
    useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isCancelling, setIsCancelling] = useState(false);
  const [isCancelDialogOpen, setIsCancelDialogOpen] = useState(false);

  // Use approval mutations for approve/reject functionality
  const { approveRequest, rejectRequest, isApproving, isRejecting } =
    useApprovalMutations();

  const {
    salaryPackage,
    editableSalaryPackage,
    included,
    mutate: mutateEmployeeDetails,
    isLoading,
  } = useEmployeeDetails(employeeId || "");

  // Check if viewing own profile
  const isViewingOwnProfile = checkIsViewingOwnProfile(
    currentEmployee?.id,
    employeeId,
  );

  // Get all salary package permissions
  const { canCalculateSalary } = getSalaryPackagePermissions(
    hasPermission,
    isViewingOwnProfile,
  );

  // Get header state using utility
  const { hasPendingSalaryPackage, isUpdateMode } = getSalaryPackageHeaderState(
    salaryPackage,
    editableSalaryPackage,
    included,
  );

  // The package to edit is the editable salary package
  const editablePackage = editableSalaryPackage;

  const buttonText = isUpdateMode ? (
    <Edit className="text-white !w-5 !h-5" />
  ) : (
    t("people.salary-package.create.button")
  );

  // Get approval request data from included data
  const approvalRequestId =
    salaryPackage?.relationships?.approval_request?.data?.id;
  const approvalRequest = approvalRequestId
    ? (getIncludedItem(
        (included as any[]) || [],
        "approval_request",
        approvalRequestId,
      ) as ApprovalRequestData | undefined)
    : undefined;

  // Check approval permissions
  const { canAct: canApprove, shouldShowButtons } =
    useApprovalPermissions(approvalRequest);

  const handleButtonClick = () => {
    if (isUpdateMode) {
      setFormMode("update");
    } else {
      setFormMode("create");
    }
    setShowSalaryForm(true);
  };

  const handleCalculatePeriod = () => {
    setShowCalculatePeriodModal(true);
  };

  const handleShowSalaryHistoryChanges = () => {
    setShowSalaryHistoryChanges(true);
  };

  const handleSalaryPackageCreated = () => {
    setShowSalaryForm(false);
    mutateEmployeeDetails(); // Refresh employee details
    // Refresh salary package history with the exact same cache key used by useSalaryPackageHistory
    if (employeeId) {
      mutate(`/api/employees/${employeeId}/salary_packages?sort=`);
    }
  };

  const handleSalaryPackageUpdated = (_updatedPackage: TSalaryPackageData) => {
    setShowSalaryForm(false);
    mutateEmployeeDetails(); // Refresh employee details
    // Refresh salary package history with the exact same cache key used by useSalaryPackageHistory
    if (employeeId) {
      mutate(`/api/employees/${employeeId}/salary_packages?sort=`);
    }
  };

  const handleSubmitForApproval = async () => {
    if (!editableSalaryPackage?.id) return;

    setIsSubmitting(true);
    try {
      const result = await submitSalaryPackage(editableSalaryPackage.id);
      if (result.success) {
        showToast("success", result.success);
        mutateEmployeeDetails(); // Refresh to get updated status
      } else if (result.error) {
        showToast("error", result.error);
      }
    } catch (error) {
      showToast("error", "Failed to submit salary package");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancelClick = () => {
    setIsCancelDialogOpen(true);
  };

  const handleConfirmCancel = async () => {
    if (!editableSalaryPackage?.id) return;

    setIsCancelling(true);
    try {
      const result = await cancelSalaryPackage(editableSalaryPackage.id);
      if (result.success) {
        showToast("success", result.success);
        mutateEmployeeDetails(); // Refresh to get updated status
        setIsCancelDialogOpen(false);
      } else if (result.error) {
        showToast("error", result.error);
      }
    } catch (error) {
      showToast("error", "Failed to cancel salary package");
    } finally {
      setIsCancelling(false);
    }
  };

  const handleApproveSalary = async () => {
    // We need to get the approval request ID from the salary package
    // This would typically come from the salary package's approval_request relationship
    const approvalRequestId =
      salaryPackage?.relationships?.approval_request?.data?.id;

    if (!approvalRequestId) {
      showToast("error", "No approval request found");
      return;
    }

    try {
      await approveRequest(approvalRequestId, "", () => {
        mutateEmployeeDetails(); // Refresh to get updated status
      });
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  const handleRejectSalary = async () => {
    // We need to get the approval request ID from the salary package
    const approvalRequestId =
      salaryPackage?.relationships?.approval_request?.data?.id;

    if (!approvalRequestId) {
      showToast("error", "No approval request found");
      return;
    }

    try {
      await rejectRequest(approvalRequestId, "", () => {
        mutateEmployeeDetails(); // Refresh to get updated status
      });
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  if (isLoading) {
    return <SalaryPackageHeaderSkeleton />;
  }

  return (
    <>
      <div className="flex items-center justify-between mb-4 flex-wrap gap-4">
        <h2>{title}</h2>
        {employeeId && (
          <div className="flex items-center gap-3">
            {canCalculateSalary && (
              <Button
                variant={"ghost"}
                className="h-10 rounded-[9px] font-medium text-xs bg-gray-100 border-none text-gray-800"
                onClick={handleCalculatePeriod}
                type="button"
              >
                {t("people.salary-calculation.calculate-period.button")}
              </Button>
            )}
            {/* {canViewSalaryHistory && ( */}
            <Button
              disabled={!salaryPackage || !!editableSalaryPackage}
              variant={"ghost"}
              className="h-10 rounded-[9px] font-medium text-xs bg-gray-100 border-none text-gray-800"
              onClick={handleShowSalaryHistoryChanges}
              type="button"
            >
              {t("people.salary-package.changes-log.button")}
            </Button>
            {/* )} */}
            {/* {showSalaryPackageButton && ( */}
            <Button
              {...(isUpdateMode ? { variant: "outline" } : {})}
              className="h-10 rounded-[9px] font-medium text-xs"
              onClick={handleButtonClick}
              disabled={ isViewingOwnProfile ||(hasPendingSalaryPackage && !isUpdateMode )}
            >
              {buttonText}
            </Button>
            {/* )} */}

            {/* Approve/Reject buttons - show when user can approve and should show buttons */}
            {shouldShowButtons && approvalRequest && (
              <>
                <Button
                  disabled={isApproving || !canApprove}
                  variant={"outline"}
                  className="h-10 rounded-[9px] font-medium text-xs group"
                  onClick={handleApproveSalary}
                  type="button"
                >
                  {isApproving ? (
                    <TbLoader className="animate-spin" />
                  ) : (
                    <Check className="!w-5 !h-5 group-hover:text-green-700" />
                  )}
                </Button>
                <Button
                  disabled={isRejecting || !canApprove}
                  variant={"outline"}
                  className="h-10 rounded-[9px] font-medium text-xs group"
                  onClick={handleRejectSalary}
                  type="button"
                >
                  {isRejecting ? (
                    <TbLoader className="animate-spin" />
                  ) : (
                    <X className="!w-5 !h-5 text-error group-hover:text-red-700" />
                  )}
                </Button>
              </>
            )}

            {/* Submit/Cancel buttons - show when there's an editable package */}
            {editableSalaryPackage && (
              <Button
                disabled={isCancelling}
                variant={"outline"}
                className="h-10 rounded-[9px] border-gray-200 font-medium text-xs text-error hover:text-red-600"
                onClick={handleCancelClick}
                type="button"
              >
                {isCancelling ? (
                  <TbLoader className="animate-spin" />
                ) : (
                  <Trash className="!w-6 !h-6" />
                )}
              </Button>
            )}

            {editableSalaryPackage && (
              <Button
                disabled={isSubmitting}
                variant={"outline"}
                className="h-10 rounded-[9px] font-medium text-xs text-gray-700 group"
                onClick={handleSubmitForApproval}
                type="button"
              >
                {isSubmitting ? (
                  <TbLoader className="animate-spin" />
                ) : (
                  <SendHorizonal className="!w-6 !h-6 rtl:-rotate-180 group-hover:text-green-700" />
                )}
              </Button>
            )}
          </div>
        )}
      </div>

      {/* Unified Salary Form Modal - For both create and update */}
      {showSalaryForm && employeeId && (
        <ResponsiveDialog
          open={showSalaryForm}
          onOpenChange={setShowSalaryForm}
          header={
            <>
              <div className="px-6 py-[22px] border-b">
                <DialogTitle className="font-semibold text-[18px] leading-[28px]">
                  {formMode === "create"
                    ? t("people.salary-package.create.title")
                    : t("people.salary-package.update.title")}
                </DialogTitle>
                <DialogDescription className="text-sm text-gray-600 mt-1 sr-only">
                  {formMode === "create"
                    ? t("people.salary-package.create.description")
                    : t("people.salary-package.update.description")}
                </DialogDescription>
              </div>
            </>
          }
        >
          <SalaryForm
            employeeId={employeeId}
            onClose={() => setShowSalaryForm(false)}
            existingPackage={
              formMode === "update" ? editablePackage : undefined
            }
            onSalaryPackageCreated={handleSalaryPackageCreated}
            onSalaryPackageUpdated={handleSalaryPackageUpdated}
            mode={formMode}
          />
        </ResponsiveDialog>
      )}

      {/* Calculate Period Modal */}
      {showCalculatePeriodModal && (
        <CalculatePeriodModal
          isOpen={showCalculatePeriodModal}
          onClose={() => setShowCalculatePeriodModal(false)}
          employeeId={employeeId}
        />
      )}

      {/* Salary History Changes Modal */}
      {showSalaryHistoryChanges && employeeId && (
        <SalaryHistoryChangesModal
          employeeId={employeeId}
          isOpen={showSalaryHistoryChanges}
          onClose={() => setShowSalaryHistoryChanges(false)}
        />
      )}

      {/* Cancel Confirmation Dialog */}
      {editableSalaryPackage && (
        <ConfirmDeleteDialog
          isOpen={isCancelDialogOpen}
          onClose={() => setIsCancelDialogOpen(false)}
          onConfirmDelete={handleConfirmCancel}
          isDeleting={isCancelling}
          itemName="Salary Package"
          titleKey="people.salary-package.cancel-dialog.title"
          descriptionKey="people.salary-package.cancel-dialog.description"
        />
      )}
    </>
  );
};

export default SalaryPackageHeader;
