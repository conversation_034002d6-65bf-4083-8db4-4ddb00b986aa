import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { LANGUAGES } from "@/constants/enum";
import { Row } from "@tanstack/react-table";
import { useLocale, useTranslations } from "next-intl";
import { CiMenuKebab } from "react-icons/ci";
import { Eye } from "../../../../../../../../public/images/icons";
import { Locale, useRouter } from "@/i18n/routing";
import { usePermission } from "@/contexts/PermissionContext";
import { PermissionEnum } from "@/enums/Permission";
import { TEmployeeData } from "../../../type/employee";

type EmployeesActionProps<TData> = {
  row: Row<TData>;
};

const EmployeesAction = <TData extends TEmployeeData>({
  row,
}: EmployeesActionProps<TData>) => {
  const { hasPermission } = usePermission();

  const locale: Locale = useLocale() as Locale;
  const isAr = locale === LANGUAGES.ARABIC;
  const t = useTranslations();
  const router = useRouter();

  // Check permissions
  const canViewEmployee = hasPermission(PermissionEnum.MANAGE_EMPLOYEE) ||
                          hasPermission(PermissionEnum.READ_EMPLOYEE);
  const canUpdateEmployee = hasPermission(PermissionEnum.MANAGE_EMPLOYEE) ||
                           hasPermission(PermissionEnum.UPDATE_EMPLOYEE);

  // Don't render the action menu if user doesn't have any permissions
  if (!canViewEmployee && !canUpdateEmployee) {
    return null;
  }

  const handleViewProfile = () => {
    const employeeId = (
      row.original as TEmployeeData & { id?: string | number }
    ).id;

    router.push(`/people/employees/${employeeId}`);
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="h-8 w-8 p-0 border border-[#EEEEEE] rounded-lg"
        >
          <span className="sr-only">Open menu</span>
          <CiMenuKebab className="stroke-[0.5px]" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        lang={locale}
        className="w-60 max-w-60 rounded-2xl space-y-1 font-semibold text-sm text-start text-[#727A90] p-2"
        align={isAr ? "start" : "end"}
      >
        <DropdownMenuItem
          onClick={
            handleViewProfile
            // hasPermission(PermissionEnum.NOAccess)
            //   ? handleViewProfile
            //   : () => alert("Not allowed")
          }
          className="flex rtl:flex-row-reverse items-center min-h-10 group rounded-2xl"
        >
          <span>
            <Eye className="!w-6 !h-6 stroke-[#6B7280] group-hover:stroke-secondary" />
          </span>
          <span>{t("people.employees-page.table.actions.Action1")}</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export { EmployeesAction };
