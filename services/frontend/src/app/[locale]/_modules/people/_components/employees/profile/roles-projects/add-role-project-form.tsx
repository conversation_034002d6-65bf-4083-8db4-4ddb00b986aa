"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useForm, useWatch } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import { useCallback, useState } from "react";
import { Loader } from "lucide-react";
import { useRoles } from "@/hooks/useRoles";
import { useProjects } from "@/hooks/useProjects";
import {
  roleProjectSchema,
  RoleProjectSchemaType,
} from "../../../../schemas/roleProjectSchema";
import { useEmployeeRoleProjectMutations } from "../../../../hooks/employees/useEmployeeRoleProjectMutations";

type AddRoleProjectFormProps = {
  employeeId: string;
  onSuccess: () => void;
  onCancel: () => void;
};

export const AddRoleProjectForm = ({
  employeeId,
  onSuccess,
  onCancel,
}: AddRoleProjectFormProps) => {
  const t = useTranslations();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { roles, isLoading: isLoadingRoles } = useRoles();
  const { projects, isLoading: isLoadingProjects } = useProjects();
  const { addRoleProject } = useEmployeeRoleProjectMutations({
    employeeId,
    onSuccess,
  });

  const getScopeByRoleId = useCallback(
    (id: string) => roles.find((r) => r.id.toString() === id)?.attributes.scope,
    [roles],
  );

  const form = useForm<RoleProjectSchemaType>({
    resolver: zodResolver(roleProjectSchema(t, getScopeByRoleId)),
    defaultValues: {
      roleId: "",
      projectId: "",
    },
  });

  // Watch roleId to determine if selected role is global
  const selectedRoleId = useWatch({
    control: form.control,
    name: "roleId",
  });
  const selectedRole = roles.find((r) => r.id.toString() === selectedRoleId);
  // Determine if the selected role is project-based or global
  const isProjectBasedRole = selectedRole?.attributes.scope === "project_based";
  const isGlobalRole = selectedRole?.attributes.scope === "global_role";

  const handleSubmit = async (data: RoleProjectSchemaType) => {
    if (!employeeId || isSubmitting) return;

    setIsSubmitting(true);
    try {
      const projectIdToSend = isGlobalRole ? "" : data.projectId;
      await addRoleProject(data.roleId, projectIdToSend);
      form.reset();
    } catch (error) {
    } finally {
      setIsSubmitting(false);
    }
  };

  const isLoading = isLoadingRoles || isLoadingProjects;

  //update design

  return (
    <div className="space-y-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="roleId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm text-gray-500">
                    {t(
                      "people.employees-page.profile.roles-projects.form.role.label",
                    )}
                  </FormLabel>
                  <Select
                    onValueChange={(val) => {
                      field.onChange(val);
                      form.setValue("projectId", "");
                    }}
                    value={field.value}
                    disabled={isLoading || isSubmitting}
                  >
                    <FormControl>
                      <SelectTrigger className="h-10 rounded-md border-gray-300 bg-white text-sm">
                        <SelectValue
                          placeholder={t(
                            "people.employees-page.profile.roles-projects.form.role.placeholder",
                          )}
                        />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {roles.length > 0 ? (
                        roles.map((role) => (
                          <SelectItem key={role.id} value={role.id.toString()}>
                            {role.attributes.name}
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="no-roles" disabled>
                          {isLoadingRoles
                            ? "Loading roles..."
                            : "No roles available"}
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="projectId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm text-gray-500">
                    {t(
                      "people.employees-page.profile.roles-projects.form.project.label",
                    )}
                  </FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    value={field.value}
                    disabled={isGlobalRole || isLoading || isSubmitting}
                  >
                    <FormControl>
                      <SelectTrigger className="h-10 rounded-md border-gray-300 bg-white text-sm">
                        <SelectValue
                          placeholder={t(
                            "people.employees-page.profile.roles-projects.form.project.placeholder",
                          )}
                        />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {projects.length > 0 ? (
                        projects.map((project) => (
                          <SelectItem
                            key={project.id}
                            value={project.id.toString()}
                          >
                            {project.attributes.name}
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="no-projects" disabled>
                          {isLoadingProjects
                            ? "Loading projects..."
                            : "No projects available"}
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="flex justify-end items-center gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              className="h-12"
              onClick={onCancel}
              disabled={isSubmitting}
            >
              {t("common.buttonText.cancel")}
            </Button>
            <Button
              type="submit"
              className="h-12"
              disabled={
                isLoading ||
                isSubmitting ||
                !form.getValues("roleId") ||
                (isProjectBasedRole && !form.getValues("projectId"))
              }
            >
              {isSubmitting && <Loader className="mr-2 h-4 w-4 animate-spin" />}
              {t("people.employees-page.profile.roles-projects.form.submit")}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};
