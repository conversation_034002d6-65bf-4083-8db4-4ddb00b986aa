"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { X } from "lucide-react";
import { useLocale, useTranslations } from "next-intl";
import { LANGUAGES, SYSTEM } from "@/constants/enum";
import RequestStatus from "@/components/status/request-status";
import {
  getLeaveRequestCategory,
  mapRequestStatusToCanonical,
} from "@/constants/translations-mapping";
import Link from "next/link";
import { TLeaveType, TMainCategory } from "../type/leave-request";
import { ChartSkeleton } from "@/components/skeletons";
import { useLeaveRequestMutations } from "../hooks/useLeaveRequestMutations";
import { TFunction } from "@/types";
import { Locale } from "@/i18n/routing";
import { LeaveDetail, TIncludedEmployee } from "../type/employee-leaves";
import { TEmployee } from "../type/employee";
import { useApprovalPermissions } from "../hooks/useApprovalPermissions";
import { getIncludedItem } from "../utils/included-data/getIncludedItem";
import { ApprovalRequestData } from "../type/approval-request";

type TabValue = "all" | "leave";

export default function LatestLeavesRequests() {
  const [selectedTab, setSelectedTab] = useState<TabValue>("all");
  const locale: Locale = useLocale() as Locale;
  const t = useTranslations();
  const isAr = locale === LANGUAGES.ARABIC;

  const {
    data: requests,
    isLoading,
    error,
    mutateData,
  } = useLeaveRequestMutations({
    key: `/api/leaves?limit=${20}&include=employee,approval_request`,
  });

  // Define tabs with type safety - removed overtime tab
  const tabs: { value: TabValue; label: string }[] = [
    { value: "all", label: t("people.leaves-requests-component.tabs.all") },
    {
      value: "leave",
      label: t("people.leaves-requests-component.tabs.leave"),
    },
  ];

  // Loading state
  if (isLoading) {
    return (
      <ChartSkeleton
        showLabel={false}
        skeletonWrapperStyle="max-h-[530px] min-h-[514px] h-full"
      />
    );
  }

  // Error state
  if (error) {
    return (
      <div className="text-center text-error min-h-[514px] font-semibold mt-4">
        Error loading requests
      </div>
    );
  }

  const employeesMap = new Map();
  if (requests.included) {
    requests.included.forEach(
      (item: TIncludedEmployee | ApprovalRequestData) => {
        if (item.type === "employee") {
          const employeeItem = item as TIncludedEmployee;
          employeesMap.set(employeeItem.id, employeeItem.attributes);
        }
      },
    );
  }

  // Filter requests based on selected tab - only leave requests
  const filteredRequests =
    selectedTab === "all"
      ? requests.data
      : requests.data.filter((req) => req.type === "leave");
  return (
    <div className="bg-white w-full h-full max-h-[530px] rounded-3xl">
      {/* Header */}
      <div className="flex justify-between items-center border-b border-gray-100 mb-2 px-6 py-[18px] max-h-[66px]">
        <h2 className="text-lg font-semibold">
          {t("people.leaves-requests-component.title")}
        </h2>
        <Button
          asChild
          variant="outline"
          className="rounded-lg px-3 py-1 text-sm font-semibold"
        >
          <Link href={`/${locale}/${SYSTEM.PEOPLE}/requests`}>
            {t("people.leaves-requests-component.allRequests")}
          </Link>
        </Button>
      </div>

      {/* Tabs for Filtering */}
      <Tabs
        defaultValue="all"
        onValueChange={(value) => setSelectedTab(value as TabValue)}
        className="m-6 mb-0"
      >
        <TabsList className="w-full flex justify-between rtl:flex-row-reverse gap-4 bg-transparent border rounded-[10px] min-h-11 p-1">
          {tabs.map((tab) => (
            <TabsTrigger
              key={tab.value}
              value={tab.value}
              className="w-full data-[state=active]:bg-[#D4F3EA] data-[state=active]:text-secondary data-[state=active]:font-semibold text-neutral-500 font-medium leading-6 min-h-9 !shadow-none rounded-[8px]"
            >
              {tab.label}
            </TabsTrigger>
          ))}
        </TabsList>
      </Tabs>

      {/* Requests List */}
      <div className="h-[380px] relative">
        <ScrollArea
          verticleScrollStyle="rtl:right-0 right:left-0 mt-6"
          dir={isAr ? "rtl" : "ltr"}
          className="h-full overflow-hidden"
        >
          <div className="space-y-5 m-6 mt-8 h-full">
            {filteredRequests.map((req) => (
              <RequestItem
                key={req.id}
                req={req}
                employee={employeesMap.get(req.relationships.employee.data.id)}
                onAccept={() =>
                  mutateData("acceptLeaveRequest", {
                    id: req.relationships.approval_request.data.id,
                    leaveId: req.id,
                  })
                }
                onReject={() =>
                  mutateData("rejectLeaveRequest", {
                    id: req.relationships.approval_request.data.id,
                    leaveId: req.id,
                  })
                }
                t={t}
                includedData={requests.included}
              />
            ))}
            {filteredRequests.length === 0 && (
              <div className="text-center text-gray-500 mt-4">
                {t("people.leaves-requests-component.noRequests")}
              </div>
            )}
          </div>
        </ScrollArea>
      </div>
    </div>
  );
}

type RequestItemProps = {
  req: LeaveDetail;
  employee: TEmployee;
  onAccept: () => void;
  onReject: () => void;
  t: TFunction;
  includedData?: (TIncludedEmployee | ApprovalRequestData)[];
};

function RequestItem({
  req,
  employee,
  onAccept,
  onReject,
  t,
  includedData,
}: RequestItemProps) {
  // Get approval request data for permissions
  const approvalRequestId = req.relationships?.approval_request?.data?.id;
  const approvalRequest =
    approvalRequestId && includedData
      ? (getIncludedItem(
          includedData,
          "approval_request",
          approvalRequestId,
        ) as ApprovalRequestData | undefined)
      : undefined;

  // Use approval permissions hook
  const { canAct, hasActed, userAction } =
    useApprovalPermissions(approvalRequest);

  const statusTranslations = mapRequestStatusToCanonical(
    req.attributes.status ?? "",
  );

  const categoryLabel = getLeaveRequestCategory(
    req.type as TMainCategory,
    req.attributes.leave_type as TLeaveType,
    t,
  );
  const label = t(`common.status.request-status.${statusTranslations}`);

  return (
    <div className="flex justify-between items-center rounded-lg m-0">
      <div className="flex items-center gap-2">
        <Avatar className="w-10 h-10">
          <AvatarImage
            className="w-full h-full"
            src={employee?.avatar_url || ""}
            alt={employee?.name}
          />
          <AvatarFallback>{employee?.name?.charAt(0) || "?"}</AvatarFallback>
        </Avatar>
        <div>
          <p className="font-semibold text-sm">{employee?.name}</p>
          <p className="text-sm text-gray-400 font-semibold">{categoryLabel}</p>
        </div>
      </div>

      {req.attributes.status === "waiting" ||
      req.attributes.status === "pending" ? (
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            className="text-black font-semibold min-w-[72px] min-h-9 text-sm disabled:opacity-50"
            onClick={() => canAct && !hasActed && onAccept()}
            disabled={!canAct || hasActed}
            title={
              hasActed && userAction === "approve"
                ? "Already Approved"
                : undefined
            }
          >
            {hasActed && userAction === "approve" ? "✓ " : ""}
            {t("people.leaves-requests-component.actions.accept")}
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="text-[#EF4444] min-h-9 rounded-[8px] max-w-9 max-h-9 text-sm disabled:opacity-50"
            onClick={() => canAct && !hasActed && onReject()}
            disabled={!canAct || hasActed}
            title={
              hasActed && userAction === "reject"
                ? "Already Rejected"
                : undefined
            }
          >
            {hasActed && userAction === "reject" ? "✓ " : ""}
            <X />
          </Button>
        </div>
      ) : (
        <RequestStatus status={statusTranslations} label={label} />
      )}
    </div>
  );
}
