"use client";

import React from "react";
import { useTranslations, useLocale } from "next-intl";
import { usePathname, useRouter } from "next/navigation";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Locale } from "@/i18n/routing";

type EmployeeProfileTabsProps = {
  employeeId: string;
  locale: string;
};

type TabKey =
  | "attendance"
  | "attendance-log"
  | "attachments"
  | "leaves"
  | "roles-projects"
  | "salary-package";

export const EmployeeProfileTabs: React.FC<EmployeeProfileTabsProps> = ({
  employeeId,
  locale,
}) => {
  const t = useTranslations();
  const currentLocale: Locale = useLocale() as Locale;
  const isAr = currentLocale === "ar";
  const pathname = usePathname();
  const router = useRouter();

  const tabs: TabKey[] = [
    "attendance",
    "attendance-log",
    "leaves",
    "attachments",
    "roles-projects",
    "salary-package",
  ];

  const activeTab =
    tabs.find((tab) => tab !== "attendance" && pathname.endsWith(`/${tab}`)) ||
    "attendance";

  const getTabRoute = (tab: TabKey) =>
    tab === "attendance"
      ? `/${locale}/people/employees/${employeeId}`
      : `/${locale}/people/employees/${employeeId}/${tab}`;

  const tabTriggerClass =
    "text-base font-medium data-[state=active]:bg-transparent data-[state=active]:text-secondary data-[state=active]:shadow-none border-b-4 border-transparent data-[state=active]:border-secondary rounded-none text-gray-400 data-[state=active]:font-bold p-0 pb-[12px] transition-all duration-200 ease-in-out will-change-transform";

  return (
    <div className="bg-none border-b border-gray-200 !mt-4 sm:!mt-8">
      <Tabs
        defaultValue="attendance"
        className="overflow-auto overflow-y-hidden custom-scroll"
        value={activeTab}
        dir={isAr ? "rtl" : "ltr"}
        onValueChange={(value) => {
          router.push(getTabRoute(value as TabKey));
        }}
      >
        <TabsList className="flex justify-start gap-x-6 lg:gap-x-8 xl:gap-x-10 pb-2 bg-transparent max-w-full w-full">
          {tabs.map((tab) => {
            const label = t(`people.employees-page.profile.tabs.${tab}`);
            return (
              <TabsTrigger
                key={tab}
                value={tab}
                className={tabTriggerClass + " text-center"}
              >
                <span className="relative inline-block">
                  <span className="invisible font-bold">{label}</span>
                  <span className="absolute inset-0 flex items-center justify-center">
                    {label}
                  </span>
                </span>
              </TabsTrigger>
            );
          })}
        </TabsList>
      </Tabs>
    </div>
  );
};
