import React from "react";
import { Separator } from "@/components/ui/separator";
import { Tag } from "@/components/ui/tag";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { AttendancePeriod } from "@/types/employee/daySummary";
import { getTimePercentage, getUniqTimePeriods } from "@/utils/time-utils";
import {
  formatTimeDisplay,
  getDefaultPeriod,
  getPeriodInfo,
} from "../../../utils/time-card-utils";
import { TFunction } from "@/types";

type DesktopViewProps = {
  date?: string;
  startTime?: string;
  endTime?: string;
  total_work_minutes?: number;
  periods?: AttendancePeriod[];
  t: TFunction;
  locale: string;
};

export const DesktopView: React.FC<DesktopViewProps> = ({
  date,
  startTime,
  endTime,
  total_work_minutes,
  periods,
  t,
  locale,
}) => {
  const timeArray = getUniqTimePeriods(periods || []);

  return (
    <div className="hidden md:flex">
      <div className="flex flex-col justify-start">
        <span className="text-xs font-alex font-normal text-gray-500 mb-6 text-nowrap">
          {t("people.employees-page.profile.attendance.timeCard.checkIn")}
        </span>
        <span className="text-sm font-medium font-alex text-nowrap w-full text-center">
          {formatTimeDisplay(date, startTime, locale)}
        </span>
      </div>
      <Separator
        orientation="vertical"
        className="h-12 w-0.5 mx-6 my-[5.5px] bg-gray-100"
      />
      <div className="w-full flex flex-col justify-evenly items-center gap-2">
        <div className="w-full flex items-center justify-between">
          {timeArray.map((time, index) => (
            <div
              className="text-xs font-alex font-normal text-gray-500 mb-2 text-nowrap"
              key={index}
            >
              {time}
            </div>
          ))}
        </div>

        <div className="w-full flex items-center gap-1">
          {periods && periods.length > 0 ? (
            periods?.map((period, idx) => {
              const periodInfo = getPeriodInfo(period);
              const tooltipText = t(`${periodInfo.translationKey}`);
              return (
                <TooltipProvider key={idx}>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Tag
                        className="text-ellipsis w-full text-sm py-2 text-center text-black !rounded-lg font-alex font-medium overflow-hidden"
                        bordered={true}
                        style={{
                          background: periodInfo.background,
                          borderColor: periodInfo.border,
                          borderWidth: "1.5px",
                          width: `${getTimePercentage(
                            period.attributes.formatted_start_time,
                            period.attributes.formatted_end_time,
                            total_work_minutes || 0,
                          )}%`,
                        }}
                      >
                        {tooltipText}
                      </Tag>
                    </TooltipTrigger>
                    <TooltipContent className="bg-white text-black">
                      <p>{tooltipText}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              );
            })
          ) : (
            <div className="flex w-full flex-col">
              <Tag
                className="w-full text-sm py-2 text-center text-black !rounded-lg font-alex font-medium"
                bordered
                style={{
                  background: getPeriodInfo(getDefaultPeriod()).background,
                  borderColor: getPeriodInfo(getDefaultPeriod()).border,
                }}
              >
                {t(`${getPeriodInfo(getDefaultPeriod()).translationKey}`)}
              </Tag>
            </div>
          )}
        </div>
      </div>
      <Separator
        orientation="vertical"
        className="w-0.5 h-12 mx-6 my-[5.5px] bg-gray-100"
      />
      <div className="flex flex-col justify-start">
        <span className="text-xs font-alex font-normal text-gray-500 mb-6 text-nowrap">
          {t("people.employees-page.profile.attendance.timeCard.checkOut")}
        </span>
        <span className="text-sm font-medium font-alex text-nowrap w-full text-center">
          {formatTimeDisplay(date, endTime, locale)}
        </span>
      </div>
      {(total_work_minutes ?? 0) >= 0 && (
        <>
          <Separator
            orientation="vertical"
            className="w-0.5 h-12 mx-6 my-[5.5px] bg-gray-100"
          />
          <div className="flex flex-col justify-start">
            <span className="text-xs font-alex font-normal text-gray-500 mb-6 text-nowrap">
              {t("people.employees-page.profile.attendance.timeCard.period")}
            </span>
            <span className="text-sm font-medium font-alex text-nowrap">
              {total_work_minutes && total_work_minutes > 0
                ? (total_work_minutes / 60).toFixed(1)
                : 0}
              {t("people.employees-page.profile.attendance.timeCard.hour")}
            </span>
          </div>
        </>
      )}
    </div>
  );
};
