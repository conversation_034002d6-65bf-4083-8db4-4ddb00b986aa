"use client";

import { useLocale, useTranslations } from "next-intl";
import { useSearchParams } from "next/navigation";
import { useState } from "react";
import { RowSelectionState } from "@tanstack/react-table";

import { DataTable } from "@/components/table";
import { PaginationWithLinks } from "@/components/pagination-with-links";
import { employeeAttendanceColumns } from "./employee-attendance-columns";

import { Button } from "@/components/ui/button";
import { PlusCircle } from "lucide-react";
import dynamic from "next/dynamic";
import { useRouter } from "next/navigation";
import LoaderPortal from "@/components/loader/loader-portal";
import { useTableRegistration } from "@/hooks/useTableRegistration";
import { useAttendanceEvents } from "@/app/[locale]/_modules/people/hooks/attendance/useAttendanceEvents";
import { usePermission } from "@/contexts/PermissionContext";
import { PermissionEnum } from "@/enums/Permission";

const AttendanceEventFormModal = dynamic(
  () =>
    import("../../../attendance-events/table/AttendanceEventFormModal").then(
      (mod) => mod.AttendanceEventFormModal,
    ),
  {
    loading: () => (
      <LoaderPortal
        size={75}
        borderWidth={4}
        overlayColor="#000"
        overlayClassName="bg-opacity-50"
      />
    ),
    ssr: false,
  },
);

type Props = {
  employeeId: string;
};

export const EmployeeAttendanceTable = ({ employeeId }: Props) => {
  const t = useTranslations();
  const locale = useLocale();
  const searchParams = useSearchParams();
  const router = useRouter();
  const sort = searchParams.get("sort") ?? "-timestamp";
  const page = searchParams.get("page") ?? "1";
  const limit = searchParams.get("limit") ?? "5";
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const { hasPermission } = usePermission();

  const [isModalOpen, setIsModalOpen] = useState(false);

  useTableRegistration("employee-attendance", employeeAttendanceColumns);

  // Use the new attendance hook with employee filter
  const {
    attendanceList,
    employeeData,
    totalCount,
    isLoading,
    error,
    mutate,
    pagination,
    meta,
  } = useAttendanceEvents(
    Number(page),
    Number(limit),
    sort,
    employeeId,
    "employee-attendance",
  );

  const firstResult = pagination?.firstResult || 1;
  const lastResult = pagination?.lastResult || 1;

  // Permission checks for employee profile context
  // Only allow create button if user has explicit CREATE or MANAGE permissions
  // RECORD_ATTENDANCE_EVENT is for check-in/check-out, not admin event creation
  const canCreateAttendance =
    hasPermission(PermissionEnum.CREATE_ATTENDANCE_EVENT) ||
    hasPermission(PermissionEnum.MANAGE_ATTENDANCE_EVENT);

  const CreateButton = canCreateAttendance ? (
    <Button
      onClick={() => setIsModalOpen(true)}
      className="min-h-11 shadow-none flex items-center justify-center"
    >
      <PlusCircle className="h-5 w-5" />
      {/* {t("people.employees-page.profile.attendance-log.create-attendance-modal.title")} */}
    </Button>
  ) : null;

  return (
    <>
      <DataTable
        data={attendanceList || []}
        columns={employeeAttendanceColumns}
        tableContainerClass="min-h-[240px]"
        title={t("people.employees-page.profile.attendance.table.title")}
        meta={{
          t,
          locale,
          employeeData,
          onEdit: (event: any) => mutate(), // Refresh table data after edit
          onViewDetails: (event: any) => {
            // Handle view details if needed
          },
          isUpdating: false,
        }}
        rowSelection={rowSelection}
        onRowSelectionChange={setRowSelection}
        translationPrefix="people.attendance-events-page.table"
        tableId="employee-attendance"
        isLoading={isLoading}
        initialLimit={Number(limit)}
        error={error}
        dataCount={totalCount}
        hideSearch={false}
        hideFilters={false}
        headerActions={CreateButton}
        exportConfig={{
          entity: "attendance-events",
          enabled: true,
          enhanced: true,
          customFilters: {
            page: 1,
            limit: 1000, // Use a larger limit for exports
          },
          columnCategories: {
            id: "Basic",
            employee_id: "Employee Info",
            type: "Record Details",
            timestamp: "Time & Date",
            event_type: "Event Details",
            activity_type: "Event Details",
            location: "Location",
            notes: "Additional Info",
            inferred_type: "Additional Info",
            created_at: "Timestamps",
            updated_at: "Timestamps",
          },
          requiredColumns: ["id", "timestamp", "event_type"],
          excludeColumns: [
            "select",
            "actions",
            "employeeName",
            "requestDate",
            "status",
            "location",
          ],
          defaultSelectedColumns: [
            "id",
            "timestamp",
            "event_type",
            "activity_type",
          ],
        }}
      />

      <div className="w-full pt-[18px]">
        <PaginationWithLinks
          page={Number(page)}
          pageSize={Number(limit)}
          totalCount={totalCount}
          firstLastCounts={{
            firstCount: firstResult,
            lastCount: lastResult,
          }}
          isLoading={isLoading}
          isDisabled={!attendanceList?.length}
          pageSizeSelectOptions={{
            pageSizeOptions: [5, 10, 25, 30, 45, 50],
            pageSizeSearchParam: "limit",
          }}
        />
      </div>

      {isModalOpen && (
        <AttendanceEventFormModal
          isOpen={isModalOpen}
          employeeId={employeeId}
          mode="add"
          showEmployeeField={false}
          onClose={() => setIsModalOpen(false)}
          onSuccess={() => {
            setIsModalOpen(false);
            mutate();
          }}
        />
      )}
    </>
  );
};
