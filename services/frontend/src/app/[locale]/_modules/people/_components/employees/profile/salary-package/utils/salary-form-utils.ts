import { format } from "date-fns";
import { formatNumber } from "@/lib/format-number";
import { Locale } from "@/i18n/routing";
import { TSalaryPackageData } from "@/app/[locale]/_modules/people/type/salary-package";
import { SalaryPackageSchemaType } from "@/app/[locale]/_modules/people/schemas/salaryPackageSchema";
import { LANGUAGES } from "@/constants/enum";

/**
 * Normalize values for comparison (removes commas, trims strings, formats dates)
 */
export const normalizeValue = (val: unknown): string => {
  if (typeof val === "string") {
    return val.replace(/,/g, "").trim();
  }
  if (val instanceof Date) {
    return format(val, "yyyy-MM-dd");
  }
  return String(val || "");
};

/**
 * Calculate total package value from form values
 */
export const calculateTotalPackage = (
  values: Partial<SalaryPackageSchemaType>,
): number => {
  const parseAmount = (val?: string) =>
    parseFloat(val?.replace(/,/g, "") || "0") || 0;

  return (
    parseAmount(values.base_salary) +
    parseAmount(values.transportation_allowance) +
    parseAmount(values.other_allowances)
  );
};

/**
 * Get default form values based on mode and existing package
 */
export const getDefaultFormValues = (
  mode: "create" | "update",
  employeeId: string,
  existingPackage?: TSalaryPackageData | null,
  locale?: Locale,
): SalaryPackageSchemaType => {
  if (mode === "create") {
    return {
      employee_id: employeeId,
      base_salary: "",
      transportation_allowance: "",
      other_allowances: "",
      effective_date: new Date(),
      end_date: undefined, // Optional field for create mode
      notes: "",
    };
  }

  const packageAttrs = existingPackage?.attributes;
  const effectiveDate = packageAttrs?.effective_date
    ? new Date(packageAttrs.effective_date)
    : new Date();

  const endDate = packageAttrs?.end_date
    ? new Date(packageAttrs.end_date)
    : undefined;

  return {
    employee_id: employeeId,
    base_salary: formatNumber(
      Number(packageAttrs?.base_salary || "00.00"),
      locale || LANGUAGES.ENGLISH,
    ),
    transportation_allowance: formatNumber(
      Number(packageAttrs?.transportation_allowance || "00.00"),
      locale || LANGUAGES.ENGLISH,
    ),
    other_allowances: formatNumber(
      Number(packageAttrs?.other_allowances || "00.00"),
      locale || LANGUAGES.ENGLISH,
    ),
    effective_date: effectiveDate,
    end_date: endDate,
    notes: packageAttrs?.notes || "",
  };
};

/**
 * Prepare form data for submission
 * Omits empty optional fields from being sent to the backend
 */
export const prepareFormDataForSubmission = (
  data: SalaryPackageSchemaType,
  mode: "create" | "update",
  existingPackageId?: string,
): FormData => {
  const formData = new FormData();

  // Only add ID for update mode
  if (mode === "update" && existingPackageId) {
    formData.append("id", existingPackageId);
  }

  // Required fields - always send these
  const requiredFields = ["employee_id", "base_salary", "effective_date"];

  Object.entries(data).forEach(([key, value]) => {
    // Skip null/undefined
    if (value === null || value === undefined) return;

    // Skip empty strings for optional fields only
    if (!requiredFields.includes(key) && value === "") return;

    // Process the value based on field type
    let processedValue: string;

    if (key === "effective_date" && value instanceof Date) {
      processedValue = format(value, "dd-MM-yyyy");
    } else if (key === "end_date" && value instanceof Date) {
      processedValue = format(value, "dd-MM-yyyy");
    } else if (typeof value === "string" &&
               ["base_salary", "transportation_allowance", "other_allowances"].includes(key)) {
      processedValue = value.replace(/,/g, ""); // Remove commas from numbers
    } else {
      processedValue = String(value);
    }

    // Only append if we have a value (extra check for cleaned numeric fields)
    if (requiredFields.includes(key) || processedValue !== "") {
      formData.append(key, processedValue);
    }
  });

  return formData;
};
