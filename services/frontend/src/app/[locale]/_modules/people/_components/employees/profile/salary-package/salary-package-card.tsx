"use client";

import React from "react";
import { useTranslations, useLocale } from "next-intl";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import FormattedCurrency from "@/components/formatted-currency";
import { TSalaryPackageData } from "../../../../type/salary-package";
import { formatDate } from "@/lib/dateFormatter";
import { Locale } from "@/i18n/routing";
import { cn } from "@/lib/utils";
import Image from "next/image";
import ApprovalWorkFlow from "../../../approval-workflow";
import { TIncludedEmployee } from "../../../../type/employee-leaves";
import { ApprovalRequestData } from "../../../../type/approval-request";
import { ArrowRight } from "lucide-react";
import { getSalaryPackageStatusStyles } from "../../../../utils/salary-package-styles";

type SalaryPackageCardProps = {
  salaryPackage: TSalaryPackageData | null;
  currentApprovedPackage?: TSalaryPackageData | null;
  included?: (TIncludedEmployee | ApprovalRequestData)[];
  onShowWorkflowModal?: (salaryPackage: TSalaryPackageData) => void;
};

const CurrencyItem = ({
  label,
  amount,
  statusStyles,
  className,
}: {
  label: string;
  amount: number;
  className?: string;
  statusStyles: ReturnType<typeof getSalaryPackageStatusStyles>;
}) => (
  <Card
    className={`shadow-sm max-h-[104px] transition-all duration-200 ${statusStyles.containerClass} ${className}`}
  >
    <CardContent className="text-start p-5">
      <p
        className={`font-medium leading-6 text-sm mb-2 ${statusStyles.textClass}`}
      >
        {label}:
      </p>
      <FormattedCurrency
        wrapperStyle="justify-start"
        amount={amount}
        numberStyle={`text-xl font-semibold leading- ${statusStyles.valueClass}`}
        currencyStyle="hidden"
      />
    </CardContent>
  </Card>
);

// Component for showing comparison between old and new values
const CurrencyComparisonItem = ({
  label,
  oldAmount,
  newAmount,
  statusStyles,
  className,
}: {
  label: string;
  oldAmount: number;
  newAmount: number;
  className?: string;
  statusStyles: ReturnType<typeof getSalaryPackageStatusStyles>;
}) => (
  <Card
    className={`shadow-sm max-h-[104px] transition-all duration-200 ${statusStyles.containerClass} ${className}`}
  >
    <CardContent className="text-start p-5">
      <p
        className={`font-medium leading-6 text-sm mb-2 ${statusStyles.textClass}`}
      >
        {label}:
      </p>
      <div className="flex items-center gap-2">
        <FormattedCurrency
          wrapperStyle="justify-start"
          amount={oldAmount}
          numberStyle="text-sm text-gray-500"
          currencyStyle="hidden"
        />
        <span>
          <ArrowRight className="h-4 w-4 rtl:rotate-180 text-amber-600" />
        </span>
        <FormattedCurrency
          wrapperStyle="justify-start"
          amount={newAmount}
          numberStyle="text-xl font-semibold text-amber-600"
          currencyStyle="hidden"
        />
      </div>
    </CardContent>
  </Card>
);

export const SalaryPackageCard = ({
  salaryPackage,
  currentApprovedPackage,
  included = [],
  onShowWorkflowModal,
}: SalaryPackageCardProps) => {
  const t = useTranslations();
  const locale = useLocale() as Locale;
  //(new package with old package for comparison)
  // Combined mode when we have both a new package (editable or pending) and an old approved package
  const isCombinedMode =
    (salaryPackage?.attributes.editable === true ||
      salaryPackage?.attributes.status === "pending_approval") &&
    currentApprovedPackage?.attributes.status === "approved";

  if (!salaryPackage) {
    return (
      <div>
        <div className="relative w-80 h-40 mx-auto">
          <Image
            fill
            priority={true}
            src="/images/EmptyState.png"
            className="object-cover"
            alt="image descripe empty salary package"
          />
        </div>
        <div className="text-center mt-2">
          <p className="text-black text-sm leading-6 font-medium">
            {t("people.salary-package.no-package")}
          </p>
          <p className="text-gray-500 text-xs mt-2">
            {t("people.salary-package.no-package-description")}
          </p>
        </div>
      </div>
    );
  }

  const { attributes } = salaryPackage;

  // Helper function to determine the display status
  const getDisplayStatus = () => {
    // If the package is approved and active, show as "active"
    if (attributes.status === "approved" && attributes.active) {
      return "active";
    }
    // Otherwise, use the actual status
    return attributes.status;
  };

  const displayStatus = getDisplayStatus();

  // Current (new) package values
  const baseSalary = Number(attributes.base_salary) || 0;
  const transportationAllowance =
    Number(attributes.transportation_allowance) || 0;
  const otherAllowances = Number(attributes.other_allowances) || 0;
  const totalPackage = baseSalary + transportationAllowance + otherAllowances;

  // Previous (approved) package values for comparison
  const oldBaseSalary = currentApprovedPackage
    ? Number(currentApprovedPackage.attributes.base_salary) || 0
    : 0;
  const oldDate = currentApprovedPackage
    ? currentApprovedPackage.attributes.effective_date
    : null;

  const oldTransportationAllowance = currentApprovedPackage
    ? Number(currentApprovedPackage.attributes.transportation_allowance) || 0
    : 0;
  const oldOtherAllowances = currentApprovedPackage
    ? Number(currentApprovedPackage.attributes.other_allowances) || 0
    : 0;

  // Get status-based styles
  const statusStyles = getSalaryPackageStatusStyles(attributes.status);

  return (
    <div className="flex flex-col gap-4">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {/* Total Package Summary */}
        <Card
          className={`rounded-2xl shadow-sm relative transition-all duration-200 ${statusStyles.containerClass}`}
        >
          <CardContent className="p-5 grid grid-cols-2 justify-between gap-y-6">
            <div className="flex flex-col items-start gap-4">
              <div className="flex items-center gap-2 mb-2">
                <p
                  className={`text-xs sm:text-sm mt-0.5 ${statusStyles.textClass}`}
                >
                  {t("people.salary-package.total-package")}
                </p>
              </div>
              {
                <FormattedCurrency
                  wrapperStyle="justify-start"
                  amount={totalPackage}
                  numberStyle={`font-bold text-2xl ${statusStyles.valueClass} ${isCombinedMode ? "text-secondary" : ""}`}
                  currencyStyle={`${statusStyles.valueClass} ${isCombinedMode ? "text-secondary" : ""}`}
                />
              }
            </div>

            {/* Effective Period */}
            <div className="flex flex-col justify-start gap-4">
              <p
                className={`text-xs sm:text-sm mt-0.5 ${statusStyles.textClass}`}
              >
                {t("people.salary-package.effective-period")}
              </p>

              {isCombinedMode ? (
                <div className="flex items-center gap-1">
                  <p
                    className={`text-sm sm:text-base self-start font-semibold text-gray-500`}
                  >
                    {formatDate(oldDate, locale)}
                  </p>
                  <span>
                    <ArrowRight className="h-4 w-4 rtl:rotate-180 text-amber-600" />
                  </span>
                  <p
                    className={cn(
                      "text-sm sm:text-base self-start font-semibold",
                      statusStyles.valueClass,
                      isCombinedMode && "text-amber-600",
                    )}
                  >
                    {formatDate(attributes.effective_date, locale)}
                  </p>
                </div>
              ) : (
                <p
                  className={`text-sm sm:text-base self-start font-semibold ${statusStyles.valueClass}`}
                >
                  {formatDate(attributes.effective_date, locale)}
                </p>
              )}
            </div>

            <div className="flex items-center gap-2">
              {/* Show approval workflow only if there's an approval request AND status is pending */}
              {salaryPackage.relationships?.approval_request?.data &&
              salaryPackage.attributes.status === "pending_approval" ? (
                <ApprovalWorkFlow
                  item={salaryPackage}
                  included={included}
                  onShowWorkflowModal={onShowWorkflowModal}
                />
              ) : (
                <div className="flex flex-col items-start gap-2">
                  <p
                    className={`font-medium leading-6 text-sm mb-2 ${statusStyles.textClass}`}
                  >
                    {t(`common.form.salary.status.label`)}:
                  </p>
                  <Badge
                    className={cn(
                      "text-xs h-8 min-w-20 w-fit flex justify-center items-center",
                      statusStyles.badgeClass,
                    )}
                  >
                    {t(`people.salary-package.status.${displayStatus}`)}
                  </Badge>
                </div>
              )}
            </div>
            <div className="">
              <p
                className={`font-medium leading-6 text-sm mb-2 ${statusStyles.textClass}`}
              >
                {t("common.form.salary.notes.label")}:
              </p>
              <p className={`rounded-lg ${statusStyles.valueClass}`}>
                {attributes.notes}
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Breakdown: Base + Allowances */}
        <div className="grid grid-cols-2 gap-4">
          {isCombinedMode ? (
            <>
              <CurrencyComparisonItem
                label={t("common.form.salary.base_salary.label")}
                oldAmount={oldBaseSalary}
                newAmount={baseSalary}
                statusStyles={statusStyles}
                className="col-span-2"
              />
              <CurrencyComparisonItem
                label={t("common.form.salary.transportation_allowance.label")}
                oldAmount={oldTransportationAllowance}
                newAmount={transportationAllowance}
                statusStyles={statusStyles}
              />
              <CurrencyComparisonItem
                label={t("common.form.salary.other_allowances.label")}
                oldAmount={oldOtherAllowances}
                newAmount={otherAllowances}
                statusStyles={statusStyles}
              />
            </>
          ) : (
            <>
              <CurrencyItem
                label={t("common.form.salary.base_salary.label")}
                amount={baseSalary}
                statusStyles={statusStyles}
                className="col-span-2"
              />
              <CurrencyItem
                label={t("common.form.salary.transportation_allowance.label")}
                amount={transportationAllowance}
                statusStyles={statusStyles}
              />
              <CurrencyItem
                label={t("common.form.salary.other_allowances.label")}
                amount={otherAllowances}
                statusStyles={statusStyles}
              />
            </>
          )}
        </div>
      </div>
    </div>
  );
};
