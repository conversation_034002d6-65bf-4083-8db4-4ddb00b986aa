"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useTranslations } from "next-intl";
import { Table, Grid3X3 } from "lucide-react";
import { cn } from "@/lib/utils";
import { FatRows, Grid2 } from "../../../../../../../public/images/icons";

type ViewMode = "table" | "cards";

type ViewToggleProps = {
  currentView: ViewMode;
  onViewChange: (view: ViewMode) => void;
  className?: string;
};

export default function ViewToggle({
  currentView,
  onViewChange,
  className,
}: ViewToggleProps) {
  const t = useTranslations();

  return (
    <div
      className={cn(
        "flex items-center gap-1 border-none rounded-xl p-2 bg-gray-100 max-h-12",
        className,
      )}
    >
      <Button
        variant={"ghost"}
        size="sm"
        onClick={() => onViewChange("cards")}
        className={cn(
          "h-8 w-8 p-0 transition-colors",
          currentView === "cards"
            ? "bg-white text-white"
            : "text-gray-500 hover:text-gray-700",
        )}
        title={t("people.devices-page.view-toggle.cards")}
      >
        <Grid2 className="!h-5 !w-5" />
      </Button>
      <Button
        variant={"ghost"}
        size="sm"
        onClick={() => onViewChange("table")}
        className={cn(
          "h-8 w-8 p-0 transition-colors",
          currentView === "table"
            ? "bg-white text-white"
            : "text-gray-500 hover:text-gray-700",
        )}
        title={t("people.devices-page.view-toggle.table")}
      >
        <FatRows className="!h-5 !w-5" />
      </Button>
    </div>
  );
}
