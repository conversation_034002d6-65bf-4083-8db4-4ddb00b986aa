"use client";

import { DialogDescription, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { useLocale, useTranslations } from "next-intl";
import {
  getMainCategoryTranslation,
  mapRequestStatusToCanonical,
} from "@/constants/translations-mapping";
import { Eye } from "../../../../../../../public/images/icons";
import ResponsiveDialog from "@/components/responsive-dialog";
import { cn } from "@/lib/utils";
import { formatDate } from "@/lib/dateFormatter";
import { formatFileSize } from "@/lib/formateFileSize";
import { Locale } from "@/i18n/routing";
import { MAIN_CATEGORY } from "../../enum";
import { LeaveDetail, TIncludedEmployee } from "../../type/employee-leaves";
import { REQUEST_STATUS } from "@/constants/enum";
import { TEmployee } from "../../type/employee";
import { Loader } from "lucide-react";
import { useLeaveAttachments } from "../../hooks/useLeaveAttachments";
import { getFileIcon } from "@/lib/getFileIcon";
import FilePreview from "@/components/file-preview";
import { useState } from "react";
import { useApprovalWorkflow } from "../../hooks/useApprovalWorkflow";
import ApprovalStepsIndicator from "@/components/approval-steps-indicator";
import { ApprovalRequestData } from "../../type/approval-request";
import { useApprovalPermissions } from "../../hooks/useApprovalPermissions";
import { getIncludedItem } from "../../utils/included-data/getIncludedItem";

type SelectedRequest = {
  employee: TEmployee | null;
  leaves: LeaveDetail | null;
};

type TEmployeeRequestDetails = {
  showRequestData: boolean;
  setShowRequestData: React.Dispatch<React.SetStateAction<boolean>>;
  employeeRequestData: SelectedRequest;
  includedData: (TIncludedEmployee | ApprovalRequestData)[];
  onAcceptRequest: (data: LeaveDetail) => void;
  onRejectRequest: (data: LeaveDetail) => void;
  onWithdrawRequest: (data: LeaveDetail) => void;
  isApproving: boolean;
  isRejecting: boolean;
  isWithdrawing: boolean;
};

const EmployeeRequestsDetailsDialog = ({
  showRequestData,
  setShowRequestData,
  employeeRequestData,
  includedData,
  onAcceptRequest,
  onRejectRequest,
  onWithdrawRequest,
  isApproving = false,
  isRejecting = false,
  isWithdrawing = false,
}: TEmployeeRequestDetails) => {
  return (
    <ResponsiveDialog
      open={showRequestData}
      onOpenChange={setShowRequestData}
      closeBtnStyle="top-[22px]"
      header={
        <RequestDetailsHeader employeeRequestData={employeeRequestData} />
      }
    >
      <RequestDetailsContent
        employeeRequestData={employeeRequestData}
        includedData={includedData}
        onAcceptRequest={onAcceptRequest}
        onRejectRequest={onRejectRequest}
        onWithdrawRequest={onWithdrawRequest}
        setShowRequestData={setShowRequestData}
        isApproving={isApproving}
        isRejecting={isRejecting}
        isWithdrawing={isWithdrawing}
      />
    </ResponsiveDialog>
  );
};

const RequestDetailsHeader = ({
  employeeRequestData,
}: {
  employeeRequestData: SelectedRequest;
}) => {
  const t = useTranslations();
  const status = mapRequestStatusToCanonical(
    employeeRequestData.leaves?.attributes.status as REQUEST_STATUS,
  );

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case "approved":
        return "bg-green-100 text-green-800";
      case "rejected":
        return "bg-red-100 text-red-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "waiting":
        return "bg-blue-100 text-blue-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="text-start">
      <DialogDescription className="sr-only">
        {t("people.leaves-requests-page.request-details.description")}
      </DialogDescription>
      <div className="px-6 py-[22px] border-b ">
        <div className="font-semibold flex items-center justify-start gap-3">
          <DialogTitle className="!m-0 text-[18px] font-semibold leading-7">
            {t("people.leaves-requests-page.request-details.title")}
          </DialogTitle>
          <span className="md:me-12">
            <span
              className={cn(
                "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",
                getStatusBadgeColor(status),
              )}
            >
              {t(`common.status.request-status.${status}`)}
            </span>
          </span>
        </div>
      </div>
    </div>
  );
};

const RequestDetailsContent = ({
  employeeRequestData: data,
  includedData,
  onAcceptRequest,
  onRejectRequest,
  onWithdrawRequest,
  setShowRequestData,
  isApproving,
  isRejecting,
  isWithdrawing,
}: {
  employeeRequestData: SelectedRequest;
  includedData: (TIncludedEmployee | ApprovalRequestData)[];
  onAcceptRequest: (data: LeaveDetail) => void;
  onRejectRequest: (data: LeaveDetail) => void;
  onWithdrawRequest: (data: LeaveDetail) => void;
  setShowRequestData: React.Dispatch<React.SetStateAction<boolean>>;
  isApproving: boolean;
  isRejecting: boolean;
  isWithdrawing: boolean;
}) => {
  const t = useTranslations();

  // Get approval workflow data
  const { steps, currentStepId } = useApprovalWorkflow(
    data.leaves,
    includedData,
  );

  // Get approval request data for permissions
  const approvalRequestId =
    data.leaves?.relationships?.approval_request?.data?.id;
  const approvalRequest = approvalRequestId
    ? (getIncludedItem(includedData, "approval_request", approvalRequestId) as
        | ApprovalRequestData
        | undefined)
    : undefined;

  // Use approval permissions hook
  const { canAct, hasActed, userAction } =
    useApprovalPermissions(approvalRequest);

  const leaveId = data.leaves?.id;
  const status = data?.leaves?.attributes.status;
  const isEndDateValid = data?.leaves?.attributes.end_date
    ? new Date(data.leaves.attributes.end_date) >= new Date()
    : false;

  // Fetch attachments for this leave request
  const {
    attachments,
    isLoading: isLoadingAttachments,
    error: isErrorLoadingAttachments,
  } = useLeaveAttachments(leaveId || "");

  // Status checks
  const isWaiting = status === "waiting" || status === "pending";
  const isApproved = status === "approved";
  const canWithdraw =
    status === "waiting" ||
    (status === "pending" && isEndDateValid) ||
    (isApproved && isEndDateValid);

  const locale: Locale = useLocale() as Locale;

  // State for file preview
  const [previewFile, setPreviewFile] = useState<{
    isOpen: boolean;
    fileName: string;
    fileUrl: string;
    fileType: string;
  }>({
    isOpen: false,
    fileName: "",
    fileUrl: "",
    fileType: "",
  });

  // Function to open file preview
  const openFilePreview = (file: {
    fileName: string;
    fileUrl: string;
    fileType: string;
  }) => {
    setPreviewFile({
      isOpen: true,
      ...file,
    });
  };

  // Function to close file preview
  const closeFilePreview = () => {
    setPreviewFile((prev) => ({ ...prev, isOpen: false }));
  };

  return (
    <div className="text-start">
      <div className={cn("space-y-4", isWaiting ? "pb-24" : "pb-10")}>
        <div className="flex flex-col gap-1 border-b border-gray-100 w-full pb-4">
          <DetailField
            label={t(
              "people.leaves-requests-page.request-details.employee-name",
            )}
            value={data?.employee?.name}
          />
          <DetailField
            label={t(
              "people.leaves-requests-page.request-details.request-type",
            )}
            value={getMainCategoryTranslation(
              data?.leaves?.type as MAIN_CATEGORY,
              t,
            )}
          />
        </div>
        <div className="flex justify-between items-center">
          <div className="flex flex-col gap-1 border-b border-gray-100 w-full pb-4">
            <DetailField
              label={t(
                "people.leaves-requests-page.request-details.start-date",
              )}
              value={
                data?.leaves?.attributes.start_date &&
                formatDate(data?.leaves?.attributes.start_date, locale)
              }
            />
          </div>
          <div className="flex flex-col gap-1 border-b border-gray-100 w-full pb-4">
            <DetailField
              label={t("people.leaves-requests-page.request-details.end-date")}
              value={
                data?.leaves?.attributes.end_date &&
                formatDate(data?.leaves?.attributes.end_date, locale)
              }
            />
          </div>
        </div>

        <div className="flex flex-col gap-1">
          <DetailField
            label={t(
              "people.leaves-requests-page.request-details.request-reason",
            )}
            value={
              data?.leaves?.attributes.reason?.trim()
                ? data.leaves.attributes.reason
                : t("people.leaves-requests-page.request-details.no-reason")
            }
            className="border-none"
          />
        </div>

        {/* Approval Steps Indicator */}
        {steps && steps.length > 0 && (
          <div className="flex flex-col gap-1 border-b border-gray-100 w-full pb-4">
            <ApprovalStepsIndicator
              steps={steps}
              currentStepId={currentStepId}
              status={
                data?.leaves?.attributes.status === "approved"
                  ? "approved"
                  : data?.leaves?.attributes.status === "rejected"
                    ? "rejected"
                    : "pending"
              }
            />
          </div>
        )}

        <div className="flex flex-col gap-1">
          <span className="text-gray-500">
            {t("people.leaves-requests-page.request-details.attachments")}
          </span>
          <div className="flex flex-col gap-4">
            {isErrorLoadingAttachments ? (
              <div className="flex items-center justify-center py-4">
                <p className="text-error">
                  {isErrorLoadingAttachments.message}
                </p>
              </div>
            ) : isLoadingAttachments ? (
              <div className="flex items-center justify-center py-4">
                <Loader className="h-6 w-6 animate-spin text-gray-500" />
              </div>
            ) : attachments && attachments.length > 0 ? (
              attachments.map((file, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between bg-gray-100 p-3 gap-1 rounded-lg"
                >
                  <div className="flex items-center gap-2.5">
                    <div>
                      {getFileIcon({
                        fileType: file.attributes.content_type,
                        fileName: file.attributes.filename,
                        fileUrl: file.attributes.url,
                        className: "w-10 h-10 object-contain",
                      })}
                    </div>
                    <div className="flex flex-col items-start gap-0.5">
                      <span className="font-medium text-secondary text-sm">
                        {file.attributes.filename}
                      </span>
                      <span className="text-gray-500 text-xs">
                        {formatFileSize(Number(file.attributes.bytes_size), t)}
                      </span>
                    </div>
                  </div>
                  <Button
                    onClick={() =>
                      openFilePreview({
                        fileName: file.attributes.filename,
                        fileUrl: file.attributes.url,
                        fileType: file.attributes.content_type,
                      })
                    }
                    variant="ghost"
                    className="h-5 w-5 p-0 min-w-[20px] min-h-[20px] flex items-center justify-center hover:bg-transparent focus-visible:ring-0 active:bg-transparent"
                  >
                    <Eye className="stroke-gray-400 !w-6 !h-6" />
                  </Button>
                </div>
              ))
            ) : (
              t("people.leaves-requests-page.request-details.no-attachments")
            )}
          </div>
        </div>
        <div className="flex flex-col gap-1">
          <span className="inline-block text-gray-500 text-sm font-medium leading-5">
            {t("people.leaves-requests-page.request-details.note")}
          </span>
          <p className="font-medium text-gray-800">
            {t("people.leaves-requests-page.request-details.no-notes")}
          </p>
        </div>
      </div>

      {/* Action buttons */}
      {isWaiting && (
        <div className="fixed bottom-0 left-0 w-full px-4 sm:px-6 sm:py-6 h-20 sm:min-h-[85px] rounded-none sm:rounded-[18px] bg-white flex flex-row items-center justify-between gap-4 sm:gap-6 rounded-t-none">
          <Button
            onClick={() => onAcceptRequest(data.leaves!)}
            className="w-full h-12 text-white font-semibold disabled:opacity-50"
            disabled={isApproving || isRejecting || !canAct || hasActed}
            title={
              hasActed && userAction === "approve"
                ? t("people.leaves-requests-page.actions.alreadyApproved")
                : !canAct
                  ? t("people.leaves-requests-page.actions.notAuthorized")
                  : undefined
            }
          >
            {isApproving ? (
              <div className="flex items-center justify-center gap-2">
                <Loader className="h-4 w-4 animate-spin" />
              </div>
            ) : (
              <>
                {hasActed && userAction === "approve" ? "✓ " : ""}
                {t("people.leaves-requests-component.actions.accept")}
              </>
            )}
          </Button>
          <Button
            onClick={() => onRejectRequest(data.leaves!)}
            variant="outline"
            className="w-full h-12 font-semibold disabled:opacity-50"
            disabled={isApproving || isRejecting || !canAct || hasActed}
            title={
              hasActed && userAction === "reject"
                ? t("people.leaves-requests-page.actions.alreadyRejected")
                : !canAct
                  ? t("people.leaves-requests-page.actions.notAuthorized")
                  : undefined
            }
          >
            {isRejecting ? (
              <div className="flex items-center justify-center gap-2">
                <Loader className="h-4 w-4 animate-spin" />
              </div>
            ) : (
              <>
                {hasActed && userAction === "reject" ? "✓ " : ""}
                {t("people.leaves-requests-component.actions.reject")}
              </>
            )}
          </Button>
        </div>
      )}

      {isApproved && canWithdraw && (
        <div className="flex gap-2 items-center">
          <Button
            variant="outline"
            onClick={() => onWithdrawRequest(data.leaves!)}
            className="w-1/2 h-12 font-semibold text-white bg-orange-700"
            disabled={isWithdrawing}
          >
            {isWithdrawing && isApproving ? (
              <div className="flex items-center justify-center gap-2">
                <Loader className="h-4 w-4 animate-spin" />
              </div>
            ) : (
              t("people.employees-page.profile.leaves.table.actions.withdraw")
            )}
          </Button>
          <Button
            variant="outline"
            onClick={() => setShowRequestData(false)}
            className="w-1/2 h-12 font-semibold text-black"
          >
            {t("common.buttonText.cancel")}
          </Button>
        </div>
      )}
      {/* File Preview Component */}
      <FilePreview
        fileName={previewFile.fileName}
        fileUrl={previewFile.fileUrl}
        fileType={previewFile.fileType}
        isOpen={previewFile.isOpen}
        onClose={closeFilePreview}
      />
    </div>
  );
};

export default EmployeeRequestsDetailsDialog;

const DetailField = ({
  label,
  value,
  className = "",
}: {
  label: string;
  value: React.ReactNode;
  className?: string;
}) => (
  <div
    className={`flex flex-col gap-1 border-b border-gray-100 w-full pb-4 ${className}`}
  >
    <span className="inline-block text-gray-500 text-sm font-medium leading-5">
      {label}
    </span>
    <span className="font-normal text-base leading-5 text-black">{value}</span>
  </div>
);
