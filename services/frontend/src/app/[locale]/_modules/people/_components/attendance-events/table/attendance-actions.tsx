"use client";

import React, { useState } from "react";
import { Row } from "@tanstack/react-table";
import { useTranslations, useLocale } from "next-intl";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { MoreVertical, Edit, Eye, Trash2 } from "lucide-react";
import { AttendanceEvent } from "../../../type/employee-leaves";
import { AttendanceEventFormModal } from "./AttendanceEventFormModal";
import ConfirmDeleteDialog from "@/components/confirm-delete-dialog";
import ViewAttendanceDialog from "./ViewAttendanceDialog";
import { usePermission } from "@/contexts/PermissionContext";
import { PermissionEnum } from "@/enums/Permission";
import { deleteAttendance } from "../../../actions/attendance";
import { useToastMessage } from "@/hooks/use-toast-message";
import { findEmployeeById } from "../../../utils/find-employee";
import { useCurrentEmployee } from "@/hooks/people/getCurrentEmployee";
import { LANGUAGES } from "@/constants/enum";

type AttendanceActionsProps = {
  row: Row<AttendanceEvent>;
  onEdit?: (event: AttendanceEvent) => void;
  onViewDetails?: (event: AttendanceEvent) => void;
  isUpdating?: boolean;
  employeeData?: any[];
};

export const AttendanceActions = ({
  row,
  onEdit,
  onViewDetails,
  isUpdating = false,
  employeeData = [],
}: AttendanceActionsProps) => {
  const t = useTranslations();
  const locale = useLocale();
  const { hasPermission } = usePermission();
  const { profile: currentEmployee } = useCurrentEmployee();
  const { showToast } = useToastMessage();
  const isAr = locale === "ar";
  const [isOpen, setIsOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Get attendance event and employee info
  const attendanceEvent = row.original;
  const eventEmployeeId = attendanceEvent.relationships.employee.data.id;
  const currentEmployeeId = currentEmployee?.id;
  const isOwnEvent = currentEmployeeId === eventEmployeeId;

  // Permission checks
  const canViewAll = hasPermission(PermissionEnum.READ_ATTENDANCE_EVENT);
  const canViewOwn = hasPermission(PermissionEnum.READ_OWN_ATTENDANCE_EVENT);
  const canUpdateAll = hasPermission(PermissionEnum.UPDATE_ATTENDANCE_EVENT);
  const canDeleteAll = hasPermission(PermissionEnum.DESTROY_ATTENDANCE_EVENT);
  const canManageAll = hasPermission(PermissionEnum.MANAGE_ATTENDANCE_EVENT);

  // Determine available actions
  const canView = canViewAll || (canViewOwn && isOwnEvent) || canManageAll;
  const canEdit = canUpdateAll || canManageAll;
  const canDelete = canDeleteAll || canManageAll;

  // If no actions are available, don't render the dropdown
  if (!canView && !canEdit && !canDelete) {
    return null;
  }

  const actionsHandlers = {
    handleView: () => {
      setIsOpen(false);
      setIsViewModalOpen(true);
    },
    handleEdit: () => {
      setIsOpen(false);
      setIsEditModalOpen(true);
    },
    handleDelete: () => {
      setIsOpen(false);
      setIsDeleteModalOpen(true);
    },
    closeEditModal: () => {
      setIsEditModalOpen(false);
    },
    closeViewModal: () => {
      setIsViewModalOpen(false);
    },
    closeDeleteModal: () => {
      setIsDeleteModalOpen(false);
    },
    onEditSuccess: () => {
      setIsEditModalOpen(false);
      onEdit?.(attendanceEvent);
    },
    onDeleteSuccess: () => {
      setIsDeleteModalOpen(false);
      onEdit?.(attendanceEvent); // Refresh table after delete
    },
    handleConfirmDelete: async () => {
      setIsDeleting(true);
      try {
        const result = await deleteAttendance(attendanceEvent.id);

        if (result.success) {
          showToast("success", result.success);
          onEdit?.(attendanceEvent); // Refresh table after delete
          setIsDeleteModalOpen(false);
        } else if (result.error) {
          showToast("error", result.error);
        }
      } catch (error) {
        showToast("error", "Failed to delete attendance record");
      } finally {
        setIsDeleting(false);
      }
    },
  };

  return (
    <>
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className="!p-2 border hover:border-black/50 rounded-lg group transition-colors"
            disabled={isUpdating}
          >
            <span className="sr-only">Open menu</span>
            <MoreVertical className="w-4 text-gray-500 group-hover:text-black" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align={isAr ? "start" : "end"}>
          {canView && (
            <DropdownMenuItem
              dir={locale === LANGUAGES.ARABIC ? "rtl" : "ltr"}
              onClick={actionsHandlers.handleView}
              className="flex items-center cursor-pointer"
            >
              <Eye className="me-2 h-4 w-4" />
              {t("people.attendance-events-page.table.actions.view")}
            </DropdownMenuItem>
          )}
          {canEdit && (
            <DropdownMenuItem
              dir={locale === LANGUAGES.ARABIC ? "rtl" : "ltr"}
              onClick={actionsHandlers.handleEdit}
              className="flex items-center cursor-pointer"
            >
              <Edit className="me-2 h-4 w-4" />
              {t("people.attendance-events-page.table.actions.edit")}
            </DropdownMenuItem>
          )}
          {canDelete && (
            <DropdownMenuItem
              dir={locale === LANGUAGES.ARABIC ? "rtl" : "ltr"}
              onClick={actionsHandlers.handleDelete}
              className="flex items-center text-error hover:text-destructive cursor-pointer"
            >
              <Trash2 className="me-2 h-4 w-4" />
              {t("people.attendance-events-page.table.actions.delete")}
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Edit Attendance Event Modal */}
      <AttendanceEventFormModal
        isOpen={isEditModalOpen}
        onClose={actionsHandlers.closeEditModal}
        onSuccess={actionsHandlers.onEditSuccess}
        mode="edit"
        attendanceEvent={attendanceEvent}
        showEmployeeField={true}
      />

      {/* View Attendance Event Modal */}
      <ViewAttendanceDialog
        attendanceEvent={attendanceEvent}
        employeeData={employeeData}
        isOpen={isViewModalOpen}
        onClose={actionsHandlers.closeViewModal}
      />

      {/* Delete Attendance Event Modal */}
      <ConfirmDeleteDialog
        isOpen={isDeleteModalOpen}
        onClose={actionsHandlers.closeDeleteModal}
        onConfirmDelete={actionsHandlers.handleConfirmDelete}
        isDeleting={isDeleting}
        itemName={`${findEmployeeById(employeeData, attendanceEvent.relationships.employee.data.id)?.name || "Employee"} - ${attendanceEvent.attributes.event_type}`}
        titleKey="people.attendance-events-page.delete.confirm-title"
        descriptionKey="people.attendance-events-page.delete.confirm-message"
        itemDetails={[
          {
            label: t("people.attendance-events-page.table.columns.event_type"),
            value: t(
              `people.attendance-events-page.table.status.${attendanceEvent.attributes.event_type.toLowerCase()}`,
            ),
            className: "text-blue-600",
          },
          {
            label: t(
              "people.attendance-events-page.table.columns.activity_type",
            ),
            value: t(
              `people.employees-page.profile.attendance.timeCard.periodKeys.${attendanceEvent.attributes.activity_type || "regular"}`,
            ),
          },
          {
            label: t("people.attendance-events-page.table.columns.timestamp"),
            value: new Date(
              attendanceEvent.attributes.timestamp * 1000,
            ).toLocaleString(locale),
          },
          ...(attendanceEvent.attributes.location
            ? [
                {
                  label: t(
                    "people.attendance-events-page.table.columns.location",
                  ),
                  value: t(
                    `people.attendance-events-page.fields.locations.${attendanceEvent.attributes.location}`,
                  ),
                },
              ]
            : []),
        ]}
      />
    </>
  );
};
