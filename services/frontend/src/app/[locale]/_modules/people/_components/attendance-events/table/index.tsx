"use client";

import React, { useState } from "react";
import dynamic from "next/dynamic";
import { useTranslations, useLocale } from "next-intl";
import { useSearchParams } from "next/navigation";
import { DataTable } from "@/components/table";
import { PaginationWithLinks } from "@/components/pagination-with-links";
import { Button } from "@/components/ui/button";
import { PlusCircle } from "lucide-react";
import LoaderPortal from "@/components/loader/loader-portal";
import { RowSelectionState } from "@tanstack/react-table";
import { columns } from "./attendance-events-columns";
import { useTableRegistration } from "@/hooks/useTableRegistration";
import { useAttendanceEvents } from "@/app/[locale]/_modules/people/hooks/attendance/useAttendanceEvents";
import { usePermission } from "@/contexts/PermissionContext";
import { PermissionEnum } from "@/enums/Permission";
const AttendanceEventFormModal = dynamic(
  () =>
    import("./AttendanceEventFormModal").then(
      (mod) => mod.AttendanceEventFormModal,
    ),
  {
    loading: () => (
      <LoaderPortal
        size={75}
        borderWidth={4}
        overlayColor="#000"
        overlayClassName="bg-opacity-50"
      />
    ),
    ssr: false,
  },
);

const AttendanceEventsTable = ({
  showPagination = false,
  searchParams: serverSearchParams,
}: {
  showPagination?: boolean;
  searchParams?: {
    [key: string]: string | string[] | undefined;
  };
}) => {
  const t = useTranslations();
  const locale = useLocale();
  const clientSearchParams = useSearchParams();
  const { hasPermission } = usePermission();

  const limit = parseInt(
    Array.isArray(serverSearchParams?.limit)
      ? (serverSearchParams.limit[0] ?? "5")
      : (serverSearchParams?.limit ?? clientSearchParams.get("limit") ?? "5"),
    10,
  );
  const page = parseInt(
    Array.isArray(serverSearchParams?.page)
      ? (serverSearchParams.page[0] ?? "1")
      : (serverSearchParams?.page ?? clientSearchParams.get("page") ?? "1"),
    10,
  );

  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [isModalOpen, setIsModalOpen] = useState(false);

  const sort = Array.isArray(serverSearchParams?.sort)
    ? (serverSearchParams.sort[0] ?? "-timestamp")
    : (serverSearchParams?.sort ??
      clientSearchParams.get("sort") ??
      "-timestamp");

  useTableRegistration("attendance-events", columns);

  // Use the new attendance hook
  const {
    attendanceList,
    employeeData,
    totalCount,
    isLoading,
    error,
    mutate,
    pagination,
    meta,
  } = useAttendanceEvents(Number(page), Number(limit), sort);

  const firstResult = pagination?.firstResult || 1;
  const lastResult = pagination?.lastResult || 1;

  // Permission checks
  const canCreateAttendance =
    hasPermission(PermissionEnum.CREATE_ATTENDANCE_EVENT) ||
    hasPermission(PermissionEnum.MANAGE_ATTENDANCE_EVENT);

  const CreateButton = canCreateAttendance ? (
    <Button
      onClick={() => setIsModalOpen(true)}
      className="min-h-11 shadow-none flex items-center justify-center "
    >
      <PlusCircle className="h-5 w-5" />
      {/* {t("people.attendance-events-page.create")} */}
    </Button>
  ) : null;

  return (
    <>
      <DataTable
        data={attendanceList || []}
        columns={columns}
        tableContainerClass="min-h-[240px]"
        title={t("people.attendance-events-page.table.title")}
        meta={{
          t,
          locale,
          employeeData,
          onEdit: (event: any) => mutate(), // Refresh table data after edit
          onViewDetails: (event: any) => {
            // Handle view details if needed
          },
          isUpdating: false,
        }}
        rowSelection={rowSelection}
        onRowSelectionChange={setRowSelection}
        translationPrefix="people.attendance-events-page.table"
        tableId="attendance-events"
        isLoading={isLoading}
        initialLimit={Number(limit)}
        error={error}
        dataCount={totalCount}
        hideSearch={false}
        hideFilters={false}
        headerActions={CreateButton}
        exportConfig={{
          entity: "attendance-events",
          enabled: true,
          enhanced: true,
          customFilters: { page: 1, limit: 1000 },
          columnCategories: {
            id: "Basic",
            employee_id: "Employee Info",
            type: "Record Details",
            timestamp: "Time & Date",
            event_type: "Event Details",
            activity_type: "Event Details",
            location: "Location",
            notes: "Additional Info",
            inferred_type: "Additional Info",
            created_at: "Timestamps",
            updated_at: "Timestamps",
          },
          requiredColumns: ["id", "timestamp", "event_type"],
          excludeColumns: ["select", "actions"],
          defaultSelectedColumns: [
            "id",
            "employeeName",
            "timestamp",
            "event_type",
            "activity_type",
            "location",
            "notes",
          ],
        }}
      />

      {showPagination && (
        <div className="w-full pt-[18px]">
          <PaginationWithLinks
            page={Number(page)}
            pageSize={Number(limit)}
            totalCount={totalCount}
            firstLastCounts={{ firstCount: firstResult, lastCount: lastResult }}
            isLoading={isLoading}
            isDisabled={!attendanceList?.length}
            pageSizeSelectOptions={{
              pageSizeOptions: [5, 10, 25, 30, 45, 50],
              pageSizeSearchParam: "limit",
            }}
          />
        </div>
      )}

      {isModalOpen && (
        <AttendanceEventFormModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          showEmployeeField={true}
          mode="add"
          onSuccess={() => {
            setIsModalOpen(false);
            mutate();
          }}
        />
      )}
    </>
  );
};

export default AttendanceEventsTable;
