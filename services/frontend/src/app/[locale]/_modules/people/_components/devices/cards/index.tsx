"use client";

import { useTranslations } from "next-intl";
import { useDeviceMutations } from "../../../hooks/devices/useDeviceMutations";
import { PaginationWithLinks } from "@/components/pagination-with-links";
import { useSearchParams } from "next/navigation";
import DeviceCard from "./device-card";
import { TFunction } from "@/types";
import DeviceCardSkeleton from "./device-card-skeleton";
import { TableHeaderControls } from "@/components/table/table-header-controls";
import ViewToggle from "../view-toggle";
import { useReactTable, getCoreRowModel } from "@tanstack/react-table";
import { columns } from "../table/devices-columns";
import { useTableRegistration } from "@/hooks/useTableRegistration";

type ViewMode = "table" | "cards";

type DevicesCardsProps = {
  showPagination?: boolean;
  searchParams?: {
    page: string;
    limit: string;
  };
  currentView: ViewMode;
  onViewChange: (view: ViewMode) => void;
};

const DevicesCards = ({
  showPagination = true,
  searchParams = { page: "1", limit: "12" },
  currentView,
  onViewChange,
}: DevicesCardsProps) => {
  const t = useTranslations() as TFunction;

  const limit = parseInt(searchParams.limit ?? 12, 10);
  const page = parseInt(searchParams.page ?? 1, 10);
  const searchParam = useSearchParams();
  const search = searchParam.get("search") || "";

  const { devices, pagination, isLoading, error } = useDeviceMutations({
    page,
    limit,
    search,
  });

  const totalCount = pagination?.count || 0;

  // Register table for filtering
  useTableRegistration("devices", columns);

  // Create a minimal table instance for TableHeaderControls
  const table = useReactTable({
    data: devices || [],
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  return (
    <>
      {/* Header Controls - Always visible */}
      <TableHeaderControls
        table={table}
        title={t("people.devices-page.table.title")}
        translationPrefix="people.devices-page.table"
        tableId="devices"
        isLoading={isLoading}
        hideColumns={true}
        headerActions={
          <ViewToggle currentView={currentView} onViewChange={onViewChange} />
        }
      />

      {/* Content based on state */}
      {error ? (
        <div className="p-4 flex items-center justify-center min-h-[240px]">
          <span className="text-error text-center">
            {t("common.Error.responses.global")}
          </span>
        </div>
      ) : isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-5">
          {Array.from({ length: limit }).map((_, index) => (
            <DeviceCardSkeleton key={index} />
          ))}
        </div>
      ) : !devices?.length ? (
        <div className="flex flex-1 flex-col items-center justify-center min-h-[240px] text-center">
          <p className="text-gray-500 mb-4">
            {t("people.devices-page.table.no-data")}
          </p>
        </div>
      ) : (
        <div className="flex-1">
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-5">
            {devices.map((device) => (
              <DeviceCard key={device.id} device={device} />
            ))}
          </div>
        </div>
      )}

      {/* Pagination - Always visible when showPagination is true */}
      {showPagination && (
        <div className="w-full pt-[18px]">
          <PaginationWithLinks
            page={pagination?.page ?? Number(page)}
            pageSize={Number(limit)}
            totalCount={Number(totalCount || 0)}
            firstLastCounts={{
              firstCount: pagination?.from ?? 1,
              lastCount: pagination?.to ?? limit,
            }}
            pageSizeSelectOptions={{
              pageSizeOptions: [12, 24, 36, 48],
              pageSizeSearchParam: "limit",
            }}
            isLoading={isLoading}
            isDisabled={!devices?.length}
          />
        </div>
      )}
    </>
  );
};

export default DevicesCards;
