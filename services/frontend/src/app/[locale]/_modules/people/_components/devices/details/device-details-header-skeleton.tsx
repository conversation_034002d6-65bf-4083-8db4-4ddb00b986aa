export default function DeviceDetailsHeaderSkeleton() {
  return (
    <div className="bg-white rounded-2xl border border-gray-200">
      <div className="flex flex-col sm:flex-row items-center justify-center sm:justify-between gap-6 sm:gap-4 p-6 flex-wrap animate-pulse">
        {/* Device Type Section */}
        <div className="flex items-center flex-col gap-3 shrink-0 max-sm:border-b sm:border-l pb-5 sm:pl-10">
          <div className="h-7 bg-gray-200 rounded w-24"></div>
          <div className="h-5 bg-gray-200 rounded w-20"></div>
        </div>

        {/* Metrics Section */}
        <div className="grid max-sm:w-full grid-cols-2 md:grid-cols-3 lg:grid-cols-4 justify-between items-center w-[60%] gap-8 sm:gap-4">
          {[...Array(4)].map((_, index) => (
            <div key={index} className="text-center lg:text-start">
              <div className="h-3 bg-gray-200 rounded w-16 mb-2 mx-auto lg:mx-0"></div>
              <div className="h-5 bg-gray-200 rounded w-20 mx-auto lg:mx-0"></div>
            </div>
          ))}
        </div>

        {/* Action Buttons Section */}
        <div className="flex items-center gap-2">
          <div className="w-10 h-10 bg-gray-200 rounded border"></div>
          <div className="w-10 h-10 bg-gray-200 rounded border"></div>
          <div className="w-10 h-10 bg-gray-200 rounded border"></div>
        </div>
      </div>
    </div>
  );
}
