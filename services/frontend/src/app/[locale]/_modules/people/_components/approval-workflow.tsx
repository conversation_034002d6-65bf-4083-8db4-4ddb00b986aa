import React from "react";
import { useApprovalWorkflow } from "../hooks/useApprovalWorkflow";
import { CircularProgress } from "@/components/circle-progress";
import {
  Tooltip,
  TooltipTrigger,
  TooltipContent,
} from "@/components/ui/tooltip";
import { TooltipArrow, TooltipProvider } from "@radix-ui/react-tooltip";
import { LeaveDetail, TIncludedEmployee } from "../type/employee-leaves";
import { TEmployee } from "../type/employee";
import { findEmployeeById } from "../utils/find-employee";
import { cn } from "@/lib/utils";
import { useTranslations } from "next-intl";
import { ApprovalRequestData } from "../type/approval-request";
import { TSalaryPackageData } from "../type/salary-package";
import { SalaryCalculation } from "../type/employees-salaries";

// Generic type for items that can have approval workflows
type ApprovalWorkflowItem =
  | LeaveDetail
  | TSalaryPackageData
  | SalaryCalculation;

const ApprovalWorkFlow = <T extends ApprovalWorkflowItem>({
  item,
  included,
  onShowDetails,
  onShowWorkflowModal,
  renderTrigger,
}: {
  item: T;
  included: (TIncludedEmployee | ApprovalRequestData)[];
  onShowDetails?: (value: T, employee: TEmployee) => void;
  onShowWorkflowModal?: (item: T) => void;
  renderTrigger?: () => React.ReactNode;
}) => {
  const { steps, approvalRequest, currentStepId } = useApprovalWorkflow(
    item,
    included,
  );
  const t = useTranslations();

  const status = approvalRequest?.attributes.status;
  const currentStep = steps.find((step) => step.id === currentStepId);
  const approvedCount = steps.filter(
    (step) => step.complete && !step.rejected,
  ).length;

  const isComplete = currentStep?.complete;
  const isRejected = currentStep?.rejected;

  // Handle cases where relationships might be undefined or structured differently
  const employeeId = item.relationships?.employee?.data?.id;
  let employee = null;

  if (employeeId) {
    employee = findEmployeeById(included as TIncludedEmployee[], employeeId);
  }

  // For employee leaves table, we don't need employee data since we're already in employee context
  if (!employee && !employeeId) {
    employee = {
      id: "current",
      name: "Current Employee",
      email: "",
      user_id: 0,
      start_date: "",
      status: null,
      department: "",
      department_name: "",
    } as TEmployee;
  }

  let variant: "default" | "success" | "pending" | "destructive" = "default";
  if (isRejected || status === "rejected") variant = "destructive";
  else if (isComplete || status === "approved") variant = "success";
  else if (status === "pending" && approvedCount > 0) variant = "pending";

  return (
    <TooltipProvider>
      <Tooltip delayDuration={100}>
        <TooltipTrigger asChild>
          {renderTrigger ? (
            <div>{renderTrigger()}</div>
          ) : (!approvalRequest || steps.length === 0) ? (
            <div className="flex items-center justify-center text-gray-400 text-xs">
              No workflow
            </div>
          ) : (
            <div className="flex flex-1 flex-shrink-0 items-center justify-start max-w-[90%] gap-3 border-b border-gray-100 pb-2">
              <CircularProgress
                value={approvedCount}
                max={steps.length}
                showCheckmark={isComplete}
                variant={variant}
              />
              <div className="text-start flex-1">
                <div className="font-medium text-sm">{currentStep?.name}</div>
                <div className="text-xs text-gray-500">
                  {`${approvedCount}/${steps.length}`}
                </div>
              </div>
            </div>
          )}
        </TooltipTrigger>

        <TooltipContent
          onClick={() => {
            if (onShowWorkflowModal) {
              onShowWorkflowModal(item);
            } else if (employee && onShowDetails) {
              onShowDetails(item, employee);
            }
          }}
          className="shadow-[0px_4px_30px_0px_rgba(0,0,0,0.05)] min-w-[188px] max-w-[188px] min-h-20 rounded-2xl bg-white p-2 cursor-pointer"
        >
          <TooltipArrow className="fill-current text-white w-6 h-5" />

          {!approvalRequest || steps.length === 0 ? (
            <div className="flex flex-col justify-center items-center py-4">
              <span className="text-sm text-gray-500">
                No approval workflow
              </span>
            </div>
          ) : status === "approved" || status === "rejected" ? (
            <div className="flex flex-col justify-center items-center py-4">
              <span
                className={cn(
                  "text-sm",
                  status === "approved" ? "text-secondary" : "text-error",
                )}
              >
                {status === "approved"
                  ? t(
                      "people.leaves-requests-page.request-details.approval-workflow.approved",
                    )
                  : t(
                      "people.leaves-requests-page.request-details.approval-workflow.rejected",
                    )}
              </span>
            </div>
          ) : (
            <>
              <h4 className="text-[10px] mb-1 leading-4 text-black">
                <span className="text-amber-600">
                  {t(
                    "people.leaves-requests-page.request-details.approval-workflow.pending",
                  )}
                </span>
              </h4>
              <p className="font-medium text-sm leading-5 text-black">
                {currentStep?.name}
              </p>
            </>
          )}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export default ApprovalWorkFlow;
