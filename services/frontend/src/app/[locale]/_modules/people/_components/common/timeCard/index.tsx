"use client";

import { useLocale, useTranslations } from "next-intl";
import React, { FC } from "react";
import { AttendancePeriod } from "@/types/employee/daySummary";
import { formatDateDisplay } from "../../../utils/time-card-utils";
import { DesktopView } from "./desktop-view";
import { MobileView } from "./mobile-view";

type TimeCardProps = {
  className?: string;
  date?: string;
  startTime?: string;
  endTime?: string;
  total_work_minutes?: number;
  periods?: AttendancePeriod[];
};

export const TimeCard: FC<TimeCardProps> = ({
  className,
  date,
  startTime,
  endTime,
  total_work_minutes,
  periods,
}) => {
  const t = useTranslations();
  const locale = useLocale();

  return (
    <div
      className={`bg-white rounded-2xl p-6 border border-gray-200 flex flex-col gap-4 min-h-[155px] ${className}`}
    >
      {/* Date header */}
      <div className="text-xl font-bold">
        {formatDateDisplay(date, t, locale)}
      </div>

      {/* Desktop View */}
      <DesktopView
        date={date}
        startTime={startTime}
        endTime={endTime}
        total_work_minutes={total_work_minutes}
        periods={periods}
        t={t}
        locale={locale}
      />

      {/* Mobile View */}
      <MobileView
        date={date}
        startTime={startTime}
        endTime={endTime}
        total_work_minutes={total_work_minutes}
        periods={periods}
        t={t}
        locale={locale}
      />
    </div>
  );
};
