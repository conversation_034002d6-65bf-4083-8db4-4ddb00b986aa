"use client";

import React, { Suspense, useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import DevicesHeader from "./header/devices-header";
import DevicesTable from "./table";
import DevicesCards from "./cards";
import { SkeletonTable } from "@/components/skeletons";
import DeviceCardSkeleton from "./cards/device-card-skeleton";

type ViewMode = "table" | "cards";

type DevicesViewProps = {
  searchParams: {
    page: string;
    limit: string;
    view?: string;
  };
};

export default function DevicesView({ searchParams }: DevicesViewProps) {
  const router = useRouter();
  const urlSearchParams = useSearchParams();

  // Get view from URL params, default to cards
  const [currentView, setCurrentView] = useState<ViewMode>(
    (searchParams.view as ViewMode) || "cards",
  );

  // Update URL when view changes
  const handleViewChange = (view: ViewMode) => {
    setCurrentView(view);

    // Create new URLSearchParams with current params
    const newParams = new URLSearchParams(urlSearchParams.toString());
    newParams.set("view", view);

    // Adjust limit based on view
    if (view === "cards" && !newParams.get("limit")) {
      newParams.set("limit", "12");
    } else if (view === "table" && !newParams.get("limit")) {
      newParams.set("limit", "5");
    }

    // Update URL
    router.push(`?${newParams.toString()}`);
  };

  // Sync state with URL params
  useEffect(() => {
    const viewFromUrl = searchParams.view as ViewMode;
    if (viewFromUrl && viewFromUrl !== currentView) {
      setCurrentView(viewFromUrl);
    }
  }, [searchParams.view, currentView]);

  // Adjust searchParams based on view
  const adjustedSearchParams = {
    ...searchParams,
    limit: searchParams.limit || (currentView === "table" ? "5" : "12"),
  };

  return (
    <>
      <DevicesHeader
        title={"إدارة أجهزة الحضور ومراقبة حالة الاتصال"}
      />

      <Suspense
        fallback={
          currentView === "table" ? (
            <SkeletonTable rowCount={15} />
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-5">
              {Array.from({ length: 12 }).map((_, index) => (
                <DeviceCardSkeleton key={index} />
              ))}
            </div>
          )
        }
      >
        {currentView === "table" ? (
          <DevicesTable
            showPagination={true}
            searchParams={adjustedSearchParams}
            currentView={currentView}
            onViewChange={handleViewChange}
          />
        ) : (
          <DevicesCards
            showPagination={true}
            searchParams={adjustedSearchParams}
            currentView={currentView}
            onViewChange={handleViewChange}
          />
        )}
      </Suspense>
    </>
  );
}
