"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { DialogTitle } from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { useTranslations } from "next-intl";
import { useEffect } from "react";
import { z } from "zod";
import {
  TDeviceUser,
  TIncludedEmployee,
} from "../../../../type/devices/usersList";
import { findEmployeeById } from "../../../../utils/find-employee";
import { LoaderCircle } from "lucide-react";
import { TFunction } from "@/types";
import dynamic from "next/dynamic";
import LoaderPortal from "@/components/loader/loader-portal";

const ResponsiveDialog = dynamic(
  () => import("@/components/responsive-dialog"),
  {
    ssr: false,
    loading: () => <LoaderPortal overlayColor="#000" overlayOpacity={0.6} />,
  },
);

// Schema for form validation
const fixCodeSchema = z.object({
  systemCode: z.string().min(1, "System code is required"),
  deviceCode: z.string().min(1, "Device code is required"),
});

type FixCodeFormData = z.infer<typeof fixCodeSchema>;

type FixCodeDialogProps = {
  isOpen: boolean;
  onClose: () => void;
  user: TDeviceUser | null;
  onSubmit: (data: FixCodeFormData) => Promise<void>;
  isLoading?: boolean;
  includedData?: TIncludedEmployee[];
};

const FixCodeDialog = ({
  isOpen,
  onClose,
  user,
  onSubmit,
  isLoading = false,
  includedData = [],
}: FixCodeDialogProps) => {
  const t = useTranslations() as TFunction;

  const form = useForm<FixCodeFormData>({
    resolver: zodResolver(fixCodeSchema),
    defaultValues: {
      systemCode: "",
      deviceCode: "",
    },
  });

  // Update form values when user changes
  useEffect(() => {
    if (user) {
      // Use the same logic as the System Code column to get employee ID
      const employeeId = user.relationships?.employee?.data?.id;
      const employee = employeeId
        ? findEmployeeById(includedData, employeeId)
        : null;

      let systemCode = "";
      const deviceCode = user.attributes.user_id || ""; // Default device code to current user_id

      // Get system code from employee data
      if (employee?.user_id) {
        systemCode = employee.user_id.toString();
      }

      form.reset({
        systemCode,
        deviceCode,
      });
    }
  }, [user, form, includedData]);

  const handleSubmit = async (data: FixCodeFormData) => {
    try {
      await onSubmit(data);
      onClose();
    } catch (error) {
      console.error("Error updating codes:", error);
    }
  };

  const handleClose = () => {
    form.reset();
    onClose();
  };

  if (!user) return null;

  return (
    <ResponsiveDialog
      open={isOpen}
      onOpenChange={handleClose}
      closeBtnStyle="top-[22px]"
      header={
        <DialogTitle className="px-6 py-[22px] border-b font-semibold text-[18px] leading-[28px] text-start">
          {t("people.devices-page.users-table.fix-code-dialog.title")}
        </DialogTitle>
      }
    >
      <div className="space-y-6">
        {/* User Info */}
        <div className="text-start mb-8">
          <h3 className="text-xl font-semibold leading-[30px] text-black">
            {user.attributes.name}
          </h3>
          <p className="text-gray-500 text-xs font-medium">٣٠ يناير ٢٠٢٥</p>
        </div>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-6"
          >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Device Code */}
              <FormField
                control={form.control}
                name="deviceCode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-start block font-medium leading-5 text-xs">
                      {t(
                        "people.devices-page.users-table.fix-code-dialog.deviceCode",
                      )}
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className="h-12 text-start"
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* System Code */}
              <FormField
                control={form.control}
                name="systemCode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-start block font-medium leading-5 text-xs">
                      {t(
                        "people.devices-page.users-table.fix-code-dialog.systemCode",
                      )}
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className="h-12 text-start"
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Action Buttons */}
            <div className="flex gap-4 pt-4">
              <Button
                type="submit"
                className="flex-1 h-12 rounded-lg"
                disabled={isLoading || !form.formState.isValid}
              >
                {isLoading ? (
                  <LoaderCircle className="animate-spin h-4 w-4" />
                ) : (
                  t("people.devices-page.users-table.fix-code-dialog.confirm")
                )}
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                className="flex-1 h-12 rounded-lg"
                disabled={isLoading}
              >
                {t("people.devices-page.users-table.fix-code-dialog.cancel")}
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </ResponsiveDialog>
  );
};

export default FixCodeDialog;
