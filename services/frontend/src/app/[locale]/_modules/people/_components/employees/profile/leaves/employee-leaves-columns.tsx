"use client";

import { Checkbox } from "@/components/ui/checkbox";
import { ColumnDef, TableMeta } from "@tanstack/react-table";
import {
  LeaveDetail,
  TIncludedEmployee,
} from "../../../../type/employee-leaves";
import { Locale } from "@/i18n/routing";
import { formatDate } from "@/lib/dateFormatter";

import { TFunction } from "@/types";
import { getLeaveTypeTranslations } from "@/constants/translations-mapping";
import RequestStatus from "@/components/status/request-status";
import { REQUEST_STATUS } from "@/constants/enum";

// Define the translation function type
interface TableMetaWithTranslation extends TableMeta<LeaveDetail> {
  t: TFunction;
  locale?: Locale;
  includedData?: (TIncludedEmployee | ApprovalRequestData)[];
  onWithdrawLeave?: (leaveId: string) => void;
  onUpdateDates?: (
    leaveId: string,
    startDate: Date,
    endDate: Date,
    leaveDuration?: string,
    leaveType?: string,
  ) => void;
  onAcceptLeave?: (leaveId: string, note?: string) => void;
  onRejectLeave?: (leaveId: string, note?: string) => void;
  isWithdrawing?: boolean;
  isUpdatingDates?: boolean;
  isAccepting?: boolean;
  isRejecting?: boolean;
  onShowWorkflowModal?: (leaveDetail: LeaveDetail) => void;
}

// Helper function to get meta data
import { Table } from "@tanstack/react-table";
import { EmployeeLeavesActions } from "./employee-leaves-actions";
import ApprovalWorkFlow from "../../../approval-workflow";
import { ApprovalRequestData } from "../../../../type/approval-request";

const getMeta = (table: Table<LeaveDetail>) =>
  table.options.meta as TableMetaWithTranslation;

export const employeeLeavesColumns: ColumnDef<LeaveDetail>[] = [
  // Selection Checkbox
  {
    id: "select",
    enableSorting: false,
    enableHiding: false,
    header: ({ table }) => (
      <Checkbox
        className="w-[11px] h-[11px] rounded-[3px] border-[#ACB5BB] [&>*:last-child]:scale-[0.5] [&>*:last-child]:stroke-[1.5] flex items-center justify-center p-0"
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        className="w-[11px] h-[11px] rounded-[3px] border-[#1A1C1E] [&>*:last-child]:scale-[0.5] [&>*:last-child]:stroke-[1.5] flex items-center justify-center p-0"
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
  },

  // Request Date
  {
    accessorKey: "created_at",
    id: "requestDate",
    header: ({ table }) => {
      return (
        <div className="text-start">
          {getMeta(table).t(
            "people.employees-page.profile.leaves.table.columns.requestDate",
          )}
        </div>
      );
    },
    cell: ({ row, table }) => {
      const { locale } = getMeta(table);
      const dateStr = row.original.attributes.created_at;
      const formattedDate = dateStr ? formatDate(dateStr, locale ?? "ar") : "-";
      return (
        <p className="text-sm font-semibold text-gray-500 text-start">
          {formattedDate}
        </p>
      );
    },
  },

  // Start and End Date
  {
    accessorKey: "date_range",
    id: "dateRange",
    header: ({ table }) => (
      <div className="text-center">
        {getMeta(table).t(
          "people.employees-page.profile.leaves.table.columns.dateRange",
        )}
      </div>
    ),
    cell: ({ row, table }) => {
      const { locale } = getMeta(table);
      const startDate = row.original.attributes.start_date;
      const endDate = row.original.attributes.end_date;

      const formattedStartDate = startDate
        ? formatDate(startDate, locale ?? "ar")
        : "-";
      const formattedEndDate = endDate
        ? formatDate(endDate, locale ?? "ar")
        : "-";

      return (
        <p className="text-sm font-semibold text-black text-center">
          {formattedStartDate} - {formattedEndDate}
        </p>
      );
    },
  },

  // Leave Type
  {
    accessorKey: "leave_type",
    id: "leaveType",
    header: ({ table }) => (
      <div className="text-center">
        {getMeta(table).t(
          "people.employees-page.profile.leaves.table.columns.leaveType",
        )}
      </div>
    ),
    cell: ({ row, table }) => {
      const { t } = getMeta(table);
      const leaveType = row.original.attributes.leave_type;
      return (
        <p className="text-sm font-semibold text-gray-500 text-center">
          {getLeaveTypeTranslations(t)[leaveType]}
        </p>
      );
    },
  },

  // Approval Workflow
  {
    accessorKey: "approval_workflow",
    id: "approvalWorkflow",
    header: ({ table }) => (
      <div className="text-center">
        {getMeta(table).t(
          "people.employees-page.profile.leaves.table.columns.approvalWorkflow",
        )}
      </div>
    ),
    cell: ({ row, table }) => {
      const { includedData, onShowWorkflowModal } = getMeta(table);
      return (
        <div className="flex justify-end">
          <ApprovalWorkFlow
            item={row.original}
            included={includedData || []}
            onShowWorkflowModal={onShowWorkflowModal}
          />
        </div>
      );
    },
  },

  // Status
  {
    accessorKey: "status",
    id: "status",
    header: ({ table }) => (
      <div className="text-center">
        {getMeta(table).t(
          "people.employees-page.profile.leaves.table.columns.status",
        )}
      </div>
    ),
    cell: ({ row, table }) => {
      const { t } = getMeta(table);
      const status = row.original.attributes.status;

      return (
        <div className="flex justify-center">
          <RequestStatus
            status={status as REQUEST_STATUS}
            label={t(`common.status.request-status.${status}`)}
          />
        </div>
      );
    },
  },

  // Actions
  {
    id: "actions",
    header: ({ table }) => (
      <div className="text-center">
        {getMeta(table).t("cm.table.columns.actions")}
      </div>
    ),
    cell: ({ row, table }) => {
      const {
        onWithdrawLeave,
        onUpdateDates,
        onAcceptLeave,
        onRejectLeave,
        isWithdrawing,
        isUpdatingDates,
        isAccepting,
        isRejecting,
        includedData,
      } = getMeta(table);

      return (
        <div className="flex justify-center">
          <EmployeeLeavesActions
            row={row}
            onWithdrawLeave={onWithdrawLeave}
            onUpdateDates={onUpdateDates}
            onAcceptLeave={onAcceptLeave}
            onRejectLeave={onRejectLeave}
            isWithdrawing={isWithdrawing}
            isUpdatingDates={isUpdatingDates}
            isAccepting={isAccepting}
            isRejecting={isRejecting}
            includedData={includedData || []}
          />
        </div>
      );
    },
  },
];
