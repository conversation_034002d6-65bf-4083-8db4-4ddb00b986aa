"use client";

import { TDevice } from "../../../type/devices/device";
import { useTranslations, useLocale } from "next-intl";
import { TFunction } from "@/types";
import { formatDate } from "@/lib/dateFormatter";
import DeviceStatus from "@/components/status/device-status";
import { DEVICE_STATUS } from "@/constants/enum";
import { mapDeviceStatusToCanonical } from "@/constants/translations-mapping";
import { Locale } from "@/i18n/routing";
import Link from "next/link";
import Image from "next/image";
import DeviceActions from "../table/device-actions";

type DeviceCardProps = {
  device: TDevice;
};

export default function DeviceCard({ device }: DeviceCardProps) {
  const t = useTranslations() as TFunction;
  const locale = useLocale() as Locale;

  const deviceStatus = device.attributes.status as DEVICE_STATUS;
  const canonicalStatus = mapDeviceStatusToCanonical(deviceStatus);

  // Get the translated label for the status
  const translatedStatusLabel = t(
    `common.status.device-status.${canonicalStatus}`,
  );

  return (
    <div className="bg-white rounded-2xl border border-gray-200 pb-4 hover:shadow-md transition-shadow min-h-[216px] shadow-sm">
      {/* Header with device name and actions */}
      <DeviceCardHeader device={device} />

      {/* Device details */}
      <div className="grid grid-cols-3 gap-6 px-4">
        {/* IP Address */}
        <div className="flex flex-col items-start gap-2 col-span-2">
          <span className="text-xs text-gray-400 font-normal">
            {t("people.devices-page.table.columns.ip_address")}
          </span>
          <span className="text-sm font-medium text-gray-900">
            {device.attributes.ip_address}
          </span>
        </div>

        {/* Port */}
        <div className="flex flex-col items-start gap-2">
          <span className="text-xs text-gray-400 font-normal">
            {t("people.devices-page.table.columns.port")}
          </span>
          <span className="text-sm font-medium text-gray-900">
            {device.attributes.port}
          </span>
        </div>

        {/* Last Seen */}
        <div className="flex flex-col items-start gap-2 col-span-2">
          <span className="text-xs text-gray-400 font-normal">
            {t("people.devices-page.table.columns.created_at")}
          </span>
          <span className="text-sm font-medium text-gray-900">
            {device.attributes.created_at
              ? formatDate(device.attributes.created_at, locale)
              : t("common.not-available")}
          </span>
        </div>

        {/* Status */}
        <div className="flex flex-col items-start gap-2">
          <span className="text-xs text-gray-400 font-normal">
            {t("people.devices-page.table.columns.status")}
          </span>
          <DeviceStatus
            status={canonicalStatus}
            label={translatedStatusLabel}
            statusStyle="text-start text-sm"
          />
        </div>
      </div>
    </div>
  );
}

const DeviceCardHeader = ({ device }: DeviceCardProps) => {
  const locale = useLocale() as Locale;
  const t = useTranslations();

  // Format location display
  const location = device.attributes.location;
  const locationDisplay =
    location === "athar_1"
      ? "Athar 1"
      : location === "athar_2"
        ? "Athar 2"
        : location || "";

  // Combine adapter type and location with dash
  const typeAndLocation = locationDisplay
    ? `${device.attributes.adapter_type} - ${locationDisplay}`
    : device.attributes.adapter_type;

  return (
    <div className="flex items-start justify-between mb-4 h-[70px] p-4 bg-neutral-50 border-b border-b-gray-100 rounded-2xl">
      <div className="flex-1">
        <h3 className="font-semibold text-sm text-black mb-1">
          {device.attributes.name}
        </h3>
        <p className="text-[10px] font-medium leading-[140%] text-gray-500">
          {typeAndLocation}
        </p>
      </div>
      <div className="flex items-center gap-2">
        <Link
          href={`/${locale}/people/devices/${device.id}`}
          className="p-2 border rounded-lg hover:border-black/50 transition-colors"
        >
          <Image
            width={16}
            height={16}
            src={"/images/icons/eye.svg"}
            alt="eye icon"
            priority={true}
          />
        </Link>
        <DeviceActions device={device} />
      </div>
    </div>
  );
};
