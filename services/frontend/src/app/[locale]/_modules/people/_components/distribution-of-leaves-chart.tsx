"use client";

import ChartHeader from "@/components/chart/chart-header";
import Custom<PERSON>AxisTick from "@/components/chart/custom-responsive-xAxis";
import { ChartSkeleton } from "@/components/skeletons";
import { LANGUAGES } from "@/constants/enum";
import { getLeaveTypeTranslations } from "@/constants/translations-mapping";
import useMediaQuery from "@/hooks/use-media-query";
import { TFunction } from "@/types";
import { useLocale, useTranslations } from "next-intl";
import React, { useEffect, useState } from "react";
import { DataItem } from "../type/DistributionOfLeavesChart";
import GenericBarChart from "@/components/chart/bar-chart/generic-bar-chart";
import { Locale } from "@/i18n/routing";

const DistributionOfLeavesChart = () => {
  // Sample data coming from your API/backend
  const sampleData: DataItem[] = [
    { type: "sick", count: 55 },
    { type: "annual", count: 50 },
    { type: "withoutSalary", count: 45 },
    { type: "emergency", count: 30 },
    { type: "educational", count: 35 },
    { type: "marriage", count: 28 },
    { type: "condolence", count: 32 },
  ];

  const [data, setData] = useState<DataItem[]>([]);
  useEffect(() => {
    setTimeout(() => {
      setData(sampleData);
    }, 2500);
  }, []);

  const locale: Locale = useLocale() as Locale;
  const isAr = locale === LANGUAGES.ARABIC;
  const t = useTranslations() as TFunction;
  const leaveTypeTranslations = getLeaveTypeTranslations(t);

  // Media queries.
  const isMobile = useMediaQuery("(max-width:480px)");
  const isMediumScreen = useMediaQuery("(max-width:768px)");
  const isLargeScreen = useMediaQuery("(max-width:1200px)");

  // Bar configuration for your chart.
  const bars = [
    {
      dataKey: "count",
      fill: "#E5E7EB",
      className: "text-gray-200",
      radius: [6, 6, 0, 0] as [number, number, number, number],
    },
  ];

  // Formatter to translate the canonical key to the current locale.
  const xAxisTickFormatter = (value: string) => {
    return leaveTypeTranslations[value] || value;
  };

  const tooltipLabelFormatter = (label: any) => (
    <span className="text-[10px] font-semibold">
      {`${leaveTypeTranslations[label]}`}
    </span>
  );

  const tooltipFormatter = (value: any, name: any, payload: any) => {
    const label =
      name === "count"
        ? t("people.distribution-leaves-chart.tooltipFormatter.count")
        : name;
    return [
      <div
        className="flex justify-between gap-1 w-full break-words items-center max-w-full"
        key={name}
      >
        <span className="text-[10px]">{label}</span>
        <span className="text-xs text-black font-semibold">
          {leaveTypeTranslations[value] || value}
        </span>
      </div>,
    ];
  };

  // If data is not loaded, show skeleton.
  if (data.length === 0) {
    return (
      <ChartSkeleton
        showSelect={false}
        showLabel={false}
        skeletonWrapperStyle="rounded-[20px] min-h-[526px] max-h-[526px]"
        titleStyle="w-[265px]"
      />
    );
  }

  return (
    <div>
      <ChartHeader
        title={t("people.distribution-leaves-chart.title")}
        titleStyle="sm:text-[20px] sm:font-medium leading-[30px] text-gray-700 font-readex_pro"
        headerClassName="w-full flex justify-between items-center border-b border-gray-100 px-6 pb-5 pt-4 max-h-[84px] sm:max-h-[66px]"
      />

      <GenericBarChart
        margin={{
          top: 33,
          right: isMediumScreen ? (isAr ? -20 : 10) : isAr ? 0 : 24,
          left: isMediumScreen ? (isAr ? 10 : -10) : isAr ? 24 : 24,
          bottom: 22,
        }}
        chartContainerClass="p-0 h-[459px]"
        data={data}
        xAxisKey="type"
        bars={bars}
        barSize={isMobile ? 16 : 32}
        ticks={[5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55]}
        stackOffset="none"
        tooltipLabelFormatter={tooltipLabelFormatter}
        tooltipFormatter={tooltipFormatter}
        // Use the custom tick renderer for X-axis.
        xAxisTick={(props) => (
          <CustomXAxisTick
            {...props}
            xAxisTickFormatter={xAxisTickFormatter}
            isMobile={isMobile}
            isLargeScreen={isLargeScreen}
            isMediumScreen={isMediumScreen}
            isAr={isAr}
          />
        )}
        xAxisTickFormatter={xAxisTickFormatter}
        xAxisAngle={isLargeScreen ? -45 : 0}
        gridStrokeDasharray="2 2"
        yAxisTick={{ fill: "#A0A0AB", fontSize: 14 }}
        yAxisDomain={[5, "dataMax"]}
      />
    </div>
  );
};

export default DistributionOfLeavesChart;
