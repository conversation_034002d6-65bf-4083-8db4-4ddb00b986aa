"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { TEmployeeAttachment } from "../../../../type/employee";
import { useTranslations } from "next-intl";
import { useLocale } from "next-intl";
import { formatFileSize } from "@/lib/formateFileSize";
import { LANGUAGES } from "@/constants/enum";
import { Locale } from "@/i18n/routing";
import { EllipsisVertical } from "../../../../../../../../../public/images/icons";
import FilePreview from "@/components/file-preview";
import { FileIcon } from "@/lib/getFileIcon";
import { getAttachmentMenuItems } from "../../../../constants";

type AttachmentCardProps = {
  attachment: TEmployeeAttachment;
  onRenameClick: (attachment: TEmployeeAttachment) => void;
  onDeleteClick: (attachment: TEmployeeAttachment) => void;
};

export default function AttachmentCard({
  attachment,
  onRenameClick,
  onDeleteClick,
}: AttachmentCardProps) {
  const t = useTranslations();
  const locale: Locale = useLocale() as Locale;
  const isAr = locale === LANGUAGES.ARABIC;
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);

  // Get menu items from constants
  const menuItems = getAttachmentMenuItems(t, attachment, {
    onView: () => setIsPreviewOpen(true),
    onRename: onRenameClick,
    onDelete: onDeleteClick,
  });

  return (
    <>
      <div className="bg-white rounded-2xl border border-gray-200 overflow-hidden max-h-[220px]">
        <div className="p-4 flex flex-col h-full items-center min-h-[220px]">
          {/* Dropdown menu */}
          <div className="flex justify-start self-start mb-2 w-full">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className="h-6 w-6 p-0 min-w-[24px] min-h-[24px] flex items-center justify-center hover:bg-transparent focus-visible:ring-0 active:bg-transparent"
                >
                  <EllipsisVertical className="stroke-gray-400 !w-6 !h-6" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                lang={locale}
                className="w-60 max-w-60 rounded-2xl space-y-1 font-semibold text-sm text-start text-[#727A90] p-2"
                align={isAr ? "end" : "start"}
              >
                {menuItems.map((item, index) => (
                  <DropdownMenuItem
                    key={index}
                    onClick={item.onClick}
                    className={item.className}
                  >
                    <span>{item.icon}</span>
                    <span>{item.label}</span>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* File icon */}
          <div
            className="flex flex-col items-center justify-center w-[100px] h-[100px] rounded-2xl cursor-pointer"
            onClick={() => setIsPreviewOpen(true)}
          >
            <FileIcon
              fileName={attachment.attributes.filename}
              fileUrl={attachment.attributes.url}
              fileType={attachment.attributes.content_type}
              width={80}
              height={80}
              className="w-20 h-20"
            />
          </div>

          {/* File info */}
          <div className="mt-4 pb-2 text-center">
            <h3
              className="font-semibold text-base truncate cursor-pointer hover:text-primary"
              title={attachment.attributes.filename}
              onClick={() => setIsPreviewOpen(true)}
            >
              {attachment.attributes.filename}
            </h3>
            <p className="text-gray-400 text-sm mt-1 font-normal">
              {formatFileSize(attachment.attributes.bytes_size, t)}
            </p>
          </div>
        </div>
      </div>

      {/* File Preview Dialog */}
      <FilePreview
        fileName={attachment.attributes.filename}
        fileUrl={attachment.attributes.url}
        fileType={attachment.attributes.content_type}
        isOpen={isPreviewOpen}
        onClose={() => setIsPreviewOpen(false)}
      />
    </>
  );
}
