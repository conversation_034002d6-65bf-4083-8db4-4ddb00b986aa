"use client";

import React from "react";
import {
  SalaryCalculation,
  SalaryCalculationDetail,
} from "../../type/employees-salaries";
import {
  getCalculationDetails,
  groupCalculationDetailsByType,
} from "../../utils/salary-calculation-details";
import FormattedCurrency from "@/components/formatted-currency";
import { TFunction } from "@/types";

type CalculationDetailsProps = {
  salaryCalculation: SalaryCalculation;
  calculationDetailsData: SalaryCalculationDetail[];
  t: TFunction;
  className?: string;
};

export const CalculationDetails: React.FC<CalculationDetailsProps> = ({
  salaryCalculation,
  calculationDetailsData,
  t,
  className = "",
}) => {
  const details = getCalculationDetails(
    salaryCalculation,
    calculationDetailsData,
  );
  const groupedDetails = groupCalculationDetailsByType(details);

  if (details.length === 0) {
    return (
      <div className={`text-sm text-gray-500 ${className}`}>
        {t("people.employees-page.salary.calculation-details.no-details")}
      </div>
    );
  }

  const renderDetailGroup = (
    type: string,
    items: SalaryCalculationDetail[],
  ) => {
    const typeLabel = getDetailTypeLabel(type, t);
    const total = items.reduce((sum, item) => sum + item.attributes.amount, 0);

    return (
      <div key={type} className="mb-4">
        <div className="flex justify-between items-center mb-2 pb-1 border-b border-gray-200">
          <h5 className="text-sm font-semibold text-secondary">{typeLabel}</h5>
          <FormattedCurrency
            amount={total}
            numberStyle="text-sm font-semibold text-secondary"
          />
        </div>
        <div className="space-y-1">
          {items.map((item) => (
            <div
              key={item.id}
              className="flex justify-between items-start text-xs"
            >
              <span className="text-gray-600 flex-1 pr-2">
                {item.attributes.description}
              </span>
              <FormattedCurrency
                amount={item.attributes.amount}
                numberStyle="font-medium text-black"
              />
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className={`space-y-3 ${className}`}>
      <h4 className="text-sm font-semibold text-secondary mb-2">
        {t("people.employees-page.salary.calculation-details.title")}
      </h4>

      {Object.entries(groupedDetails).map(([type, items]) =>
        renderDetailGroup(type, items),
      )}

      {/* Summary */}
      <div className="pt-2 border-t border-gray-300">
        <div className="flex justify-between items-center">
          <span className="text-sm font-semibold text-secondary">
            {t("people.employees-page.salary.calculation-details.net-salary")}:
          </span>
          <FormattedCurrency
            amount={Number(salaryCalculation.attributes.net_salary)}
            numberStyle="text-sm font-bold text-secondary"
          />
        </div>
      </div>
    </div>
  );
};

/**
 * Get localized label for detail type
 */
const getDetailTypeLabel = (type: string, t: TFunction): string => {
  const labelMap: Record<string, string> = {
    base: t("people.employees-page.salary.calculation-details.types.base"),
    deduction: t(
      "people.employees-page.salary.calculation-details.types.deduction",
    ),
    allowance: t(
      "people.employees-page.salary.calculation-details.types.allowance",
    ),
    bonus: t("people.employees-page.salary.calculation-details.types.bonus"),
  };

  return labelMap[type] || type;
};
