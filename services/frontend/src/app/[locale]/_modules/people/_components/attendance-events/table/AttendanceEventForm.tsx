"use client";

import React, { useState, useEffect, startTransition } from "react";
import { useForm, FormProvider } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useLocale, useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import { Calendar, Loader } from "lucide-react";
import FormFieldRenderer from "@/components/auth/fields/form-field-renderer";
import { PAGES } from "@/enums";
import {
  createAttendance,
  updateAttendance,
} from "@/app/[locale]/_modules/people/actions/attendance";
import {
  createAttendanceSchema,
  CreateAttendanceSchemaType,
} from "@/app/[locale]/_modules/people/schemas/attendanceSchema";
import { useToastMessage } from "@/hooks/use-toast-message";
import { useActionState } from "@/hooks/use-action-state";
import useFormFields from "@/hooks/useFormFields";
import { SingleDateTimeModal } from "@/components/modals/single-datetime-modal";
import { formatDate } from "@/lib/dateFormatter";
import { Attendance_Event_type } from "@/constants/enum";
import { useEmployees } from "@/app/[locale]/_modules/people/hooks/employees/useEmployees";
import { Locale } from "@/i18n/routing";
import {
  ATTENDANCE_ACTIVITY_TYPE,
  ATTENDANCE_LOCATION,
} from "@/app/[locale]/_modules/people/schemas/attendanceSchema";

type AttendanceEventFormProps = {
  mode?: "add" | "edit";
  attendanceEvent?: any;
  showEmployeeField?: boolean;
  employeeId?: string;
  onSuccess?: () => void;
  onCancel?: () => void;
};

export default function AttendanceEventForm({
  mode = "add",
  attendanceEvent = null,
  showEmployeeField = false,
  employeeId,
  onSuccess,
  onCancel,
}: AttendanceEventFormProps) {
  const t = useTranslations();
  const { getFormFields } = useFormFields({
    formType: PAGES.CREATEATTENDANCE,
    t,
  });
  const fields = getFormFields();
  const { showToast } = useToastMessage();

  const { employees, isLoading: employeesLoading } = useEmployees(
    1,
    1000,
    "name",
  );
  const locale = useLocale() as Locale;
  const [isDateTimePickerOpen, setIsDateTimePickerOpen] = useState(false);

  const initialState = { data: null, success: "", error: "", issues: [] };

  // Create bound action like other working forms
  const boundAction =
    mode === "edit" && attendanceEvent
      ? updateAttendance.bind(null, attendanceEvent.id)
      : createAttendance.bind(null, employeeId || undefined);

  const [state, submitAction, isPending] = useActionState(
    boundAction,
    initialState,
  );

  // Helper function to get default values based on mode
  const getDefaultValues = () => {
    if (mode === "edit" && attendanceEvent) {
      const eventDate = new Date(attendanceEvent.attributes.timestamp * 1000);
      return {
        employee_id: attendanceEvent.relationships.employee.data.id || "",
        event_type: attendanceEvent.attributes
          .event_type as Attendance_Event_type,
        activity_type:
          attendanceEvent.attributes.activity_type ||
          ATTENDANCE_ACTIVITY_TYPE.REGULAR,
        location:
          attendanceEvent.attributes.location || ATTENDANCE_LOCATION.OFFICE,
        timestamp: eventDate,
        note: attendanceEvent.attributes.notes || "",
      };
    }

    return {
      employee_id: employeeId || "",
      event_type: Attendance_Event_type.CheckIn,
      activity_type: ATTENDANCE_ACTIVITY_TYPE.REGULAR,
      location: ATTENDANCE_LOCATION.OFFICE,
      timestamp: new Date(),
      note: "",
    };
  };

  const form = useForm<CreateAttendanceSchemaType>({
    resolver: zodResolver(createAttendanceSchema(t)),
    defaultValues: getDefaultValues(),
    mode: "all",
  });

  useEffect(() => {
    if (state.success) {
      showToast(
        "success",
        t(
          "people.employees-page.profile.attendance-log.create-attendance-modal.success",
        ),
      );
      form.reset({
        employee_id: "",
        event_type: Attendance_Event_type.CheckIn,
        activity_type: ATTENDANCE_ACTIVITY_TYPE.REGULAR,
        location: ATTENDANCE_LOCATION.OFFICE,
        timestamp: new Date(),
        note: "",
      });
      onSuccess?.();
    } else if (state.error) {
      showToast("error", state.error);
    }
  }, [state.success, state.error]);

  const handleDateTimeSelection = (selectedDateTime: Date) => {
    form.setValue("timestamp", selectedDateTime);
    setIsDateTimePickerOpen(false);
  };
  const openDateTimePicker = () => setIsDateTimePickerOpen(true);

  const onSubmit = (data: CreateAttendanceSchemaType) => {
    if (isPending) {
      return;
    }

    const fd = new FormData();

    // Don't include ID in FormData - it goes in the URL for PUT requests
    fd.append("attendance_event[employee_id]", data.employee_id || "");
    fd.append("attendance_event[event_type]", data.event_type);
    fd.append("attendance_event[activity_type]", data.activity_type);
    fd.append("attendance_event[location]", data.location);
    const epochSeconds = Math.floor(data.timestamp.getTime() / 1000);
    fd.append("attendance_event[timestamp]", epochSeconds.toString());
    if (data.note && data.note.trim())
      fd.append("attendance_event[notes]", data.note.trim());

    startTransition(() => submitAction(fd));
  };

  const DateRangeField = () => {
    const timestamp = form.watch("timestamp");
    return (
      <div className="space-y-2">
        <div className="text-sm font-medium">
          {t("people.attendance-events-page.fields.timestamp")}
        </div>
        <Button
          variant="outline"
          className="w-full text-left"
          onClick={openDateTimePicker}
        >
          <Calendar className="mr-2 h-4 w-4" />
          <span>
            {timestamp
              ? `${formatDate(timestamp, locale, "dd mmmm yyyy")} – ${timestamp.toLocaleTimeString(
                  locale,
                  { hour: "2-digit", minute: "2-digit" },
                )}`
              : t("...select-dates")}
          </span>
        </Button>
      </div>
    );
  };

  return (
    <>
      <div className="bg-white rounded-lg max-w-2xl mx-auto w-[99.5%]">
        <FormProvider {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex flex-col"
          >
            <div className="flex-1 flex flex-col gap-6 min-h-[calc(100vh-15rem)] pb-6">
              <DateRangeField />
              {showEmployeeField &&
                (mode === "edit" ? (
                  // Read-only employee field for edit mode
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700">
                      {t("people.attendance-events-page.fields.employee")}
                    </label>
                    <div className="p-3 bg-gray-50 rounded-md text-sm text-gray-600">
                      {employees?.find(
                        (e) => e.id === form.watch("employee_id"),
                      )?.attributes.name || "Unknown Employee"}
                    </div>
                  </div>
                ) : (
                  // Editable employee field for add mode
                  <FormFieldRenderer
                    fieldConfig={{
                      name: "employee_id",
                      type: "select",
                      label: t("people.attendance-events-page.fields.employee"),
                      options:
                        employees?.map((e) => ({
                          value: e.id,
                          label: e.attributes.name,
                        })) || [],
                      placeholder: employeesLoading
                        ? t("common.loading")
                        : t(
                            "people.attendance-events-page.fields.employee-placeholder",
                          ),
                      className: "w-full",
                      readOnly: employeesLoading,
                    }}
                    form={form}
                    isPending={isPending || !employees}
                  />
                ))}

              {fields.map((f) => (
                <FormFieldRenderer
                  key={f.name}
                  fieldConfig={f}
                  form={form}
                  isPending={isPending}
                />
              ))}
            </div>

            <div className="sticky border-t border-t-slate-200 pt-6 bottom-0 left-0 w-[100%] rounded-t-[18px] bg-white flex flex-row items-center justify-between gap-4 sm:gap-6 px-6">
              <Button
                type="submit"
                disabled={isPending}
                className="px-4 py-2 w-full h-12 sm:max-w-[244px] rounded-lg"
              >
                {isPending ? (
                  <Loader className="animate-spin" />
                ) : mode === "add" ? (
                  t("common.buttonText.add")
                ) : (
                  t("common.buttonText.update")
                )}
              </Button>
              <Button
                variant="outline"
                type="button"
                className="w-full h-12 sm:max-w-[244px] rounded-lg"
                onClick={onCancel}
                disabled={isPending}
              >
                {t("common.buttonText.cancel2")}
              </Button>
            </div>
          </form>
        </FormProvider>
      </div>
      <SingleDateTimeModal
        isOpen={isDateTimePickerOpen}
        onClose={() => setIsDateTimePickerOpen(false)}
        onConfirm={handleDateTimeSelection}
        initialDateTime={form.getValues("timestamp")}
        isLoading={isPending}
        title={t("people.attendance-events-page.fields.timestamp")}
        description={t("people.attendance-events-page.edit.description")}
        allowPastDates={true}
      />
    </>
  );
}
