import { Skeleton } from "@/components/ui/skeleton";

export default function DeviceCardSkeleton() {
  return (
    <div className="bg-white rounded-2xl border border-gray-200 pb-4 shadow-sm min-h-[216px] animate-pulse">
      {/* Header skeleton */}
      <div className="flex items-start justify-between mb-4 h-[70px] p-4 bg-neutral-50 border-b border-b-gray-100 rounded-2xl">
        <div className="flex-1">
          {/* Device name skeleton */}
          <Skeleton className="h-4 w-32 mb-1 bg-gray-200" />
          {/* Device type skeleton */}
          <Skeleton className="h-3 w-20 bg-gray-200" />
        </div>
        <div className="flex items-center gap-2">
          {/* Eye icon skeleton */}
          <Skeleton className="h-8 w-8 rounded-lg bg-gray-200" />
          {/* Actions button skeleton */}
          <Skeleton className="h-8 w-8 rounded-lg bg-gray-200" />
        </div>
      </div>

      {/* Device details skeleton */}
      <div className="grid grid-cols-3 gap-6 px-4">
        {/* IP Address skeleton */}
        <div className="flex flex-col items-start gap-2 col-span-2">
          <Skeleton className="h-3 w-16 bg-gray-200" />
          <Skeleton className="h-4 w-24 bg-gray-200" />
        </div>

        {/* Port skeleton */}
        <div className="flex flex-col items-start gap-2">
          <Skeleton className="h-3 w-12 bg-gray-200" />
          <Skeleton className="h-4 w-16 bg-gray-200" />
        </div>

        {/* Created date skeleton */}
        <div className="flex flex-col items-start gap-2 col-span-2">
          <Skeleton className="h-3 w-20 bg-gray-200" />
          <Skeleton className="h-4 w-28 bg-gray-200" />
        </div>

        {/* Status skeleton */}
        <div className="flex flex-col items-start gap-2">
          <Skeleton className="h-3 w-14 bg-gray-200" />
          <Skeleton className="h-6 w-16 rounded-sm bg-gray-200" />
        </div>
      </div>
    </div>
  );
}
