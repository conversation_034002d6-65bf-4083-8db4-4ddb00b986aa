"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import { Loader } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { TFunction } from "@/types";
import {
  ProjectSchemaType,
  projectSchema,
} from "../../../schemas/projectSchema";
import { TProject } from "@/types/core/project";
import { useProjectMutations } from "../../../hooks/projects/useProjectMutations";

type AddProjectFormProps = {
  mode?: "add" | "edit";
  project?: TProject | null;
  onSuccess?: () => void;
  onCancel?: () => void;
};

export default function AddProjectForm({
  mode = "add",
  project = null,
  onSuccess,
  onCancel,
}: AddProjectFormProps = {}) {
  const t = useTranslations() as TFunction;
  const { createProject, updateProject, isCreating, isUpdating } =
    useProjectMutations({
      onSuccess,
    });

  const form = useForm<ProjectSchemaType>({
    resolver: zodResolver(projectSchema),
    defaultValues: {
      name: project?.attributes?.name || "",
      description: project?.attributes?.description || "",
      status:
        (project?.attributes?.status as "active" | "inactive") || "active",
    },
  });

  const onSubmit = async (data: ProjectSchemaType) => {
    try {
      const projectData = {
        type: "project" as const,
        attributes: {
          name: data.name,
          description: data.description,
          status: data.status as "active" | "inactive",
        },
      };

      if (mode === "add") {
        await createProject(projectData);
      } else if (project?.id) {
        await updateProject(project.id, { ...projectData, id: project.id });
      }
    } catch (error) {
      console.error("Error saving project:", error);
    }
  };

  return (
    <div className="bg-white rounded-lg max-w-2xl mx-auto w-[99.5%]">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="flex flex-col">
          <div className="flex flex-col gap-6 pb-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("projects.page.form.name.label")}</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={t("projects.page.form.name.placeholder")}
                      disabled={isCreating || isUpdating}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {t("projects.page.form.description.label")}
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder={t(
                        "projects.page.form.description.placeholder",
                      )}
                      className="min-h-[100px]"
                      disabled={isCreating || isUpdating}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("projects.page.form.status.label")}</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    disabled={isCreating || isUpdating}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue
                          placeholder={t(
                            "projects.page.form.status.placeholder",
                          )}
                        />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="active">
                        {t("projects.page.form.status.active")}
                      </SelectItem>
                      <SelectItem value="inactive">
                        {t("projects.page.form.status.inactive")}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="sticky border-t border-t-slate-200 pt-6 bottom-0 left-0 w-[100%] rounded-t-[18px] bg-white flex flex-row items-center justify-between gap-4 sm:gap-6 px-6">
            <Button
              type="submit"
              disabled={isCreating || isUpdating}
              className="px-4 py-2 w-full h-12 sm:max-w-[244px] rounded-lg"
            >
              {isCreating || isUpdating ? (
                <Loader className="animate-spin" />
              ) : mode === "add" ? (
                t("common.buttonText.add")
              ) : (
                t("common.buttonText.update")
              )}
            </Button>
            <Button
              variant="outline"
              type="button"
              className="w-full h-12 sm:max-w-[244px] rounded-lg"
              onClick={onCancel}
              disabled={isCreating || isUpdating}
            >
              {t("common.buttonText.cancel2")}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
