"use client";

import { DialogDescription, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import ResponsiveDialog from "@/components/responsive-dialog";
import { useState } from "react";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { useTranslations } from "next-intl";

type TEmployeeNoteProps = {
  showNoteDialog: boolean;
  setShowNoteDialog: React.Dispatch<React.SetStateAction<boolean>>;
  onAddNote: (note: string) => void;
};
const EmployeeNoteDialog = ({
  showNoteDialog,
  setShowNoteDialog,
  onAddNote,
}: TEmployeeNoteProps) => {
  return (
    <ResponsiveDialog
      open={showNoteDialog}
      onOpenChange={setShowNoteDialog}
      closeBtnStyle="top-[22px]"
      header={<RequestDetailsHeader />}
    >
      <RequestDetailsContent onAddNote={onAddNote} />
    </ResponsiveDialog>
  );
};

const RequestDetailsHeader = () => {
  const t = useTranslations();
  return (
    <div className="text-start">
      <DialogDescription className="sr-only">{/* to do*/}</DialogDescription>
      <div className="px-6 py-[22px] border-b ">
        <div className="font-semibold flex items-center justify-start gap-3">
          <DialogTitle className="!m-0 text-[18px] font-semibold leading-7">
            {t("people.leaves-requests-page.employee-note-dialog.header-title")}
          </DialogTitle>
        </div>
      </div>
    </div>
  );
};

const RequestDetailsContent = ({
  onAddNote,
}: {
  onAddNote: (note: string) => void;
}) => {
  const [note, setNote] = useState("");
  const t = useTranslations();
  const handleSaveNote = () => {
    if (note.trim()) {
      onAddNote(note);
      setNote("");
    }
  };
  return (
    <div className={"text-start"}>
      <div className="mb-10">
        <Label className="font-medium text-sm leading-[18px] text-gray-500">
          {t("people.leaves-requests-page.dialog-common.note-label")}
        </Label>
        <Textarea
          value={note}
          onChange={(e) => setNote(e.target.value)}
          placeholder={t(
            "people.leaves-requests-page.dialog-common.note-placeholder",
          )}
          className="w-full rounded-[10px] text-start min-h-[138px] max-h-[138px] placeholder:text-gray-400 placeholder:text-base placeholder:leading-5 placeholder:font-normal"
        />
      </div>
      <div className="w-full rounded-none sm:rounded-[18px] bg-white flex flex-row items-center justify-between gap-4 sm:gap-6 rounded-t-none">
        <>
          <Button
            onClick={() => handleSaveNote()}
            className="w-full h-12 text-white font-semibold"
          >
            {t("common.buttonText.add")}
          </Button>
          <Button
            onClick={() => setNote("")}
            variant="outline"
            className="w-full h-12 font-semibold"
          >
            {t("common.buttonText.cancel2")}
          </Button>
        </>
      </div>
    </div>
  );
};

export default EmployeeNoteDialog;
