"use client";

import { Checkbox } from "@/components/ui/checkbox";
import { ColumnDef, TableMeta, Table } from "@tanstack/react-table";
import { Locale } from "@/i18n/routing";
import { TFunction } from "@/types";
import { EmployeeRolesProjectsActions } from "./employee-roles-projects-actions";
import { TRoleProjectPair } from "../../../../type/employee-roles-projects";

// Define the translation function type
interface TableMetaWithTranslation extends TableMeta<TRoleProjectPair> {
  t: TFunction;
  locale?: Locale;
  onDeleteRoleProject?: (roleProjectId: string) => void;
  isDeleting?: boolean;
}

// Helper function to get meta data
const getMeta = (table: Table<TRoleProjectPair>) =>
  table.options.meta as TableMetaWithTranslation;

export const employeeRolesProjectsColumns: ColumnDef<TRoleProjectPair>[] = [
  // Selection Checkbox
  {
    id: "select",
    enableSorting: false,
    enableHiding: false,
    header: ({ table }) => (
      <Checkbox
        className="w-[11px] h-[11px] rounded-[3px] border-[#ACB5BB] [&>*:last-child]:scale-[0.5] [&>*:last-child]:stroke-[1.5] flex items-center justify-center p-0"
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        className="w-[11px] h-[11px] rounded-[3px] border-[#1A1C1E] [&>*:last-child]:scale-[0.5] [&>*:last-child]:stroke-[1.5] flex items-center justify-center p-0"
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
  },

  // Role
  {
    accessorKey: "roleName",
    id: "role",
    header: ({ table }) => (
      <div className="text-start justify-end">
        {getMeta(table).t(
          "people.employees-page.profile.roles-projects.table.columns.role",
        )}
      </div>
    ),
    cell: ({ row }) => {
      const roleName = row.original.roleName;
      return (
        <p className="text-sm font-semibold text-black text-start justify-start">
          {roleName}
        </p>
      );
    },
  },

  // Project
  {
    accessorKey: "projectName",
    id: "projectName",
    header: ({ table }) => {
      return (
        <div className="text-center justify-end">
          {getMeta(table).t(
            "people.employees-page.profile.roles-projects.table.columns.projectName",
          )}
        </div>
      );
    },
    cell: ({ row }) => {
      const projectName = row.original.projectName;
      return (
        <p className="text-sm font-semibold text-black text-center ">
          {projectName}
        </p>
      );
    },
  },

  // Actions
  {
    id: "actions",
    header: ({ table }) => (
      <div className="text-end">
        {getMeta(table).t("cm.table.columns.actions")}
      </div>
    ),
    cell: ({ row, table }) => {
      const { onDeleteRoleProject, isDeleting } = getMeta(table);

      return (
        <div className="flex justify-end">
          <EmployeeRolesProjectsActions
            row={row}
            onDeleteRoleProject={onDeleteRoleProject}
            isDeleting={isDeleting}
          />
        </div>
      );
    },
  },
];
