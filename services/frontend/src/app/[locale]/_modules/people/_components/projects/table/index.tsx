"use client";

import { PaginationWithLinks } from "@/components/pagination-with-links";
import { DataTable } from "@/components/table";
import React, { useState } from "react";
import { columns } from "./projects-columns";
import { RowSelectionState } from "@tanstack/react-table";
import { useTranslations, useLocale } from "next-intl";
import { useProjects } from "@/hooks/useProjects";

import { useTableRegistration } from "@/hooks/useTableRegistration";
import { TFunction } from "@/types";
import { Locale } from "@/i18n/routing";
import { TProject } from "@/types/core/project";
import { useProjectMutations } from "../../../hooks/projects/useProjectMutations";
import ConfirmDeleteDialog from "@/components/confirm-delete-dialog";
import ProjectDialog from "../project-dialog";
import { mutate } from "swr";

type ProjectsTableProps = {
  showPagination: boolean;
  searchParams: {
    [key: string]: string | string[] | undefined;
  };
  onProjectUpdated?: () => void;
};

const ProjectsTable = ({
  showPagination = true,
  searchParams = { page: "1", limit: "5" },
  onProjectUpdated,
}: ProjectsTableProps) => {
  const t = useTranslations() as TFunction;
  const locale = useLocale() as Locale;

  const limit = parseInt(
    Array.isArray(searchParams.limit)
      ? (searchParams.limit[0] ?? "5")
      : (searchParams.limit ?? "5"),
    10,
  );
  const page = parseInt(
    Array.isArray(searchParams.page)
      ? (searchParams.page[0] ?? "1")
      : (searchParams.page ?? "1"),
    10,
  );
  const sort = Array.isArray(searchParams.sort)
    ? (searchParams.sort[0] ?? "id")
    : (searchParams.sort ?? "id");

  const { projects, totalCount, pagination, isLoading, error } = useProjects(
    page,
    limit,
    sort,
  );

  // Function to refresh projects data
  const refreshProjects = () => {
    // Use the callback from parent if provided, otherwise use local mutate
    if (onProjectUpdated) {
      onProjectUpdated();
    } else {
      // Fallback: Mutate the SWR cache to refetch projects data
      mutate(
        (key: string) =>
          typeof key === "string" && key.includes("/api/projects"),
      );
    }
  };

  // Use mutations hook for actions
  const { updateProject, deleteProject, isUpdating, isDeleting } =
    useProjectMutations({
      onSuccess: () => {
        // Refresh the projects data after successful operation
        refreshProjects();
      },
      onError: (error) => {
        console.error("Project mutation error:", error);
      },
    });

  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [editingProject, setEditingProject] = useState<TProject | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [deletingProject, setDeletingProject] = useState<TProject | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  useTableRegistration("projects", columns);

  // Action handlers
  const handleEditProject = (project: TProject) => {
    setEditingProject(project);
    setIsEditDialogOpen(true);
  };

  const handleCloseEditDialog = () => {
    setIsEditDialogOpen(false);
    setEditingProject(null);
  };

  const handleDeleteProject = (projectId: string) => {
    const project = projects?.find((p) => p.id === projectId);
    if (project) {
      setDeletingProject(project);
      setIsDeleteDialogOpen(true);
    }
  };

  const handleConfirmDelete = async () => {
    if (deletingProject) {
      await deleteProject(deletingProject.id);
      setIsDeleteDialogOpen(false);
      setDeletingProject(null);
    }
  };

  return (
    <>
      <DataTable
        data={projects}
        dataCount={totalCount}
        columns={columns}
        tableContainerClass="min-h-[240px] text-start"
        title={t("projects.page.table.title")}
        meta={{
          t,
          locale,
          onEditProject: handleEditProject,
          onDeleteProject: handleDeleteProject,
          isUpdating,
          isDeleting,
        }}
        rowSelection={rowSelection}
        onRowSelectionChange={setRowSelection}
        translationPrefix="projects.page.table"
        tableId="projects"
        isLoading={isLoading}
        initialLimit={5}
        error={error}
        tableHeadStyle="text-start"
        tableCellStyle="text-start"
        hideColumns={true}
      />
      <div className="w-full pt-[18px]">
        {showPagination && (
          <PaginationWithLinks
            page={pagination.page ?? Number(page)}
            pageSize={Number(limit)}
            totalCount={Number(totalCount || 0)}
            firstLastCounts={{
              firstCount: pagination.firstResult ?? 1,
              lastCount: pagination.lastResult ?? 5,
            }}
            pageSizeSelectOptions={{
              pageSizeOptions: [5, 10, 25, 30, 45, 50],
              pageSizeSearchParam: "limit",
            }}
            isLoading={isLoading}
            isDisabled={!projects?.length}
          />
        )}
      </div>

      {/* Edit Project Dialog */}
      <ProjectDialog
        mode="edit"
        project={editingProject}
        isOpen={isEditDialogOpen}
        onClose={handleCloseEditDialog}
        onSuccess={() => {
          refreshProjects();
        }}
      />

      {/* Delete Project Dialog */}
      {deletingProject && (
        <ConfirmDeleteDialog
          isOpen={isDeleteDialogOpen}
          onClose={() => setIsDeleteDialogOpen(false)}
          onConfirmDelete={handleConfirmDelete}
          isDeleting={isDeleting}
          itemName={deletingProject.attributes.name}
          titleKey="projects.page.delete.title"
          descriptionKey="projects.page.delete.confirm"
          itemDetails={[
            {
              label: t("projects.page.table.columns.name"),
              value: deletingProject.attributes.name,
              className: "text-blue-600 font-semibold",
            },
            ...(deletingProject.attributes.description
              ? [
                  {
                    label: t("projects.page.table.columns.description"),
                    value: deletingProject.attributes.description,
                    className: "text-gray-600",
                  },
                ]
              : []),
            ...(deletingProject.attributes.status
              ? [
                  {
                    label: t("projects.page.table.columns.status"),
                    value: t(
                      `projects.page.table.status.${deletingProject.attributes.status}`,
                    ),
                    className:
                      deletingProject.attributes.status === "active"
                        ? "text-green-600"
                        : "text-gray-500",
                  },
                ]
              : []),
          ]}
        />
      )}
    </>
  );
};

export default ProjectsTable;
