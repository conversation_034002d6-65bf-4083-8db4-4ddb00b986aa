import { Card } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { useTranslations } from "next-intl";
import { TFunction } from "@/types";

export default function DeviceCommandsSkeleton() {
  const t = useTranslations() as TFunction;

  return (
    <Card className="p-6 shadow-none">
      <h3 className="text-lg font-semibold mb-6 text-start">
        {t("people.devices-page.device-details.tabs.commands")}
      </h3>

      <div className="space-y-6">
        {/* Command Selection Skeleton*/}
        <div className="space-y-2">
          <Skeleton className="h-5 w-20 bg-gray-200" /> {/* Label */}
          <Skeleton className="h-12 w-full sm:w-1/2 bg-gray-200" />
        </div>

        {/* Parameters Section Skeleton*/}
        <div className="space-y-4">
          <Skeleton className="h-6 w-32 bg-gray-200" /> {/* Parameters title */}
          {/* Add Parameter Button Skeleton*/}
          <div className="border-none rounded-lg bg-gray-50">
            <Skeleton className="h-8 w-40 bg-gray-200" />
            {/* */}
          </div>
        </div>

        {/* Execute Button Skeleton */}
        <div className="flex justify-end">
          <Skeleton className="h-12 w-32 bg-gray-200" />
        </div>
      </div>
    </Card>
  );
}
