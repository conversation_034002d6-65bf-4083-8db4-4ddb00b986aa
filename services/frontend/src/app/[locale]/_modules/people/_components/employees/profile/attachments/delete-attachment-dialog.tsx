"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { DialogDescription, DialogTitle } from "@/components/ui/dialog";
import ResponsiveDialog from "@/components/responsive-dialog";
import { useTranslations } from "next-intl";

import { TEmployeeAttachment } from "../../../../type/employee";
import { LoaderCircle } from "lucide-react";

type DeleteAttachmentDialogProps = {
  attachment: TEmployeeAttachment | null;
  isOpen: boolean;
  onClose: () => void;
  onDelete: (attachmentId: string) => Promise<boolean>;
  isDeleting: boolean;
};

export default function DeleteAttachmentDialog({
  attachment,
  isOpen,
  onClose,
  onDelete,
  isDeleting,
}: DeleteAttachmentDialogProps) {
  const t = useTranslations();
  const handleDelete = async () => {
    if (!attachment) return;

    const success = await onDelete(attachment.id);
    if (success) {
      onClose();
    }
  };

  return (
    <ResponsiveDialog
      open={isOpen}
      onOpenChange={onClose}
      closeBtnStyle="top-[22px]"
      header={
        <DialogTitle className="px-6 py-[22px] border-b font-semibold text-[18px] leading-[28px]">
          {t("people.employees-page.profile.attachments.delete-dialog.title")}
        </DialogTitle>
      }
    >
      <div className="space-y-6">
        <DialogDescription className="text-base">
          {t(
            "people.employees-page.profile.attachments.delete-dialog.description",
            {
              fileName: attachment?.attributes?.filename || "",
            },
          )}
        </DialogDescription>

        <div className="flex justify-end gap-3 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={isDeleting}
            className="h-12 px-6"
          >
            {t("common.buttonText.cancel")}
          </Button>
          <Button
            type="button"
            onClick={handleDelete}
            disabled={isDeleting}
            className="h-12 px-6 bg-error"
          >
            {isDeleting ? (
              <>
                <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
                {t("common.buttonText.deleting")}
              </>
            ) : (
              t("common.buttonText.delete")
            )}
          </Button>
        </div>
      </div>
    </ResponsiveDialog>
  );
}
