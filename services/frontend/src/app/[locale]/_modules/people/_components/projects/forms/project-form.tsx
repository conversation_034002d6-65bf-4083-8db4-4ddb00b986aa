"use client";

import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useTranslations } from "next-intl";
import { TFunction } from "@/types";
import { TProject } from "@/types/core/project";

const projectFormSchema = (t: TFunction) =>
  z.object({
    name: z.string().min(1, {
      message: t("projects.page.form.name.required"),
    }),
    description: z.string().optional(),
    status: z.enum(["active", "inactive"]).default("active"),
  });

type ProjectFormData = z.infer<ReturnType<typeof projectFormSchema>>;

type ProjectFormProps = {
  project?: TProject;
  onSubmit: (data: ProjectFormData) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
};

const ProjectForm = ({
  project,
  onSubmit,
  onCancel,
  isLoading = false,
}: ProjectFormProps) => {
  const t = useTranslations() as TFunction;

  const form = useForm<ProjectFormData>({
    resolver: zodResolver(projectFormSchema(t)),
    defaultValues: {
      name: project?.attributes.name || "",
      description: project?.attributes.description || "",
      status: (project?.attributes.status as "active" | "inactive") || "active",
    },
  });

  const handleSubmit = async (data: ProjectFormData) => {
    try {
      await onSubmit(data);
    } catch (error) {
      console.error("Form submission error:", error);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("projects.page.form.name.label")}</FormLabel>
              <FormControl>
                <Input
                  placeholder={t("projects.page.form.name.placeholder")}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("projects.page.form.description.label")}</FormLabel>
              <FormControl>
                <Textarea
                  placeholder={t("projects.page.form.description.placeholder")}
                  className="resize-none"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="status"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("projects.page.form.status.label")}</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder={t("projects.page.form.status.placeholder")} />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="active">
                    {t("projects.page.form.status.active")}
                  </SelectItem>
                  <SelectItem value="inactive">
                    {t("projects.page.form.status.inactive")}
                  </SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isLoading}
          >
            {t("common.buttonText.cancel")}
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading
              ? t("common.buttonText.saving")
              : project
              ? t("common.buttonText.update")
              : t("common.buttonText.create")}
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default ProjectForm;
