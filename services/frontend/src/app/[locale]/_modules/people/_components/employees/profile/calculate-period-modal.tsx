"use client";

import { useState } from "react";
import { useTranslations, useLocale } from "next-intl";
import { But<PERSON> } from "@/components/ui/button";
import ResponsiveDialog from "@/components/responsive-dialog";
import { DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Locale } from "@/i18n/routing";
import { format } from "date-fns";
import { useToastMessage } from "@/hooks/use-toast-message";
import { calculateSalaryPeriod } from "../../../actions/salary-calculations";
import { DatePicker } from "@/components/ui/date-picker";
import { mutate } from "swr";
import { useSearchParams } from "next/navigation";

type CalculatePeriodModalProps = {
  isOpen: boolean;
  onClose: () => void;
  employeeId?: string;
};

export function CalculatePeriodModal({
  isOpen,
  onClose,
  employeeId,
}: CalculatePeriodModalProps) {
  const t = useTranslations();
  const locale: Locale = useLocale() as Locale;
  const { showToast } = useToastMessage();
  const searchParams = useSearchParams();
  const limit = searchParams.get("limit") ?? "5";
  const page = searchParams.get("page") ?? "1";

  // State for selected date and loading status
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(
    new Date(),
  );
  const [isLoading, setIsLoading] = useState(false);

  // Format the date as YYYY-MM for the API
  const formatDateForApi = (date: Date | undefined): string => {
    if (!date) return "";
    return format(date, "yyyy-MM");
  };

  // Handle the calculation
  const handleCalculate = async () => {
    if (!selectedDate) {
      showToast("error", t("common.form.date.error.required"));
      return;
    }

    try {
      setIsLoading(true);
      const period = formatDateForApi(selectedDate);
      const result = await calculateSalaryPeriod(period);

      if (result.error) {
        showToast("error", result.error);
      } else {
        showToast("success", result.success ?? t("common.success.generic"));

        // Mutate the salary table data if employeeId is provided
        if (employeeId) {
          const apiUrl = `/api/finance/salary_calculations?sort=-1&filter[employee_id_eq]=${employeeId}&page=${page}&limit=${limit}&include=employee`;
          await mutate(apiUrl);
        }

        onClose();
      }
    } catch (error) {
      showToast("error", t("common.error.generic"));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ResponsiveDialog
      open={isOpen}
      onOpenChange={onClose}
      header={
        <>
          <DialogTitle className="px-6 py-[22px] border-b font-semibold text-[18px] leading-[28px]">
            {t("people.salary-calculation.calculate-period.title")}
          </DialogTitle>
          <DialogDescription className="sr-only">
            {t("people.salary-calculation.calculate-period.description")}
          </DialogDescription>
        </>
      }
      className="max-w-[500px]"
    >
      <div className="p-6 space-y-6">
        <div className="space-y-2">
          <label className="text-sm font-medium">
            {t("people.salary-calculation.calculate-period.select-month")}
          </label>
          <DatePicker
            date={selectedDate}
            setDate={setSelectedDate}
            placeholder={t(
              "people.salary-calculation.calculate-period.placeholder",
            )}
          />
          <p className="text-sm text-gray-500">
            {t("people.salary-calculation.calculate-period.help-text")}
          </p>
        </div>

        <div className="flex justify-end gap-3 pt-4">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isLoading}
            type="button"
          >
            {t("common.buttonText.cancel")}
          </Button>
          <Button
            onClick={handleCalculate}
            disabled={!selectedDate || isLoading}
            type="button"
            className="min-w-[100px] min-h-10"
          >
            {isLoading ? t("common.loading") : t("common.calculate")}
          </Button>
        </div>
      </div>
    </ResponsiveDialog>
  );
}
