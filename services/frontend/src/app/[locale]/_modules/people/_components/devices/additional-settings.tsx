"use client";

import { useEffect, useState } from "react";
import { useTranslations } from "next-intl";
import { Plus } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { TFunction } from "@/types";
import { Trash } from "../../../../../../../public/images/icons";

type AdditionalSetting = {
  id: string;
  type: string;
  value: string;
};

interface AdditionalSettingsProps {
  initialSettings?: AdditionalSetting[];
  onSettingsChange?: (settings: AdditionalSetting[]) => void;
}

export default function AdditionalSettings({
  onSettingsChange,
  initialSettings = [],
}: AdditionalSettingsProps) {
  const t = useTranslations() as TFunction;
  const [additionalSettings, setAdditionalSettings] =
    useState<AdditionalSetting[]>(initialSettings);

  useEffect(() => {
    setAdditionalSettings(initialSettings);
  }, [initialSettings]);

  // Functions to handle additional settings
  const addAdditionalSetting = () => {
    const newSetting: AdditionalSetting = {
      id: Date.now().toString(),
      type: "",
      value: "",
    };
    const updatedSettings = [...additionalSettings, newSetting];
    setAdditionalSettings(updatedSettings);
    onSettingsChange?.(updatedSettings);
  };

  const removeAdditionalSetting = (id: string) => {
    const updatedSettings = additionalSettings.filter(
      (setting) => setting.id !== id,
    );
    setAdditionalSettings(updatedSettings);
    onSettingsChange?.(updatedSettings);
  };

  const updateAdditionalSetting = (
    id: string,
    field: "type" | "value",
    newValue: string,
  ) => {
    const updatedSettings = additionalSettings.map((setting) =>
      setting.id === id ? { ...setting, [field]: newValue } : setting,
    );
    setAdditionalSettings(updatedSettings);
    onSettingsChange?.(updatedSettings);
  };

  return (
    <div className="space-y-4">
      {/* Additional Settings Section */}
      {additionalSettings.length > 0 && (
        <div className="mb-4 flex flex-col gap-4 ">
          {additionalSettings.map((setting, idx) => (
            <div
              key={setting.id}
              className="rounded-lg p-3 border border-gray-200 bg-gray-50"
            >
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-right font-medium text-gray-900">
                  {t(
                    "people.devices-page.add-device-dialog.form.new-settings-title",
                  ) + ` ${`(${idx + 1})`}`}
                </h3>
                {/* Delete Button */}
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="p-2 h-8 w-8 text-red-500 hover:text-red-700 hover:bg-red-50"
                  onClick={() => removeAdditionalSetting(setting.id)}
                >
                  <Trash className="!w-6 !h-6 text-error" />
                </Button>
              </div>
              <div className="flex items-center gap-4">
                {/* Settings Type Dropdown */}
                <div className="flex-1">
                  <label className="block text-sm font-medium text-gray-700 mb-2 text-right">
                    {t(
                      "people.devices-page.add-device-dialog.form.settings-type-label",
                    )}
                  </label>
                  <Select
                    value={setting.type}
                    onValueChange={(value) =>
                      updateAdditionalSetting(setting.id, "type", value)
                    }
                  >
                    <SelectTrigger className="w-full text-right h-12">
                      <SelectValue
                        placeholder={t(
                          "people.devices-page.add-device-dialog.form.settings-type-placeholder",
                        )}
                      />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="connection_config">
                        Connection Config
                      </SelectItem>
                      <SelectItem value="sync_config">Sync Config</SelectItem>
                      <SelectItem value="capabilities">Capabilities</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Settings Value Dropdown */}
                <div className="flex-1">
                  <label className="block text-sm font-medium text-gray-700 mb-2 text-right">
                    {t(
                      "people.devices-page.add-device-dialog.form.settings-value-label",
                    )}
                  </label>
                  <Input
                    className="h-12 text-right"
                    value={setting.value}
                    placeholder={t(
                      "people.devices-page.add-device-dialog.form.settings-value-placeholder",
                    )}
                    onChange={(e) =>
                      updateAdditionalSetting(
                        setting.id,
                        "value",
                        e.target.value,
                      )
                    }
                    disabled={!setting.type}
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Add Other Settings Button */}
      <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
        <Button
          type="button"
          variant="ghost"
          className="w-full h-12 border border-dashed border-gray-300 rounded-lg bg-white hover:bg-gray-50 flex items-center justify-center gap-2 text-gray-600 hover:text-gray-800"
          onClick={addAdditionalSetting}
        >
          <Plus className="w-4 h-4" />
          {t("people.devices-page.add-device-dialog.form.add-other-settings")}
        </Button>
      </div>
    </div>
  );
}
