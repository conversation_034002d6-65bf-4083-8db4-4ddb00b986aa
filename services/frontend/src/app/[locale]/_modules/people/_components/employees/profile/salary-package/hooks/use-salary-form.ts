import {
  useEffect,
  useRef,
  useState,
  startTransition,
  useActionState,
} from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useLocale, useTranslations } from "next-intl";
import { useToastMessage } from "@/hooks/use-toast-message";
import { ActionState, TFunction } from "@/types";
import { Locale } from "@/i18n/routing";

import {
  getDefaultFormValues,
  prepareFormDataForSubmission,
  calculateTotalPackage,
} from "../utils/salary-form-utils";
import { saveSalaryPackage } from "@/app/[locale]/_modules/people/actions/salary-package";
import {
  TSalaryPackageData,
  TSalaryPackageResponse,
} from "@/app/[locale]/_modules/people/type/salary-package";
import {
  salaryPackageSchema,
  SalaryPackageSchemaType,
} from "@/app/[locale]/_modules/people/schemas/salaryPackageSchema";

type UseSalaryFormProps = {
  employeeId: string;
  mode: "create" | "update";
  existingPackage?: TSalaryPackageData | null;
  onClose: () => void;
  onSalaryPackageCreated?: (newPackage: TSalaryPackageData) => void;
  onSalaryPackageUpdated?: (updatedPackage: TSalaryPackageData) => void;
};

export const useSalaryForm = ({
  employeeId,
  mode,
  existingPackage,
  onClose,
  onSalaryPackageCreated,
  onSalaryPackageUpdated,
}: UseSalaryFormProps) => {
  const t = useTranslations() as TFunction;
  const locale = useLocale() as Locale;
  const formRef = useRef<HTMLFormElement | null>(null);
  const { showToast } = useToastMessage();
  const [editingFields, setEditingFields] = useState<Set<string>>(new Set());

  const initialState: ActionState<TSalaryPackageResponse> = {
    error: "",
    success: "",
    issues: [],
    data: null,
  };

  const [state, submitAction, isPending] = useActionState(
    saveSalaryPackage,
    initialState,
  );

  // Initialize form
  const form = useForm<SalaryPackageSchemaType>({
    resolver: zodResolver(salaryPackageSchema(t, mode)),
    defaultValues: getDefaultFormValues(
      mode,
      employeeId,
      existingPackage,
      locale,
    ),
    mode: "all",
  });

  // Handle form submission success/error
  useEffect(() => {
    if (state?.success) {
      showToast("success", state.success);

      if (state.data?.data) {
        const packageData = Array.isArray(state.data.data)
          ? state.data.data[0]
          : state.data.data;

        if (packageData) {
          if (mode === "create" && onSalaryPackageCreated) {
            onSalaryPackageCreated(packageData);
          } else if (mode === "update" && onSalaryPackageUpdated) {
            onSalaryPackageUpdated(packageData);
          }
        }
      }

      onClose();
    }

    if (state?.error) {
      showToast("error", state.issues?.[0] || state.error);
    }
  }, [state, mode]);

  //only allow one field to be edited at a time
  const toggleFieldEdit = (fieldName: string) => {
    setEditingFields((prev) => {
      const newSet = new Set<string>();
      if (prev.has(fieldName)) {
        // If the field is currently being edited, stop editing it
        // newSet remains empty, so no fields will be in edit mode
      } else {
        // If the field is not being edited, start editing it (and stop editing any other field)
        newSet.add(fieldName);
      }
      return newSet;
    });
  };

  // Check if any field is currently being edited
  const isAnyFieldEditing = editingFields.size > 0;

  // Handle form submission
  const handleSubmit = (data: SalaryPackageSchemaType) => {
    const formData = prepareFormDataForSubmission(
      data,
      mode,
      existingPackage?.id,
    );

    startTransition(() => {
      submitAction(formData);
    });
  };

  // Calculate total package value
  const calculateTotal = (): number => {
    const values = form.getValues();
    return calculateTotalPackage(values);
  };

  return {
    form,
    formRef,
    isPending,
    editingFields,
    isAnyFieldEditing,
    toggleFieldEdit,
    handleSubmit,
    calculateTotal,
    onClose,
    t,
    locale,
  };
};
