"use client";

import { PaginationWithLinks } from "@/components/pagination-with-links";
import { DataTable } from "@/components/table";
import React, { useState } from "react";
import { columns } from "./employees-columns";
import { RowSelectionState } from "@tanstack/react-table";
import { useTranslations } from "next-intl";
import { useEmployees } from "../../../hooks/employees/useEmployees";
import { useRouter } from "@/i18n/routing";
import { useSearchParams } from "next/navigation";
import { useTableRegistration } from "@/hooks/useTableRegistration";

type EmployeesTableProps = {
  showPagination: boolean;
  searchParams: {
    [key: string]: string | string[] | undefined;
  };
};

const EmployeesTable = ({
  showPagination,
  searchParams,
}: EmployeesTableProps) => {
  const t = useTranslations();
  const router = useRouter();
  const urlSearchParams = useSearchParams();

  const limit = parseInt(
    Array.isArray(searchParams.limit)
      ? searchParams.limit[0] ?? "5"
      : searchParams.limit ?? "5",
    10
  );
  const page = parseInt(
    Array.isArray(searchParams.page)
      ? searchParams.page[0] ?? "1"
      : searchParams.page ?? "1",
    10
  );
  const search = urlSearchParams.get("search") || "";
  const sort = Array.isArray(searchParams.sort)
    ? searchParams.sort[0] ?? "start_date"
    : searchParams.sort ?? "start_date";

  const { employees, totalCount, pagination, isLoading, error } = useEmployees(
    page,
    limit,
    sort,
  );

  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});

  useTableRegistration("employees", columns);

  return (
    <>
      <DataTable
        data={employees}
        dataCount={totalCount}
        columns={columns}
        tableContainerClass="text-start"
        title={t("people.employees-page.table.title")}
        meta={{}}
        rowSelection={rowSelection}
        onRowSelectionChange={setRowSelection}
        translationPrefix="people.employees-page.table"
        tableId="employees"
        isLoading={isLoading}
        initialLimit={5}
        error={error}
        tableHeadStyle="text-start"
        tableCellStyle="text-start"
        exportConfig={{
          entity: "employees",
          enabled: true,
          enhanced: true, // Enable enhanced export with column selection
          customFilters: {
            page: 1, // Always start from page 1 for exports
            limit: 1000, // Use a larger limit for exports
          },
          columnCategories: {
            id: "Basic",
            name: "Basic",
            department_name: "Job Details",
            start_date: "Employment",
            user_id: "Basic",
            email: "Contact",
            phone: "Contact",
            position: "Job Details",
            salary: "Compensation",
            end_date: "Employment",
            status: "Status",
            created_at: "Metadata",
            updated_at: "Metadata",
          },
          columnDescriptions: {
            id: "Unique identifier for the employee record",
            name: "Full name of the employee",
            department_name: "Department or team name",
            start_date: "Employment start date",
            user_id: "Associated user account ID",
            email: "Email address",
            phone: "Phone number",
            position: "Job position/title",
            salary: "Current salary amount",
            end_date: "Employment end date (if applicable)",
            status: "Current employment status",
            created_at: "Record creation date",
            updated_at: "Last update date",
          },
          requiredColumns: ["id", "name"],
          excludeColumns: ["select", "actions"],
        }}
        onRowClick={(row) => {
          const employeeId = row.attributes.user_id;
          if (employeeId) {
            router.push(`/people/employees/${employeeId}`);
          }
        }}
      />
      {showPagination && (
        <div className="w-full pt-[18px]">
          <PaginationWithLinks
            page={pagination.page ?? Number(page)}
            pageSize={Number(limit)}
            totalCount={Number(totalCount || 0)}
            firstLastCounts={{
              firstCount: pagination.firstResult ?? 1,
              lastCount: pagination.lastResult ?? 5,
            }}
            pageSizeSelectOptions={{
              pageSizeOptions: [5, 10, 25, 30, 45, 50],
              pageSizeSearchParam: "limit",
            }}
            isLoading={isLoading}
            isDisabled={!employees?.length}
          />
        </div>
      )}
    </>
  );
};

export default EmployeesTable;
