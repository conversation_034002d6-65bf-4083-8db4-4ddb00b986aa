"use client";

import { DataTable } from "@/components/table";
import { TFunction } from "@/types";
import { useTranslations, useLocale } from "next-intl";
import { useState } from "react";
import { RowSelectionState } from "@tanstack/react-table";
import { useDeviceMutations } from "../../../hooks/devices/useDeviceMutations";
import { PaginationWithLinks } from "@/components/pagination-with-links";
import { columns } from "./devices-columns";
import { useSearchParams } from "next/navigation";
import ViewToggle from "../view-toggle";
import { useTableRegistration } from "@/hooks/useTableRegistration";

type ViewMode = "table" | "cards";

type DevicesTableProps = {
  showPagination?: boolean;
  searchParams?: {
    page: string;
    limit: string;
  };
  currentView: ViewMode;
  onViewChange: (view: ViewMode) => void;
};

const DevicesTable = ({
  showPagination = true,
  searchParams = { page: "1", limit: "5" },
  currentView,
  onViewChange,
}: DevicesTableProps) => {
  const t = useTranslations() as TFunction;
  const locale = useLocale();
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});

  const limit = parseInt(searchParams.limit ?? 5, 10);
  const page = parseInt(searchParams.page ?? 1, 10);
  const searchParam = useSearchParams();
  const search = searchParam.get("search") || "";

  const { devices, pagination, isLoading, error } = useDeviceMutations({
    page,
    limit,
    search,
  });

  const totalCount = pagination?.count || 0;

  useTableRegistration("devices", columns);

  return (
    <>
      <DataTable
        data={devices}
        dataCount={totalCount}
        columns={columns}
        tableContainerClass="min-h-[240px] text-start"
        title={t("people.devices-page.table.title")}
        meta={{
          t,
          locale,
        }}
        rowSelection={rowSelection}
        onRowSelectionChange={setRowSelection}
        headerActions={
          <ViewToggle currentView={currentView} onViewChange={onViewChange} />
        }
        translationPrefix="people.devices-page.table"
        tableId="devices"
        isLoading={isLoading}
        initialLimit={5}
        error={error}
        tableHeadStyle="text-start"
        tableCellStyle="text-start"
        hideColumns={true}
      />
      <div className="w-full pt-[18px]">
        {showPagination && (
          <PaginationWithLinks
            page={pagination?.page ?? Number(page)}
            pageSize={Number(limit)}
            totalCount={Number(totalCount || 0)}
            firstLastCounts={{
              firstCount: pagination?.from ?? 1,
              lastCount: pagination?.to ?? 5,
            }}
            pageSizeSelectOptions={{
              pageSizeOptions: [5, 10, 25, 30, 45, 50],
              pageSizeSearchParam: "limit",
            }}
            isLoading={isLoading}
            isDisabled={!devices?.length}
          />
        )}
      </div>
    </>
  );
};

export default DevicesTable;
