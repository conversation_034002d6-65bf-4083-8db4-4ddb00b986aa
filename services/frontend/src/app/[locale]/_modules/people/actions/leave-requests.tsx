"use server";

import { peopleService } from "@/services/api/people";

import { getTranslations } from "next-intl/server";
import { TFunction } from "@/types";
import { ApprovalActionResponse } from "../type/approval-request";
import { leaveDateUpdateSchema } from "../schemas/leaveSchema";

export async function withdrawLeaveRequest(
  id: string,
  employeeId?: string,
): Promise<ApprovalActionResponse> {
  try {
    if (!id) {
      throw new Error("Leave ID is required");
    }

    if (!employeeId) {
      throw new Error("Employee ID is required");
    }

    // Use the new API endpoint for withdrawal
    await peopleService.withdrawLeaveRequest(employeeId, id);

    return {
      data: {
        id,
        status: "withdrawn",
        updatedAt: new Date().toISOString(),
      },
    };
  } catch (err) {
    return {
      error:
        err instanceof Error
          ? err
          : new Error("Unknown error during withdraw leave request"),
    };
  }
}

export async function acceptLeaveRequest(
  id: string,
  employeeId: string,
  approvalRequestId?: string,
  comment: string = "",
): Promise<ApprovalActionResponse> {
  try {
    if (!id) {
      throw new Error("Leave ID is required");
    }

    if (!employeeId) {
      throw new Error("Employee ID is required");
    }

    if (!approvalRequestId) {
      throw new Error("Approval request ID is required");
    }

    // Use the new API endpoint for approval
    await peopleService.approveLeaveRequest(approvalRequestId, comment);

    return {
      data: {
        id,
        status: "approved",
        updatedAt: new Date().toISOString(),
      },
    };
  } catch (err) {
    return {
      error:
        err instanceof Error
          ? err
          : new Error("Unknown error during accept leave request"),
    };
  }
}

export async function rejectLeaveRequest(
  id: string,
  employeeId: string,
  approvalRequestId?: string,
  comment: string = "",
): Promise<ApprovalActionResponse> {
  try {
    if (!id) {
      throw new Error("Leave ID is required");
    }

    if (!employeeId) {
      throw new Error("Employee ID is required");
    }

    if (!approvalRequestId) {
      throw new Error("Approval request ID is required");
    }

    // Use the new API endpoint for rejection
    await peopleService._rejectLeaveRequest(approvalRequestId, comment);

    return {
      data: {
        id,
        status: "rejected",
        updatedAt: new Date().toISOString(),
      },
    };
  } catch (err) {
    return {
      error:
        err instanceof Error
          ? err
          : new Error("Unknown error during reject leave request"),
    };
  }
}

export async function updateLeaveDates(
  id: string,
  employeeId: string,
  startDate: string,
  endDate: string,
  leaveDuration?: string,
  leaveType?: string,
): Promise<{
  data?: {
    id: string;
    startDate: string;
    endDate: string;
    updatedAt: string;
    leaveDuration?: string;
  };
  error?: Error;
}> {
  try {
    // Get translations for validation messages
    const t = (await getTranslations()) as TFunction;

    // Validate using Zod schema
    const validationResult = leaveDateUpdateSchema(t).safeParse({
      id,
      employeeId,
      startDate: new Date(startDate),
      endDate: new Date(endDate),
      leaveDuration,
      leaveType,
    });

    if (!validationResult.success) {
      // Extract the first error message
      const errorMessage =
        validationResult.error.errors[0]?.message ||
        "Invalid leave date update data";
      throw new Error(errorMessage);
    }
    // For overlap checking, we need to create Date objects
    const startDateObj = new Date(startDate);
    const endDateObj = new Date(endDate);

    // Check for overlapping leave periods
    try {
      const employeeLeaves =
        await peopleService.getEmployeeLeaveDetails(employeeId);

      // Filter to only include pending or approved leaves that are not the current leave
      const activeLeaves = employeeLeaves.data.filter(
        (leave) =>
          leave.id !== id &&
          (leave.attributes.status === "waiting" ||
            leave.attributes.status === "approved"),
      );

      // Check for overlaps
      for (const leave of activeLeaves) {
        const leaveStartDate = new Date(leave.attributes.start_date);
        const leaveEndDate = new Date(leave.attributes.end_date);

        // Check if the new leave period overlaps with an existing leave
        if (startDateObj <= leaveEndDate && endDateObj >= leaveStartDate) {
          throw new Error(
            "The leave period overlaps with another pending or approved leave",
          );
        }
      }
    } catch (error) {
      // We'll continue even if we can't check for overlaps, as this might be a network issue
    }

    // Use the people service to update the leave dates
    await peopleService.updateLeaveDates(
      employeeId,
      id,
      startDate,
      endDate,
      leaveDuration,
    );

    return {
      data: {
        id,
        startDate,
        endDate,
        leaveDuration,
        updatedAt: new Date().toISOString(),
      },
    };
  } catch (err) {
    return {
      error:
        err instanceof Error
          ? err
          : new Error("Unknown error during leave date update"),
    };
  }
}
