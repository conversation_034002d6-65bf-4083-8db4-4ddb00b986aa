"use server";

import { ActionState } from "@/types";
import { handleError } from "@/lib/utils";
import { peopleService } from "@/services/api/people";
import { TEmployee } from "../type/employee";

export async function saveEmployee(
  employeeId: string | null,
  _prevState: ActionState<TEmployee>,
  formData: FormData,
): Promise<ActionState<TEmployee>> {
  try {
    const isUpdate = !!employeeId;

    const savedEmployee = await peopleService.saveEmployee(
      formData,
      employeeId || undefined,
    );

    return {
      success: isUpdate
        ? "Employee updated successfully"
        : "Employee added successfully",
      error: "",
      issues: [],
      data: savedEmployee,
    };
  } catch (err) {
    const isUpdateForError = !!employeeId;
    return handleError(
      err,
      isUpdateForError ? "Failed to update employee" : "Failed to add employee",
      [],
    );
  }
}
