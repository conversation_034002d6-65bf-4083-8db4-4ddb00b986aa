import useSWR from "swr";
import { fetcher } from "@/services/fetcher";
import { EmployeeLeavesStats } from "../../type/employee-leaves";

// Define the API response type for leave statistics
type ApiLeaveStatsResponse = {
  data: {
    id: string;
    type: string;
    attributes: EmployeeLeavesStats;
  };
  meta: Record<string, unknown>;
};

export const useEmployeeLeavesStats = (employeeId: string) => {
  const { data, error, isLoading, mutate } = useSWR<ApiLeaveStatsResponse>(
    employeeId ? `/api/employees/${employeeId}/leaves-stats` : null,
    fetcher,
  );

  return {
    leavesStats: data?.data?.attributes,
    isLoading,
    error,
    mutate,
  };
};

type APIEmployeeStatistics = {
  data: {
    employee_id: number;
    period: {
      start_date: string;
      end_date: string;
    };
    statistics: {
      leaves_taken: number;
      arrived_late: number;
      departed_early: number;
      absent: number;
    };
  };
};

export const useEmployeeLeavesStatistics = (employeeId: string) => {
  const { data, error, isLoading, mutate } = useSWR<APIEmployeeStatistics>(
    employeeId ? `/api/employees/${employeeId}/leaves` : null,
    fetcher,
  );

  return {
    leavesStatistics: data?.data,
    isLoading,
    error,
    mutate,
  };
};
