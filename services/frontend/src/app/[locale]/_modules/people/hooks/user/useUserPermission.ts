import useSWR from "swr";
import { fetcher } from "@/services/fetcher";
import { Permission } from "@/types/auth";
import { TSystems } from "@/types";

interface PermissionsResponse {
  data: Permission[];
  meta: {
    scope: string;
    count: number;
  };
}

export const useUserPermission = (system?: TSystems) => {
  // Fetch permissions if a valid system is provided (including 'core')
  const shouldFetch = system && system !== undefined;

  const { data, error, isLoading, mutate } = useSWR<PermissionsResponse>(
    shouldFetch ? `/api/users/me/permissions?scope=${system}` : null,
    fetcher,
  );

  return {
    permission: data?.data,
    isLoading,
    error,
    mutate,
  };
};
