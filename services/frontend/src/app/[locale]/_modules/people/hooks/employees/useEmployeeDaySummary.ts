import useSWR from "swr";
import { EmployeeAttendanceAttributes } from "../../type/employee";
import { fetcher } from "@/services/fetcher";
import { AttendanceMeta, AttendancePeriod } from "@/types/employee/daySummary";

export interface AttendancePeriodsResponse {
  data: AttendancePeriod[];
  meta: AttendanceMeta;
}

export const useGetDayStatistic = (employeeId: string, date: string) => {
  const { data, error, isLoading, mutate } = useSWR<AttendancePeriodsResponse>(
    employeeId && date
      ? `/api/attendance/periods/daily_records?date=${date}&employee_id=${employeeId}`
      : null,
    fetcher,
  );

  return {
    attendanceStats: data?.data,
    meta: data?.meta,
    isLoading,
    error,
    mutate,
  };
};
