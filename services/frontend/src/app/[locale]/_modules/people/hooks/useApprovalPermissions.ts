"use client";

import { useCurrentEmployee } from "@/hooks/people/getCurrentEmployee";
import { ApprovalRequestData } from "../type/approval-request";
import {
  hasUserAlreadyActed,
  isUserApprover,
  getApprovalStatus,
  getUserActionInfo,
} from "../utils/approval-utils";

/**
 * This hook provides all the necessary information about a user's ability to act on approval requests
 * @returns Object containing permission flags and status information
 */
export const useApprovalPermissions = (
  approvalRequest: ApprovalRequestData | undefined,
) => {
  const { profile: currentEmployee } = useCurrentEmployee();
  const currentUserId = currentEmployee?.attributes?.user_id;

  if (!currentUserId) {
    return {
      canAct: false,
      hasActed: false,
      isApprover: false,
      shouldShowButtons: false,
      userAction: undefined,
      userActionInfo: { hasActed: false },
      approvalStatus: { status: "pending" as const, isCompleted: false },
      currentUserId: 0,
      currentEmployee: null,
    };
  }

  // Get user action status for current step (for canAct logic)
  const {
    hasActed,
    action: userAction,
    canAct,
  } = hasUserAlreadyActed(approvalRequest, currentUserId);

  const isApprover = isUserApprover(approvalRequest, currentUserId);
  const approvalStatus = getApprovalStatus(approvalRequest);
  const userActionInfo = getUserActionInfo(approvalRequest, currentUserId);
  const shouldShowButtons = approvalStatus.status === "pending";

  return {
    // Permission flags
    canAct,
    hasActed,
    isApprover,
    shouldShowButtons,
    // User action details
    userAction,
    userActionInfo,
    // Approval status
    approvalStatus,
    // Current user info
    currentUserId,
    currentEmployee,
  };
};
