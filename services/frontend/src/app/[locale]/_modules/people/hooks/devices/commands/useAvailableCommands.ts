import useS<PERSON> from "swr";
import { TAvailableCommandsResponse } from "../../../type/devices/usersList";
import { fetcher } from "@/services/fetcher";

export function useAvailableCommands(deviceId: string) {
  const apiUrl = deviceId
    ? `/api/attendance/devices/${deviceId}/commands/available`
    : null;

  const { data, error, isLoading, mutate } = useSWR<TAvailableCommandsResponse>(
    apiUrl,
    fetcher,
  );

  return {
    commands: data?.data || [],
    meta: {
      device_id: deviceId,
      device_name: "Device " + deviceId, // We don't have device name in this response
      adapter_type: "zkteco", // We don't have adapter type in this response
      supports_commands: (data?.data?.length ?? 0) > 0,
    },
    error,
    isLoading,
    mutate,
  };
}
