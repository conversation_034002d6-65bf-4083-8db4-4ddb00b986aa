"use client";

import { useState } from "react";
import { useToastMessage } from "@/hooks/use-toast-message";
import { useTranslations } from "next-intl";
import {
  deleteUserRole,
  addUserRole,
} from "../../actions/employee-role-project-actions";

type UseEmployeeRoleProjectMutationsProps = {
  employeeId: string;
  onSuccess?: () => void;
};

export const useEmployeeRoleProjectMutations = ({
  employeeId,
  onSuccess,
}: UseEmployeeRoleProjectMutationsProps) => {
  const [isDeleting, setIsDeleting] = useState(false);
  const [isAdding, setIsAdding] = useState(false);
  const { showToast } = useToastMessage();
  const t = useTranslations();

  // Delete role project
  const deleteRoleProject = async (userRoleId: string) => {
    if (!employeeId || !userRoleId) return;

    try {
      setIsDeleting(true);

      // Call the server action to delete the user role
      const result = await deleteUserRole(employeeId, userRoleId);

      // If the deletion failed, throw an error
      if (!result.success) {
        throw new Error(result.error);
      }

      showToast(
        "success",
        t("people.employees-page.profile.roles-projects.delete.success"),
      );

      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      showToast(
        "error",
        t("people.employees-page.profile.roles-projects.delete.error"),
      );
    } finally {
      setIsDeleting(false);
    }
  };

  // Add role project
  const addRoleProject = async (
    roleId: string,
    projectId: string | undefined,
  ) => {
    if (!employeeId || !roleId) return;
    const isGlobal = !projectId || projectId.trim() === "";
    try {
      setIsAdding(true);

      // Call the server action to add the user role
      const result = await addUserRole(employeeId, roleId, projectId || "");

      // If the addition failed, throw an error
      if (!result.success) {
        throw new Error(result.error);
      }

      showToast(
        "success",
        t(
          isGlobal
            ? "people.employees-page.profile.roles-projects.add-global-role.success"
            : "people.employees-page.profile.roles-projects.add.success",
        ),
      );

      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      showToast(
        "error",
        t(
          isGlobal
            ? "people.employees-page.profile.roles-projects.add-global-role.error"
            : "people.employees-page.profile.roles-projects.add.error",
        ),
      );
    } finally {
      setIsAdding(false);
    }
  };

  return {
    deleteRoleProject,
    addRoleProject,
    isDeleting,
    isAdding,
  };
};
