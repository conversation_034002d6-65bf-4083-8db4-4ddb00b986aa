import useS<PERSON> from "swr";
import { fetcher } from "@/services/fetcher";
import { useSearchParams } from "next/navigation";
import { TSalaryPackageResponse } from "../../type/salary-package";

export const useSalaryPackageHistory = (
  page: number,
  limit: number,
  sortedBy: string = "start_date",
  employeeId: string | number,
) => {
  const searchParams = useSearchParams();
  const filters = searchParams.get("filters") || "";

  // Build the API URL following the finance endpoint pattern
  let apiUrl = `/api/finance/salary_packages?sort=${sortedBy}&page=${page}&limit=${limit}&filter[employee_id_eq]=${employeeId}`;

  // Add additional filter parameters directly to the URL
  if (filters) {
    try {
      // Parse the filters parameter
      const filterParams = new URLSearchParams(filters);

      // Add each filter parameter directly to the API URL
      filterParams.forEach((value, key) => {
        // Only include actual filter parameters, not the group parameters
        if (key.startsWith("filter[") && !key.startsWith("group[")) {
          apiUrl += `&${key}=${encodeURIComponent(value)}`;
        }
      });
    } catch (error) {
      console.error("Error parsing filters:", error);
    }
  }

  const { data, error, isLoading, mutate } = useSWR<TSalaryPackageResponse>(
    apiUrl,
    fetcher,
  );

  const from = data?.meta?.pagination?.from;
  const to = data?.meta?.pagination?.to;

  return {
    salaryPackageHistory: data?.data ?? [],
    totalCount: data?.meta?.pagination?.count ?? 0,
    isLoading,
    error,
    mutate,
    pagination: {
      firstResult: from,
      lastResult: to,
      limit: Number(limit),
      page: Number(page),
    },
  };
};
