import useS<PERSON> from "swr";
import { useToastMessage } from "@/hooks/use-toast-message";
import { useState } from "react";
import { TDevice } from "../../type/devices/device";
import { deleteDevice as deleteDeviceAction } from "../../actions/device-action";
import { fetcher } from "@/services/fetcher";

export function useDevice(deviceId: string) {
  const { showToast } = useToastMessage();
  const [isDeleting, setIsDeleting] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const apiUrl = deviceId ? `/api/attendance/devices/${deviceId}` : null;

  const {
    data: rawData,
    error,
    isLoading,
    mutate,
  } = useSWR<{ data: TDevice }>(apiUrl, fetcher);

  const data = rawData?.data;

  // Refresh single device data
  const refreshDevice = async () => {
    try {
      setIsRefreshing(true);
      // Force SWR to actually wait for the API call to complete
      await mutate(fetcher(apiUrl!));
      showToast("success", "Device data refreshed");
    } catch (error) {
      showToast("error", "Failed to refresh device data");
    } finally {
      setIsRefreshing(false);
    }
  };

  // Update single device optimistically
  const updateDevice = async (updatedDevice: TDevice) => {
    try {
      await mutate({ data: updatedDevice }, false);
      return true;
    } catch (error) {
      return false;
    }
  };

  // Delete single device
  const deleteDevice = async () => {
    try {
      if (!deviceId) return false;

      setIsDeleting(true);
      const result = await deleteDeviceAction(deviceId);

      if (result.error) {
        showToast("error", result.error);
        return false;
      }

      showToast("success", "Device deleted successfully");
      return true;
    } catch (error) {
      showToast("error", "Failed to delete device");
      return false;
    } finally {
      setIsDeleting(false);
    }
  };

  return {
    data,
    error,
    isLoading,
    refreshDevice,
    updateDevice,
    deleteDevice,
    isDeleting,
    isRefreshing,
  };
}
