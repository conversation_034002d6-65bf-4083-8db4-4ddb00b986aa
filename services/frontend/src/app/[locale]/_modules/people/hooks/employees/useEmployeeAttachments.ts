import { useState } from "react";
import { fetcher } from "@/services/fetcher";
import { useToastMessage } from "@/hooks/use-toast-message";
import {
  renameAttachment as renameAttachmentAction,
  deleteAttachment as deleteAttachmentAction,
  createAttachment as createAttachmentAction,
} from "@/app/[locale]/_modules/people/actions/attachments";
import { useOptimisticMutation } from "@/hooks/ues-optimistic-mutatation";
import {
  TEmployeeAttachmentsResponse,
  TEmployeeAttachment,
} from "../../type/employee";

export const useEmployeeAttachments = (employeeId: string) => {
  const [isRenaming, setIsRenaming] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const { showToast } = useToastMessage();

  const apiKey = employeeId ? `/api/employees/${employeeId}/attachments` : "";

  const { data, error, isLoading, mutateData } = useOptimisticMutation<
    TEmployeeAttachmentsResponse,
    { attachmentId?: string; newName?: string; newAttachment?: File }
  >({
    key: apiKey,
    fetcher,
    defaultData: {
      data: [],
      meta: {
        pagination: {
          count: 0,
          page: 1,
          limit: 20,
          from: 0,
          to: 0,
        },
      },
    },
    swrOptions: {
      keepPreviousData: true,
      dedupingInterval: 5000,
      revalidateAfterMutation: true,
    },
    mutations: {
      createAttachment: {
        updateFn: (currentData, { newAttachment }) => {
          if (!currentData || !newAttachment) return currentData;

          const tempAttachment: TEmployeeAttachment = {
            id: `temp-${Date.now()}`,
            type: "attachment",
            attributes: {
              filename: newAttachment.name,
              content_type: newAttachment.type,
              created_at: new Date().toISOString(),
              url: "",
              bytes_size: newAttachment.size,
            },
          };

          return {
            ...currentData,
            data: [...currentData.data, tempAttachment],
            meta: {
              ...currentData.meta,
              pagination: {
                ...currentData.meta.pagination,
                count: currentData.meta.pagination.count + 1,
              },
            },
          };
        },
        mutationFn: async ({ newAttachment }) => {
          if (!newAttachment) throw new Error("No file provided");
          setIsCreating(true);

          try {
            const formData = new FormData();
            formData.append("attachments[]", newAttachment);
            await createAttachmentAction(employeeId, formData);
            showToast("success", "Attachment uploaded successfully");
            return { data: undefined };
          } catch (error) {
            showToast("error", "Failed to upload attachment");
            return { error: error as Error };
          } finally {
            setIsCreating(false);
          }
        },
      },
      renameAttachment: {
        updateFn: (currentData, { attachmentId, newName }) => {
          if (!currentData || !newName) return currentData;

          return {
            ...currentData,
            data: currentData.data.map((attachment) =>
              attachment.id === attachmentId
                ? {
                    ...attachment,
                    attributes: {
                      ...attachment.attributes,
                      filename: newName,
                    },
                  }
                : attachment,
            ),
          };
        },
        mutationFn: async ({ attachmentId, newName }) => {
          if (!attachmentId || !newName) throw new Error("Missing parameters");
          setIsRenaming(true);

          try {
            const res = (await renameAttachmentAction(
              employeeId,
              attachmentId,
              newName,
            )) as { error?: string };
            if (res?.error) throw new Error(res.error);
            showToast("success", "Attachment renamed successfully");
            return { data: undefined };
          } catch (error) {
            showToast(
              "error",
              error instanceof Error ? error.message : "Rename failed",
            );
            throw error;
          } finally {
            setIsRenaming(false);
          }
        },
      },
      deleteAttachment: {
        updateFn: (currentData, { attachmentId }) => {
          if (!currentData) return currentData;

          const updatedData = currentData.data.filter(
            (attachment) => attachment.id !== attachmentId,
          );

          return {
            ...currentData,
            data: updatedData,
            meta: {
              ...currentData.meta,
              pagination: {
                ...currentData.meta.pagination,
                count: updatedData.length,
              },
            },
          };
        },
        mutationFn: async ({ attachmentId }) => {
          if (!attachmentId) throw new Error("Attachment ID is required");
          setIsDeleting(true);

          try {
            await deleteAttachmentAction(employeeId, attachmentId);
            showToast("success", "Attachment deleted successfully");
            return { data: undefined };
          } catch (error) {
            showToast(
              "error",
              error instanceof Error ? error.message : "Delete failed",
            );
            throw error;
          } finally {
            setIsDeleting(false);
          }
        },
      },
    },
  });

  const renameAttachment = async (attachmentId: string, newName: string) => {
    try {
      await mutateData("renameAttachment", { attachmentId, newName });
      return true;
    } catch {
      return false;
    }
  };

  const deleteAttachment = async (attachmentId: string) => {
    try {
      await mutateData("deleteAttachment", { attachmentId });
      return true;
    } catch {
      return false;
    }
  };

  const createAttachment = async (file: File) => {
    try {
      await mutateData("createAttachment", { newAttachment: file });
      return true;
    } catch {
      return false;
    }
  };

  return {
    attachments: data?.data || [],
    totalCount: data?.meta?.pagination?.count || 0,
    isLoading,
    error,
    mutate: mutateData,
    isRenaming,
    isDeleting,
    isCreating,
    renameAttachment,
    deleteAttachment,
    createAttachment,
  };
};
