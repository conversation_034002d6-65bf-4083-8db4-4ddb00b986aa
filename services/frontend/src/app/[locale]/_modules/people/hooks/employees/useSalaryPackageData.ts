import { TSalaryPackageData } from "../../type/salary-package";
import { ApprovalRequestData } from "../../type/approval-request";

/**
 * Hook to extract, organize, and relate salary package data and approval requests
 */
export const useSalaryPackageData = (included: any[]) => {
  const allSalaryPackages = included.filter(
    (item): item is TSalaryPackageData => item.type === "salary_package",
  );

  const allApprovalRequests = included.filter(
    (item): item is ApprovalRequestData => item.type === "approval_request",
  );

  // Identify packages by status
  const editableSalaryPackage =
    allSalaryPackages.find(
      (pkg) => pkg.attributes.editable && !pkg.attributes.active,
    ) || null;

  const pendingSalaryPackage =
    allSalaryPackages.find(
      (pkg) =>
        !pkg.attributes.editable &&
        !pkg.attributes.submittable &&
        pkg.attributes.status === "pending_approval",
    ) || null;

  const submittedSalaryPackage =
    allSalaryPackages.find(
      (pkg) =>
        !pkg.attributes.submittable &&
        pkg.attributes.active &&
        !pkg.attributes.editable,
    ) || null;

  // Prioritize which package to display
  const salaryPackage =
    editableSalaryPackage ||
    pendingSalaryPackage ||
    submittedSalaryPackage ||
    null;

  // Helper to attach approval request to a given package
  const attachApprovalRequest = (pkg: TSalaryPackageData | null): void => {
    if (!pkg) return;

    const matchingRequest = allApprovalRequests.find(
      (req) => (req.attributes as any).approvable?.id === parseInt(pkg.id),
    );

    if (matchingRequest) {
      pkg.relationships.approval_request = {
        data: { id: matchingRequest.id, type: "approval_request" },
      };
    }
  };

  // Attach approval requests to matching packages
  attachApprovalRequest(editableSalaryPackage);
  attachApprovalRequest(pendingSalaryPackage);
  attachApprovalRequest(submittedSalaryPackage);

  // Current salary package status (if any)
  const salaryPackageStatus = salaryPackage?.attributes.status || null;

  return {
    allSalaryPackages,
    allApprovalRequests,
    salaryPackage,
    editableSalaryPackage,
    pendingSalaryPackage,
    submittedSalaryPackage,
    salaryPackageStatus,
  };
};
