"use client";

import { useState } from "react";
import { useToastMessage } from "@/hooks/use-toast-message";
import { useTranslations } from "next-intl";
import { TFunction } from "@/types";
import { CreateProjectData, UpdateProjectData } from "@/types/core/project";

type UseProjectMutationsProps = {
  page?: string | number;
  limit?: string | number;
  sort?: string;
  onSuccess?: () => void;
  onError?: (error: Error) => void;
};

export const useProjectMutations = ({
  page,
  limit,
  sort,
  onSuccess,
  onError,
}: UseProjectMutationsProps = {}) => {
  const [isCreating, setIsCreating] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const { showToast } = useToastMessage();
  const t = useTranslations() as TFunction;

  // Create project
  const createProject = async (data: CreateProjectData) => {
    try {
      setIsCreating(true);

      // Send data in the format expected by the CoreAPI (through API route)
      const response = await fetch("/api/projects", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error("Failed to create project");
      }

      const result = await response.json();

      showToast("success", t("projects.page.create.success"));

      if (onSuccess) {
        onSuccess();
      }

      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      showToast("error", t("projects.page.create.error"));

      if (onError) {
        onError(error instanceof Error ? error : new Error(errorMessage));
      }

      throw error;
    } finally {
      setIsCreating(false);
    }
  };

  // Update project
  const updateProject = async (id: string, data: UpdateProjectData) => {
    try {
      setIsUpdating(true);

      // Send data in the format expected by the CoreAPI (through API route)
      const response = await fetch(`/api/projects/${id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error("Failed to update project");
      }

      const result = await response.json();

      showToast("success", t("projects.page.update.success"));

      if (onSuccess) {
        onSuccess();
      }

      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      showToast("error", t("projects.page.update.error"));

      if (onError) {
        onError(error instanceof Error ? error : new Error(errorMessage));
      }

      throw error;
    } finally {
      setIsUpdating(false);
    }
  };

  // Delete project
  const deleteProject = async (id: string) => {
    try {
      setIsDeleting(true);

      const response = await fetch(`/api/projects/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete project");
      }

      showToast("success", t("projects.page.delete.success"));

      if (onSuccess) {
        onSuccess();
      }

      return true;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      showToast("error", t("projects.page.delete.error"));

      if (onError) {
        onError(error instanceof Error ? error : new Error(errorMessage));
      }

      throw error;
    } finally {
      setIsDeleting(false);
    }
  };

  return {
    createProject,
    updateProject,
    deleteProject,
    isCreating,
    isUpdating,
    isDeleting,
  };
};
