"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { PAGES } from "@/enums";
import useFormFields from "@/hooks/useFormFields";
import { onSubmitLogin } from "@/server/actions/auth";
import { loginSchema, loginSchemaType } from "@/schemas/auth";
import { ActionState, TFunction, TinputField } from "@/types";
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader } from "lucide-react";
import React, {
  startTransition,
  useActionState,
  useEffect,
  useRef,
} from "react";
import { useForm } from "react-hook-form";
import Link from "next/link";
import { useLocale, useTranslations } from "next-intl";
import FormFieldRenderer from "@/components/auth/fields/form-field-renderer";
import ErrorMessage from "@/components/errorMessage";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { Locale } from "@/i18n/routing";
import { resetTokenExpiredFlag } from "@/lib/token-expiration-manager";

const LoginForm = () => {
  const t = useTranslations() as TFunction;
  const locale: Locale = useLocale() as Locale;
  const { toast } = useToast();
  const { getFormFields } = useFormFields({ formType: PAGES.LOGIN, t });
  const formRef = useRef<HTMLFormElement | null>(null);
  const router = useRouter();
  const initialState: ActionState<null> = {
    error: "",
    success: "",
    issues: [],
  };

  const [state, submitAction, isPending] = useActionState(
    onSubmitLogin,
    initialState,
  );

  const form = useForm<loginSchemaType>({
    resolver: zodResolver(loginSchema(t)),
    defaultValues: {
      email: "",
      password: "",
    },
    mode: "all",
  });

  useEffect(() => {
    if (state.success) {
      resetTokenExpiredFlag();
      toast({
        className: "toast-success",
        description: state.success,
      });

      router.replace(`/${locale}/auth/select-system`);
    }
  }, [state, locale, router, toast]);

  return (
    <Form {...form}>
      <form
        ref={formRef}
        action={submitAction}
        onSubmit={(e) => {
          e.preventDefault();
          form.handleSubmit(() => {
            startTransition(() => {
              if (formRef.current) {
                submitAction(new FormData(formRef.current!));
              }
            });
          })(e);
        }}
        className="space-y-3 mt-10"
      >
        {(getFormFields() as TinputField<loginSchemaType>[])?.map(
          (fieldConfig) => {
            return (
              <FormFieldRenderer
                key={fieldConfig.name}
                fieldConfig={fieldConfig}
                form={form}
                isPending={isPending}
              />
            );
          },
        )}
        {/* handle form errors */}
        {state?.error && (!state?.issues || state.issues.length === 0) && (
          <ErrorMessage message={state.error} />
        )}
        {Array.isArray(state?.issues) && state?.issues?.length > 0 && (
          <ErrorMessage message={state.issues[0]} />
        )}
        <Button
          disabled={isPending}
          type="submit"
          className="w-full h-12 max-h-12 !mt-10"
        >
          {!isPending ? (
            t("auth.login.buttons.login")
          ) : (
            <Loader className="animate-spin text-white" />
          )}
        </Button>
        <Button
          type="button"
          asChild
          variant={"outline"}
          className="p-0 w-full h-12 max-h-12 "
          disabled={isPending}
        >
          <Link href={`/${locale}/auth/forgot-password`}>
            {t("auth.login.buttons.forgotPass")}
          </Link>
        </Button>
      </form>
    </Form>
  );
};

export default LoginForm;
