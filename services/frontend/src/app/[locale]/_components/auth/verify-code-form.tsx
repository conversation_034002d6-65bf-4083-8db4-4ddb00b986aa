"use client";

import { startTransition, useEffect, useRef } from "react";
import { useSearchParams } from "next/navigation";
import { useForm } from "react-hook-form";
import { useActionState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { onSubmitVerifyCode } from "@/server/actions/auth";
import ErrorMessage from "@/components/errorMessage";
import { useLocale, useTranslations } from "next-intl";
import { PAGES } from "@/enums";
import { Form } from "@/components/ui/form";
import FormFieldRenderer from "@/components/auth/fields/form-field-renderer";
import { ActionState, TFunction, TinputField } from "@/types";
import { verifyCodeSchema, verifyCodeSchemaType } from "@/schemas/auth";
import { Loader } from "lucide-react";
import { zodResolver } from "@hookform/resolvers/zod";
import useFormFields from "@/hooks/useFormFields";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { Locale } from "@/i18n/routing";

const VerifyCodeForm = () => {
  const t = useTranslations() as TFunction;
  const searchParams = useSearchParams();
  const locale: Locale = useLocale() as Locale;
  const router = useRouter();
  const { toast } = useToast();
  const { getFormFields } = useFormFields({ formType: PAGES.VERIFYCODE, t });
  const formRef = useRef<HTMLFormElement | null>(null);
  const initialState: ActionState<null> = {
    error: "",
    success: "",
    issues: [],
  };

  const [state, submitAction, isPending] = useActionState(
    onSubmitVerifyCode,
    initialState,
  );

  const form = useForm<verifyCodeSchemaType>({
    resolver: zodResolver(verifyCodeSchema(t)),
    defaultValues: {
      code: "",
    },
    mode: "all",
  });
  const isSubmitSuccessful = form.formState.isSubmitSuccessful;
  useEffect(() => {
    if (isSubmitSuccessful) {
      form.reset();
    }
  }, [form, form.reset, isSubmitSuccessful]);

  useEffect(() => {
    const urlCode = searchParams.get("code");
    if (urlCode) {
      form.setValue("code", urlCode);
      startTransition(async () => {
        const formData = new FormData();
        formData.set("code", urlCode);
        submitAction(formData); // Auto-submit if code is present
      });
    }
  }, [searchParams, form, form.setValue, submitAction]);

  useEffect(() => {
    if (state.success) {
      toast({ className: "toast-success", description: state.success });
      router.replace(`/${locale}/auth/reset-password`);
    }
  }, [state.success, router, locale, toast]);

  return (
    <Form {...form}>
      <form
        ref={formRef}
        action={submitAction}
        onSubmit={(e) => {
          e.preventDefault();
          form.handleSubmit(() => {
            startTransition(async () => {
              if (formRef.current) {
                await submitAction(new FormData(formRef.current!));
              }
            });
          })(e);
        }}
        className="space-y-3"
      >
        {(getFormFields() as TinputField<verifyCodeSchemaType>[])?.map(
          (fieldConfig) => {
            return (
              <FormFieldRenderer
                key={fieldConfig.name}
                fieldConfig={fieldConfig}
                form={form}
                isPending={isPending}
              />
            );
          },
        )}
        {/* handle form errors */}
        {state?.error && (!state?.issues || state.issues.length === 0) && (
          <ErrorMessage message={state.error} />
        )}
        {Array.isArray(state?.issues) && state?.issues?.length > 0 && (
          <ErrorMessage message={state.issues[0]} />
        )}
        <Button
          disabled={isPending}
          type="submit"
          className="w-full h-12 max-h-12 !mt-10"
        >
          {!isPending ? (
            t("auth.verificationCode.buttons.verify")
          ) : (
            <Loader className="animate-spin text-white" />
          )}
        </Button>
      </form>
    </Form>
  );
};

export default VerifyCodeForm;
