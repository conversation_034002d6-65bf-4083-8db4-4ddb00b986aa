"use client";

import React from "react";
import { useTranslations } from "next-intl";
import { useAttendanceSettings } from "@/hooks/use-system-settings";
import { Skeleton } from "@/components/ui/skeleton";
import AutoSaveSettingsForm from "@/components/settings/auto-save-settings-form";

const AttendanceSettings = () => {
  const t = useTranslations();
  const { settings, isLoading, error, mutate } = useAttendanceSettings();

  if (isLoading || !settings) {
    return (
      <div className="space-y-6">
        <div className="space-y-2">
          <Skeleton className="h-4 w-[200px]" />
          <Skeleton className="h-4 w-[300px]" />
        </div>
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="space-y-2">
              <Skeleton className="h-4 w-[200px]" />
              <Skeleton className="h-10 w-full" />
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <div className="text-red-600 mb-2">
          {t("common.error.loadingFailed")}
        </div>
        <p className="text-muted-foreground text-sm">
          {error.message || "Failed to load attendance settings"}
        </p>
      </div>
    );
  }

  if (settings.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">
          {t("common.settings.attendance.noSettings")}
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="relative mb-10 md:mb-auto">
        <h3 className="text-lg max-md:hidden font-semibold mb-0.5">
          {t("common.settings.tabs.attendance")}
        </h3>
        <p className="text-muted-foreground text-sm absolute md:relative max-md:-top-2">
          {t("common.settings.attendance.description")}
        </p>
      </div>

      {/* Use AutoSaveSettingsForm once for all settings */}
      <AutoSaveSettingsForm
        settings={settings}
        onSettingUpdate={() => mutate()}
      />
    </div>
  );
};

export default AttendanceSettings;
