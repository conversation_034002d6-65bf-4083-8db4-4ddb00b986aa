"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { PAGES } from "@/enums";
import useFormFields from "@/hooks/useFormFields";
import {
  TChangePasswordFormProps,
  TinputField,
  TFunction,
  ActionState,
} from "@/types";
import { TUser } from "@/types/auth";
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader } from "lucide-react";
import React, {
  startTransition,
  useActionState,
  useEffect,
  useRef,
} from "react";
import { useForm } from "react-hook-form";
import { useTranslations } from "next-intl";
import FormFieldRenderer from "@/components/auth/fields/form-field-renderer";
import ErrorMessage from "@/components/errorMessage";
import { editProfileSchema, editProfileSchemaType } from "@/schemas/settings";
import { onSubmitEditForm } from "@/server/actions/settings";
import { useUser } from "@/contexts/user-provider";
import { useToastMessage } from "@/hooks/use-toast-message";

const ChangePasswordForm = ({ backToGeneral }: TChangePasswordFormProps) => {
  const t = useTranslations() as TFunction;
  const { getFormFields } = useFormFields({ formType: PAGES.EDITPROFILE, t });
  const formRef = useRef<HTMLFormElement | null>(null);
  const { showToast } = useToastMessage();
  const initialState: ActionState<TUser> = {
    error: "",
    success: "",
    issues: [],
  };

  const [state, submitAction, isPending] = useActionState(
    onSubmitEditForm,
    initialState,
  );
  const { user, mutateUser } = useUser();

  const form = useForm<editProfileSchemaType>({
    resolver: zodResolver(editProfileSchema(t)),
    defaultValues: {
      name: user?.name ?? "",
      email: user?.email ?? "",
      password: "",
      avatar: user?.avatar,
    },
    mode: "all",
  });

  useEffect(() => {
    if (state.success && state.data) {
      mutateUser(state.data as TUser, { revalidate: false });
      showToast("success", "profile updated");
    }

    if (state.error || (state.issues && state.issues.length > 0)) {
      const errorMessage =
        state.error || (state.issues && state.issues[0]) || "An error occurred";
      showToast("error", errorMessage);
    }
  }, [state, t]);

  return (
    <div className="relative">
      <Form {...form}>
        <form
          ref={formRef}
          action={submitAction}
          onSubmit={(e) => {
            e.preventDefault();
            form.handleSubmit((value) => {
              const formData = new FormData(formRef.current!);
              const isNameChanged = form.formState.dirtyFields.name;
              const isImageChanged = form.formState.dirtyFields.avatar;

              if (!isNameChanged && !isImageChanged) {
                showToast("error", t("settings.editProfile.mesaages.edit"));
                return;
              }
              if (!(value.avatar instanceof File)) {
                formData.delete("avatar");
              }
              startTransition(async () => {
                if (formRef.current) {
                  await submitAction(formData);
                }
              });
            })(e);
          }}
          className="space-y-3"
        >
          {(getFormFields() as TinputField<editProfileSchemaType>[])?.map(
            (fieldConfig) => {
              return (
                <FormFieldRenderer
                  key={fieldConfig.name}
                  fieldConfig={fieldConfig}
                  form={form}
                  isPending={isPending}
                  backToGeneral={backToGeneral}
                />
              );
            },
          )}

          {/* handle form errors */}
          {state?.error && (!state?.issues || state.issues.length === 0) && (
            <ErrorMessage message={state.error} />
          )}
          {Array.isArray(state?.issues) && state?.issues?.length > 0 && (
            <ErrorMessage message={state.issues[0]} />
          )}
          <div className="flex justify-end items-center gap-4">
            <Button
              disabled={isPending}
              type="submit"
              className="w-full max-w-[155px] md:max-w-[149px] h-12 max-h-12 !mt-10"
            >
              {!isPending ? (
                <span>{t("settings.buttons.update")}</span>
              ) : (
                <Loader className="animate-spin text-white" />
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default ChangePasswordForm;
