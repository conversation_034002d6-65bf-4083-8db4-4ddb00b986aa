"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { PAGES } from "@/enums";
import useFormFields from "@/hooks/useFormFields";
import {
  TChangePasswordFormProps,
  TinputField,
  TFunction,
  ActionState,
} from "@/types";
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader } from "lucide-react";
import React, {
  startTransition,
  useActionState,
  useEffect,
  useRef,
} from "react";
import { useForm } from "react-hook-form";
import { useTranslations } from "next-intl";
import FormFieldRenderer from "@/components/auth/fields/form-field-renderer";
import { useRouter } from "next/navigation";
import ErrorMessage from "@/components/errorMessage";
import { useToast } from "@/hooks/use-toast";
import {
  changePasswordSchema,
  changePasswordSchemaType,
} from "@/schemas/settings";
import { onSubmitChangePassword } from "@/server/actions/settings";

const ChangePasswordForm = ({ backToGeneral }: TChangePasswordFormProps) => {
  const t = useTranslations() as TFunction;
  const router = useRouter();
  const { toast } = useToast();
  const { getFormFields } = useFormFields({ formType: PAGES.CHANGEPASS, t });
  const formRef = useRef<HTMLFormElement | null>(null);
  const initialState: ActionState<null> = {
    error: "",
    success: "",
    issues: [],
  };

  const [state, submitAction, isPending] = useActionState(
    onSubmitChangePassword,
    initialState,
  );

  const form = useForm<changePasswordSchemaType>({
    resolver: zodResolver(changePasswordSchema(t)),
    defaultValues: {
      currentPassword: "",
      password: "",
      confirmPassword: "",
    },
    mode: "all",
  });
  const isSubmitSuccessful = form.formState.isSubmitSuccessful;
  useEffect(() => {
    if (isSubmitSuccessful) {
      form.reset();
    }
  }, [form, form.reset, isSubmitSuccessful]);
  useEffect(() => {
    if (state.success) {
      toast({ description: "password changed", className: "bg-background-v2" });
      if (backToGeneral) {
        backToGeneral();
      }
    }
  }, [state, router, toast, backToGeneral]);

  return (
    <Form {...form}>
      <form
        ref={formRef}
        action={submitAction}
        onSubmit={(e) => {
          e.preventDefault();
          form.handleSubmit(() => {
            startTransition(async () => {
              if (formRef.current) {
                await submitAction(new FormData(formRef.current!));
              }
            });
          })(e);
        }}
        className="flex-1 h-full flex flex-col justify-between gap-2"
      >
        <div className="space-y-3">
          {(getFormFields() as TinputField<changePasswordSchemaType>[])?.map(
            (fieldConfig) => {
              return (
                <FormFieldRenderer
                  key={fieldConfig.name}
                  fieldConfig={fieldConfig}
                  form={form}
                  isPending={isPending}
                />
              );
            },
          )}
          {/* handle form errors */}
          {state?.error && (!state?.issues || state.issues.length === 0) && (
            <ErrorMessage message={state.error} />
          )}
          {Array.isArray(state?.issues) && state?.issues?.length > 0 && (
            <ErrorMessage message={state.issues[0]} />
          )}
        </div>
        <div className="flex justify-end items-center gap-4">
          <Button
            variant={"outline"}
            onClick={backToGeneral}
            disabled={isPending}
            type="button"
            className="w-full max-w-[155px] md:max-w-[149px] h-12 max-h-12 !mt-10"
          >
            {!isPending ? (
              <span>{t("settings.buttons.back")}</span>
            ) : (
              <Loader className="animate-spin text-white" />
            )}
          </Button>
          <Button
            disabled={isPending}
            type="submit"
            className="w-full max-w-[155px] md:max-w-[149px] h-12 max-h-12 !mt-10"
          >
            {!isPending ? (
              <span>{t("settings.buttons.update")}</span>
            ) : (
              <Loader className="animate-spin text-white" />
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default ChangePasswordForm;
