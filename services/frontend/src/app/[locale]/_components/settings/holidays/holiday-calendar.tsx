"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { useLocale, useTranslations } from "next-intl";
import { LANGUAGES } from "@/constants/enum";
import { Locale } from "@/i18n/routing";
import Calendar from "@/components/calendar";
import { Plus } from "lucide-react";
import { cn } from "@/lib/utils";
import { THoliday } from "@/types/settings/holidays";

type HolidayCalendarProps = {
  holidays: THoliday[];
  onAddHoliday?: () => void;
  onHolidayClick?: (holiday: THoliday) => void;
  className?: string;
};

// Handle event click
type CalendarEventData = {
  holiday?: THoliday;
  [key: string]: unknown;
};

// Handle day click for holidays
type DayDataItem = {
  extendedProps?: {
    holiday?: THoliday;
    [key: string]: unknown;
  };
  [key: string]: unknown;
};

// Custom day cell content for holidays
type DayCellArg = {
  date: Date;
  isToday: boolean;
  [key: string]: unknown;
};

export default function HolidayCalendar({
  holidays,
  onAddHoliday,
  onHolidayClick,
  className,
}: HolidayCalendarProps) {
  const t = useTranslations();
  const locale: Locale = useLocale() as Locale;
  const isAr = locale === LANGUAGES.ARABIC;

  // Convert holidays to FullCalendar events format
  const calendarEvents = Array.isArray(holidays)
    ? holidays.map((holiday, index) => {
        const holidayData = holiday.attributes;
        const startDate = new Date(holidayData.start_date);
        const endDate = new Date(holidayData.end_date);
        return {
          id: `${holiday.id}-${index}`,
          title: holiday.attributes.name,
          start: startDate,
          end: endDate,
          backgroundColor: `hsl(var(--secondary))`, // Default color for holidays
          borderColor: "none",
          textColor: "#333",
          extendedProps: {
            holiday: holiday,
          },
        };
      })
    : [];

  const handleEventClick = (eventData: CalendarEventData) => {
    const holiday = eventData.holiday;
    if (holiday && onHolidayClick) {
      onHolidayClick(holiday);
    }
  };

  const handleDayClick = (date: Date, dayData: DayDataItem[]) => {
    if (dayData.length > 0 && onHolidayClick) {
      const holidayToEdit = dayData[0];
      if (holidayToEdit.extendedProps?.holiday) {
        onHolidayClick(holidayToEdit.extendedProps.holiday);
      }
    }
  };

  const renderHolidayDayCell = (
    arg: DayCellArg,
    dayHolidays: CalendarEventData[],
  ) => {
    const hasHolidays = dayHolidays.length > 0;

    return (
      <div
        className={`flex flex-col items-center justify-center h-full w-full ${
          hasHolidays ? "hover:bg-gray-50" : ""
        }`}
      >
        <div
          className={cn(
            `flex items-center justify-center text-gray-500 w-8 h-8 mx-auto border rounded-full`,
            { "bg-secondary !text-white": arg.isToday },
            { "line-through bg-gray-200": hasHolidays },
          )}
        >
          {String(arg.date.getDate()).padStart(2, "0")}
        </div>
      </div>
    );
  };

  return (
    <div className={className}>
      {/* Calendar Header */}
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold">
          {t("settings.holidays.title")}
        </h3>
        {onAddHoliday && (
          <Button onClick={onAddHoliday} className="h-10 text-xs">
            {<Plus />}
            {t("settings.holidays.calendar.add-holiday")}
          </Button>
        )}
      </div>

      {/* FullCalendar */}
      <Calendar
        events={calendarEvents}
        dayData={calendarEvents}
        locale={isAr ? "ar" : "en"}
        isRtl={isAr}
        loading={false}
        onEventClick={handleEventClick}
        onDayClick={handleDayClick}
        customDayCellContent={renderHolidayDayCell}
      />
    </div>
  );
}
