"use client";

import { Switch } from "@/components/ui/switch";
import { useLocale, useTranslations } from "next-intl";
import { useState, useEffect } from "react";
import { Locale } from "@/i18n/routing";

const NotificationSound = () => {
  const locale: Locale = useLocale() as Locale;
  const [muteSound, setMuteSound] = useState(false);
  const t = useTranslations();

  // On component mount, initialize the muteSound state from localStorage
  useEffect(() => {
    if (typeof window !== "undefined") {
      const storedMute = localStorage.getItem("muteSound");
      if (storedMute !== null) {
        setMuteSound(JSON.parse(storedMute));
      }
    }
  }, []);

  // Update localStorage whenever muteSound changes
  useEffect(() => {
    if (typeof window !== "undefined") {
      localStorage.setItem("muteSound", JSON.stringify(muteSound));
    }
  }, [muteSound]);

  return (
    <div className="flex justify-between items-center w-full flex-row-reverse">
      <Switch
        dir="ltr"
        lang={locale}
        checked={muteSound}
        onCheckedChange={(checked) => setMuteSound(checked)}
      />
      <div className="flex flex-col">
        <span className="text-[#1C1C1C]">
          {t("settings.notifications.muteSound")}
        </span>
        <span className="text-[#656F7D]">
          {t("settings.notifications.disableNotificationSound")}
        </span>
      </div>
    </div>
  );
};

export default NotificationSound;
