#!/bin/bash -e

# Set the DNS domain from the environment variable or default to "example.test"
DNS_DOMAIN="${DOMAIN:-athar.test}"

echo "🔧 Installing Xcode Command Line Tools..."
xcode-select --install || echo "✅ Xcode Command Line Tools already installed."

echo "🔧 Installing Homebrew (if not installed)..."
if ! command -v brew &> /dev/null; then
  /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
  echo 'export PATH="/opt/homebrew/bin:$PATH"' >> ~/.zprofile
  source ~/.zprofile
  echo "✅ Homebrew installed successfully"
else
  echo "✅ Homebrew already installed, skipping..."
fi

echo "🔧 Installing Homebrew dependencies from Brewfile..."
brew bundle

echo "🔧 Installing mise version manager..."
if ! command -v mise &> /dev/null; then
  curl https://mise.run | sh
  echo "✅ mise installed successfully"
else
  echo "✅ mise already installed, skipping..."
fi

#echo "🔧 Registering mise in PATH..."
#if ! grep -q 'mise activate zsh' ~/.zprofile; then
#  echo "eval \"\$(~/.local/bin/mise activate zsh)\"" >> ~/.zprofile
#  echo "✅ mise added to ~/.zprofile"
#else
#  echo "✅ mise already set in ~/.zprofile, skipping..."
#fi

echo "🔧 Registering mise shims in PATH..."
if ! grep -q 'mise activate zsh --shims' ~/.zprofile; then
  echo "eval \"\$(~/.local/bin/mise activate zsh --shims)\"" >> ~/.zprofile
  echo "✅ mise shims added to ~/.zprofile"
else
  echo "✅ mise shims already set in ~/.zprofile, skipping..."
fi

echo "🔧 Adding PostgreSQL binaries to PATH..."
if ! grep -q 'libpq)/bin:$PATH' ~/.zprofile; then
  echo 'export PATH="$(brew --prefix libpq)/bin:$PATH"' >> ~/.zprofile
  echo "✅ PostgreSQL path added to ~/.zprofile"
else
  echo "✅ PostgreSQL path already set, skipping..."
fi

# Read Ruby version from .ruby-version
RUBY_VERSION_FILE=".ruby-version"
if [[ -f "$RUBY_VERSION_FILE" ]]; then
  RUBY_VERSION=$(tr -d '[:space:]' < "$RUBY_VERSION_FILE")
  echo "🔧 Ruby version detected: $RUBY_VERSION"
else
  echo "❌ Error: .ruby-version file not found! Exiting..."
  exit 1
fi

echo "🔧 Installing Ruby $RUBY_VERSION with mise..."
if ! mise list ruby | grep -q "$RUBY_VERSION"; then
  mise install ruby@"$RUBY_VERSION"
  mise use --global ruby@"$RUBY_VERSION"
  echo "✅ Ruby $RUBY_VERSION installed and set as global version"
else
  echo "✅ Ruby $RUBY_VERSION already installed, skipping..."
fi

echo "🔧 Setting up direnv..."
if ! grep -q 'direnv hook zsh' ~/.zprofile; then
  echo 'eval "$(direnv hook zsh)"' >> ~/.zprofile
  echo "✅ direnv added to ~/.zprofile"
else
  echo "✅ direnv already configured, skipping..."
fi

# Ensure direnv.toml exists
DIRENV_TOML="$HOME/.config/direnv/direnv.toml"
mkdir -p "$(dirname "$DIRENV_TOML")"
touch "$DIRENV_TOML"

# If file was newly created, initialize it properly
if ! grep -q '^\[whitelist\]' "$DIRENV_TOML"; then
  echo -e '[whitelist]\nprefix = []' > "$DIRENV_TOML"
  echo "✅ Added missing [whitelist] section to direnv.toml"
fi

# Append ~/workspace without duplicates & Fix empty list issue
if ! grep -q "\"$HOME/workspace\"" "$DIRENV_TOML"; then
  awk -v workspace="\"$HOME/workspace\"" '
  /^\[whitelist\]/ { found=1 }
  found && /^prefix = \[/ {
    if ($0 ~ /\[\]/) { print "prefix = [" workspace "]"; next }
    else { sub(/\]$/, ", " workspace "]") }
  }
  { print }
  ' "$DIRENV_TOML" > "$DIRENV_TOML.tmp" && mv "$DIRENV_TOML.tmp" "$DIRENV_TOML"

  echo "✅ Appended ~/workspace to direnv.toml whitelist"
else
  echo "✅ ~/workspace already exists in direnv.toml, skipping..."
fi

# Set up dnsmasq.d for dynamic configurations
DNSMASQ_DIR="$(brew --prefix)/etc"
DNSMASQ_CONF="$DNSMASQ_DIR/dnsmasq.conf"
DNSMASQ_D_DIR="$DNSMASQ_DIR/dnsmasq.d"
DNSMASQ_DYNAMIC_CONF="$DNSMASQ_D_DIR/$DNS_DOMAIN.conf"

# Ensure dnsmasq.d directory exists
if [ ! -d "$DNSMASQ_D_DIR" ]; then
  echo "Creating missing directory: $DNSMASQ_D_DIR..."
  sudo mkdir -p "$DNSMASQ_D_DIR"
  echo "✅ Directory $DNSMASQ_D_DIR created."
fi

# Ensure only the ".conf" conf-dir line is uncommented
if grep -qE "^\s*#\s*conf-dir=.*dnsmasq.d/.*,*.conf" "$DNSMASQ_CONF"; then
  echo "Uncommenting the correct conf-dir entry for .conf files in dnsmasq.conf..."
  sed -i '' "s|^\s*#\s*\(conf-dir=.*dnsmasq.d/.*,*.conf\)|\1|" "$DNSMASQ_CONF"
  echo "✅ Uncommented conf-dir for .conf files."
elif ! grep -qE "^\s*conf-dir=.*dnsmasq.d/.*,*.conf" "$DNSMASQ_CONF"; then
  echo "conf-dir=$DNSMASQ_D_DIR/,*.conf" | sudo tee -a "$DNSMASQ_CONF"
  echo "✅ Added conf-dir=$DNSMASQ_D_DIR/,*.conf to dnsmasq.conf."
else
  echo "✅ dnsmasq.conf already includes conf-dir for .conf files, skipping..."
fi

# Create dynamic configuration file for the specified domain
if [ ! -f "$DNSMASQ_DYNAMIC_CONF" ]; then
  echo "address=/$DNS_DOMAIN/127.0.0.1" | sudo tee "$DNSMASQ_DYNAMIC_CONF"
  echo "✅ Added dynamic dnsmasq rule for *.$DNS_DOMAIN in $DNSMASQ_DYNAMIC_CONF."
else
  echo "✅ Dynamic dnsmasq rule for *.$DNS_DOMAIN already exists, skipping..."
fi

# Restart dnsmasq service
sudo brew services restart dnsmasq

# Configure macOS resolver for the dynamic domain
RESOLVER_DIR="/etc/resolver"
RESOLVER_FILE="$RESOLVER_DIR/$DNS_DOMAIN"
if [ ! -f "$RESOLVER_FILE" ]; then
  sudo mkdir -p "$RESOLVER_DIR"
  echo "nameserver 127.0.0.1" | sudo tee "$RESOLVER_FILE"
  echo "✅ macOS resolver configured for *.$DNS_DOMAIN."
else
  echo "✅ macOS resolver for *.$DNS_DOMAIN already exists, skipping..."
fi

# Restart networking to apply DNS changes
sudo dscacheutil -flushcache
sudo killall -HUP mDNSResponder
echo "✅ DNS cache flushed and networking restarted."

echo "🔧 Building Docker images..."
if docker-compose build; then
  echo "✅ Docker images built successfully"
else
  echo "❌ Docker build failed!"
  exit 1
fi

echo "🎉 Setup complete! Run 'docker-compose up' to start services."
