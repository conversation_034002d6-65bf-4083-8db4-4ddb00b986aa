#!/bin/bash -e

# Setup QA Secrets Script
# This script sets up the ATHAR_PAT secret in all repositories

echo "🔧 Setting up QA deployment secrets..."

# Check if GitHub CLI is installed and authenticated
if ! command -v gh &> /dev/null; then
    echo "❌ GitHub CLI (gh) is not installed. Please install it first:"
    echo "   brew install gh"
    exit 1
fi

# Check if authenticated
if ! gh auth status &> /dev/null; then
    echo "❌ Not authenticated with GitHub CLI. Please run:"
    echo "   gh auth login"
    exit 1
fi

# Get current token
echo "📝 Getting your GitHub CLI token..."
TOKEN=$(gh auth token)

if [ -z "$TOKEN" ]; then
    echo "❌ Could not get GitHub token. Please check your authentication."
    exit 1
fi

echo "✅ Token retrieved successfully"

# Create/update .env file
echo "📄 Updating .env file..."
if [ ! -f ".env" ]; then
    cp .env.example .env
    echo "✅ Created .env from .env.example"
fi

# Update ATHAR_PAT in .env file
if grep -q "ATHAR_PAT=" .env; then
    # Replace existing ATHAR_PAT
    sed -i.bak "s/ATHAR_PAT=.*/ATHAR_PAT=$TOKEN/" .env
    echo "✅ Updated ATHAR_PAT in .env file"
else
    # Add ATHAR_PAT if it doesn't exist
    echo "ATHAR_PAT=$TOKEN" >> .env
    echo "✅ Added ATHAR_PAT to .env file"
fi

# Set secrets in repositories
echo "🔐 Setting ATHAR_PAT secret in repositories..."

# List of repositories
repos=("athar-ems" "core" "people" "case-manager" "procure" "frontend")

for repo in "${repos[@]}"; do
    echo "  Setting secret in athar-association/$repo..."
    if gh secret set ATHAR_PAT --body "$TOKEN" --repo "athar-association/$repo" 2>/dev/null; then
        echo "  ✅ Success: athar-association/$repo"
    else
        echo "  ⚠️  Warning: Could not set secret in athar-association/$repo (check permissions)"
    fi
done

echo ""
echo "🎉 Setup complete!"
echo ""
echo "📋 Next steps:"
echo "1. Verify secrets: gh secret list --repo athar-association/athar-ems"
echo "2. Check .env file has been updated with your token"
echo "3. Test QA deployment workflow"
echo ""
echo "⚠️  Security reminder:"
echo "- Keep your .env file private (it's in .gitignore)"
echo "- Rotate your token periodically"
echo "- Only share tokens with trusted team members"
